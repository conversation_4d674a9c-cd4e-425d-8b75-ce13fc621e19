import sys
import os
BASE_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(BASE_DIR)
from task.segment import read_cws_tsv_data
from task.postag import read_pos_data
from task.ner import read_ner_tsv_data
from task.dep_transition import read_CoNLL_data
from task.dep_graph import read_CoNLL_data as read_CoNLL_data2
from multi_task_learning import MultiTaskLearning

import torch
import argparse
from collections import OrderedDict


def evalution(args):
    data_home = args.data_home
    _CTB8_HOME = data_home + '/thirdparty/wakespace.lib.wfu.edu/bitstream/handle/10339/39379/LDC2013T21/data/'
    tok = args.tok
    pos = args.pos
    ner = args.ner
    dep = args.dep
    dep_full = args.dep_full
    ignore_proj = args.ignore_proj
    graph = args.graph
    device = args.device
    save_dir = args.save_dir

    eval_portion = None
    if args.demo:
        eval_portion = 200
    eval_dict = OrderedDict()
    if tok:
        CTB8_CWS_DEV = _CTB8_HOME + 'tasks/cws/dev.txt'
        CTB8_CWS_TEST = _CTB8_HOME + 'tasks/cws/test.txt'
        seg_dev = read_cws_tsv_data(CTB8_CWS_DEV)[:eval_portion]
        eval_dict[tok] = seg_dev
        print('Segment: dev size, %d.' % len(seg_dev))
    if pos:
        CTB8_POS_DEV = _CTB8_HOME + 'tasks/pos/dev.tsv'
        CTB8_POS_TEST = _CTB8_HOME + 'tasks/pos/test.tsv'
        pos_dev = read_pos_data(path=CTB8_POS_DEV)[:eval_portion]
        eval_dict[pos] = pos_dev
        print('Postag: dev size,%d.' % len(pos_dev))
    if ner:
        MSRA_NER_DEV = data_home + '/thirdparty/file.hankcs.com/corpus/msra_ner_token_level/word_level.dev.short.tsv'
        MSRA_NER_TEST = data_home + '/thirdparty/file.hankcs.com/corpus/msra_ner_token_level/word_level.test.short.tsv'
        ner_dev = read_ner_tsv_data(MSRA_NER_DEV)[:eval_portion]
        eval_dict[ner] = ner_dev
        print('Ner: dev size,%d.' % len(ner_dev))
    if dep:
        CTB8_DEP_DEV = '../raw_data/dev.conll'
        CTB8_DEP_TEST = '../raw_data/test.conll'
        if graph:
            dep_dev = read_CoNLL_data2(CTB8_DEP_DEV, not ignore_proj)[:eval_portion]
        else:
            dep_dev = read_CoNLL_data(CTB8_DEP_DEV)[:eval_portion]
        eval_dict[dep] = dep_dev
        print('Dep: dev size,%d.' % len(dep_dev))
    if dep_full:
        CTB8_DEP_DEV_HanLP = _CTB8_HOME + 'tasks/dep/dev.conllx'
        CTB8_DEP_TEST_HanLP = _CTB8_HOME + 'tasks/dep/test.conllx'
        if graph:
            dep_dev_HanLP = read_CoNLL_data2(CTB8_DEP_DEV_HanLP, not ignore_proj)[:eval_portion]
        else:
            dep_dev_HanLP = read_CoNLL_data(CTB8_DEP_DEV_HanLP)[:eval_portion]
        eval_dict[dep_full] = dep_dev_HanLP
        print('Dep_HanLP: dev size,%d.' % len(dep_dev_HanLP))

    mlt = MultiTaskLearning()

    mlt.load(save_dir, device=device)
    res = mlt.eval(eval_dict, device=device)
    # print(save_dir, '\n', res)
    # mlt.save('data/save_load_test/')
    # mlt = MultiTaskLearning()
    # mlt.load(save_dir='data/save_load_test/', device=device)
    # res = mlt.eval(eval_dict, device=device)
    # print('data/save_load_test/', '\n', res)
    with open('temp.log', mode='a', encoding='utf-8') as fw:
        fw.write(res + '\n')


parser = argparse.ArgumentParser(description='eval pretrained parameter.')
parser.add_argument('--demo', action='store_true', help='指定该参数将只评估训练集前100个句子。')
parser.add_argument('--data_home', type=str, required=True)  # /home/<USER>/.hanlp
parser.add_argument('--tok', default='', type=str, help='segment task name.')
parser.add_argument('--pos', default='', type=str, help='postag task name.')
parser.add_argument('--ner', default='', type=str, help='ner task name.')
parser.add_argument('--dep', default='', type=str, help='dep parsing task name.')
parser.add_argument('--dep_full', default='', type=str, help='dep parsing task use data provided by hanlp.')
parser.add_argument('--ignore_proj', action='store_true', help='使用该参数，则不会剔除训练和测试语料中不符合投射性的树。')
parser.add_argument('--graph', action='store_true', help='基于图算法，否则基于转化。')
parser.add_argument('--save_dir', type=str, required=True)

parser.add_argument('--device', default='cuda:0' if torch.cuda.is_available() else 'cpu')
args = parser.parse_args()
print(args)
evalution(args)

# --data_home C:\Users\<USER>\AppData\Roaming\hanlp --tok tok --pos pos --ner ner --dep dep --graph --save_dir D:/workspace/pycharm/nlp_project/kbnlp/mlt/data/pretrained/electra_small_tok_pos_ner_dep_rte_dist
