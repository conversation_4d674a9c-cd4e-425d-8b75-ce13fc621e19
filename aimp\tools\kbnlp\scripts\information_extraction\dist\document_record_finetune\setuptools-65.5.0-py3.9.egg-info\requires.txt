
[certs]

[docs]
sphinx>=3.5
jaraco.packaging>=9
rst.linker>=1.9
furo
jaraco.tidelift>=1.4
pygments-github-lexers==0.0.5
sphinx-favicon
sphinx-inline-tabs
sphinx-reredirects
sphinxcontrib-towncrier
sphinx-notfound-page==0.8.3
sphinx-hoverxref<2

[ssl]

[testing]
pytest>=6
pytest-checkdocs>=2.4
pytest-flake8
flake8<5
pytest-enabler>=1.3
pytest-perf
mock
flake8-2020
virtualenv>=13.0.0
wheel
pip>=19.1
jaraco.envs>=2.2
pytest-xdist
jaraco.path>=3.2.0
build[virtualenv]
filelock>=3.4.0
pip_run>=8.8
ini2toml[lite]>=0.9
tomli-w>=1.0.0

[testing-integration]
pytest
pytest-xdist
pytest-enabler
virtualenv>=13.0.0
tomli
wheel
jaraco.path>=3.2.0
jaraco.envs>=2.2
build[virtualenv]
filelock>=3.4.0

[testing:platform_python_implementation != "PyPy"]
pytest-black>=0.3.7
pytest-cov
pytest-mypy>=0.9.1
