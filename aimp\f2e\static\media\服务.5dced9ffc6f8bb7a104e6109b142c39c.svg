<?xml version="1.0" encoding="UTF-8"?>
<svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>形状结合</title>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="首页" transform="translate(-222.000000, -703.000000)">
            <g id="编组" transform="translate(222.000000, 703.000000)">
                <rect id="矩形" x="0" y="0" width="16" height="16"></rect>
                <path d="M8,0.5 C12.1421356,0.5 15.5,3.85786438 15.5,8 C15.5,12.1421356 12.1421356,15.5 8,15.5 C3.85786438,15.5 0.5,12.1421356 0.5,8 C0.5,3.85786438 3.85786438,0.5 8,0.5 Z M8,1.5 C4.41014913,1.5 1.5,4.41014913 1.5,8 C1.5,11.5898509 4.41014913,14.5 8,14.5 C11.5898509,14.5 14.5,11.5898509 14.5,8 C14.5,4.41014913 11.5898509,1.5 8,1.5 Z M11,4.5 C11.5522847,4.5 12,4.97756293 12,5.56666667 L12,8.76666667 C12,9.3557704 11.5522847,9.83333333 11,9.83333333 L8.5,9.83333333 L8.5,11.433 L9.5,11.4333333 C9.77614237,11.4333333 10,11.657191 10,11.9333333 L10,12 C10,12.2761424 9.77614237,12.5 9.5,12.5 L6.5,12.5 C6.22385763,12.5 6,12.2761424 6,12 L6,11.9333333 C6,11.657191 6.22385763,11.4333333 6.5,11.4333333 L7.5,11.433 L7.5,9.83333333 L5,9.83333333 C4.44771525,9.83333333 4,9.3557704 4,8.76666667 L4,5.56666667 C4,4.97756293 4.44771525,4.5 5,4.5 L11,4.5 Z M11,5.56666667 L5,5.56666667 L5,8.76666667 L11,8.76666667 L11,5.56666667 Z" id="形状结合" fill-opacity="0.446514423" fill="#000000" fill-rule="nonzero"></path>
            </g>
        </g>
    </g>
</svg>