# -*- coding: utf-8 -*-
# __author__:SH
# 2022/9/14 16:33
import socket

service_config = {
    "auth":1,
    "server_list":["*********"],
    "is_KaPaaS":1,
    "stop_server":1,
    "text_unit":{"CPU":"2","memory":"2","GPU":"2"},
    "image_unit":{"CPU":"2","memory":"2","GPU":"2"},
    "usual_unit":{"CPU":"2","memory":"2","GPU":"2"}
}



host = "127.0.0.1"
port = 8000
app_url = f"http://{host}:{port}"
prompt_prefix = '文本分类'
prompt_prefix4 = "意图识别"
base_host = socket.gethostname()
base_host = socket.getfqdn(base_host)
# 出现max retries exceeded,主机名称里面有.lan,需要将自己的ip配置到wlan
base_url = f"http://{base_host}:{port}"
