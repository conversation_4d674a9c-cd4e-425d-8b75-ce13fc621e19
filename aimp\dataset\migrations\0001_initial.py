# Generated by Django 4.1.1 on 2022-12-16 02:42

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="DataSet",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("create_time", models.IntegerField(default=1671158522)),
                ("update_time", models.IntegerField(default=1671158522)),
                ("dataset_name", models.CharField(max_length=255)),
                ("create_user", models.CharField(max_length=32, null=True)),
                ("dataset_type", models.IntegerField(default=0)),
                ("path", models.CharField(max_length=255, null=True)),
            ],
            options={"abstract": False,},
        ),
        migrations.CreateModel(
            name="Event",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("event_name", models.<PERSON>r<PERSON>ield(max_length=255)),
                (
                    "dataset",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="dataset.dataset",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="FileBox",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("text", models.CharField(max_length=255)),
                ("bbox", models.CharField(max_length=255)),
                ("bbox_id", models.IntegerField(default=0)),
            ],
        ),
        migrations.CreateModel(
            name="RawImage",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("path", models.TextField()),
                ("name", models.CharField(max_length=255)),
                ("raw_status", models.IntegerField(default=0)),
                ("tesing_user", models.CharField(default=None, max_length=32)),
                ("imgage_width", models.IntegerField(default=1)),
                ("imgage_height", models.IntegerField(default=1)),
                (
                    "data_id",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="dataset.dataset",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="RawText",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("text", models.TextField()),
                ("raw_status", models.IntegerField(default=1)),
                ("tesing_user", models.CharField(default="root", max_length=32)),
                (
                    "data_id",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="dataset.dataset",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Relation",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("relation_name", models.CharField(max_length=255)),
                (
                    "data_id",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="dataset.dataset",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Tag",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("tag_name", models.CharField(max_length=255)),
            ],
        ),
        migrations.CreateModel(
            name="TagIntention",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "data_id",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="dataset.dataset",
                    ),
                ),
                (
                    "tag_id",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="dataset.tag"
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="TextBox",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("bbox_begin", models.IntegerField(default=0)),
                ("bbox_end", models.IntegerField(default=0)),
                (
                    "bbox",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="dataset.filebox",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="TagTextType",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "tag",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="dataset.tag"
                    ),
                ),
                (
                    "text",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="dataset.rawtext",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="TagText",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("tag_begin_x", models.IntegerField(default=0)),
                ("tag_end_x", models.IntegerField(default=0)),
                ("tag_begin_width", models.IntegerField(default=0)),
                ("tag_end_width", models.IntegerField(default=0)),
                ("tag_line_pos", models.IntegerField(default=0)),
                ("tag_line", models.IntegerField(default=0)),
                ("tag_begin_line", models.IntegerField(default=0)),
                ("tag_end_line", models.IntegerField(default=0)),
                ("tag_begin_line_pos", models.IntegerField(default=0)),
                ("tag_end_line_pos", models.IntegerField(default=0)),
                ("tag_text_begin", models.IntegerField(default=0)),
                ("tag_text_end", models.IntegerField(default=0)),
                (
                    "rawtext",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="dataset.rawtext",
                    ),
                ),
                (
                    "tag",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="dataset.tag"
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="TagSlot",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("slot", models.CharField(max_length=255)),
                (
                    "tagintentions",
                    models.ForeignKey(
                        default=1,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="dataset.tagintention",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="TagImageType",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "image",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="dataset.rawimage",
                    ),
                ),
                (
                    "tag",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="dataset.tag"
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="TagImage",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("param", models.TextField(default="")),
                ("area", models.FloatField(default=0.0)),
                ("segmentation", models.TextField(default=None)),
                ("bbox", models.CharField(default="", max_length=255)),
                (
                    "image",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="dataset.rawimage",
                    ),
                ),
                (
                    "tag",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="dataset.tag"
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="SlotText",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("tag_begin_x", models.IntegerField(default=0)),
                ("tag_end_x", models.IntegerField(default=0)),
                ("tag_begin_width", models.IntegerField(default=0)),
                ("tag_end_width", models.IntegerField(default=0)),
                ("tag_line_pos", models.IntegerField(default=0)),
                ("tag_line", models.IntegerField(default=0)),
                ("tag_begin_line", models.IntegerField(default=0)),
                ("tag_end_line", models.IntegerField(default=0)),
                ("tag_begin_line_pos", models.IntegerField(default=0)),
                ("tag_end_line_pos", models.IntegerField(default=0)),
                ("tag_text_begin", models.IntegerField(default=0)),
                ("tag_text_end", models.IntegerField(default=0)),
                (
                    "raw",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="dataset.rawtext",
                    ),
                ),
                (
                    "slot",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="dataset.tagslot",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="RelationText",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("relation_begin_x", models.IntegerField(default=0)),
                ("relation_end_x", models.IntegerField(default=0)),
                ("relation_begin_line", models.IntegerField(default=0)),
                ("relation_end_line", models.IntegerField(default=0)),
                ("relation_begin_pos", models.IntegerField(default=0)),
                ("relation_end_pos", models.IntegerField(default=0)),
                (
                    "rawtext_id",
                    models.ForeignKey(
                        default=1,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="dataset.rawtext",
                    ),
                ),
                (
                    "relation_begin",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="relation_begin",
                        to="dataset.tagtext",
                    ),
                ),
                (
                    "relation_end",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="relation_end",
                        to="dataset.tagtext",
                    ),
                ),
                (
                    "relation_id",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="dataset.relation",
                    ),
                ),
            ],
        ),
        migrations.AddField(
            model_name="relation",
            name="tag_begin",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="tag_begin",
                to="dataset.tag",
            ),
        ),
        migrations.AddField(
            model_name="relation",
            name="tag_end",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="tag_end",
                to="dataset.tag",
            ),
        ),
        migrations.CreateModel(
            name="RawIntention",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "raw",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="dataset.rawtext",
                    ),
                ),
                (
                    "tag_type",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="dataset.tagintention",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="RawFilesImage",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("path", models.TextField()),
                ("name", models.CharField(max_length=255)),
                ("raw_status", models.IntegerField(default=0)),
                ("testing_user", models.CharField(default="root", max_length=32)),
                ("image_width", models.IntegerField(default=1)),
                ("image_height", models.IntegerField(default=1)),
                ("image_id", models.IntegerField(default=1)),
                (
                    "data_id",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="dataset.dataset",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="MarkBox",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "tag",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="dataset.tag"
                    ),
                ),
                (
                    "text",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="dataset.textbox",
                    ),
                ),
            ],
        ),
        migrations.AddField(
            model_name="filebox",
            name="raw_file",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="dataset.rawfilesimage"
            ),
        ),
        migrations.CreateModel(
            name="EventText",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("event_begin_x", models.IntegerField(default=0)),
                ("event_end_x", models.IntegerField(default=0)),
                ("event_begin_line", models.IntegerField(default=0)),
                ("event_end_line", models.IntegerField(default=0)),
                ("event_begin_pos", models.IntegerField(default=0)),
                ("event_end_pos", models.IntegerField(default=0)),
                (
                    "event_begin",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="event_begin",
                        to="dataset.tagtext",
                    ),
                ),
                (
                    "event_end",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="event_end",
                        to="dataset.tagtext",
                    ),
                ),
                (
                    "event_id",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="dataset.event"
                    ),
                ),
                (
                    "rawtext_id",
                    models.ForeignKey(
                        default=1,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="dataset.rawtext",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="EventTag",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "event",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="dataset.event"
                    ),
                ),
                (
                    "tag",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="dataset.tag"
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="DatasetTag",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("color", models.CharField(max_length=255)),
                ("shoutcut_key", models.CharField(max_length=255)),
                (
                    "data_id",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="dataset.dataset",
                    ),
                ),
                (
                    "tag_id",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="dataset.tag"
                    ),
                ),
            ],
        ),
    ]
