# coding:utf-8
# Copyright (c) 2021  PaddlePaddle Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License"
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import contextlib
import copy
import csv
import json
import math
import os
import pickle
import re
import traceback
import warnings
from collections import OrderedDict, namedtuple
from dataclasses import dataclass
from datetime import datetime
from functools import cmp_to_key
from typing import Any, Dict, List, Optional, Tuple, Union
import numpy as np
import six

from PIL import Image

from ..scripts.information_extraction.metric import SpanEvaluator
from ..scripts.information_extraction.log import logger


def cut_chinese_sent(para):
    """
    Cut the Chinese sentences more precisely, reference to "https://blog.csdn.net/blmoistawinde/article/details/82379256".
    """
    para = re.sub(r"([。！？\?])([^”’])", r"\1\n\2", para)
    para = re.sub(r"(\.{6})([^”’])", r"\1\n\2", para)
    para = re.sub(r"(\…{2})([^”’])", r"\1\n\2", para)
    para = re.sub(r"([。！？\?][”’])([^，。！？\?])", r"\1\n\2", para)
    para = para.rstrip()
    return para.split("\n")


class SchemaTree(object):
    """
    Implementataion of SchemaTree
    """

    def __init__(self, name="root", children=None):
        self.name = name
        self.children = []
        self.prefix = None
        self.parent_relations = None
        self.parent = None
        if children is not None:
            for child in children:
                self.add_child(child)

    def __repr__(self):
        return self.name

    def add_child(self, node):
        assert isinstance(node, SchemaTree), "The children of a node should be an instacne of SchemaTree."
        self.children.append(node)


def get_id_and_prob(span_set, offset_mapping):
    """
    Return text id and probability of predicted spans
    Args:
        span_set (set): set of predicted spans.
        offset_mapping (list[int]): list of pair preserving the
                index of start and end char in original text pair (prompt + text) for each token.
    Returns:
        sentence_id (list[tuple]): index of start and end char in original text.
        prob (list[float]): probabilities of predicted spans.
    """
    prompt_end_token_id = offset_mapping[1:].index([0, 0])
    bias = offset_mapping[prompt_end_token_id][1] + 1
    for idx in range(1, prompt_end_token_id + 1):
        offset_mapping[idx][0] -= bias
        offset_mapping[idx][1] -= bias

    sentence_id = []
    prob = []
    for start, end in span_set:
        prob.append(start[1] * end[1])
        start_id = offset_mapping[start[0]][0]
        end_id = offset_mapping[end[0]][1]
        sentence_id.append((start_id, end_id))
    return sentence_id, prob


def dbc2sbc(s):
    rs = ""
    for char in s:
        code = ord(char)
        if code == 0x3000:
            code = 0x0020
        else:
            code -= 0xFEE0
        if not (0x0021 <= code and code <= 0x7E):
            rs += char
            continue
        rs += chr(code)
    return rs


# @dataclass
# class DataCollatorGP:
#     tokenizer: PretrainedTokenizerBase
#     padding: Union[bool, str, PaddingStrategy] = True
#     max_length: Optional[int] = None
#     label_maps: Optional[dict] = None
#     task_type: Optional[str] = None
#
#     def __call__(self, features: List[Dict[str, Union[List[int], paddle.Tensor]]]) -> Dict[str, paddle.Tensor]:
#         new_features = [{k: v for k, v in f.items() if k not in ["offset_mapping", "text"]} for f in features]
#
#         batch = self.tokenizer.pad(
#             new_features,
#             padding=self.padding,
#         )
#
#         batch = [paddle.to_tensor(batch[k]) for k in batch.keys()]
#         batch.append([feature["offset_mapping"] for feature in features])
#         batch.append([feature["text"] for feature in features])
#         return batch
#
#
# def gp_decode(batch_outputs, offset_mappings, texts, label_maps, task_type="relation_extraction"):
#     if task_type == "entity_extraction":
#         batch_ent_results = []
#         for entity_output, offset_mapping, text in zip(batch_outputs[0].numpy(), offset_mappings, texts):
#             entity_output[:, [0, -1]] -= np.inf
#             entity_output[:, :, [0, -1]] -= np.inf
#             entity_probs = F.softmax(paddle.to_tensor(entity_output), axis=1).numpy()
#             ent_list = []
#             for l, start, end in zip(*np.where(entity_output > 0.0)):
#                 ent_prob = entity_probs[l, start, end]
#                 start, end = (offset_mapping[start][0], offset_mapping[end][-1])
#                 ent = {
#                     "text": text[start:end],
#                     "type": label_maps["id2entity"][str(l)],
#                     "start_index": start,
#                     "probability": ent_prob,
#                 }
#                 ent_list.append(ent)
#             batch_ent_results.append(ent_list)
#         return batch_ent_results
#     else:
#         batch_ent_results = []
#         batch_rel_results = []
#         for entity_output, head_output, tail_output, offset_mapping, text in zip(
#             batch_outputs[0].numpy(),
#             batch_outputs[1].numpy(),
#             batch_outputs[2].numpy(),
#             offset_mappings,
#             texts,
#         ):
#             entity_output[:, [0, -1]] -= np.inf
#             entity_output[:, :, [0, -1]] -= np.inf
#             entity_probs = F.softmax(paddle.to_tensor(entity_output), axis=1).numpy()
#             head_probs = F.softmax(paddle.to_tensor(head_output), axis=1).numpy()
#             tail_probs = F.softmax(paddle.to_tensor(tail_output), axis=1).numpy()
#
#             ents = set()
#             ent_list = []
#             for l, start, end in zip(*np.where(entity_output > 0.0)):
#                 ent_prob = entity_probs[l, start, end]
#                 ents.add((start, end))
#                 start, end = (offset_mapping[start][0], offset_mapping[end][-1])
#                 ent = {
#                     "text": text[start:end],
#                     "type": label_maps["id2entity"][str(l)],
#                     "start_index": start,
#                     "probability": ent_prob,
#                 }
#                 ent_list.append(ent)
#             batch_ent_results.append(ent_list)
#
#             rel_list = []
#             for sh, st in ents:
#                 for oh, ot in ents:
#                     p1s = np.where(head_output[:, sh, oh] > 0.0)[0]
#                     p2s = np.where(tail_output[:, st, ot] > 0.0)[0]
#                     ps = set(p1s) & set(p2s)
#                     for p in ps:
#                         rel_prob = head_probs[p, sh, oh] * tail_probs[p, st, ot]
#                         if task_type == "relation_extraction":
#                             rel = {
#                                 "subject": text[offset_mapping[sh][0] : offset_mapping[st][1]],
#                                 "predicate": label_maps["id2relation"][str(p)],
#                                 "object": text[offset_mapping[oh][0] : offset_mapping[ot][1]],
#                                 "subject_start_index": offset_mapping[sh][0],
#                                 "object_start_index": offset_mapping[oh][0],
#                                 "probability": rel_prob,
#                             }
#                         else:
#                             rel = {
#                                 "aspect": text[offset_mapping[sh][0] : offset_mapping[st][1]],
#                                 "sentiment": label_maps["id2relation"][str(p)],
#                                 "opinion": text[offset_mapping[oh][0] : offset_mapping[ot][1]],
#                                 "aspect_start_index": offset_mapping[sh][0],
#                                 "opinion_start_index": offset_mapping[oh][0],
#                                 "probability": rel_prob,
#                             }
#                         rel_list.append(rel)
#             batch_rel_results.append(rel_list)
#         return (batch_ent_results, batch_rel_results)


def pad_batch_data(
    insts,
    pad_idx=0,
    max_seq_len=None,
    return_pos=False,
    return_input_mask=False,
    return_max_len=False,
    return_num_token=False,
    return_seq_lens=False,
    pad_2d_pos_ids=False,
    pad_segment_id=False,
    select=False,
    extract=False,
):
    """
    Pad the instances to the max sequence length in batch, and generate the
    corresponding position data and attention bias.
    """
    return_list = []
    max_len = max(len(inst) for inst in insts) if max_seq_len is None else max_seq_len
    # Any token included in dict can be used to pad, since the paddings' loss
    # will be masked out by weights and make no effect on parameter gradients.
    if pad_2d_pos_ids:
        boxes = [x + [[0, 0, 0, 0]] * (max_len - len(x)) for x in insts]
        boxes = np.array(boxes, dtype="int64")
        return boxes

    inst_data = np.array([inst + list([pad_idx] * (max_len - len(inst))) for inst in insts])
    return_list += [inst_data.astype("int64").reshape([-1, max_len, 1])]

    # position data
    if return_pos:
        inst_pos = np.array([list(range(0, len(inst))) + [pad_idx] * (max_len - len(inst)) for inst in insts])

        return_list += [inst_pos.astype("int64").reshape([-1, max_len, 1])]

    if return_input_mask:
        # This is used to avoid attention on paddings.
        input_mask_data = np.array([[1] * len(inst) + [0] * (max_len - len(inst)) for inst in insts])
        input_mask_data = np.expand_dims(input_mask_data, axis=-1)
        return_list += [input_mask_data.astype("float32")]

    if return_max_len:
        return_list += [max_len]

    if return_num_token:
        num_token = 0
        for inst in insts:
            num_token += len(inst)
        return_list += [num_token]

    if return_seq_lens:
        seq_lens = np.array([len(inst) for inst in insts])
        return_list += [seq_lens.astype("int64").reshape([-1, 1])]

    return return_list if len(return_list) > 1 else return_list[0]


def get_doc_pred(result, ans_pos, example, tokenizer, feature, do_lower_case, all_key_probs, example_index):
    def _compute_softmax(scores):
        """Compute softmax probability over raw logits."""
        if len(scores) == 0:
            return []

        max_score = None
        for score in scores:
            if max_score is None or score > max_score:
                max_score = score

        exp_scores = []
        total_sum = 0.0
        for score in scores:
            x = math.exp(score - max_score)
            exp_scores.append(x)
            total_sum += x

        probs = []
        for score in exp_scores:
            probs.append(score / total_sum)
        return probs

    preds = []
    for start_index, end_index in ans_pos:
        # process data
        tok_tokens = feature.tokens[start_index : end_index + 1]
        tok_text = " ".join(tok_tokens)
        # De-tokenize WordPieces that have been split off.
        tok_text = tok_text.replace(" ##", "")
        tok_text = tok_text.replace("##", "")
        tok_text = tok_text.strip()
        tok_text = "".join(tok_text.split())

        orig_doc_start = feature.token_to_orig_map[start_index]
        orig_doc_end = feature.token_to_orig_map[end_index]
        orig_tokens = example.doc_tokens[orig_doc_start : orig_doc_end + 1]

        # Clean whitespace
        orig_text = "".join(["".join(x) for x in orig_tokens])
        final_text = get_final_text(tok_text, orig_text, tokenizer, do_lower_case)

        probs = []
        for idx, logit in enumerate(result.seq_logits[start_index : end_index + 1]):
            if idx == 0:
                # -1 is for B in  OIB or I in OI
                probs.append(_compute_softmax(logit)[-1])
            else:
                # 1 is for I in OIB or I in OI
                probs.append(_compute_softmax(logit)[1])
        avg_prob = sum(probs) / len(probs)
        preds.append({"value": final_text, "prob": round(avg_prob, 2), "start": orig_doc_start, "end": orig_doc_end})
    return preds


def get_final_text(pred_text, orig_text, tokenizer, do_lower_case):
    """Project the tokenized prediction back to the original text."""

    def _strip_spaces(text):
        ns_chars = []
        ns_to_s_map = OrderedDict()
        for (i, c) in enumerate(text):
            if c == " ":
                continue
            ns_to_s_map[len(ns_chars)] = i
            ns_chars.append(c)
        ns_text = "".join(ns_chars)
        return (ns_text, ns_to_s_map)

    tok_text = " ".join(tokenizer.tokenize(orig_text))

    start_position = tok_text.find(pred_text)
    if start_position == -1:
        return orig_text
    end_position = start_position + len(pred_text) - 1

    (orig_ns_text, orig_ns_to_s_map) = _strip_spaces(orig_text)
    (tok_ns_text, tok_ns_to_s_map) = _strip_spaces(tok_text)

    if len(orig_ns_text) != len(tok_ns_text):
        return orig_text

    # We then project the characters in `pred_text` back to `orig_text` using
    # the character-to-character alignment.
    tok_s_to_ns_map = {}
    for (i, tok_index) in six.iteritems(tok_ns_to_s_map):
        tok_s_to_ns_map[tok_index] = i

    orig_start_position = None
    if start_position in tok_s_to_ns_map:
        ns_start_position = tok_s_to_ns_map[start_position]
        if ns_start_position in orig_ns_to_s_map:
            orig_start_position = orig_ns_to_s_map[ns_start_position]

    if orig_start_position is None:
        return orig_text

    orig_end_position = None
    if end_position in tok_s_to_ns_map:
        ns_end_position = tok_s_to_ns_map[end_position]
        if ns_end_position in orig_ns_to_s_map:
            orig_end_position = orig_ns_to_s_map[ns_end_position]

    if orig_end_position is None:
        return orig_text

    output_text = orig_text[orig_start_position : (orig_end_position + 1)]
    return output_text


def find_bio_pos(label):
    """find answer position from BIO label"""
    e = []
    cand_ans = []
    last_l = None
    for idx, l in enumerate(label):
        if l == "O":
            if e:
                cand_ans.append([e[0], e[-1]])
            e = []
        elif l.startswith("B"):
            if last_l == "O" or last_l is None:
                if len(e) != 0:
                    e = []
                e.append(idx)
            else:  # I B
                if e:
                    cand_ans.append([e[0], e[-1]])
                    e = []
                e.append(idx)
        elif l.startswith("I"):
            if len(e) == 0:
                continue
            else:
                e.append(idx)
        last_l = l
    if e:
        cand_ans.append([e[0], e[-1]])
    return cand_ans


def viterbi_decode(logits):
    np_logits = np.array(logits)  # shape: L * D
    length, dim = np_logits.shape
    f = np.zeros(np_logits.shape)
    path = [["" for i in range(dim)] for j in range(length)]
    label_scheme = "OIB"
    # oib label 0:O, 1:I, 2:B
    # illegal matrix: [O, I ,B, start, end] * [O, I, B, start, end]
    illegal = np.array([[0, -1, 0, -1, 0], [0, 0, 0, -1, 0], [0, 0, 0, 0, 0], [0, -1, 0, 0, 0], [-1, -1, -1, -1, -1]])
    illegal = illegal * 1000

    f[0, :] = np_logits[0, :] + illegal[3, :3]
    path[0] = [label_scheme[i] for i in range(dim)]

    for step in range(1, length):
        last_s = f[step - 1, :]
        for d in range(dim):
            cand_score = illegal[:3, d] + last_s + np_logits[step, d]
            f[step, d] = np.max(cand_score)
            path[step][d] = path[step - 1][np.argmax(cand_score)] + label_scheme[d]
    final_path = path[-1][np.argmax(f[-1, :])]
    return final_path


def find_answer_pos(logits, feature):
    start_index = -1
    end_index = -1
    ans = []
    cand_ans = []

    best_path = viterbi_decode(logits)
    cand_ans = find_bio_pos(best_path)

    for start_index, end_index in cand_ans:
        is_valid = True
        if start_index not in feature.token_to_orig_map:
            is_valid = False
        if end_index not in feature.token_to_orig_map:
            is_valid = False
        if not feature.token_is_max_context.get(start_index, False):
            is_valid = False
        if end_index < start_index:
            is_valid = False
        if is_valid:
            ans.append([start_index, end_index])

    return ans


def calEuclidean(x_list, y_list):
    """
    Calculate euclidean distance
    """
    if x_list is None or y_list is None:
        return None
    else:
        dist = np.sqrt(np.square(x_list[0] - y_list[0]) + np.square(x_list[1] - y_list[1]))
        return dist


def longestCommonSequence(question_tokens, context_tokens):
    """
    Longest common sequence
    """
    max_index = -1
    max_len = 0
    m, n = len(question_tokens), len(context_tokens)
    dp = [[0] * (n + 1) for _ in range(m + 1)]
    for i in range(1, m + 1):
        for j in range(1, n + 1):
            if question_tokens[i - 1].lower() == context_tokens[j - 1][0].lower():
                dp[i][j] = 1 + dp[i - 1][j - 1]
                if dp[i][j] > max_len:
                    max_len = dp[i][j]
                    max_index = j - 1
    return max_index, max_len


def sort_res(prompt, ans_list, context, boxes, lang="en"):
    if len(ans_list) == 1:
        return ans_list
    else:
        ans_val = []
        for ans in ans_list:
            ans_val.append(ans["value"])
        if len(set(ans_val)) == len(ans_val):
            sorted_ans_list = sorted(ans_list, key=lambda x: x["prob"], reverse=True)
            return sorted_ans_list
        else:
            if lang == "en":
                clean_prompt = [word for word in prompt.split(" ")]
            else:
                clean_prompt = [word for word in prompt]

            max_index, max_len = longestCommonSequence(clean_prompt, context)
            if max_index == -1:
                sorted_ans_list = sorted(ans_list, key=lambda x: x["prob"], reverse=True)
                return sorted_ans_list
            else:
                prompt_center = []
                for idx in range(max_index - max_len + 1, max_index + 1):
                    box = boxes[idx][0]
                    x = box.left + box.width / 2
                    y = box.top + box.height / 2
                    prompt_center.append([x, y])

                ans_center = []
                ans_prob = []
                for ans in ans_list:
                    ans_prob.append(ans["prob"])
                    cent_list = []
                    for idx in range(ans["start"], ans["end"] + 1):
                        box = boxes[idx][0]
                        x = box.left + box.width / 2
                        y = box.top + box.height / 2
                        cent_list.append([x, y])
                    ans_center.append(cent_list)

                ans_odist = []
                for ans_c in ans_center:
                    odist = 0
                    for a_c in ans_c:
                        for p_c in prompt_center:
                            odist += calEuclidean(a_c, p_c)
                    odist /= len(ans_c)
                    ans_odist.append(odist * (-1))

                ans_score = np.sum([ans_prob, ans_odist], axis=0).tolist()
                sorted_ans_list = sorted(ans_list, key=lambda x: ans_score[ans_list.index(x)], reverse=True)
                return sorted_ans_list


def map_offset(ori_offset, offset_mapping):
    """
    map ori offset to token offset
    """
    for index, span in enumerate(offset_mapping):
        if span[0] <= ori_offset < span[1]:
            return index
    return -1


# def pad_image_data(image_data):
#     if not image_data:
#         image = np.zeros([3, 224, 224])
#         return image
#     # decode image
#     data = np.frombuffer(bytearray(image_data), dtype="uint8")
#     image = np.array(Image.open(BytesIO(data)).convert("RGB"))
#     sample = {"image": image}
#     # resize image
#     sample = resize_func(sample)
#     # norm image
#     sample = norm_func(sample)
#     # permute
#     sample = permute_func(sample)
#     return sample["image"]


def unify_prompt_name(prompt):
    # The classification labels are shuffled during finetuning, so they need
    # to be unified during evaluation.
    if re.search(r"\[.*?\]$", prompt):
        prompt_prefix = prompt[: prompt.find("[", 1)]
        cls_options = re.search(r"\[.*?\]$", prompt).group()[1:-1].split(",")
        cls_options = sorted(list(set(cls_options)))
        cls_options = ",".join(cls_options)
        prompt = prompt_prefix + "[" + cls_options + "]"
        return prompt
    return prompt


def get_relation_type_dict(relation_data, schema_lang="ch"):
    def compare(a, b, schema_lang="ch"):
        if schema_lang == "ch":
            a = a[::-1]
            b = b[::-1]

        res = ""
        for i in range(min(len(a), len(b))):
            if a[i] == b[i]:
                res += a[i]
            else:
                break
        if res == "":
            return res
        if schema_lang == "ch" and res[::-1][0] == "的":
            return res[::-1][1:]
        elif schema_lang == "en" and res[-3:] == " of":
            return res[:-3]
        return ""

    relation_type_dict = {}
    added_list = []
    for i in range(len(relation_data)):
        added = False
        if relation_data[i][0] not in added_list:
            for j in range(i + 1, len(relation_data)):
                match = compare(relation_data[i][0], relation_data[j][0], schema_lang=schema_lang)
                if match != "":
                    match = unify_prompt_name(match)
                    if relation_data[i][0] not in added_list:
                        added_list.append(relation_data[i][0])
                        relation_type_dict.setdefault(match, []).append(relation_data[i][1])
                    added_list.append(relation_data[j][0])
                    relation_type_dict.setdefault(match, []).append(relation_data[j][1])
                    added = True
            if not added:
                added_list.append(relation_data[i][0])
                if schema_lang == "ch":
                    suffix = relation_data[i][0].rsplit("的", 1)[1]
                    suffix = unify_prompt_name(suffix)
                    relation_type = suffix
                else:
                    prefix = relation_data[i][0].split(" of ", 1)[0]
                    prefix = unify_prompt_name(prefix)
                    relation_type = prefix
                relation_type_dict.setdefault(relation_type, []).append(relation_data[i][1])
    return relation_type_dict


def compute_metrics(p):
    metric = SpanEvaluator()
    start_prob, end_prob = p.predictions
    start_ids, end_ids = p.label_ids
    metric.reset()

    num_correct, num_infer, num_label = metric.compute(start_prob, end_prob, start_ids, end_ids)
    metric.update(num_correct, num_infer, num_label)
    precision, recall, f1 = metric.accumulate()
    metric.reset()

    return {"precision": precision, "recall": recall, "f1": f1}


def get_bool_ids_greater_than(probs, limit=0.5, return_prob=False):
    """
    Get idx of the last dimension in probability arrays, which is greater than a limitation.
    Args:
        probs (List[List[float]]): The input probability arrays.
        limit (float): The limitation for probability.
        return_prob (bool): Whether to return the probability
    Returns:
        List[List[int]]: The index of the last dimension meet the conditions.
    """
    probs = np.array(probs)
    dim_len = len(probs.shape)
    if dim_len > 1:
        result = []
        for p in probs:
            result.append(get_bool_ids_greater_than(p, limit, return_prob))
        return result
    else:
        result = []
        for i, p in enumerate(probs):
            if p > limit:
                if return_prob:
                    result.append((i, float(p)))
                else:
                    result.append(i)
        return result


def get_span(start_ids, end_ids, with_prob=False):
    """
    Get span set from position start and end list.
    Args:
        start_ids (List[int]/List[tuple]): The start index list.
        end_ids (List[int]/List[tuple]): The end index list.
        with_prob (bool): If True, each element for start_ids and end_ids is a tuple aslike: (index, probability).
    Returns:
        set: The span set without overlapping, every id can only be used once .
    """
    if with_prob:
        start_ids = sorted(start_ids, key=lambda x: x[0])
        end_ids = sorted(end_ids, key=lambda x: x[0])
    else:
        start_ids = sorted(start_ids)
        end_ids = sorted(end_ids)

    start_pointer = 0
    end_pointer = 0
    len_start = len(start_ids)
    len_end = len(end_ids)
    couple_dict = {}
    while start_pointer < len_start and end_pointer < len_end:
        if with_prob:
            start_id = start_ids[start_pointer][0]
            end_id = end_ids[end_pointer][0]
        else:
            start_id = start_ids[start_pointer]
            end_id = end_ids[end_pointer]

        if start_id == end_id:
            couple_dict[end_ids[end_pointer]] = start_ids[start_pointer]
            start_pointer += 1
            end_pointer += 1
            continue
        if start_id < end_id:
            couple_dict[end_ids[end_pointer]] = start_ids[start_pointer]
            start_pointer += 1
            continue
        if start_id > end_id:
            end_pointer += 1
            continue
    result = [(couple_dict[end], end) for end in couple_dict]
    result = set(result)
    return result

