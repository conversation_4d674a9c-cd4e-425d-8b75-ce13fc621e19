# coding=utf-8
# Copyright (c) 2022 PaddlePaddle Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
import re
import os
import math
import random
import warnings
from itertools import accumulate
from copy import copy

import torch.utils.data
from PIL import Image
from tqdm import tqdm
from torch.utils.data.dataloader import DataLoader
import numpy as np
import time
from decimal import Decimal
import json
import jsonlines
from log import logger
from temp_doc_parser import DocParser
from kbnlp.scripts.information_extraction.utils import isInside, box_coverage_area, sort_boxes


class BaseConvertor:
    def __init__(self,
                 file_path,
                 splits=(0.8, 0.1, 0.1),
                 task_type="ext",
                 is_shuffle=True,
                 seed=0,
                 anno_type="image",
                 verbose=True,
                 **kwargs):
        self.file_path = file_path

        self.splits = splits
        self.task_type = task_type
        self.is_shuffle = is_shuffle
        self.seed = seed
        self.anno_type = anno_type
        self.verbose = verbose
        if self.seed:
            random.seed(seed)
            np.random.seed(seed)

    def process_text_tag(self, line, task_type='ext', **kwargs):
        if 'bboxes' in line:
            line['bbox'] = line['bboxes']
        if 'content' in line:
            line['text'] = line['content']
        return line

    def process_image_tag(self, line, task_type='ext', **kwargs):
        if 'bboxes' in line:
            line['bbox'] = line['bboxes']
        if 'content' in line:
            line['text'] = line['content']
        return line

    def read_data(self):
        """
        读取jsonlines文件，或者json文件，该json文件只有一行，代表一个列表，列表中每个元素都是字典，该字典代表一样样本。
        Args:
            file_path:

        Returns: List[Dict],返回所有的样本。

        """

        with jsonlines.open(self.file_path) as jr:
            examples = list(jr)
        if self.anno_type == 'text':
            examples = [self.process_text_tag(example) for example in examples if example]
        else:
            examples = [self.process_image_tag(example) for example in examples if example]
        return examples

    def _save_examples(self, save_dir, file_name, examples):
        count = 0
        save_path = os.path.join(save_dir, file_name)
        with open(save_path, "w", encoding="utf-8") as f:
            for example in examples:
                f.write(json.dumps(example, ensure_ascii=False) + "\n")
                count += 1
        if self.verbose:
            logger.info("Save %d examples to %s." % (count, save_path))

    def _convert(self, train_ds, dev_ds, test_ds, **kwargs):
        return train_ds, dev_ds, test_ds

    def convert(self,
                save_dir=None,
                **kwargs):
        tic_time = time.time()
        if not os.path.exists(self.file_path):
            raise ValueError(f"Please input the correct path of labeled file:{self.file_path}")

        if len(self.splits) != 0 and len(self.splits) != 3:
            raise ValueError("Only []/ len(splits)==3 accepted for splits.")

        def _check_sum(splits):
            return Decimal(str(splits[0])) + Decimal(str(splits[1])) + Decimal(
                str(splits[2])) == Decimal("1")

        if len(self.splits) == 3 and not _check_sum(self.splits):
            raise ValueError(
                "Please set correct splits, sum of elements in splits should be equal to 1."
            )
        raw_examples = self.read_data()
        if self.is_shuffle:
            indexes = np.random.permutation(len(raw_examples))
            index_list = indexes.tolist()
            raw_examples = [raw_examples[i] for i in indexes]
        else:
            index_list = list(range(len(raw_examples)))

        i1, i2, _ = self.splits
        p1 = int(len(raw_examples) * i1)
        p2 = int(len(raw_examples) * (i1 + i2))

        train_ids = index_list[:p1]
        dev_ids = index_list[p1:p2]
        test_ids = index_list[p2:]
        train_examples, dev_examples, test_examples = self._convert(raw_examples[:p1],
                                                                    raw_examples[p1:p2],
                                                                    raw_examples[p2:])
        if save_dir:
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)
            with open(os.path.join(save_dir, "sample_index.json"), "w") as fp:
                maps = {
                    "train_ids": train_ids,
                    "dev_ids": dev_ids,
                    "test_ids": test_ids
                }
                fp.write(json.dumps(maps))
            self._save_examples(save_dir, "train.txt", train_examples)
            self._save_examples(save_dir, "dev.txt", dev_examples)
            self._save_examples(save_dir, "test.txt", test_examples)
        if self.verbose:
            logger.info('Finished! It takes %.2f seconds' % (time.time() - tic_time))
        return train_examples, dev_examples, test_examples


class DoccanoConvertorForCloseDomain(BaseConvertor):
    def __init__(self,
                 file_path,
                 splits=[0.8, 0.1, 0.1],
                 task_type="ext",
                 is_shuffle=True,
                 seed=0,
                 anno_type="image",
                 verbose=True,
                 **kwargs):
        super().__init__(file_path=file_path, splits=splits, task_type=task_type, is_shuffle=is_shuffle,
                         seed=seed, anno_type=anno_type, verbose=verbose, **kwargs)

    def process_text_tag(self, line, task_type="ext", **kwargs):
        return super().process_text_tag(line, task_type)

    def process_image_tag(self, line, task_type="ext", **kwargs):
        return super().process_image_tag(line, task_type)


def process_label_studio_text_tag_to_doccano_format(line, task_type):
    items = {}
    items['text'] = line['data']['text']
    if task_type == "ext":
        items['entities'] = []
        items['relations'] = []
        result_list = line['annotations'][0]['result']
        for a in result_list:
            if a['type'] == "labels":
                items['entities'].append({
                    "id": a['id'],
                    "start_offset": a['value']['start'],
                    "end_offset": a['value']['end'],
                    "label": a['value']['labels'][0]
                })
            else:
                items['relations'].append({
                    "id":
                        a['from_id'] + "-" + a['to_id'],
                    "from_id":
                        a['from_id'],
                    "to_id":
                        a['to_id'],
                    "type":
                        a['labels'][0]
                })
    elif task_type == "cls":
        items['label'] = line['annotations'][0]['result'][0]['value'][
            'choices']
    elif task_type == 'cls_ext':
        raise NotImplementedError('task_type为cls_ext还未实现。')
    else:
        raise ValueError('不能识别该参数。task_type: %s' % task_type)
    return items


def process_label_studio_image_tag_to_doccano_format(line, file_path, doc_parser, task_type="ext",
                                                     image2ocr_res=None, **kwargs):
    """
    Args:
        line: 一个样本
        file_path: json文件路径
        doc_parser:
        task_type: ‘ext’或者‘cls’
        image2ocr_res: 缓存ocr结果, dict
        **kwargs:

    Returns:

    """

    def _find_segment_in_box(layouts, box, threshold=0.5):
        positions = []
        global_offset = 0
        for seg in layouts:
            sbox = seg[0]
            text_len = len(seg[1])
            if text_len == 0:
                continue
            char_w = (sbox[2] - sbox[0]) * 1.0 / text_len
            for i in range(len(seg[1])):
                cbox = [
                    sbox[0] + i * char_w, sbox[1],
                    sbox[0] + (i + 1) * char_w, sbox[3]
                ]
                c_covered = box_coverage_area(cbox, box)
                if c_covered >= threshold:
                    positions.append(global_offset)
                elif cbox[2] == min(cbox[2], box[2]) and cbox[0] == max(cbox[0], box[0]) \
                        and cbox[1] < box[1] and cbox[3] > box[3]:
                    # all covered on x-axis
                    if c_covered > 0.5:
                        positions.append(global_offset)
                global_offset += 1
        offsets = []
        if not positions:
            return offsets
        spos = positions[0]  # start position
        for i in range(1, len(positions)):
            if positions[i] != positions[i - 1] + 1:
                offsets.append((spos, positions[i - 1] + 1))
                spos = positions[i]
        offsets.append((spos, positions[-1] + 1))
        return offsets

    items = {}
    img_file = os.path.basename(line['data']['image'])
    p = img_file.find("-")
    img_file = img_file[p + 1:]
    #  百度错误,文件路径不能简单用'/'切分
    # img_path = os.path.join("/".join(self.label_studio_file.split_trim_underline_fake_relation('/')[:-1]), "images", img_file)
    img_path = os.path.join(os.path.dirname(file_path), 'images', img_file)
    if img_path not in image2ocr_res:
        if not os.path.exists(img_path):
            logger.warning(
                "Image file %s not exist in %s" %
                (img_file, os.path.join(os.path.dirname(file_path), 'images')))
            return None
        logger.info("Parsing image file %s ..." % (img_file))
        # doc_parser = DocParser(layout_analysis=self.layout_analysis)
        parsed_doc = doc_parser.parse({'doc': img_path})
        image2ocr_res[img_path] = parsed_doc
    else:
        parsed_doc = image2ocr_res[img_path]
    img_w, img_h = parsed_doc['img_w'], parsed_doc['img_h']
    # print(img_path, (img_h, img_w))
    text = ''
    bbox = []
    word = []
    for segment in parsed_doc['layout']:
        # box = self.doc_parser._normalize_box(segment[0], [img_w, img_h],
        #                                      [1000, 1000])
        box = segment[0]
        text += segment[1]
        # bbox.extend([box] * len(segment[1]))
        bbox.append(box)
        word.append(segment[1])
    # assert len(text) == len(bbox), "len of text is not equal to len of bbox"
    items['text'] = text
    items['word'] = word
    items['bbox'] = bbox
    items['image_width'] = img_w
    items['image_height'] = img_h
    items['image'] = os.path.join("images", img_file)
    # items['image'] = parsed_doc['image']  # 不把image直接记录到items中
    if task_type == "ext":
        items['entities'] = []
        items['relations'] = []

        result_list = line['annotations'][0]['result']
        ent_ids = []
        for e in result_list:
            if e['type'] != 'rectanglelabels':
                continue
            assert img_w == e['original_width'] and img_h == e[
                'original_height'], "Image size not match"
            box = [
                e['value']['x'] * 0.01 * img_w,
                e['value']['y'] * 0.01 * img_h,
                (e['value']['x'] + e['value']['width']) * 0.01 * img_w,
                (e['value']['y'] + e['value']['height']) * 0.01 * img_h
            ]
            offsets = _find_segment_in_box(parsed_doc['layout'], box)  # b5.jpg box = [1477, 2365, 2101, 2429]
            if len(offsets) > 0:
                items['entities'].append({
                    'id':
                        e['id'],
                    'start_offset':
                        offsets[0][0],
                    'end_offset':
                        offsets[0][1],
                    'label':
                        e['value']['rectanglelabels'][0]
                })
                ent_ids.append(e['id'])
        for r in result_list:
            if r['type'] != 'relation':
                continue
            if r['from_id'] in ent_ids and r['to_id'] in ent_ids:
                items['relations'].append({
                    'id':
                        r['from_id'] + '-' + r['to_id'],
                    'from_id':
                        r['from_id'],
                    'to_id':
                        r['to_id'],
                    'type':
                        r['labels'][0]
                })
    elif task_type == 'cls':
        # 以下代码没有验证过
        items['cls_label'] = line['annotations'][0]['result'][0]['value'][
            'choices']
    elif task_type == 'cls_ext':
        raise NotImplementedError('task_type为cls_ext还未实现。')
    else:
        raise ValueError('不能识别该参数。task_type: %s' % task_type)
    return items


class LabelStudioConvertorForCloseDomain(BaseConvertor):
    def __init__(self,
                 file_path,
                 layout_analysis=False,
                 splits=[0.8, 0.1, 0.1],
                 task_type="ext",
                 is_shuffle=True,
                 seed=0,
                 anno_type="image",
                 verbose=True,
                 **kwargs):
        super().__init__(file_path, splits=splits, task_type=task_type, is_shuffle=is_shuffle,
                         seed=seed, anno_type=anno_type, verbose=verbose, **kwargs)
        self.layout_analysis = layout_analysis
        self.image2ocr_res = {}

    @property
    def doc_parser(self):
        if not hasattr(self, '_doc_parser'):
            self._doc_parser = DocParser(ocr_lang='ch', layout_analysis=self.layout_analysis)
        return self._doc_parser

    def process_text_tag(self, line, task_type="ext", **kwargs):
        process_label_studio_text_tag_to_doccano_format(line, self.task_type)

    def process_image_tag(self, line, task_type="ext", **kwargs):
        return process_label_studio_image_tag_to_doccano_format(line, self.file_path, self.doc_parser, self.task_type,
                                                                self.image2ocr_res)

    def _convert(self, train_ds, dev_ds, test_ds, **kwargs):
        return super()._convert(train_ds, dev_ds, test_ds, **kwargs)


class AIDPDrawBoxConvertorForPSS(BaseConvertor):
    """
        目前实现is_shuffle必须为false。
    """
    def __init__(self,
                 file_path,
                 image_base_path=None,
                 layout_analysis=False,
                 schema_lang="ch",
                 splits=(0.8, 0.1, 0.1),
                 task_type="ext",
                 is_shuffle=True,
                 seed=0,
                 anno_type="image",
                 verbose=True,
                 **kwargs):
        super().__init__(
            file_path,
            splits=splits,
            task_type=task_type,
            is_shuffle=is_shuffle,
            seed=seed,
            anno_type=anno_type,
            verbose=verbose,
            **kwargs)
        self.image_base_path = image_base_path
        self.layout_analysis = layout_analysis
        self.schema_lang = schema_lang

    @property
    def doc_parser(self):
        if not hasattr(self, '_doc_parser'):
            from zhu_ocr import OCRSystem
            self._doc_parser = OCRSystem(det_model_path='dtm/001.pt',
                                         cls_model_path='dtm/003.pt', cls_vertical_model_path='dtm/006.pt',
                                         rec_model_path='dtm/tp1t7.pt',
                                         rec_char_dict_path='dtm/ppocr_keys_v1.txt')
        return self._doc_parser

    def read_data(self):
        """
        读取jsonlines文件，或者json文件，该json文件只有一行，代表一个列表，列表中每个元素都是字典，该字典代表一样样本。
        Args:
            file_path:

        Returns: List[Dict],返回所有的样本。

        """

        with jsonlines.open(self.file_path) as jr:
            examples = list(jr)

        class _X(list):
            def __getitem__(_self, i):
                exam = super().__getitem__(i)
                path = os.path.join(self.image_base_path, *re.split(r'\\|/', exam['image']))
                if not os.path.isfile(path):
                    raise ValueError(f'文件{path}不存在。')
                try:
                    img = Image.open(path).convert("RGB")
                except Exception as e:
                    exam['image_width'] = 1010
                    exam['image_height'] = 1010
                    exam['char_positions'] = [[0, 0]]
                    exam['word'] = ['']
                    exam['text'] = ''
                    exam['bbox'] = [[0, 0, 0, 0]]
                    print(f'{path}文件无法正常读取。')
                else:
                    exam['image_width'] = img.width
                    exam['image_height'] = img.height
                    ocr_res = self.doc_parser.ocr(img)[0]
                    ocr_res = [[[min(box[0][0], box[2][0]), min(box[0][1], box[2][1]),
                                 max(box[0][0], box[2][0]), max(box[0][1], box[2][1])], (t_s_c[0], t_s_c[1], t_s_c[2])]
                               for box, t_s_c in ocr_res if
                               t_s_c[0].strip()]  # 这里只将strip后是空字符串的丢掉。strip后不是空的把未strip的原字符串返回。
                    # 如果需要重新对bbox排序，需要在此处理ocr_res,而不是直接对下面的bbox，word排序。
                    word = []
                    bbox = []
                    char_positions = []
                    for box, t_s_c in ocr_res:
                        bbox.append(box)
                        word.append(t_s_c[0])
                        char_positions.append(t_s_c[2])

                    indices, bbox, _ = sort_boxes(bbox)
                    word = [word[ind] for ind in indices]
                    exam['char_positions'] = [char_positions[ind] for ind in indices]
                    exam['word'] = word
                    exam['text'] = ''.join(word)
                    exam['bbox'] = bbox
                return exam

        loader = DataLoader(_X(examples), batch_size=None)
        examples = [data for data in loader]
        del self._doc_parser, loader
        torch.cuda.empty_cache()
        if self.anno_type == 'text':
            examples = [self.process_text_tag(example) for example in examples if example]
        else:
            examples = [self.process_image_tag(example) for example in examples if example]
        last_file_id = -1
        for exam in examples:
            file_id = exam['file_id']
            if file_id != last_file_id and file_id != '0':
                exam['pss_label'] = 'B'
            else:
                exam['pss_label'] = 'I'
            last_file_id = file_id
        return examples

    def process_image_tag(self, line, task_type="ext", check=False, **kwargs):
        example = copy(line)
        return example


class ConvertorForUIEX(BaseConvertor):

    """
    读取数据格式要求。
    是一个json line文件，一行为一个样本，每个样本是一个字典，至少包括'word'、'bbox'、'entities'、'relations'字段。
    {"word": ['w1', 'w2', 'w3'],
     "bbox": [[1, 1, 1, 1], [2, 2, 2, 2], [3, 3, 3, 3]],
     "entities": [],  # doccano格式，内部包含'id'、'start_offset'、'end_offset'、'label'字段。
     "relation":[],  # doccano格式，内部包含'id'、'from_id'、'to_id'、'type'字段。
     # "image": '图片路径'  # 如果是图片数据需要包含该数据。所有图片路径除文件名以外要一致，可以是局部路径。如，'images/1.jpg'。
    }
    最外层字典还可以有其他任何字段，最终会被原样保留。
    专门为uiex设计的convert，目的是为doccano格式的标注数据构造负样本，并转换为UIEX模型可以训练的数据。
    """
    def __init__(self,
                 file_path,
                 image_base_path=None,
                 negative_ratio=5,
                 prompt_prefix="情感倾向",
                 options=None,
                 layout_analysis=False,
                 separator="##",
                 schema_lang="ch",
                 splits=(0.8, 0.1, 0.1),
                 task_type="ext",
                 is_shuffle=True,
                 seed=0,
                 anno_type="image",
                 verbose=True,
                 **kwargs):
        super().__init__(file_path, splits=splits, task_type=task_type, is_shuffle=is_shuffle,
                         seed=seed, anno_type=anno_type, verbose=verbose, **kwargs)
        self.image_base_path = image_base_path if image_base_path else file_path
        self.negative_ratio = negative_ratio
        self.prompt_prefix = prompt_prefix
        self.options = options
        self.layout_analysis = layout_analysis
        self.separator = separator
        self.schema_lang = schema_lang
        self.ignore_list = []  # ["属性值", "object"]

    def _convert(self, train_ds, dev_ds, test_ds, **kwargs):
        if self.task_type == 'ext':
            train_examples = self.convert_ext_examples(train_ds)
            dev_examples = self.convert_ext_examples(dev_ds, is_train=False)
            test_examples = self.convert_ext_examples(test_ds, is_train=False)
        elif self.task_type == 'cls':
            options = []
            for line in train_ds + dev_ds + test_ds:
                if 'cls_label' in line:
                    for cls_l in line['cls_label']:
                        if cls_l not in options:
                            options.append(cls_l)
            if not self.options:
                self.options = options
            else:
                assert isinstance(self.options, list) and isinstance(self.options[0], str)
            self.options = sorted(self.options)
            if self.options:
                train_examples = self.convert_cls_examples(train_ds)
                dev_examples = self.convert_cls_examples(dev_ds)
                test_examples = self.convert_cls_examples(test_ds)
            else:
                warnings.warn('选择了分类任务，但所有样本都不包含类别。')
                train_examples = dev_examples = test_examples = []
        elif self.task_type == 'monograph_record':
            def fun(ds):
                for d in ds:
                    if 'cls_label' in d:
                        d.pop('cls_label')
                key_entities = set()
                e_labels = set()
                e_label2in_degree = {}
                for d in ds:
                    e_id2e_label = {}
                    for e in d['entities']:
                        e_id2e_label[e['id']] = e['label']
                        e_labels.add(e['label'])
                    for rel in d['relations']:
                        e_label2in_degree[e_id2e_label[rel['to_id']]] = e_label2in_degree.setdefault(
                            e_id2e_label[rel['to_id']], 0) + 1
                for label in e_labels:
                    if label not in e_label2in_degree:
                        key_entities.add(label)
                ds = self.convert_ext_examples(ds)
                # 把样本中‘prompt’字段是非关键的实体类型的样本剔除。如果全部都是非关键实体，则全部保留（正常不希望出现这种情况）。
                # 这样一是为了缓解专题著录中漏标现象，二是专题著录不需要非关键实体，非关键实体通过关系抽取。
                # 所谓关键实体就是专题的本体中入度为0的实体，这种实体在专题中一般不作为其他实体的主实体。
                # print('key_entities:', key_entities)
                return [exam for exam in ds
                        if exam['prompt'] != '忽略自然段' and  # 约定字段
                        ((not key_entities) or  # 非正常情况
                         (exam['prompt'] in key_entities) or  # 关键实体保留
                         (exam['prompt'] not in e_labels))]  # 所有关系都保留

            train_examples = fun(train_ds)
            dev_examples = fun(dev_ds)
            test_examples = fun(test_ds)
            return train_examples, dev_examples, test_examples

        elif self.task_type == 'cls_ext' or self.task_type == 'intent' or self.task_type == 'document_record':
            if self.task_type == 'document_record':
                train_ds_ext = [d for d in train_ds if d['cls_label'] != ['否']]
                dev_ds_ext = [d for d in dev_ds if d['cls_label'] != ['否']]
                test_ds_ext = [d for d in test_ds if d['cls_label'] != ['否']]
                train_ds_no_entity = [d for d in train_ds if d['cls_label'] == ['否']]
                dev_ds_no_entity = [d for d in dev_ds if d['cls_label'] == ['否']]
                test_ds_no_entity = [d for d in test_ds if d['cls_label'] == ['否']]
                if len(train_ds_no_entity):
                    ids = random.sample(range(len(train_ds_no_entity)), k=max(1, min(int(len(train_ds_ext) * 0.3), len(train_ds_no_entity))))
                    train_ds_no_entity = [train_ds_no_entity[i] for i in ids]
                    train_ds_ext += train_ds_no_entity
                if len(dev_ds_no_entity):
                    ids = random.sample(range(len(dev_ds_no_entity)), k=max(1, min(int(len(dev_ds_ext) * 0.3), len(dev_ds_no_entity))))
                    dev_ds_no_entity = [dev_ds_no_entity[i] for i in ids]
                    dev_ds_ext += dev_ds_no_entity
                if len(test_ds_no_entity):
                    ids = random.sample(range(len(test_ds_no_entity)), k=max(1, min(int(len(test_ds_ext) * 0.3), len(test_ds_no_entity))))
                    test_ds_no_entity = [test_ds_no_entity[i] for i in ids]
                    test_ds_ext += test_ds_no_entity

                train_examples = self.convert_ext_examples(train_ds_ext)
                dev_examples = self.convert_ext_examples(dev_ds_ext, is_train=False)
                test_examples = self.convert_ext_examples(test_ds_ext, is_train=False)
            else:
                train_ds_ext = train_ds
                dev_ds_ext = dev_ds
                test_ds_ext = test_ds
                train_examples = self.convert_ext_examples(train_ds_ext)
                dev_examples = self.convert_ext_examples(dev_ds_ext, is_train=False)
                test_examples = self.convert_ext_examples(test_ds_ext, is_train=False)
            options = []

            for line in train_ds + dev_ds + test_ds:
                if 'cls_label' in line:
                    for cls_l in line['cls_label']:
                        if cls_l not in options:
                            options.append(cls_l)
            if not self.options:
                self.options = options
            else:
                assert isinstance(self.options, list) and isinstance(self.options[0], str)
            self.options = sorted(self.options)
            if self.options:
                train_examples.extend(self.convert_cls_examples(train_ds))
                dev_examples.extend(self.convert_cls_examples(dev_ds))
                test_examples.extend(self.convert_cls_examples(test_ds))
        else:
            raise ValueError('不能识别该参数。task_type: %s' % self.task_type)
        return train_examples, dev_examples, test_examples

    def convert_cls_examples(self, raw_examples):
        """
        Convert labeled data for classification taskflow.
        """
        examples = []
        logger.info(f"Converting annotation data...")
        with tqdm(total=len(raw_examples)) as pbar:
            for line in raw_examples:
                if self.anno_type == "text":
                    items = line
                    image, bbox, word = None, None, None
                    image_width, image_height = None, None
                elif self.anno_type == "image":
                    items = line
                    if items is None:
                        continue
                    image, bbox, word = items['image'], items['bbox'], items['word']
                    image_width, image_height = items['image_width'], items['image_height']
                else:
                    raise ValueError(
                        "The type of annotation should be text or image")
                text, labels = items["text"], items["cls_label"]
                # TODO: 保留其他所有字段。
                example = self.generate_cls_example(text, labels,
                                                    self.prompt_prefix,
                                                    self.options, image, bbox, word, image_width, image_height)
                # if 'image' not in example:
                #     print()
                examples.append(example)
        return examples

    def convert_ext_examples(self, raw_examples, is_train=True):
        """
        Convert labeled data for extraction taskflow.
        """

        def _sep_cls_label(label, separator):
            label_list = label.split(separator)
            if len(label_list) == 1:
                return label_list[0], None
            return label_list[0], label_list[1:]

        texts = []  # 列表，长度和总样本相同,每个元素是该样本的文本内容。
        # 嵌套列表，长度和总样本相同，列表中每一个元素也是一个列表。内层列表中的元素是该样本的得到的一条关于实体的训练数据。形如：{"content": "...", "result_list": ['...'], "prompt": "..."}
        entity_examples = []
        # {"content": "", "result_list": [], "prompt": "X的Y"}
        relation_examples = []
        # {"content": "", "result_list": [], "prompt": "X的情感倾向[正向，负向]"}
        entity_cls_examples = []

        # 整个数据集中所有的实体类型的集合，但不包括'观点词'这一单词。如， ["时间", "地点", ... ]
        entity_label_set = []
        # Entity name set: ["2月8日上午", "北京", ... ]
        entity_name_set = []
        # Predicate set: ["歌手", "所属专辑", ... ]
        predicate_set = []

        # List[List[str]]
        # 嵌套列表，每个元素都是一个样本的实体列表（和数据中顺序相同）。[['开票日期', '名称'], ['开票日期', ...], ...]
        entity_prompt_list = []
        # List of relation prompt for each example
        relation_prompt_list = []
        # Golden subject entity name for each example
        subject_golden_list = []
        # List of inverse relation for each example
        inverse_relation_list = []
        # List of predicate for each example
        predicate_list = []

        if self.anno_type == "text":
            images, bbox_list, word_list, image_width_list, image_height_list = None, None, None, None, None
        else:
            images, bbox_list, word_list, image_width_list, image_height_list = [], [], [], [], []

        logger.info(f"Converting annotation data...")

        with tqdm(total=len(raw_examples)) as pbar:
            for line in raw_examples:

                if self.anno_type == "text":
                    items = line
                    image, bbox, word = None, None, None
                    image_width, image_height = None, None
                elif self.anno_type == "image":
                    items = line
                    if items is None:
                        continue
                    image, bbox, word = items['image'], items['bbox'], items['word']
                    # if not ('image_width' in items and 'image_height' in items):
                    #     items['image_width']
                    #     items['image_height']
                    image_width, image_height = items['image_width'], items['image_height']
                    images.append(image)
                    bbox_list.append(bbox)
                    word_list.append(word)
                    image_width_list.append(image_width)
                    image_height_list.append(image_height)
                else:
                    raise ValueError(
                        "The type of annotation should be text or image")

                text, relations, entities = items["text"], items[
                    "relations"], items["entities"]
                texts.append(text)

                entity_example = []
                entity_prompt = []
                entity_example_map = {}
                entity_map = {}  # id to entity name
                for entity in entities:
                    entity_name = text[
                        entity["start_offset"]:entity["end_offset"]]
                    entity_map[entity["id"]] = {
                        "name": entity_name,
                        "start": entity["start_offset"],
                        "end": entity["end_offset"]
                    }
                    if entity["label"] in self.ignore_list:
                        continue

                    entity_label, entity_cls_label = _sep_cls_label(
                        entity["label"], self.separator)

                    # Define the prompt prefix for entity-level classification
                    # xxx + "的" + 情感倾向 -> Chinese
                    # Sentiment classification + " of " + xxx -> English
                    if self.schema_lang == "ch":
                        entity_cls_prompt_prefix = entity_name + "的" + self.prompt_prefix
                    else:
                        entity_cls_prompt_prefix = self.prompt_prefix + " of " + entity_name
                    if entity_cls_label is not None:
                        entity_cls_example = self.generate_cls_example(
                            text, entity_cls_label, entity_cls_prompt_prefix,
                            self.options, image, bbox, word, image_width, image_height)

                        entity_cls_examples.append(entity_cls_example)

                    result = {
                        "text": entity_name,
                        "start": entity["start_offset"],
                        "end": entity["end_offset"]
                    }
                    if entity_label not in entity_example_map.keys():
                        entity_example_map[entity_label] = {
                            "text": text,
                            "result_list": [result],
                            "prompt": entity_label
                        }
                        if self.anno_type == "image":
                            entity_example_map[entity_label]['image'] = image
                            entity_example_map[entity_label]['bbox'] = bbox
                            entity_example_map[entity_label]['word'] = word
                            entity_example_map[entity_label]['image_width'] = image_width
                            entity_example_map[entity_label]['image_height'] = image_height
                    else:
                        entity_example_map[entity_label]["result_list"].append(
                            result)

                    if entity_label not in entity_label_set and entity_label != "观点词":
                        entity_label_set.append(entity_label)
                    if entity_name not in entity_name_set:
                        entity_name_set.append(entity_name)
                    entity_prompt.append(entity_label)

                for v in entity_example_map.values():
                    entity_example.append(v)

                entity_examples.append(entity_example)
                entity_prompt_list.append(entity_prompt)

                subject_golden = []  # Golden entity inputs
                relation_example = []
                relation_prompt = []
                relation_example_map = {}
                inverse_relation = []
                predicates = []
                for relation in relations:
                    predicate = relation["type"]
                    subject_id = relation["from_id"]
                    object_id = relation["to_id"]
                    # The relation prompt is constructed as follows:
                    # subject + "的" + predicate -> Chinese
                    # predicate + " of " + subject -> English
                    if self.schema_lang == "ch":
                        prompt = entity_map[subject_id]["name"] + "的" + predicate
                        inverse_negative = entity_map[object_id][
                            "name"] + "的" + predicate
                    else:
                        prompt = predicate + " of " + entity_map[subject_id][
                            "name"]
                        inverse_negative = predicate + " of " + entity_map[
                            object_id]["name"]

                    if entity_map[subject_id]["name"] not in subject_golden:
                        subject_golden.append(entity_map[subject_id]["name"])
                    result = {
                        "text": entity_map[object_id]["name"],
                        "start": entity_map[object_id]["start"],
                        "end": entity_map[object_id]["end"]
                    }

                    inverse_relation.append(inverse_negative)
                    predicates.append(predicate)

                    if prompt not in relation_example_map.keys():
                        relation_example_map[prompt] = {
                            "text": text,
                            "result_list": [result],
                            "prompt": prompt
                        }
                        if self.anno_type == "image":
                            relation_example_map[prompt]['image'] = image
                            relation_example_map[prompt]['bbox'] = bbox
                            relation_example_map[prompt]['word'] = word
                            relation_example_map[prompt]['image_width'] = image_width
                            relation_example_map[prompt]['image_height'] = image_height
                    else:
                        relation_example_map[prompt]["result_list"].append(
                            result)

                    if predicate not in predicate_set:
                        predicate_set.append(predicate)
                    relation_prompt.append(prompt)
                    inverse_relation = [inverse_rel for inverse_rel in inverse_relation if inverse_rel not in relation_example_map]  # 个人添加
                for v in relation_example_map.values():
                    relation_example.append(v)

                relation_examples.append(relation_example)
                relation_prompt_list.append(relation_prompt)
                subject_golden_list.append(subject_golden)
                inverse_relation_list.append(inverse_relation)
                predicate_list.append(predicates)
                pbar.update(1)

        logger.info(f"Adding negative samples for first stage prompt...")
        positive_examples, negative_examples = self.add_entity_negative_example(
            entity_examples, texts, entity_prompt_list, entity_label_set,
            images, bbox_list, word_list, image_width_list, image_height_list)
        if len(positive_examples) == 0:
            all_entity_examples = []
        else:
            all_entity_examples = positive_examples + negative_examples

        all_relation_examples = []
        if len(predicate_set) != 0:
            logger.info(f"Adding negative samples for second stage prompt...")
            if is_train:

                positive_examples = []
                negative_examples = []
                per_n_ratio = self.negative_ratio // 3

                with tqdm(total=len(texts)) as pbar:
                    for i, text in enumerate(texts):
                        negative_example = []
                        collects = []
                        num_positive = len(relation_examples[i])

                        # 1. inverse_relation_list
                        redundants1 = inverse_relation_list[i]

                        # 2. entity_name_set ^ subject_golden_list[i]
                        redundants2 = []
                        if len(predicate_list[i]) != 0:
                            nonentity_list = list(
                                set(entity_name_set)
                                ^ set(subject_golden_list[i]))  # 此代码是否有意义？看起来后者必然包含于前者。
                            nonentity_list.sort()

                            if self.schema_lang == "ch":
                                redundants2 = [
                                    nonentity + "的" +
                                    predicate_list[i][random.randrange(  # 这里仅以第i句中出现的谓词作为候选集是否不妥？
                                        len(predicate_list[i]))]
                                    for nonentity in nonentity_list
                                ]
                            else:
                                redundants2 = [
                                    predicate_list[i][random.randrange(
                                        len(predicate_list[i]))] + " of " +
                                    nonentity for nonentity in nonentity_list
                                ]

                        # 3. entity_label_set ^ entity_prompt_list[i]
                        redundants3 = []
                        if len(subject_golden_list[i]) != 0:
                            non_ent_label_list = list(
                                set(entity_label_set)
                                ^ set(entity_prompt_list[i]))
                            non_ent_label_list.sort()

                            if self.schema_lang == "ch":
                                redundants3 = [
                                    subject_golden_list[i][random.randrange(
                                        len(subject_golden_list[i]))] + "的" +
                                    non_ent_label
                                    for non_ent_label in non_ent_label_list
                                ]
                            else:
                                redundants3 = [
                                    non_ent_label + " of " +
                                    subject_golden_list[i][random.randrange(
                                        len(subject_golden_list[i]))]
                                    for non_ent_label in non_ent_label_list
                                ]

                        redundants_list = [
                            redundants1, redundants2, redundants3
                        ]

                        for redundants in redundants_list:
                            if self.anno_type == "text":
                                added, rest = self.add_relation_negative_example(
                                    redundants,
                                    texts[i],
                                    num_positive,
                                    per_n_ratio,
                                )
                            else:
                                added, rest = self.add_relation_negative_example(
                                    redundants, texts[i], num_positive,
                                    per_n_ratio, images[i], bbox_list[i], word_list[i],
                                    image_width_list[i], image_height_list[i])
                            negative_example.extend(added)
                            collects.extend(rest)

                        num_sup = num_positive * self.negative_ratio - len(
                            negative_example)
                        if num_sup > 0 and collects:
                            if num_sup > len(collects):
                                idxs = [k for k in range(len(collects))]
                            else:
                                idxs = random.sample(range(0, len(collects)),
                                                     num_sup)
                            for idx in idxs:
                                negative_example.append(collects[idx])

                        positive_examples.extend(relation_examples[i])
                        negative_examples.extend(negative_example)
                        pbar.update(1)
                all_relation_examples = positive_examples + negative_examples
            else:
                relation_examples = self.add_full_negative_example(
                    relation_examples, texts, relation_prompt_list,
                    predicate_set, subject_golden_list,
                    images, bbox_list, word_list, image_width_list, image_height_list)
                all_relation_examples = [
                    r for relation_example in relation_examples
                    for r in relation_example
                ]
        return all_entity_examples + all_relation_examples + entity_cls_examples

    def generate_cls_example(self,
                             text,
                             labels,
                             prompt_prefix,
                             options,
                             image=None,
                             bbox=None,
                             word=None,
                             image_width=None,
                             image_height=None):
        # random.shuffle(self.options)
        cls_options = ",".join(sorted(list(set(self.options))))
        prompt = prompt_prefix + "[" + cls_options + "]"

        result_list = []
        example = {
            "text": text,
            "result_list": result_list,
            "prompt": prompt
        }
        if image is not None and bbox is not None and word is not None:
            example['image'] = image
            example['bbox'] = bbox
            example['word'] = word
            example['image_width'] = image_width
            example['image_height'] = image_height
        for label in labels:
            start = prompt.rfind(label) - len(prompt) - 1
            end = start + len(label)
            result = {"text": label, "start": start, "end": end}
            example["result_list"].append(result)
        return example

    def add_full_negative_example(self,
                                  examples,
                                  texts,
                                  relation_prompt_list,
                                  predicate_set,
                                  subject_golden_list,
                                  images=None,
                                  bbox_list=None,
                                  word_list=None,
                                  image_width_list=None,
                                  image_height_list=None):
        with tqdm(total=len(relation_prompt_list)) as pbar:
            for i, relation_prompt in enumerate(relation_prompt_list):
                negative_sample = []
                for subject in subject_golden_list[i]:
                    for predicate in predicate_set:
                        # The relation prompt is constructed as follows:
                        # subject + "的" + predicate -> Chinese
                        # predicate + " of " + subject -> English
                        if self.schema_lang == "ch":
                            prompt = subject + "的" + predicate
                        else:
                            prompt = predicate + " of " + subject
                        if prompt not in relation_prompt:
                            negative_result = {
                                "text": texts[i],
                                "result_list": [],
                                "prompt": prompt
                            }
                            if images is not None and bbox_list is not None and word_list is not None:
                                negative_result['image'] = images[i]
                                negative_result['bbox'] = bbox_list[i]
                                negative_result['word'] = word_list[i]
                                negative_result['image_width'] = image_width_list[i]
                                negative_result['image_height'] = image_height_list[i]
                            negative_sample.append(negative_result)
                examples[i].extend(negative_sample)
                pbar.update(1)
        return examples

    def add_entity_negative_example(self,
                                    examples,
                                    texts,
                                    prompts,
                                    label_set,
                                    images=None,
                                    bbox_list=None,
                                    word_list=None,
                                    image_width_list=None,
                                    image_height_list=None):
        negative_examples = []
        positive_examples = []
        with tqdm(total=len(prompts)) as pbar:
            for i, prompt in enumerate(prompts):
                redundants = list(set(label_set) ^ set(prompt))
                redundants.sort()

                num_positive = len(examples[i])
                if num_positive != 0:
                    actual_ratio = math.ceil(len(redundants) / num_positive)
                else:
                    # Set num_positive to 1 for text without positive example
                    num_positive, actual_ratio = 1, 0

                if actual_ratio <= self.negative_ratio or self.negative_ratio == -1:
                    idxs = [k for k in range(len(redundants))]
                else:
                    idxs = random.sample(range(0, len(redundants)),
                                         self.negative_ratio * num_positive)

                for idx in idxs:
                    negative_result = {
                        "text": texts[i],
                        "result_list": [],
                        "prompt": redundants[idx]
                    }
                    if images is not None and bbox_list is not None and word_list is not None:
                        negative_result['image'] = images[i]
                        negative_result['bbox'] = bbox_list[i]
                        negative_result['word'] = word_list[i]
                        negative_result['image_width'] = image_width_list[i]
                        negative_result['image_height'] = image_height_list[i]
                    negative_examples.append(negative_result)
                positive_examples.extend(examples[i])
                pbar.update(1)
        return positive_examples, negative_examples

    def add_relation_negative_example(self,
                                      redundants,
                                      text,
                                      num_positive,
                                      ratio,
                                      image=None,
                                      bbox=None,
                                      word=None,
                                      image_width=None,
                                      image_height=None):
        added_example = []
        rest_example = []

        if num_positive != 0:
            actual_ratio = math.ceil(len(redundants) / num_positive)
        else:
            # Set num_positive to 1 for text without positive example
            num_positive, actual_ratio = 1, 0

        all_idxs = [k for k in range(len(redundants))]
        if actual_ratio <= ratio or ratio == -1:
            idxs = all_idxs
            rest_idxs = []
        else:
            idxs = random.sample(range(0, len(redundants)),
                                 ratio * num_positive)
            rest_idxs = list(set(all_idxs) ^ set(idxs))

        for idx in idxs:
            negative_result = {
                "text": text,
                "result_list": [],
                "prompt": redundants[idx]
            }
            if image is not None and bbox is not None and word is not None:
                negative_result['image'] = image
                negative_result['bbox'] = bbox
                negative_result['word'] = word
                negative_result['image_width'] = image_width
                negative_result['image_height'] = image_height
            added_example.append(negative_result)

        for rest_idx in rest_idxs:
            negative_result = {
                "text": text,
                "result_list": [],
                "prompt": redundants[rest_idx]
            }
            if image is not None and bbox is not None and word is not None:
                negative_result['image'] = image
                negative_result['bbox'] = bbox
                negative_result['word'] = word
                negative_result['image_width'] = image_width
                negative_result['image_height'] = image_height
            rest_example.append(negative_result)

        return added_example, rest_example


class AIDPDrawBoxConvertorForUIEX(ConvertorForUIEX):

    @property
    def doc_parser(self):
        if not hasattr(self, '_doc_parser'):
            from zhu_ocr import OCRSystem
            self._doc_parser = OCRSystem(det_model_path='dtm/001.pt',
                                         cls_model_path='dtm/003.pt', cls_vertical_model_path='dtm/006.pt',
                                         rec_model_path='dtm/tp1t7.pt',
                                         rec_char_dict_path='dtm/ppocr_keys_v1.txt',
                                         rec_image_shape='3,32,640',  # 文本框文字过于筹码需要设置为'3,32,640'
                                         )
            # 临时使用
            # from kbnlp.scripts.information_extraction.pp_ocr_customized import PaddleOCRCustomized
            # import paddle
            # self._doc_parser = PaddleOCRCustomized(show_log=False, lang='ch',
            #                                        use_gpu=True if paddle.get_device().startswith('gpu') else False)
        return self._doc_parser


        # del self._doc_parser

    def read_data(self):
        """
        读取jsonlines文件，或者json文件，该json文件只有一行，代表一个列表，列表中每个元素都是字典，该字典代表一样样本。
        Args:
            file_path:

        Returns: List[Dict],返回所有的样本。

        """

        with jsonlines.open(self.file_path) as jr:
            examples = list(jr)

        class _X(list):
            def __getitem__(_self, i):
                exam = super().__getitem__(i)
                path = os.path.join(self.image_base_path, *re.split(r'\\|/', exam['image']))
                if not os.path.isfile(path):
                    raise ValueError(f'文件{path}不存在。')
                try:
                    img = Image.open(os.path.join(self.image_base_path,
                                                  *re.split(r'\\|/', exam['image']))).convert("RGB")
                except Exception as e:
                    exam['image_width'] = 1010
                    exam['image_height'] = 1010
                    exam['char_positions'] = [[0, 0]]
                    exam['word'] = ['']
                    exam['text'] = ''
                    exam['bbox'] = [[0, 0, 0, 0]]
                    print(f'{path}文件无法正常读取。')
                else:
                    exam['image_width'] = img.width
                    exam['image_height'] = img.height
                    img = np.asarray(img.convert("RGB"))[:, :, [2, 1, 0]]  # 转化为BGR,兼容ppocr提前转换
                    ocr_res = self.doc_parser.ocr(img)[0]
                    ocr_res = [[[min(box[0][0], box[2][0]), min(box[0][1], box[2][1]),
                                 max(box[0][0], box[2][0]), max(box[0][1], box[2][1])], (t_s_c[0], t_s_c[1], t_s_c[2])]
                               for box, t_s_c in ocr_res if
                               t_s_c[0].strip()]  # 这里只将strip后是空字符串的丢掉。strip后不是空的把未strip的原字符串返回。
                    # 如果需要重新对bbox排序，需要在此处理ocr_res,而不是直接对下面的bbox，word排序。
                    word = []
                    bbox = []
                    char_positions = []
                    for box, t_s_c in ocr_res:
                        bbox.append(box)
                        word.append(t_s_c[0])
                        char_positions.append(t_s_c[2])

                    indices, bbox, _ = sort_boxes(bbox)
                    word = [word[ind] for ind in indices]
                    exam['char_positions'] = [char_positions[ind] for ind in indices]
                    exam['word'] = word
                    exam['text'] = ''.join(word)
                    exam['bbox'] = bbox
                return exam

        loader = DataLoader(_X(examples), batch_size=None)
        examples = [data for data in loader]
        if hasattr(self, '_doc_parser'):
            del self._doc_parser, loader
        torch.cuda.empty_cache()
        if self.anno_type == 'text':
            examples = [self.process_text_tag(example) for example in examples if example]
        else:
            examples = [self.process_image_tag(example) for example in examples if example]
            # TODO: 处理分件的cls_label
        return examples

    def process_text_tag(self, line, task_type="ext", **kwargs):
        example = copy(line)

        entities = []
        relations = []
        entity_id = 0
        relation_id = 0

        word_lens = [0] + list(accumulate(map(len, example['word'])))
        for entity in line['entities']:
            label = entity['label']
            record_type = entity['record_type']  # volume, file
            entities_frag = []
            for frag in entity['fragment']:
                draw_box = frag['bbox']
                mention = frag['mention']
                for box_id, (box, char_coords) in enumerate(zip(example['bbox'], example['char_positions'])):
                    draw_box = [min(draw_box[0], draw_box[2]), min(draw_box[1], draw_box[3]),
                                max(draw_box[0], draw_box[2]), max(draw_box[1], draw_box[3])]
                    if box_coverage_area(box, draw_box) > 0:
                        last_inside = False
                        start_offset = -1
                        end_offset = -1
                        for i, coord in enumerate(char_coords):
                            cur_inside = isInside(draw_box[0], draw_box[1], draw_box[2], draw_box[1],
                                                  draw_box[2], draw_box[3], draw_box[0], draw_box[3],
                                                  coord[0], coord[1])
                            if cur_inside:
                                if not last_inside:
                                    start_offset = i
                            else:
                                if last_inside:
                                    end_offset = i
                                    entities_frag.append({'id': entity_id,
                                                          'box_id': box_id,
                                                          'label': label,
                                                          'start_offset': start_offset + word_lens[box_id],
                                                          'end_offset': end_offset + word_lens[box_id]})
                                    entity_id += 1
                                    start_offset = -1
                                    end_offset = -1
                            last_inside = cur_inside
                        if start_offset != -1 and end_offset == -1:
                            entities_frag.append({'id': entity_id,
                                                  'box_id': box_id,
                                                  'label': label,
                                                  'start_offset': start_offset + word_lens[box_id],
                                                  'end_offset': len(char_coords) + word_lens[box_id]})
                            entity_id += 1
            if entities_frag:
                entities.append(entities_frag)
        entities_ = []
        for entities_frag in entities:
            if entities_frag:
                entities_frag_ = [entities_frag[0]]
                for e in entities_frag[1:]:
                    last_e = entities_frag_[-1]
                    if e['start_offset'] == last_e['end_offset']:
                        last_e['end_offset'] = e['end_offset']
                        last_e['box_id'] = e['box_id']
                    else:
                        entities_frag_.append(e)
                entities_.extend(entities_frag_)
        for e in entities_:
            e.pop('box_id')
        entities = entities_
        # 将原本有的关系保留（之后处理不连续实体，或者相邻文本框被分开的实体，处理已有的关系需要重新考虑。）
        for rel in line['relations']:
            relations.append(rel)
        example['entities'] = entities
        example['relations'] = relations  # 之后标注平台支持关系标注后，则需要处理已有的relations。
        if 'cls_label' not in example:
            example['cls_label'] = ['是' if example['entities'] else '否']
        example.pop('word')
        example.pop('bbox')
        example.pop('image')
        example.pop('image_width')
        example.pop('image_height')
        return example

    def process_image_tag(self, line, task_type="ext", check=False, **kwargs):
        example = copy(line)

        entities = []
        relations = []
        relation_id = 0

        word_lens = [0] + list(accumulate(map(len, example['word'])))
        for entity in line['entities']:
            label = entity['label']
            entity_id = entity['id']
            record_type = entity['record_type']  # volume, file
            entities_frag = []
            for frag in entity['fragment']:
                draw_box = frag['bbox']
                mention = frag['mention']
                for box_id, (box, char_coords) in enumerate(zip(example['bbox'], example['char_positions'])):
                    draw_box = [min(draw_box[0], draw_box[2]), min(draw_box[1], draw_box[3]),
                                max(draw_box[0], draw_box[2]), max(draw_box[1], draw_box[3])]
                    if box_coverage_area(box, draw_box) > 0:
                        last_inside = False
                        start_offset = -1
                        end_offset = -1
                        for i, coord in enumerate(char_coords):
                            cur_inside = isInside(draw_box[0], draw_box[1], draw_box[2], draw_box[1],
                                                  draw_box[2], draw_box[3], draw_box[0], draw_box[3],
                                                  coord[0], coord[1])
                            if cur_inside:
                                if not last_inside:
                                    start_offset = i
                            else:
                                if last_inside:
                                    end_offset = i
                                    entities_frag.append({'id': entity_id,
                                                          'box_id': box_id,
                                                          'label': label,
                                                          'start_offset': start_offset + word_lens[box_id],
                                                          'end_offset': end_offset + word_lens[box_id]})

                                    start_offset = -1
                                    end_offset = -1
                            last_inside = cur_inside
                        if start_offset != -1 and end_offset == -1:
                            entities_frag.append({'id': entity_id,
                                                  'box_id': box_id,
                                                  'label': label,
                                                  'start_offset': start_offset + word_lens[box_id],
                                                  'end_offset': len(char_coords) + word_lens[box_id]})

            if entities_frag:
                entities.append(entities_frag)
        entities_ = []
        for entities_frag in entities:
            if entities_frag:
                entities_frag_ = [entities_frag[0]]
                for e in entities_frag[1:]:
                    last_e = entities_frag_[-1]
                    if e['start_offset'] == last_e['end_offset']:
                        last_e['end_offset'] = e['end_offset']
                        last_e['box_id'] = e['box_id']
                    else:
                        entities_frag_.append(e)
                entities_.extend(entities_frag_)
        for e in entities_:
            e.pop('box_id')
        entities = entities_
        # 此时，如过同一个fragment中的mention已经尽可能被合并为一个实体。
        # 但是如果用户标注不当，或者排序算法的失误，导致一个完整的实体被分开成几个同类型的实体。
        # 那么此时他们暂时公用一个id，处理完关系再做唯一化处理。
        for rel in line['relations']:
            relations.append(rel)

        # TODO: ocr可能导致用户标注的某个实体，最终不在entities列表中，但是relations中的某个关系用到了该实体，需要剔除关系。

        example['entities'] = entities
        example['relations'] = relations  # 之后标注平台支持关系标注后，则需要处理已有的relations。
        if 'cls_label' not in example:
            example['cls_label'] = ['是' if example['entities'] else '否']
        return example


class AIDPConvertorForUIEX(ConvertorForUIEX):
    pass

    def _convert(self, train_ds, dev_ds, test_ds, **kwargs):
        train_ds, dev_ds, test_ds = super()._convert(train_ds, dev_ds, test_ds, **kwargs)
        return train_ds, dev_ds, test_ds

    def process_text_tag(self, line, task_type="ext", **kwargs):
        example = copy(line)

        if 'text' not in line and 'word' in line:
            example['text'] = ''.join(line['word'])
        if not line['entities'] and not line['relations']:
            if 'cls_label' not in example:
                example['cls_label'] = ['否']
            return example

        entities = []
        relations = []
        entity_id = 0
        relation_id = 0
        word_lens = [0] + list(accumulate(map(len, line['word'])))
        for entity in line['entities']:
            if 'fragment' not in entity:  # 直接加载doccano格式数据
                entities.append(entity)
            else:
                frags = entity['fragment']
                label = entity['label']
                whole_entity = []
                for frag in frags:
                    box_id = frag['box_id']
                    frag_entities = []  # 一个fragment中所有的实体片段。
                    for mention, offset in zip(frag['mention'], frag['offset_map']):
                        start_offset = word_lens[box_id] + offset[0]
                        end_offset = word_lens[box_id] + offset[1]
                        frag_entities.append({'id': entity_id,
                                              'label': label,
                                              'start_offset': start_offset,
                                              'end_offset': end_offset})
                        entity_id += 1
                    whole_entity.extend(frag_entities)
                entities.extend(whole_entity)
                # 暂时不支持不连续实体功能。
                # if len(whole_entity) >= 2:
                #     for e_i in range(len(whole_entity) - 1):
                #         from_id = whole_entity[e_i]['id']
                #         to_id = whole_entity[e_i + 1]['id']
                #         relations.append({'id': relation_id,
                #                           'from_id': from_id,
                #                           'to_id': to_id,
                #                           'type': '后续'})
                #         relation_id += 1
        # 将原本有的关系保留（之后处理不连续实体，或者相邻文本框被分开的实体，处理已有的关系需要重新考虑。）
        for rel in line['relations']:
            relations.append(rel)
        example['entities'] = entities
        example['relations'] = relations  # 之后标注平台支持关系标注后，则需要处理已有的relations。
        # example = deal_next_relation2([example])[0]  # 该方法有待检查
        # 利用图片中包含的著录项做图片标签。（经验证，直接这么使用不太可行。）
        # example['cls_label'] = ['|'.join(sorted(set([e['label'] for e in example['entities']])))] \
        #     if example['entities'] else []
        if 'cls_label' not in example:
            example['cls_label'] = ['是' if example['entities'] else '否']
        return example

    def process_image_tag(self, line, task_type="ext", check=False, **kwargs):
        """
                将aidp导出的数据处理成文档信息抽取脚本（uie、uiex）能处理的doccano格式。如果读取数据已经是文档信息抽取可以处理的doccano格式
            数据,则把数据原样返回；如果该doccano格式数据中没有cls_label,则cls_label被创建为空列表 。

            # 文档标注导出数据格式
            数据导出成jsonlines格式，每行一个样本，图片放在jsonl文件的同级目录下的images/file_name文件夹下。在jsonl文件中，每个样本是一个字典，包含字段：
            'word': List[str], ocr得到的文字列表。
            'bbox': List[List[int, int, int, int]], ocr得到的所有文本框的bbox列表。
            'image': str, 记录jsonl文件的同级images目录到该图片文件的路径信息）,如，某张图片名为'pig1.jgp',其放在images/pig目录下，该本字段记录为'images/pig/pig1.jpg'。
            'entities': 如下示例，id代表该图片中标注的实体id,从0开始，依次递增，label代表实体标签（著录项），fragment记录用户对该著录项的标注结果，
                        该示例中fragment列表中的第一个著录项'住址著录项'对应的标注步骤如下：
                            1，用户首先在box_id为0的文本框中选中‘上海市’，’上海市‘在该文本框中的start、end值为(2, 5);
                            2，然后在该文本框中选中'SOHO东海广场',其在文本框中的start、end值为(8, 16)；
                            3，最后在box_id为3的文本框中选中了'2208室'，其在3号文本框中的start、end值为(7, 12)。
                        '公司名称著录项'著录像类似。
                        例子：
                        [{'fragment': [{'box_id': 0, 'mention': ['上海市', 'SOHO东海广场'], 'offset_map': [(2, 5), (8, 16)]},
                                       {'box_id': 3, 'mention': ['2208室'], 'offset_map': [(7, 12)]}],
                          'id': 0,
                          'label': '住址著录项'},
                         {'fragment': [{'box_id': 5, 'mention': ['北京酷豹（上海数豹）科技'], 'offset_map': [(3, 15)]},
                                       {'box_id': 6, 'mention': ['有限公司'], 'offset_map': [(0, 4)]}],
                          'id': 1,
                          'label': '公司名称著录项'},
                         {'fragment': [{'box_id': 21, 'mention': ['上海市南京西路'], 'offset_map': [(11, 18)]}],
                          'id': 8,
                          'label': '住址著录项'}]
                        注意：如果一个实体类型（著录项）对应不不止一个实体实例（著录结果），如'住址著录项'包括’上海市SOHO东海广场‘、’上海市南京西路‘，
                        则他们需要要在entities中分别占据一项记录。
            relations: 一个空列表（不是None）。目前不做关系抽取。
            #按照目前设计逻辑，从AIDP导出的数据需要做文本图片做分类。所以task_type参数无效。

            文档信息抽取模型所需doccano格式数据：jsonlines文件，每行是一个样本，每个样本都是一个字典。有text、word、bbox、image、entities、
            relations字段。image和aidp格式数据一致。

        """
        example = copy(line)

        if not ('image_width' in example and 'image_height' in example):
            path = os.path.join(self.image_base_path, *re.split(r'\\|/', example['image']))
            try:
                img = Image.open(path).convert("RGB")
            except Exception as e:
                example['image_width'] = 1010
                example['image_height'] = 1010
                print(f'{path}文件无法正常读取。')
            else:
                example['image_width'] = img.width
                example['image_height'] = img.height
        if 'text' not in line and 'word' in line:
            example['text'] = ''.join(line['word'])
        if not line['entities'] and not line['relations']:
            if 'cls_label' not in example:
                example['cls_label'] = ['否']
            return example

        entities = []
        relations = []
        entity_id = 0
        relation_id = 0
        word_lens = [0] + list(accumulate(map(len, line['word'])))
        for entity in line['entities']:
            if 'fragment' not in entity:  # 直接加载doccano格式数据
                entities.append(entity)
            else:
                frags = entity['fragment']
                label = entity['label']
                whole_entity = []
                for frag in frags:
                    box_id = frag['box_id']
                    frag_entities = []  # 一个fragment中所有的实体片段。
                    for mention, offset in zip(frag['mention'], frag['offset_map']):
                        start_offset = word_lens[box_id] + offset[0]
                        end_offset = word_lens[box_id] + offset[1]
                        frag_entities.append({'id': entity_id,
                                              'label': label,
                                              'start_offset': start_offset,
                                              'end_offset': end_offset})
                        entity_id += 1
                    whole_entity.extend(frag_entities)
                entities.extend(whole_entity)
                # 暂时不支持不连续实体功能。
                # if len(whole_entity) >= 2:
                #     for e_i in range(len(whole_entity) - 1):
                #         from_id = whole_entity[e_i]['id']
                #         to_id = whole_entity[e_i + 1]['id']
                #         relations.append({'id': relation_id,
                #                           'from_id': from_id,
                #                           'to_id': to_id,
                #                           'type': '后续'})
                #         relation_id += 1
        # 将原本有的关系保留（之后处理不连续实体，或者相邻文本框被分开的实体，处理已有的关系需要重新考虑。）
        for rel in line['relations']:
            relations.append(rel)
        example['entities'] = entities
        example['relations'] = relations  # 之后标注平台支持关系标注后，则需要处理已有的relations。
        # example = deal_next_relation2([example])[0]  # 该方法有待检查
        # 利用图片中包含的著录项做图片标签。（经验证，直接这么使用不太可行。）
        # example['cls_label'] = ['|'.join(sorted(set([e['label'] for e in example['entities']])))] \
        #     if example['entities'] else []
        if 'cls_label' not in example:
            example['cls_label'] = ['是' if example['entities'] else '否']
        return example


class LabelStudioConvertorForUIEX(ConvertorForUIEX):
    """Convertor to convert data export from annotation platform"""
    def __init__(self, file_path, **kwargs):
        super(LabelStudioConvertorForUIEX, self).__init__(file_path, **kwargs)
        self.image2ocr_res = {}

    @property
    def doc_parser(self):
        if not hasattr(self, '_doc_parser'):
            import paddle
            self._doc_parser = DocParser(ocr_lang=self.schema_lang, layout_analysis=self.layout_analysis,
                                         device='gpu' if paddle.device.is_compiled_with_cuda() else 'cpu')
        return self._doc_parser

    def process_text_tag(self, line, task_type="ext", **kwargs):
        return process_label_studio_text_tag_to_doccano_format(line, self.task_type)

    def process_image_tag(self, line, task_type="ext", **kwargs):
        return process_label_studio_image_tag_to_doccano_format(line, self.file_path, self.doc_parser, self.task_type,
                                                                self.image2ocr_res)


if __name__ == '__main__':
    convertor = AIDPDrawBoxConvertorForUIEX(file_path=r'D:\workspace\pycharm\nlp_project\kbnlp\raw_data\银行年报\train.jsonl',
                                            splits=[0.8, 0.2, 0], seed=1000)
    trains, devs, tests = convertor.convert()
    print('finish.')
    # from kbnlp.experiment.yml.utils import draw_ocr_result, draw_bbox_label
    # from PIL import ImageFont
    # i = 0
    # draw_bbox_label(
    #     Image.open(os.path.join(r'D:\workspace\pycharm\nlp_project\kbnlp\raw_data\tax', trains[i]['image'])).convert(
    #         "RGB"), trains[i]['word'], trains[i]['bbox'], inplace=False,
    #     font=ImageFont.truetype(r'D:\workspace\pycharm\layoutLMv3\simfang.ttf', size=35)).show()
    # print([(e['label'], trains[i]['text'][e['start_offset']: e['end_offset']]) for e in trains[i]['entities']])
    # print('finish.')
