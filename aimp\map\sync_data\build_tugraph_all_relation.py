import requests
from openpyxl import Workbook, load_workbook
from common.logger import logger
from common.http_client import hc
from .utils import configMap


config = configMap()
# login_url = "http://lgraph:7090/login"
# # graph_name = 'archive'
# graph_name = 'MovieDemo1'
# data = requests.post(login_url, json={'user': "admin", 'password': "73@TuGraph"})
# print(data)
_url = config.TUGRAPH_CONFIG.get("url")
login_url = f'{_url}/login'
graph_name = config.TUGRAPH_CONFIG.get("graph_name")
data = requests.post(login_url, json={'user': config.TUGRAPH_CONFIG.get("user"), 'password': config.TUGRAPH_CONFIG.get("password")})

jwt = data.json()['jwt']
# print(jwt)
_auth = {'Authorization': f'Bearer {jwt}'}
headers = {'Accept': 'application/json; charset=UTF-8',
           'Content-Type': 'application/json; charset=UTF-8'}
headers.update(_auth)
# 图数据库的url
# _url = f'http://lgraph:7090'
# 执行cypher语句的url
_cypher_url = '/'.join([_url, 'cypher'])
# 插入顶点（实体）实例的url
_url_node = '/'.join([_url, 'db', graph_name, 'node'])
# 创建标签的url，包括顶点和边
_url_create_label = '/'.join([_url, 'db', graph_name, 'label'])
# 获取标签的url前缀
_url_get_label_prefix = '/'.join([_url, 'db', graph_name, 'label', 'node'])


def run(cypher):
    # try:
    query_data = {
        "graph": graph_name,
        "script": cypher,
    }
    query_ret = requests.post(_cypher_url, json=query_data, headers=headers)
    return query_ret


def insert_edge(subject_type, predicate, object_type, SRC_ID, DST_ID):
    insert_flag = False
    check_cypher = "MATCH (a:%s)-[rel:%s]-> (b:%s) WHERE a.ID = '%s' AND b.ID = '%s' return  rel" % (
    subject_type, predicate, object_type, SRC_ID, DST_ID)
    insert_cypher = "MATCH (a:%s {ID:'%s'}), (b:%s {ID:'%s'})  CREATE (a)-[r:%s]->(b)" % (
    subject_type, SRC_ID, object_type, DST_ID, predicate)
    check_res = run(check_cypher)
    if check_res.status_code != 200:
        print(check_cypher)
        print(check_res)
        raise Exception(check_res)
    else:
        if len(check_res.json()['result']) == 0:
            insert_res = run(insert_cypher)
            if insert_res.status_code != 200:
                print('insert_relation_cypher_error:', insert_cypher)
                raise Exception(insert_res.text)
            else:
                insert_flag = True
    return insert_flag


def insert_edges(path):
    """
        给定代表关系三元组的csv文件，该文件需要符合特定格式。第一行只有三个单元格数据，第一个形如‘LABEL=籍贯关系’，代表‘籍贯关系’这一关系，
    第二个形如‘SRC_LABEL=人物’，‘人物’代表主实体（顶点）的名称，第三个形如’DST_LABEL=籍贯‘，’籍贯‘代表客实体的名称。
    第二行有两个单元格数据可以为空（暂时不处理第二行数据，留着之后用）。
    从第三行开始，每行代表一个关系对，每行都只有两个单元格有数据，其中第一个单元格代表主实体的ID属性，第二个单元格代表客实体的单元格的ID属性。
    Args:
        path: csv文件路径。

    Returns:

    """
    wb_person_troop = load_workbook(path)
    ws_person_troop = wb_person_troop.active
    rel_label = ''
    subject_type = ''
    object_type = ''
    for i, row in enumerate(ws_person_troop.rows):
        if i == 0:
            labels = [col.value.split('=')[-1] for col in row if col.value]
            rel_label = labels[0]
            subject_type = labels[1]
            object_type = labels[2]
        elif i == 1:
            pass
        else:
            ids = [str(col.value) for col in row if col.value]
            src_id = ids[0]
            dst_id = ids[1]
            insert_res = insert_edge(subject_type, rel_label, object_type, src_id, dst_id)
            if not insert_res:
                print(i, (subject_type, rel_label, object_type, src_id, dst_id))


def insert_node(path=r'D:\workspace\pycharm\nlp_project\ymlkbqa\籍贯_图库文件.xlsx'):
    """
        给定定义顶点实例的csv文件的路径。该文件需要符合特定格式，该文件的第一个sheet的名字为对应的顶点（即实体）名，
    文件第一行为所有的属性名，从第二行开始，每行代表一个实体实例，其属性值和第一行一一对应。其中属性必须包含ID、name属性，
    且他们不能为空，且ID属性不能重复，name属性可以重复。其他属性可以缺失、重复。
    Args:
        path: csv文件的路径。

    Returns: None

    """
    wb = load_workbook(path)
    ws = wb.active
    label_name = ws.title
    column_names = []
    datas = []
    insert_flag = False
    for i, rows in enumerate(ws.rows):
        if i == 0:
            column_names.extend([col.value for col in rows if col.value])
        else:
            datas.append(dict((name, str(val)) for val, name in zip([col.value for col in rows], column_names)))
    assert 'ID' in column_names, "任何节点都必须包含'ID'属性和'name'属性。"
    assert 'name' in column_names, "任何节点都必须包含'ID'属性和'name'属性。"
    for data in datas:
        node = {'label': label_name, 'property': data}
        assert data['name'] and data['ID']
        check_cypher = "MATCH (a:%s) WHERE a.ID = '%s' return  a" % (label_name, data['ID'])
        check_res = run(check_cypher)

        if check_res.status_code != 200:
            print(check_cypher)
            print(check_res)
            raise Exception(check_res)
        else:
            if len(check_res.json()['result']) == 0:
                ret = requests.post(_url_node, json=node, headers=headers)
                if ret.status_code != 200:
                    print('创建节点失败，失败节点：', node)
                    raise Exception(ret.text)
                else:
                    insert_flag = True
    return insert_flag


def create_label(path):
    """
        给定标签定义的csv文件路径，该文件需要符合特定格式。该文件只有一行数据。对于顶点标签，第一个单元格形如‘LABEL=学生’,其中‘学生’代表该顶点的名字，第二个为
    ‘IS_VERTEX=FALSE’代表是顶点的标签，‘ID’、‘name’、‘age’代表该顶点的所有属性名。目前所有属于都已字符串存储。对于边标签，第二个单元格是‘IS_VERTEX=TRUE’，其他位置的含义相同。

    Args:
        path: csv文件路径。

    Returns: None
    """
    wb = load_workbook(path)
    ws = wb.active
    label_name = ''
    is_vertex = True
    fields = []
    constraints = []
    for i, rows in enumerate(ws.rows):
        if i == 0:
            values = [col.value.strip() for col in rows if col.value.strip()]
            assert len(values) >= 2, "第一行至少记录标签名和表示是否是顶点的标识字符串。"
            label_name = values[0].split('=')[-1].strip()
            if not values[0].startswith('LABEL=') or not label_name:
                raise ValueError("第一行第一列字段形如'LABEL=学生',等号右侧表示该标签名。")
            is_vertex = True if values[1] == 'IS_VERTEX=TRUE' else False
            if not values[1] in ('IS_VERTEX=TRUE', 'IS_VERTEX=FALSE'):
                raise ValueError("第一行第二列字段必须是'IS_VERTEX=TRUE'或者'IS_VERTEX=FALSE'中的一个,等号右侧为'TRUE'表示该标签是顶点标签，否则为关系标签。")
            fields.extend(values[2:])
            if is_vertex:
                assert len(fields) >= 2, "顶点必须至少包括'ID'、'name'属性，关系可以没有属性。"
        elif not is_vertex:

            constraint = [col.value.strip() for col in rows if col.value.strip()]
            assert len(constraint) == 2, '该必须包含两个值，分别是头顶点的标签名和尾顶点的标签名。'
            constraints.append(constraint)

    if is_vertex:
        json_input = {'name': label_name,
                      "fields": [{'name': f, 'type': 'string', 'optional': False if f == 'ID' else True} for f in
                                 fields],
                      'is_vertex': True,
                      "primary": "ID"
                      }
    else:
        json_input = {'name': label_name,
                      "fields": [{'name': f, 'type': 'string', 'optional': True} for f in fields],
                      'is_vertex': False,
                      }
    ret = requests.post(_url_create_label, json=json_input, headers=headers)
    if ret.status_code != 200:
        print('创建节点失败，失败节点：', json_input)
        raise Exception(ret.text)


def get_label(label_name: str):
    """
    根据顶点的名字获取顶点的属性名列表。
    Args:
        label_name:顶点的名字。
    Returns: 对应的顶点的属性名列表。
    """
    ret = requests.get('/'.join([_url_get_label_prefix, label_name]), headers=headers)
    if ret.status_code != 200:
        print('获取节点Label数据格式定义失败:', label_name)
        raise Exception(ret.text)
    return list(ret.json().keys())


def _exam_attrs(attrs, is_vertex=True):
    """
    对属性列表检验。对应顶点attrs中至少包含'ID'和'name'字段。对于边属性列表attrs可以为空
    Args:
        attrs:  属性值列表。

    Returns: bool

    """
    if isinstance(attrs, list):
        for attr in attrs:
            if not isinstance(attr, str):
                return False
    else:
        return False

    if is_vertex:
        return len(attrs) >= 2 and 'ID' in attrs and 'name' in attrs
    else:
        return True


@logger.catch
def create_node_label_and_insert_node(id, path):
    """
        通过定义顶点实例的csv文件，插入顶点实例。如果没有对应的顶点label，则先创建该顶点的label。如果发现已经有该顶点，但是属性值不相同则直接报错。
        其他要求查看insert_node方法。
    Args:
        path: 定义顶点实例的csv文件路径。

    Returns: 插入顶点实例成功则返回True，否则返回False。

    """
    wb = load_workbook(path)
    ws = wb.active
    label_name = ws.title.strip()
    column_names = []
    for rows in ws.rows:
        column_names.extend([col.value for col in rows if col.value])
        break
    assert _exam_attrs(column_names, is_vertex=True), "顶点必须至少包括'ID'、'name'属性，关系可以没有属性。"
    try:
        attrs = get_label(label_name)
        if sorted(label_name) != sorted(column_names):
            raise ValueError('图谱中已有%s顶点标签，属性值列表为%s，本次插入顶点的属性列表为%s。两者不相同，请检查。'
                             % (label_name, str(sorted(attrs)), str(sorted(column_names))))
    except Exception as e:
        hc.request_api("POST", "http://localhost:8000/map/update_status", json={"data":{"id": id, "status": 2}})
        json_input = {'name': label_name,
                      "fields": [{'name': f, 'type': 'string', 'optional': False if f == 'ID' else True} for f in
                                 column_names],
                      'is_vertex': True,
                      "primary": "ID"
                      }

        ret = requests.post(_url_create_label, json=json_input, headers=headers)
        if ret.status_code != 200:
            print('创建节点失败，失败节点信息：', json_input)
            raise Exception(ret.text)
    insert_node(path)
    hc.request_api("POST", "http://localhost:8000/map/stop_map", json={"id": id})
    hc.request_api("POST", "http://localhost:8000/map/update_status", json={"data": {"id": id, "dataNums": len(list(ws.rows)),
                                                                          "surplus": 0, "nodeNums": 1,
                                                                          "relationNums": 0, "status": 3}})
    return True

