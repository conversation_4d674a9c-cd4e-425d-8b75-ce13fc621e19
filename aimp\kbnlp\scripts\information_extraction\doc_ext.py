def train(model_name_or_path,
          output_dir,
          max_seq_len=512,
          doc_stride=128,
          num_train_epochs=30,
          learning_rate=1e-5,
          **kwargs
          ):
    """

    Args:
        model_name_or_path: 基模型的存放路径或标识符,str.
        output_dir: 模型训练后的保存路径.str
        max_seq_len: 句子的最大长度，默认512。int
        doc_stride: 未处理长文档设置的回退滑窗大小，默认128,必须小于max_seq_len。int。
        num_train_epochs: 最大的训练轮数,默认30。int
        learning_rate: 学习率，默认1e-5。float
        **kwargs: 其他参数字典，普通用户不需要设置。

    Returns: None

    """
    pass


def predict(document,
            uiex,
            max_seq_len=512,
            doc_stride=128,
            batch_size=1,
            **kwargs
            ):
    """

    Args:
        document: ocr处理后的图片数据，List[Dict]。列表大小对应图片张数。字典的key包括‘text’,代表ocr处理后得到所有文本框;'bbox'代表
            对应文本框的坐标,共四个坐标值，[left_top_x, left_top_y, right_bottom_x, right_bottom_y]。
            [{'text': ['成绩单', '姓名', '张三'...],
              'bbox': [[551, 97, 636, 137], [551, 97, 636, 137], [551, 97, 636, 137]]},
             {'text': ['成绩单', '姓名', '李四'...],
              'bbox': [[688, 113, 874, 156], [688, 113, 874, 156], [688, 113, 874, 156]]},
             ...
            ]
        uiex: 文档信息抽取模型，torch.nn.Module。
        max_seq_len: 句子的最大长度，默认512,不能超过512。int。
        doc_stride: 未处理长文档设置的回退滑窗大小，默认128,必须小于max_seq_len。int。
        batch_size: 预测是批量大小，默认为1，int。
        **kwargs: 其他参数字典

    Returns:  Dict,代表抽取到的信息。key代表著录项，value代表对应的抽取结果。

    """
    pass


def get_all_models():
    """

    Returns:list[str]，返回所有模型路径。

    """
    pass