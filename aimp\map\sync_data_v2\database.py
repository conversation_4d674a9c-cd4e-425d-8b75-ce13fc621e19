import re
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, scoped_session
# from sqlalchemy.ext.declarative import declarative_base
from urllib import parse
from contextlib import contextmanager


@contextmanager
def session_scope(session):
    try:
        yield session
        session.commit()
    except:
        session.rollback()
        raise
    finally:
        session.close()


def parse_uri(database_uri):
    search_ret = re.search("://.*?:(.*)@", database_uri)
    password = search_ret.group(1)
    new_password = parse.quote_plus(password)
    new_database_uri = database_uri.replace(password, new_password)

    return new_database_uri


class DatabaseClient:

    def __init__(self, database_uri):
        # password = parse.quote_plus('be@MysqL')
        # SQLALCHEMY_DATABASE_URI = f"mysql+pymysql://{root}:{password}@{mysql}:{3306}/{aimp}"
        self.engine = create_engine(parse_uri(database_uri))
        self.database = self.engine.url.database
        self.session_factory = sessionmaker(bind=self.engine)
        self.Session = scoped_session(self.session_factory)
        # 实例化session
        self.session = self.Session()
