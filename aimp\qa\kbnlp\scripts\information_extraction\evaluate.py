# Copyright (c) 2022 PaddlePaddle Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import argparse
import re
from functools import partial
import jsonlines

import torch
from torch.utils.data import DataLoader
from qa.kbnlp.task.information_extraction import UIEDataset
from transformers import AutoTokenizer
from qa.kbnlp.scripts.information_extraction.metric import SpanEvaluator
from log import logger

from qa.kbnlp.task.models.information_extraction_model import UIE, AutoPromptUIE, AutoPromptUIEConfig


def evaluate(model, metric, data_loader, device):
    """
    Given a dataset, it evals model and computes the metric.
    Args:
        model(obj:`paddle.nn.Layer`): A model to classify texts.
        metric(obj:`paddle.metric.Metric`): The evaluation metric.
        data_loader(obj:`paddle.io.DataLoader`): The dataset loader which generates batches.
        device:
    """
    model.eval()
    metric.reset()
    with torch.no_grad():
        for batch in data_loader:
            start_ids = batch.pop('start_ids')
            end_ids = batch.pop('end_ids')
            batch = dict((k, v.to(device) if isinstance(v, torch.Tensor) else v) for k, v in batch.items())
            inputs = [batch['input_ids'], batch['attention_mask'], batch['token_type_ids'], batch['position_ids']]
            if 'auto_prompt_ids' in batch:
                inputs.append(batch['auto_prompt_ids'])
            res = model(*inputs)
            start_prob, end_prob = res['start_prob'], res['end_prob']
            num_correct, num_infer, num_label = metric.compute(
                start_prob.to('cpu'), end_prob.to('cpu'), start_ids, end_ids)
            metric.update(num_correct, num_infer, num_label)
        precision, recall, f1 = metric.accumulate()
        model.train()
    return precision, recall, f1


def unify_prompt_name(prompt):
    # The classification labels are shuffled during finetuning, so they need
    # to be unified during evaluation.
    if re.search(r'\[.*?\]$', prompt):
        prompt_prefix = prompt[:prompt.find("[", 1)]
        cls_options = re.search(r'\[.*?\]$', prompt).group()[1:-1].split(",")
        cls_options = sorted(list(set(cls_options)))
        cls_options = ",".join(cls_options)
        prompt = prompt_prefix + "[" + cls_options + "]"
        return prompt
    return prompt


def get_infer_model(model, tokenizer, data, device='cpu'):
    model.eval()
    dataset = UIEDataset(data)
    loader = DataLoader(dataset,
                        collate_fn=partial(model.collate, tokenizer=tokenizer, max_seq_len=args.max_seq_len),
                        batch_size=1)
    with torch.no_grad():
        for b in loader:
            b.pop('start_ids', None)
            b.pop('end_ids', None)
            b = dict((k, v.to(device) if isinstance(v, torch.Tensor) else v) for k, v in b.items())
            torch._C._jit_set_profiling_mode(False)
            inputs = [b['input_ids'], b['attention_mask'], b['token_type_ids'], b['position_ids']]
            if 'auto_prompt_ids' in b:
                inputs.append(b['auto_prompt_ids'])
            model = torch.jit.trace(model.to(device), inputs, strict=False)
            logger.info('trace finished.')
            break
    return model


def do_eval():
    tokenizer = AutoTokenizer.from_pretrained(args.model_path)
    if args.model_type == 'auto':
        config = AutoPromptUIEConfig.from_pretrained(args.model_path)
        model = AutoPromptUIE(config)
        model.load_state_dict(
            torch.load(args.model_path + '/pytorch_model.bin', map_location=torch.device('cpu')))
        model.to(args.device)
    else:
        model = UIE.from_pretrained(args.model_path).to(args.device)

    with jsonlines.open(file=args.test_path, mode='r') as jr:
        dev_dataset = []
        for d in jr:
            if 'content' in d:
                d['text'] = d.pop('content')
            dev_dataset.append(d)
    collate = model.collate  # trace之后得到的ScriptModule没有collate属性。# TODO：把collate放到Module外边。
    if args.trace:
        model = get_infer_model(model, tokenizer, dev_dataset[:1], device=args.device)
    class_dict = {}
    if args.debug:
        for data in dev_dataset:
            class_name = unify_prompt_name(data['prompt']
                                           if '的' not in data['prompt'] else '的' + data['prompt'].rsplit('的')[-1])
            # Only positive examples are evaluated in debug mode
            if len(data['result_list']) != 0:
                class_dict.setdefault(class_name, []).append(data)
    else:
        class_dict["all_classes"] = dev_dataset
    logger.info('start eval.')
    for key in list(class_dict.keys())[:]:
        dev_dataset = UIEDataset(class_dict[key])
        dev_loader = DataLoader(dev_dataset,
                                collate_fn=partial(collate, tokenizer=tokenizer, max_seq_len=args.max_seq_len),
                                batch_size=args.batch_size)

        metric = SpanEvaluator()
        precision, recall, f1 = evaluate(model, metric, dev_loader, args.device)
        logger.info("-----------------------------")
        logger.info("Class Name: %s" % key)
        logger.info("Evaluation Precision: %.5f | Recall: %.5f | F1: %.5f. " %
                    (precision, recall, f1))
    logger.info('eval finished.')


if __name__ == "__main__":
    # yapf: disable
    parser = argparse.ArgumentParser()

    parser.add_argument("--model_path", type=str, default=None, help="The path of saved model that you want to load.")
    parser.add_argument("--model_type", choices=['uie', 'auto'], default='uie', help='eval UIE or AutoPromptUIE model.')
    parser.add_argument("--trace", action='store_true', help='whether trace the model.')
    parser.add_argument("--test_path", type=str, default=None, help="The path of test set.")
    parser.add_argument("--device", default='cuda', type=str)
    parser.add_argument("--batch_size", type=int, default=16, help="Batch size per GPU/CPU for training.")
    parser.add_argument("--max_seq_len", type=int, default=512, help="The maximum total input sequence length after tokenization.")
    parser.add_argument("--debug", action='store_true', help="Precision, recall and F1 score are calculated for each class separately if this option is enabled.")

    args = parser.parse_args()
    # yapf: enable

    do_eval()
