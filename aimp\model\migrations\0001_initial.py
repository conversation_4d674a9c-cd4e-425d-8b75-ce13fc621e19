# Generated by Django 4.1.1 on 2022-12-16 02:42

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("dataset", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="AiModels",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("create_time", models.IntegerField(default=1671158522)),
                ("update_time", models.IntegerField(default=1671158522)),
                ("model_name", models.CharField(max_length=32)),
                ("create_user", models.Char<PERSON>ield(default="root", max_length=32)),
                ("version", models.IntegerField(default=None, null=True)),
                ("is_publish", models.IntegerField(default=0)),
                ("explain", models.TextField(default=None, null=True)),
                ("mode", models.IntegerField(default=None, null=True)),
                ("status", models.IntegerField(default=0)),
                ("practice_version", models.IntegerField(default=1)),
                ("finish_rounds", models.IntegerField(default=0)),
                ("all_rounds", models.IntegerField(default=0)),
            ],
            options={"abstract": False,},
        ),
        migrations.CreateModel(
            name="AiModelsVersion",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=255)),
                ("version", models.IntegerField(default=1)),
                ("status", models.IntegerField(default=1)),
                ("job_id", models.IntegerField(default=0)),
                ("path", models.CharField(default="", max_length=255)),
                ("is_publish", models.IntegerField(default=0)),
                ("epoch", models.IntegerField(default=0)),
                ("epoch_count", models.IntegerField(default=1)),
                ("explain", models.CharField(default="", max_length=255)),
                ("test_type", models.IntegerField(default=1)),
                (
                    "model",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="model.aimodels"
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="VersionId",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("new_tag_id", models.IntegerField(default=0)),
                (
                    "tag",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="dataset.tag"
                    ),
                ),
                (
                    "version",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="model.aimodelsversion",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="PraticeDataset",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "ai_model",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="model.aimodels"
                    ),
                ),
                (
                    "dataset",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="dataset.dataset",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="ModelDataset",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "ai_model",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="model.aimodels"
                    ),
                ),
                (
                    "dataset",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="dataset.dataset",
                    ),
                ),
            ],
        ),
    ]
