import re
from typing import Dict
import json
import jsonlines
from typing import List
from functools import partial
from collections import defaultdict

import torch
from torch.utils.data.dataloader import DataLoader
import numpy as np

from qa.kbnlp.mlt.task import Task
from qa.kbnlp.mlt.util import merge_locals_kwargs
from qa.kbnlp.mlt.data_transform import VocabDict
from qa.kbnlp.mlt.layers.decoder.grte_decoder import GRTEDecoder
from qa.kbnlp.mlt.common.vocab import Vocab
from qa.kbnlp.mlt.data_transform import TransformList, rename_transform
from qa.kbnlp.mlt.dataset import BaseDataset, SortingSamplerBuilder
from qa.kbnlp.mlt.util.tensor_util import lengths_to_mask
from qa.kbnlp.mlt.common.metric import F1


class RteDataset(BaseDataset):
    def __init__(self, data, transform_list: TransformList = None):
        super(RteDataset, self).__init__(data, transform_list)


def load_Baidu_rte_schemas(path='../../raw_data/baidu_rte/all_50_schemas'):
    schemas_list = []
    with jsonlines.open(path, mode='r') as jr:
        for dic in jr:
            schemas_list.append('_'.join([dic['subject_type'], dic['object_type'], dic['predicate']]))
    return schemas_list


def read_Baidu_rte_pseudo_data(path='../../raw_data/baidu_rte/train_data_pseudo.json', max_len=255):
    data = []
    with jsonlines.open(path, mode='r') as jr:
        i = 0
        for dic in jr:
            i += 1
            # if i == 201:
            #     break
            cum_group_length = np.array([0] + [len(g) for g in dic['words']])
            cum_group_length = cum_group_length.cumsum(axis=-1)
            words_flat = sum(dic['words'], [])
            for spo in dic['spo_list']:
                left, right = spo['object_group_id'][0], spo['object_group_id'][-1]
                spo['object_interval'] = cum_group_length[left], cum_group_length[right]
                spo.pop('object_group_id')

                left, right = spo['subject_group_id'][0], spo['subject_group_id'][-1]
                spo['subject_interval'] = cum_group_length[left], cum_group_length[right]
                spo.pop('subject_group_id')
            data.append((sum(dic['words'], []), dic['spo_list']))
    return data


def baidu_rte_pseudo_data_transform(sample: dict, schema_vocab: Vocab, target_vocab: Vocab):
    sop = []  # 列表中每个元素都是一个五元组，(l_s, r_s, p, l_o, r_o)。分别表示，主实体起始位置、主实体终止位置（不包括）、关系id、客实体起始位置、客实体终止位置（不包括）
    # 在数据准备阶段直接计算存储所有数据的label过于耗费内存。这里只好先将每一个关系三元组都先转化为一个列表，记录label数组中对应位置上标签。
    # 目标的label的shape：(b, l, l, num_schema)
    sop2label = []  # 一个列表。列表存放元素见下面if、else分支中的操作。主要就是为了在计算loss时才创建label，并填充值。
    for spo in sample['spo_list']:
        schema_id = schema_vocab('_'.join([spo['subject_type'], spo['object_type'], spo['predicate']]))
        subj_start, subj_end = spo['subject_interval']
        obj_start, obj_end = spo['object_interval']
        sop.append((subj_start, subj_end, obj_start, obj_end, schema_id))
        if subj_start == subj_end - 1 and obj_start == obj_end - 1:
            sop2label.append(((subj_start, obj_start, schema_id), target_vocab('SS')))
        elif subj_start == subj_end - 1 and obj_start != obj_end - 1:
            sop2label.append(((subj_start, obj_start, schema_id), target_vocab('SMH')))
            sop2label.append(((subj_end - 1, obj_end - 1, schema_id), target_vocab('SMT')))
        elif subj_start != subj_end - 1 and obj_start == obj_end - 1:
            sop2label.append(((subj_start, obj_start, schema_id), target_vocab('MSH')))
            sop2label.append(((subj_end - 1, obj_end - 1, schema_id), target_vocab('MST')))
        elif subj_start != subj_end - 1 and obj_start != obj_end - 1:
            sop2label.append(((subj_start, obj_start, schema_id), target_vocab('MMH')))
            sop2label.append(((subj_end - 1, obj_end - 1, schema_id), target_vocab('MMT')))
    # if len(set(one[0] for one in sop2label)) != len(sop2label):
    #     print(sample['token'])

        # 内存不够用。
        # if subj_start == subj_end - 1 and obj_start == obj_end - 1:
        #     label[subj_start, obj_start, schema_id] = target_vocab('SS')
        # elif subj_start == subj_end - 1 and obj_start != obj_end - 1:
        #     label[subj_start, obj_start, schema_id] = target_vocab('SMH')
        #     label[subj_end - 1, obj_end - 1, schema_id] = target_vocab('SMT')
        # elif subj_start != subj_end - 1 and obj_start == obj_end - 1:
        #     label[subj_start, obj_start, schema_id] = target_vocab('MSH')
        #     label[subj_end - 1, obj_end - 1, schema_id] = target_vocab('MST')
        # elif subj_start != subj_end - 1 and obj_start != obj_end - 1:
        #     label[subj_start, obj_start, schema_id] = target_vocab('MMH')
        #     label[subj_end - 1, obj_end - 1, schema_id] = target_vocab('MMT')
    sample['sop'] = sop
    sample['sop2label'] = sop2label  # 列表是一个二元组，第一个元素是一个三元组，分别代表(sub, obj, pre);第二个元素代表根据grte模型判定的label。
    return sample


def read_NYT_data(path):
    pass


class Grte(Task):
    def __init__(self,
                 trn=None,
                 dev=None,
                 tst=None,
                 schemas: List[str]=None,
                 batch_size=None,
                 batch_max_tokens=None,
                 eval_batch_size=None,
                 eval_batch_max_tokens=None,
                 # dependencies: str = None,
                 lr=None,
                 separate_optimizer=False,
                 cls_is_bos=False,
                 sep_is_eos=False,
                 use_grte_decoder=False,  # 以下是grte参数
                 rounds=2,
                 extra_layer_normal=False,
                 num_attention_heads=4,
                 attention_probs_dropout_prob=0.1,
                 hidden_dropout_prob=0.1,
                 max_position_embeddings=512,
                 is_decoder=False,
                 layer_norm_eps=1e-12,
                 intermediate_size=1024,
                 hidden_act='gelu',
                 **kwargs
                 ):
        super(Grte, self).__init__(**merge_locals_kwargs(locals(), kwargs, excludes=['self', 'kwargs', '__class__']))
        self.vocabs = VocabDict()  # 至少需要一个tag的vocab
        self.schemas = schemas

    def build_model(self, encoder_size, task_name, **kwargs):
        return GRTEDecoder(num_p=len(self.vocabs['%s_schema_tag' % task_name]),
                           num_label=len(self.vocabs['%s_tag' % task_name]), hidden_size=encoder_size,
                           **self.config)

    def build_vocab(self, task_name):
        self.vocabs['%s_tag' % task_name] = Vocab(["N/A", "SMH", "SMT", "SS", "MMH", "MMT", "MSH", "MST"],
                                                  pad_token=None, unk_token=None)
        self.vocabs['%s_schema_tag' % task_name] = Vocab(self.schemas, pad_token=None, unk_token=None)
        for vocab in self.vocabs.values():
            vocab.lock()
            if vocab.unk_token is None:
                vocab.set_unk_as_safe_unk()

    def build_sample(self, data):
        examples = []
        for i, d in enumerate(data):
            words, spo_list = None, None
            if len(d) == 2:
                words, spo_list = d
            else:
                words = d
            examples.append({'original_input': words})
            examples[-1]['_id'] = i
            assert isinstance(words, list), 'words 必须是分好词的列表。'
            examples[-1]['token'] = words
            examples[-1]['spo_list'] = spo_list
        return examples

    def build_transformList(self, mode, task_name, **kwargs):
        if mode == 'train' or mode == 'eval':
            get_sop2label = partial(baidu_rte_pseudo_data_transform,
                                    schema_vocab=self.vocabs['%s_schema_tag' % task_name],
                                    target_vocab=self.vocabs['%s_tag' % task_name])
            return TransformList(get_sop2label)
        elif mode == 'predict':
            return TransformList()
        else:
            raise ValueError("mode必须在'train', 'eval', 'predict' 中取值。")

    def build_dataset(self, data, transform_list, **kwargs):
        return RteDataset(data, transform_list)

    def build_dataloader(self, dataset, batch_size=None, batch_max_tokens=None, shuffle=False, generator=None, gradient_accumulation=1,
                         use_effective_tokens=False, **kwargs):

        return DataLoader(dataset=dataset,
                          batch_sampler=SortingSamplerBuilder.build([data['subtoken_len'] for data in dataset],
                                                                    batch_size, batch_max_tokens, use_effective_tokens,
                                                                    shuffle, generator, gradient_accumulation),
                          collate_fn=partial(RteDataset.collate_fn, padding_value=0))

    def build_criterion(self, reduction='none', **kwargs):
        return torch.nn.CrossEntropyLoss(reduction=reduction)

    def build_optimizer(self, decoder, **kwargs):
        pass

    def compute_loss(self, task_name, batch, out, mask, criterion):
        score = out[:, 1: -1, 1: -1]  # grte返回最后一层的logit，包括cls和sep。 score:(b, l, l, num_p, num_l)
        b, l, _, schema_size, n_label = score.size()
        mask = mask[:, 2:]  # 去除cls和sep。
        mask = mask.unsqueeze(-1) & mask.unsqueeze(1)
        score = score[mask].view(-1, n_label)
        label = torch.zeros((b, l, l, schema_size), dtype=torch.long, device=score.device)
        for idx, sop2label_one_sample in enumerate(batch['sop2label']):
            for sop, target in sop2label_one_sample:
                label[idx][sop] = target

        loss = criterion(score - torch.max(score, dim=-1, keepdim=True).values, label[mask].view(-1)).sum()  # reduction='none'
        loss = criterion(score - torch.max(score, dim=-1, keepdim=True).values, label[mask].view(-1)).sum() / b  # reduction='none'
        #loss = criterion(score, label[mask].view(-1)) * (score.size(0) / b)  # # reduction='mean'
        return loss

    def build_metric(self):
        return F1()

    def update_metric(self, prediction, batch, metric, task_name):
        for p, g in zip(prediction, batch['sop']):
            pred = set(p)
            gold = set(g)
            metric(pred, gold)

    def feed_batch(self, mode: str, h: torch.FloatTensor, batch: Dict, output_dict: Dict, decoder: torch.nn.Module,
                   task_name):
        output_dict[task_name] = {}
        mask = lengths_to_mask(batch['token_len'])  # GrteDecoder前向传播需要cls和sep。
        mask_token_ids = torch.zeros_like(mask, dtype=torch.int)
        mask_token_ids[mask] = 1
        score_tuple = decoder(h,
                              mask_token_ids)  # 将所有层的结构都返回，每一层的score的size是(b, l+2, l+2, num_relation, 2), 每个relation对应有关和无关两种情况。
        output_dict[task_name]['output'] = score_tuple
        output_dict[task_name]['mask'] = mask  # (b, l+2, l+2),cls和sep都标记为True.

    def decode_output(self, mode, output_dict, batch, model, task_name, **kwargs):
        score = output_dict[task_name]['output'][:, 1: -1, 1: -1]  # grte返回最后一层的logit，包括cls和sep。 score:(b, l, l, num_p, num_l)
        mask = output_dict[task_name]['mask']
        pred_label = score.argmax(dim=-1).cpu().detach().numpy()
        output_dict[task_name]['prediction'] = self.get_pred_sop(pred_label, mask, task_name)

    def get_pred_sop(self, pred_label, mask, task_name):
        label_voc = self.vocabs['%s_tag' % task_name]
        b, length, _, num_schemas = pred_label.shape  # pred_label的size: (b, l, l, num_schemas)
        mask = mask[:, 2:]
        mask = (mask.unsqueeze(-1) & mask.unsqueeze(1)).unsqueeze(-1).cpu().detach().numpy()
        all_loc = np.where((pred_label != label_voc("N/A")) & mask)

        res, res_dict = [], []
        for i in range(b):
            res_dict.append(defaultdict(list))
            res.append([])

        for b_id, s_id, o_id, schema_id in reversed(list(zip(*all_loc))):
            pred_l = pred_label[b_id, s_id, o_id, schema_id]
            if pred_l == label_voc['SS']:
                res[b_id].append((s_id, s_id + 1, o_id, o_id + 1, schema_id))
            elif pred_l == label_voc['SMH']:
                for (b_id_, s_id_, o_id_, schema_id_), pred_l_ in res_dict[b_id][schema_id]:
                    if pred_l_ == label_voc['SMT'] and s_id == s_id_ and o_id_ > o_id:
                        res[b_id].append((s_id, s_id_ + 1, o_id, o_id_ + 1, schema_id))
                        break
            elif pred_l == label_voc['MSH']:
                for (b_id_, s_id_, o_id_, schema_id_), pred_l_ in res_dict[b_id][schema_id]:
                    if pred_l_ == label_voc['MST'] and s_id < s_id_ and o_id == o_id_:
                        res[b_id].append((s_id, s_id_ + 1, o_id, o_id_ + 1, schema_id))
                        break
            elif pred_l == label_voc['MMH']:
                for (b_id_, s_id_, o_id_, schema_id_), pred_l_ in res_dict[b_id][schema_id]:
                    if pred_l_ == label_voc['MMT'] and s_id < s_id_ and o_id < o_id_:
                        res[b_id].append((s_id, s_id_ + 1, o_id, o_id_ + 1, schema_id))
                        break
            res_dict[b_id][schema_id].append(((b_id, s_id, o_id, schema_id), pred_l))
        return res

        # for i in range(len(all_loc[0])):
        #     res_dict[all_loc[0][i]].append([all_loc[1][i], all_loc[2][i], all_loc[3][i]])
        #
        # for i in range(b):
        #     for l1, l2, r in res_dict[i]:
        #         if pred_label[i, l1, l2, r] == label_voc("SS"):
        #             res[i].append([l1, l1, r, l2, l2])
        #         elif pred_label[i, l1, l2, r] == label_voc("SMH"):
        #             for l1_, l2_, r_ in res_dict[i]:
        #                 if r == r_ and pred_label[i, l1_, l2_, r_] == label_voc("SMT") and l1_ == l1 and l2_ > l2:
        #                     res[i].append([l1, l1, r, l2, l2_])
        #                     break
        #         elif pred_label[i, l1, l2, r] == label_voc("MMH"):
        #             for l1_, l2_, r_ in res_dict[i]:
        #                 if r == r_ and pred_label[i, l1_, l2_, r_] == label_voc("MMT") and l1_ > l1 and l2_ > l2:
        #                     res[i].append([l1, l1_, r, l2, l2_])
        #                     break
        #         elif pred_label[i, l1, l2, r] == label_voc("MSH"):
        #             for l1_, l2_, r_ in res_dict[i]:
        #                 if r == r_ and pred_label[i, l1_, l2_, r_] == label_voc("MST") and l1_ > l1 and l2_ == l2:
        #                     res[i].append([l1, l1_, r, l2, l2_])
        #                     break
        # return res

    def prediction_to_result(self, pred, vocab, batch, output_dict, task_name, **kwargs):
        vocab = self.vocabs['%s_schema_tag' % task_name]
        res = []
        for pred_per_sent, tokens in zip(pred, batch['token']):
            res_per_sent = []
            for (s_l, s_r, o_l, o_r, p) in pred_per_sent:
                subject_type, object_type, predicate = vocab.get_token(p).split('_')
                res_per_sent.append({'subject_type': subject_type,
                                     'subject': ''.join(tokens[s_l: s_r]),
                                     'sub_start': s_l + 1,
                                     'sub_end': s_r + 1,
                                     'object_type': object_type,
                                     'object': ''.join(tokens[o_l: o_r]),
                                     'obj_start': o_l + 1,
                                     'obj_end': o_r + 1,
                                     'predicate': predicate})
            res.append(res_per_sent)
        return res

    def post_transform(self, task_name, batch, output_dict, device, **kwargs):
        output_dict.pop(task_name)


if __name__ == '__main__':
    data_ = read_Baidu_rte_pseudo_data(path='../../raw_data/baidu_rte/dev_data_pseudo.json', max_len=10000)[:]
    grte = Grte(schemas=load_Baidu_rte_schemas(path='../../raw_data/baidu_rte/all_50_schemas'))
    samples = grte.build_sample(data_)
    grte.build_vocab(task_name='rte')
    datasets = grte.build_dataset(samples, grte.build_transformList(mode='train', task_name='rte'))
    print('finish.')
