import json
import os
import jieba.analyse
import sqlalchemy
import traceback
from datetime import datetime
from fastapi import APIRouter, Depends, BackgroundTasks, Form, File, UploadFile, Request
from pydantic import BaseModel, validator
from typing import Optional, List
from sqlalchemy import create_engine
from common.customResponse import resp
from .models import Map, GraphWord, GraphImage
from sqlalchemy.orm import Session
from common.database import get_db
from .sync_data.main import SyncData
from .sync_data_v2.main import new_sync_data
from sqlalchemy import func, or_
from common.logger import logger

router = APIRouter(
    prefix="/map",
    tags=["map"],
    responses={404: {"description": "Not found"}}
)


class Param(BaseModel):
    database_uri: str


@router.post("/test_db")
@logger.catch
def test_db(param: Param):
    """
    测试连接
    mysql: mysql+pymysql://username:password@hostname:port/dbname
    oracle: //scott:tiger@hostname:1521/sidname
    sql server: mssql+pymssql://<username>:<password>@<freetds_name>/?charset=utf8
    """
    from .extensions import parse_uri
    database_uri = param.database_uri
    try:
        engine = create_engine(parse_uri(database_uri))  # 创建引擎
        conn = engine.connect()  # 连接
        conn.close()  # 关闭连接
        return resp(msg="连接成功")
    except Exception as e:
        return resp(code=400, msg="连接失败")


@router.post("/db_name")
@logger.catch
def db_name(param: Param):
    """
    获取数据库名
    """
    from .extensions import parse_uri
    database_uri = param.database_uri
    if not database_uri:
        return resp(msg="连接字符串不能为空!", code=400)
    engine = create_engine(parse_uri(database_uri))  # 创建引擎
    insp = sqlalchemy.inspect(engine)
    db_names = insp.get_schema_names()
    sys_db = ['information_schema', 'mysql', 'performance_schema', 'sys']
    my_db = list(set(db_names).difference(set(sys_db)))
    return resp(data=my_db)


class TableParam(BaseModel):
    database_uri: str
    db_name: Optional[str]


@router.post("/table_name")
@logger.catch
def table_name(param: TableParam):
    """
    获取表名
    """
    from .extensions import parse_uri
    database_uri = param.database_uri
    try:
        engine = create_engine(parse_uri(database_uri))
        tables = engine.table_names()
        table_info = [item for item in tables]
        return resp(data=table_info)
    except Exception as e:
        return resp(code=400, msg=e.args[-1])


class ColumnParam(BaseModel):
    database_uri: str
    table_name: str


@router.post("/column_name")
@logger.catch
def column_name(param: ColumnParam):
    from .extensions import parse_uri
    database_uri = param.database_uri
    table_name = param.table_name
    try:
        engine = create_engine(parse_uri(database_uri))
        md = sqlalchemy.MetaData()
        table = sqlalchemy.Table(table_name, md, autoload=True, autoload_with=engine)
        column_info = [c.name for c in table.c]
        return resp(data=column_info)
    except Exception as e:
        return resp(code=400, msg=e.args[-1])


class TgParam(BaseModel):
    label_name: str


@router.post("/label_fields")
def get_tugraph_fields(param: TgParam):
    from .sync_data.build_tugraph_all_relation import get_label
    ret = []
    try:
        label_name = param.label_name
        ret = get_label(label_name)
    except Exception as e:
        import traceback
        logger.warning(traceback.format_exc())
    return resp(data=ret)


class TmParam(BaseModel):
    config: dict = Form(...)
    file: UploadFile = File(...)


@router.post("/create_map")
@logger.catch
async def create_map(request: Request, db: Session = Depends(get_db)):
    try:
        from .extensions import config_map, my_minio
        param = await request.form()
        file = param.get("file")
        config = json.loads(param.get("config"))
        map_type = config.get("MapType")
        qa_row = Map(
            name=config.get("SubgraphName"),
            map_type=map_type,
            config=json.dumps(config, ensure_ascii=False)
        )
        db.add(qa_row)
        db.flush()
        qa_row_id = qa_row.id
        db.commit()
        if int(map_type) == 1:
            # dir_path = f"{config_map.MINIO_UPLOAD_DIR}/{qa_row_id}"
            # if not os.path.exists(dir_path):
            #     os.makedirs(dir_path)
            # with open(f"{dir_path}/{file.filename}", "wb", 0) as f:
            #     f.write(await file.read())
            file_path = f"{config_map.MINIO_UPLOAD_DIR}/{qa_row_id}/{file.filename}"
            size = os.fstat(file.file.fileno()).st_size
            my_minio.put_object(file_path, file.file, size)
        code = 200
        message = "创建成功"
    except Exception as e:
        import traceback
        logger.error(traceback.format_exc())
        code = 500
        message = "创建失败"
    return resp(code=code, msg=message)


class MapInnerSchema(BaseModel):
    id: int
    name: str
    dataNums: int
    surplus: int
    nodeNums: int
    relationNums: int
    construction_method: str
    config: str
    status: int
    is_active: bool
    create_time: datetime
    update_time: datetime

    class Config:
        orm_mode = True

    @validator('config')
    def json_to_dict(cls, v):
        return json.loads(v)

    @validator('create_time')
    def format_time(cls, v):
        return v.strftime('%Y-%m-%d %H:%M:%S')


class MapOutSchema(BaseModel):
    data: List[MapInnerSchema]
    total: int


@router.get("/map_task", response_model=MapOutSchema)
@logger.catch
def map(page=1, pageSize=9, db: Session = Depends(get_db)):
    """
    获取图谱构建任务列表
    """
    count = db.query(func.count(Map.id)).scalar()
    ret = db.query(Map).order_by(Map.create_time.desc()).limit(pageSize).offset((int(page) - 1) * int(pageSize)).all()
    return {
        "data": ret,
        "total": count
    }


@router.get("/map_detail", response_model=MapInnerSchema)
def map_detail(id, db: Session = Depends(get_db)):
    """
    获取图谱构建详情
    """
    ret = db.query(Map).filter(Map.id == id).first()
    return ret


class IdListParam(BaseModel):
    id: List


@router.post("/delete_map")
@logger.catch
def delete_map(param: IdListParam, db: Session = Depends(get_db)):
    """
    删除图谱构建任务
    """
    id = param.id
    db.query(Map).filter(Map.id.in_(id)).delete()
    db.commit()
    return resp()


class IdParam(BaseModel):
    id: int


@router.post("/start_map")
@logger.catch
def start_map(param: IdParam, background_tasks: BackgroundTasks, db: Session = Depends(get_db)):
    from .sync_data.build_tugraph_all_relation import create_node_label_and_insert_node
    id = param.id
    map = db.query(Map).filter(Map.id == id)
    map.update({
        "is_active": True,
        "status": 1
    })
    db.commit()
    config = json.loads(map.first().config) if map.first().config else {}
    map_type = map.first().map_type
    if int(map_type) == 0 or int(map_type) == 2:
        database_uri = config.get("databaseUri")
        if not database_uri:
            raise Exception("databaseUri not found")
        background_tasks.add_task(SyncData(database_uri).run, id, config)
    elif int(map_type) == 1:
        file_name = config.get("uploadFile")
        file_path = f"//ubt-B450M-02@19002/share/aimp/upload_file/{id}/{file_name}"
        background_tasks.add_task(create_node_label_and_insert_node, id, file_path)
    return resp()


@router.post("/stop_map")
@logger.catch
def stop_map(param: IdParam, db: Session = Depends(get_db)):
    id = param.id
    db.query(Map).filter(Map.id == id).update({
        "is_active": False,
        "status": 0
    })
    db.commit()
    return resp()


class KeywordParam(BaseModel):
    page: int = 1
    pageSize: int = 9
    keyword: str


@router.post("/search_map", response_model=MapOutSchema)
@logger.catch
def search_map(param: KeywordParam, db: Session = Depends(get_db)):
    page = param.page
    pageSize = param.pageSize
    keyword = param.keyword
    count = db.query(func.count(Map.id)).scalar()
    ret = db.query(Map).filter(Map.name.like(f"%{keyword}%")).order_by(Map.create_time.desc()).limit(pageSize).offset(
        (int(page) - 1) * int(pageSize)).all()
    return {
        "data": ret,
        "total": count
    }


class UpdateParam(BaseModel):
    data: dict


@router.post("/update_status")
@logger.catch
def update_status(param: UpdateParam, db: Session = Depends(get_db)):
    data = param.data
    id = data.get("id")
    del data["id"]
    db.query(Map).filter(Map.id == id).update(data)
    db.commit()
    return resp()


@router.get("/map_info")
@logger.catch
def map_info(db: Session = Depends(get_db)):
    from .extensions import tugraph_db
    label_info = tugraph_db.labels.get()
    vertex = label_info.get("vertex")
    node_nums = len(vertex)
    relation_nums = len(label_info.get("edge"))
    attr_nums = 0
    for item in vertex:
        item_info = tugraph_db.labels.fields(item)
        attr_nums += len(list(item_info.keys()))
    count = db.query(func.count(Map.id)).filter(Map.map_type == 1).scalar()
    data = {
        "node_nums": node_nums,
        "attr_nums": attr_nums,
        "relation_nums": relation_nums,
        "zt_nums": count
    }
    return resp(data=data)


@router.get("/get_tugraph_labels")
@logger.catch
def get_tugraph_labels(map_id: int, db: Session = Depends(get_db)):
    ret = db.query(Map).filter(Map.id == map_id).first()
    if ret.map_type == 0:  # 通用图谱
        subgraph_name = ["item", "FakeSentence", "WordOccurrence", "word", "Img", "Box"]
    if ret.map_type == 1:  # 专题图谱
        subgraph_name = [json.loads(ret.config).get("sheet")]
    # query_data = tugraph_db.labels.get()
    return resp(data=subgraph_name)


@router.get("/get_labels_attr")
@logger.catch
def get_labels_attr():
    from .extensions import tugraph_db
    query_data = tugraph_db.labels.get()
    return resp(data=query_data)


# sync_data_v2


@router.get("/graph_sync")
def async_tugraph(background_tasks: BackgroundTasks, ):

    try:
        get_db()
        background_tasks.add_task(new_sync_data.run)
    except Exception as e:
        logger.error(traceback.format_exc())
    return resp()


@router.get("/graph_search")
def graph_search(keyword: str, db: Session = Depends(get_db)):
    try:
        keyword_list = jieba.analyse.extract_tags(keyword)
        if not keyword_list:
            keyword_list = [keyword]
        node_limit = 10
        data_filter = [keyword]
        data = [
            {
                "name": keyword,
                "des": keyword,
                "symbolSize": 65,
                "category": 0,
                "itemStyle": {
                    "emphasis": {
                        "color": '#af9369'
                    },
                },
            }
        ]
        links = []
        a_w = []
        i_w = []
        image_id_list = []
        for k_y in keyword_list:
            if k_y not in data_filter:
                data_filter.append(k_y)
                data.append({
                    "name": k_y,
                    "des": k_y,
                    "symbolSize": 50,
                    "category": 1,
                    "itemStyle": {
                        "emphasis": {
                            "color": '#B6996E'
                        },
                    },
                })
                links.append({
                    "source": keyword,
                    "target": k_y,
                    "name": "",
                    # "label": {"show": True, "formatter": "item["rule"]", "fontSize": 12}
                })
            my_graph_word = db.query(GraphWord).order_by(GraphWord.create_time.desc()).filter(or_(
                GraphWord.epistatic_word.like(f"%、{k_y}、%"),
                GraphWord.epistatic_word.like(f"{k_y}、%"),
                GraphWord.epistatic_word.like(f"%、{k_y}"),
                GraphWord.epistatic_word == k_y)).limit(node_limit).all()
            for g_w in my_graph_word:
                # ------- 获取同位词、下位词 --------
                current_a_w = g_w.apposition_word.split("、")
                if "" in current_a_w:
                    current_a_w.remove("")
                a_w.extend(current_a_w)
                current_i_w = g_w.inferior_word.split("、")
                if "" in current_i_w:
                    current_i_w.remove("")
                i_w.extend(current_i_w)
                # ------- image_id --------
                if g_w.image_id not in image_id_list:
                    image_id_list.append(g_w.image_id)
                # ---------------
                word = g_w.name
                if word not in data_filter:
                    data_filter.append(word)
                    data.append({
                        "name": word,
                        "des": word,
                        "symbolSize": 50,
                        "category": 1,
                        "itemStyle": {
                            "emphasis": {
                                "color": '#B6996E'
                            },
                        },
                    })
                    links.append({
                        "source": k_y,
                        "target": word,
                        "name": "",
                        # "label": {"show": True, "formatter": "item["rule"]", "fontSize": 12}
                    })
                my_graph_word2 = db.query(GraphWord).order_by(GraphWord.create_time.desc()).filter(or_(
                    GraphWord.epistatic_word.like(f"%、{word}、%"),
                    GraphWord.epistatic_word.like(f"{word}、%"),
                    GraphWord.epistatic_word.like(f"%、{word}"),
                    GraphWord.epistatic_word == word)).limit(node_limit).all()
                for g_w2 in my_graph_word2:
                    word2 = g_w2.name
                    if word2 not in data_filter:
                        data_filter.append(word2)
                        data.append({
                            "name": word2,
                            "des": word2,
                            "symbolSize": 50,
                            "category": 1,
                            "itemStyle": {
                                "emphasis": {
                                    "color": '#B6996E'
                                },
                            },
                        })
                        links.append({
                            "source": word,
                            "target": word2,
                            "name": "",
                            # "label": {"show": True, "formatter": "item["rule"]", "fontSize": 12}
                        })
        # 获取文件信息
        my_graph_image = db.query(GraphImage).order_by(GraphImage.create_time.desc()).filter(
            GraphImage.id.in_(image_id_list)).limit(node_limit).all()
        image_info = []
        for m_g_i in my_graph_image:
            image_info.append({
                "file_name": m_g_i.file_name,
                "file_path": m_g_i.file_path,
                "content": m_g_i.content
            })
        ret = {
            "keyword_list": keyword_list,
            "data": data,
            "links": links,
            "a_w": list(set(a_w))[:10],
            "i_w": list(set(i_w))[:10],
            "image_info": image_info[:10]
        }
        # print(ret)
        return resp(data=ret)
    except Exception as e:
        logger.error(traceback.format_exc())
        return resp(code=400, msg="error")
