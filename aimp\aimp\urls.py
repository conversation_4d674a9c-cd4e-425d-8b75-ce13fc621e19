"""aimp URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.1/topics/http/urls/
Examples:
Function views
    2. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    2. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    2. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path


# import service.views

# from service import views as service_view
# from ninja import NinjaAPI
# from dataset.views import router as dataSet

#
# api = NinjaAPI()
#
# api.add_router("/dataSet/",dataSet)

urlpatterns = [
    path("admin/", admin.site.urls),
    # path(r'in/', views.index),
    # path(r"test/",views.getDatasource),
    # path(r"addDataSource/",views.addDataSource)

    # path(r"service/",service_view.getService),
    # path(r"delService/",service.views.delService),
    # path(r"getConfig/",service.views.getConfig),
    # path(r"getDataSet/",dataset.views.getDataSet),
    # path("api/", api.urls)
]
