from torch import nn


def MLP(layer_sizes, dropout=0.0, activation=nn.ReLU, output_dropout=None, output_activation=None):
    """
    layer_sizers参数的首位两个值表示输入和输出的size，中间的时hidden size。
    :param layer_sizes: [input_size, h1_size,...,hk_size, out_size],长度至少为2.
    :param dropout:
    :param activation:
    :param output_dropout:
    :param output_activation:
    :return:
    """
    assert len(layer_sizes) >= 2
    layers = []
    hid_layers = len(layer_sizes) - 2
    for index in range(hid_layers):
        layers.extend([
            nn.Linear(layer_sizes[index], layer_sizes[index + 1]),
            activation(),
            nn.Dropout(p=dropout)
        ])

    layers.append(nn.Linear(layer_sizes[-2], layer_sizes[-1]))
    if output_activation is not None:
        layers.append(output_activation())
    if output_activation is not None:
        layers.append(nn.Dropout(p=output_dropout))
    return nn.Sequential(*layers)


if __name__ == '__main__':
    print(MLP([128, 256, 90, 512], dropout=0.1, output_dropout=0.2, output_activation=nn.Mish))
