from django.db import models
from dataset.models import DataSet
import time
from dataset.models import BaseClass
# Create your models here.


class TimeClass(models.Model):
    create_time = models.IntegerField(default=0)  # 创建时间
    update_time = models.IntegerField(default=0)  # 更新时间

    def __init__(self):
        _time = int(time.time())
        self._create_time = _time
        self.update_time = _time

    # 抽象类，不创建实际数据库
    class Meta:
        abstract = True


class AiModels(TimeClass,models.Model):
    model_name = models.CharField(max_length=32) # 模型名称
    create_user = models.CharField(max_length=32,default="root") # 创建用户
    version = models.IntegerField(null=True,default=None) #发布版本
    is_publish = models.IntegerField(default=0) # 是否发布
    explain = models.TextField(null=True,default=None)
    mode = models.IntegerField(null=True,default=None)
    status = models.IntegerField(default=0)
    practice_version = models.IntegerField(default=1)
    finish_rounds = models.IntegerField(default=0)  # 完成轮次
    all_rounds = models.IntegerField(default=0)  # 总轮次

    def __init__(self, *args, **kwargs):
        "重写__init__方法"
        super(TimeClass, self).__init__(*args, **kwargs)
        super(AiModels, self).__init__()
        self.update_time = int(time.time())

    def save(self, *args, **kwargs):
        if not self.create_time:
            self.create_time = self._create_time
        self.update_time = self.update_time
        super(AiModels, self).save(*args, **kwargs)

# class PraticeModel(models.Model):
#     practice_status = models.IntegerField(default=2)  # 训练状态
#     practice_version = models.IntegerField(default=2) # 训练版本
#     finish_rounds = models.IntegerField(default=0)  # 完成轮次
#     all_rounds = models.IntegerField(default=0)  # 总轮次
#     model = models.ForeignKey("AiModels", on_delete=models.CASCADE)


class PraticeDataset(models.Model):
    dataset = models.ForeignKey("dataset.DataSet", on_delete=models.CASCADE)
    ai_model = models.ForeignKey("AiModels", on_delete=models.CASCADE)


class ModelDataset(models.Model):
    ai_model = models.ForeignKey("AiModels",on_delete=models.CASCADE)
    dataset = models.ForeignKey("dataset.DataSet",on_delete=models.CASCADE)


class AiModelsVersion(models.Model):
    model = models.ForeignKey("AiModels",on_delete=models.CASCADE) # 模型Id
    name = models.CharField(max_length=255)
    version = models.IntegerField(default=1) # 模型版本
    status = models.IntegerField(default=1) # 模型状态
    job_id = models.IntegerField(default=0)
    path = models.CharField(max_length=255,default="")
    is_publish = models.IntegerField(default=0)  # 是否发布
    epoch =  models.IntegerField(default=0) # 轮次
    epoch_count = models.IntegerField(default=1) # 总数
    explain = models.CharField(max_length=255,default="")
    test_type = models.IntegerField(default=1) # 1-uie,2-uiex
    max_seq_len =models.IntegerField(default=512) # 现在主要为著录用。


class VersionId(models.Model):
    version = models.ForeignKey("AiModelsVersion",on_delete=models.CASCADE)
    tag = models.ForeignKey("dataset.tag",on_delete=models.CASCADE)
    new_tag_id =models.IntegerField(default=0)
