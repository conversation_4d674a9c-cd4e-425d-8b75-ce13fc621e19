# encoding=utf-8
import os
import re


def load_data_from_dir(path):
    for dir_path, dir_names, file_names in os.walk(path):
        for file in file_names:
            full_path = os.path.join(dir_path, file)
            for line in open(full_path, encoding='UTF-8'):
                yield line.strip().split("\t")


def remove_punctuation(query):
    # 去掉标点
    punctuation = "[\s+\.\!\?\/_,$%(+\"\']+|[+——！，。？、~@#￥%……&（）]+"
    return re.sub(punctuation, ' ', query)


def remove_ln(query):
    # 去掉字母和数字
    return re.sub('[a-zA-Z0-9]', '', query)


def is_contain_chinese(check_str):
    """
    判断字符串中是否包含中文
    :param check_str: {str} 需要检测的字符串
    :return: {bool} 包含返回True， 不包含返回False
    """
    for ch in check_str:
        if u'\u4e00' <= ch <= u'\u9fff':
            return True
    return False
