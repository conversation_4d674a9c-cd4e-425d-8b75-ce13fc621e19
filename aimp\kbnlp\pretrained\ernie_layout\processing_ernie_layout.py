# coding=utf-8
# Copyright 2021 The HuggingFace Inc. team.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
"""
Processor class for LayoutXLM.
"""
import warnings
from typing import List, Optional, Union


from transformers.processing_utils import ProcessorMixin, transformers_module
from transformers.tokenization_utils_base import BatchEncoding, PaddingStrategy, PreTokenizedInput, TextInput, TruncationStrategy
from transformers.utils import TensorType

from kbnlp.pretrained.ernie_layout.feature_extraction_ernie_layout import ErnieLayoutFeatureExtractor
from kbnlp.pretrained.ernie_layout.tokenization_ernie_layout import ErnieLayoutTokenizer
from kbnlp.pretrained.ernie_layout.tokenization_ernie_layout_fast import Ernie<PERSON>ayoutTokenizerFast
if not hasattr(transformers_module, 'ErnieLayoutFeatureExtractor'):
    setattr(transformers_module, 'ErnieLayoutFeatureExtractor', ErnieLayoutFeatureExtractor)
if not hasattr(transformers_module, 'ErnieLayoutTokenizer'):
    setattr(transformers_module, 'ErnieLayoutTokenizer', ErnieLayoutTokenizer)
if not hasattr(transformers_module, 'ErnieLayoutTokenizerFast'):
    setattr(transformers_module, 'ErnieLayoutTokenizerFast', ErnieLayoutTokenizerFast)


class ErnieLayoutProcessor(ProcessorMixin):
    r"""
    Constructs a ErnieLayout processor which combines a ErnieLayout feature extractor and a ErnieLayout tokenizer into a
    single processor.

    [`ErnieLayoutProcessor`] offers all the functionalities you need to prepare data for the model.

    It first uses [`ErnieLayoutFeatureExtractor`] to resize document images to a fixed size, and optionally applies OCR
    to get words and normalized bounding boxes. These are then provided to [`ErnieLayoutTokenizer`] or
    [`ErnieLayoutTokenizerFast`], which turns the words and bounding boxes into token-level `input_ids`,
    `attention_mask`, `token_type_ids`, `bbox`. Optionally, one can provide integer `word_labels`, which are turned
    into token-level `labels` for token classification tasks (such as FUNSD, CORD).

    Args:
        feature_extractor (`ErnieLayoutFeatureExtractor`):
            An instance of [`ErnieLayoutFeatureExtractor`]. The feature extractor is a required input.
        tokenizer (`ErnieLayoutTokenizer` or `ErnieLayoutTokenizerFast`):
            An instance of [`ErnieLayoutTokenizer`] or [`ErnieLayoutTokenizerFast`]. The tokenizer is a required input.
    """
    feature_extractor_class = "ErnieLayoutFeatureExtractor"
    tokenizer_class = ("ErnieLayoutTokenizer", "ErnieLayoutTokenizerFast")

    def __call__(
        self,
        images,
        text: Union[TextInput, PreTokenizedInput, List[TextInput], List[PreTokenizedInput]] = None,
        text_pair: Optional[Union[PreTokenizedInput, List[PreTokenizedInput]]] = None,
        boxes: Union[List[List[int]], List[List[List[int]]]] = None,
        # word_labels: Optional[Union[List[int], List[List[int]]]] = None,
        add_special_tokens: bool = True,
        padding: Union[bool, str, PaddingStrategy] = False,
        truncation: Union[bool, str, TruncationStrategy] = False,
        max_length: Optional[int] = 512,
        stride: int = 0,
        pad_to_multiple_of: Optional[int] = None,
        return_token_type_ids: Optional[bool] = None,
        return_attention_mask: Optional[bool] = None,
        # return_overflowing_tokens: bool = False,
        return_special_tokens_mask: bool = False,
        return_offsets_mapping: bool = False,
        return_word_ids: bool = False,
        return_sequence_ids: bool = False,
        return_length: bool = False,
        verbose: bool = True,
        return_tensors: Optional[Union[str, TensorType]] = None,
        **kwargs
    ) -> BatchEncoding:
        """
        This method first forwards the `images` argument to [`~ErnieLayoutFeatureExtractor.__call__`]. In case
        [`ErnieLayoutFeatureExtractor`] was initialized with `apply_ocr` set to `True` or added key word param
        `apply_ocr` is `True`, it passes the obtained words and bounding boxes along with the additional arguments
        to [`~LayoutXLMTokenizer.__call__`] and returns the output, together with resized `images`.
        In case [`ErnieLayoutFeatureExtractor`] was initialized with `apply_ocr` set to `False` or added key word param
        `apply_ocr` is `False`, it passes the words
         (`text`/``text_pair`) and `boxes` specified by the user along with the additional
        arguments to [`~LayoutXLMTokenizer.__call__`] and returns the output, together with resized `images``.
        Note: the priority of keyword param `apply_ocr` is higher than `apply_ocr` attr of `ErnieLayoutFeatureExtractor`
        ,but would not revise the value of `apply_ocr` attr.
        """

        if not isinstance(max_length, int) and max_length is not None:
            warnings.warn('max_length必须是整数或者None.')
            if isinstance(max_length, int):
                assert 0 < max_length <= 512, 'max_length必须在区间(0, 512],'

        if 'truncation' in kwargs:
            warnings.warn('不接受truncation参数，在text_pair为None情况下默认截断text，否则截断text_pair。')
        if max_length is not None:
            if text_pair is None:
                truncation = 'only_first'
            else:
                truncation = 'only_second'
        else:
            truncation = False

        if 'return_overflowing_tokens' in kwargs:
            if not kwargs.pop('return_overflowing_tokens'):
                warnings.warn('不接受return_overflowing_tokens参数，当max_length不是None时该参数自动设置为True。')
        if max_length is not None:
            return_overflowing_tokens = True
        else:
            return_overflowing_tokens = False

        if return_tensors is not None and (return_word_ids or return_sequence_ids):
            return_word_ids = return_sequence_ids = False
            warnings.warn('当return_tensors参数不是None时，无法返回return_word_ids、return_sequence_ids参数。')
        # # 处理keyword param `apply_ocr`
        # apply_ocr = self.feature_extractor.apply_ocr
        # if 'apply_ocr' in kwargs:
        #     apply_ocr = kwargs.pop('apply_ocr')
        #     assert isinstance(apply_ocr, bool), "the value of keyword param 'apply_ocr' must be instance of 'bool'."
        # # verify input
        # if apply_ocr and (boxes is not None):
        #     raise ValueError(
        #         "You cannot provide bounding boxes "
        #         "if you initialized the feature extractor with apply_ocr set to True."
        #     )
        #
        # if apply_ocr and not ((text is None and text_pair is None) or (text is not None and text_pair is None)):
        #     raise ValueError(
        #         "使用ocr时，需要保证text和text_pair都是None，或者text不是None而text_pair是None."
        #     )
        #
        # # first, apply the feature extractor. Note: feature extractor always adds a batch dimension.
        # features = self.feature_extractor(images=images, return_tensors=return_tensors, apply_ocr=apply_ocr)
        #
        # # second, apply the tokenizer
        # if apply_ocr:
        #     boxes = features['boxes']
        # if apply_ocr and text is None and text_pair is None:
        #     text = features["words"]
        # if apply_ocr and text is not None and text_pair is None:
        #     if isinstance(text, str):
        #         text = [text]  # add batch dimension (as the feature extractor always adds a batch dimension)
        #     text_pair = features["words"]

        is_batched = self.exam(text, text_pair, boxes)

        # 先当做batch处理，然后做后处理。
        batched_text = [text] if not is_batched else text
        batched_text_pair = [None] * len(batched_text) if text_pair is None else ([text_pair] if not is_batched else text_pair)
        batched_boxes = [boxes] if not is_batched else boxes
        batch_outputs = []
        is_fast = self.tokenizer.is_fast
        if not is_fast:
            raise ValueError('暂时仅支持tokenizer fast.')
        # sample text, sample text pair, sample boxes
        for sample_id, (s_text, s_text_pair, s_boxes) in enumerate(zip(batched_text, batched_text_pair, batched_boxes)):

            if s_text_pair is None:
                all_segment_texts = s_text
                all_segment_boxes = s_boxes
            else:
                all_segment_texts = [s_text] + s_text_pair
                all_segment_boxes = [self.tokenizer.pad_token_box] + s_boxes
            seg_outs = []

            for segment, segment_box in zip(all_segment_texts, all_segment_boxes):
                segment = '&' + segment
                encodings = self.tokenizer.encode_plus(text=[segment],
                                                       text_pair=None,
                                                       boxes=[segment_box],
                                                       add_special_tokens=False,
                                                       padding=False,
                                                       truncation=False,
                                                       max_length=None,
                                                       stride=0,
                                                       return_token_type_ids=False,
                                                       return_attention_mask=return_attention_mask,
                                                       return_overflowing_tokens=False,
                                                       return_offsets_mapping=is_fast,
                                                       return_length=False,
                                                       verbose=True,
                                                       return_tensors=None,
                                                       # **kwargs,
                                                       )
                seg_out = dict((k, v[1:]) for k, v in encodings.items())
                # if 'offset_mapping' not in seg_out:
                #     tokens = self.tokenizer.convert_ids_to_tokens(seg_out['input_ids'])

                seg_outs.append(seg_out)

            if s_text_pair is None:
                text_out = {}
                text_pair_out = None
                text_word_ids = sum([[i] * len(seg_out['input_ids']) for i, seg_out in enumerate(seg_outs)], [])
                text_pair_word_ids = None
                for key in seg_outs[0].keys():
                    stack = [seg_out[key] for seg_out in seg_outs]
                    text_out[key] = sum(stack, [])
                    if key == 'offset_mapping':
                        text_out[key] = [(left-1, right-1) for left, right in text_out[key]]
            else:
                text_out = seg_outs[0]
                text_pair_out = {}
                text_word_ids = [0] * len(seg_outs[0]['input_ids'])
                text_pair_word_ids = sum([[i] * len(seg_out['input_ids']) for i, seg_out in enumerate(seg_outs[1:])], [])
                text_out['offset_mapping'] = [(left-1, right-1) for left, right in text_out['offset_mapping']]
                for key in seg_outs[1].keys():
                    stack = [seg_out[key] for seg_out in seg_outs[1:]]
                    text_pair_out[key] = sum(stack, [])
                    if key == 'offset_mapping':
                        text_pair_out[key] = [(left-1, right-1) for left, right in text_pair_out[key]]

            # Backward compatibility for 'truncation_strategy', 'pad_to_max_length'
            padding_strategy, _, max_length, kwargs = self.tokenizer._get_padding_truncation_strategies(
                padding=padding,
                truncation=truncation,
                max_length=max_length,
                pad_to_multiple_of=pad_to_multiple_of,
                verbose=verbose,
                **kwargs,
            )

            doc_spans = []
            start_offset = 0
            all_doc_input_ids = text_out['input_ids'] if text_pair_out is None else text_pair_out['input_ids']
            if max_length is not None:
                max_length_for_doc = max_length - self.tokenizer.num_special_tokens_to_add(
                    pair=text_pair_out is not None) - (0 if text_pair_out is None else len(text_out['input_ids']))
                if max_length_for_doc <= 0:
                    max_length_for_doc = 1
                    warnings.warn('max_length参数过小，如非必要，请适当增大max_length。')

                # hg 定义的stride称为step back比较合适。
                stride_ = max_length_for_doc - stride
                if stride_ <= 0:
                    stride_ = 1
                elif stride_ > max_length_for_doc:
                    stride_ = max_length_for_doc
                while start_offset < len(all_doc_input_ids):
                    length = len(all_doc_input_ids) - start_offset
                    if length > max_length_for_doc:
                        length = max_length_for_doc
                    doc_spans.append({"start": start_offset, "length": length})
                    if start_offset + length == len(all_doc_input_ids):
                        break
                    start_offset += min(length, stride_, max_length_for_doc)
            else:
                doc_spans.append({"start": start_offset, "length": len(all_doc_input_ids)})

            main_out = text_pair_out if text_pair_out is not None else text_out

            main_word_ids = text_pair_word_ids if text_pair_out is not None else text_word_ids
            encodings_sample = []
            for (doc_span_index, doc_span) in enumerate(doc_spans):
                d_doc = {}
                start = doc_span['start']
                end = start + doc_span['length']

                # 处理boxes
                for key in main_out.keys():
                    if key == 'offset_mapping':
                        continue
                    d_doc[key] = main_out[key][start: end]

                d_doc['offset_mapping'] = main_out['offset_mapping'][start: end]
                d_doc['word_ids'] = main_word_ids[start: end]
                d_doc['overflow_to_sample_mapping'] = sample_id
                encodings_sample.append(d_doc)

            if text_pair_out is not None:
                for d in encodings_sample:
                    text_pair_length = len(d['input_ids'])
                    d['input_ids'] = self.tokenizer.build_inputs_with_special_tokens(text_out['input_ids'],
                                                                                     d['input_ids'])
                    real_length = len(d['input_ids'])
                    d['attention_mask'] = [1] * real_length
                    d['bbox'] = [self.tokenizer.cls_token_box] + text_out['bbox'] + \
                                [self.tokenizer.sep_token_box] * 2 + d['bbox'] + [self.tokenizer.sep_token_box]
                    if return_word_ids:
                        d['word_ids'] = [None] + text_word_ids + [None] * 2 + d['word_ids'] + [None]
                    else:
                        d.pop('word_ids')
                    if return_sequence_ids:
                        d['sequence_ids'] = [None] + [0] * len(text_out['input_ids']) + [None] * 2 + [
                            1] * text_pair_length + [None]
                    if return_token_type_ids:
                        d['token_type_ids'] = [0] * real_length
                    if return_special_tokens_mask:
                        d['special_tokens_mask'] = [1] + [0] * len(text_out['input_ids']) + [1, 1] + [
                            0] * text_pair_length + [1]
                    if return_offsets_mapping:
                        d['offset_mapping'] = [(0, 0)] + text_out['offset_mapping'] \
                                              + [(0, 0)] * 2 + d['offset_mapping'] + [(0, 0)]
                    else:
                        d.pop('offset_mapping')
                    if not return_overflowing_tokens:
                        d.pop('overflow_to_sample_mapping')
                    batch_outputs.append(d)
            else:
                for d in encodings_sample:
                    text_length = len(d['input_ids'])
                    d['input_ids'] = self.tokenizer.build_inputs_with_special_tokens(d['input_ids'], None)
                    real_length = len(d['input_ids'])
                    d['attention_mask'] = [1] * real_length
                    d['bbox'] = [self.tokenizer.cls_token_box] + d['bbox'] + [self.tokenizer.sep_token_box]
                    if return_word_ids:
                        d['word_ids'] = [None] + d['word_ids'] + [None]
                    else:
                        d.pop('word_ids')
                    if return_sequence_ids:
                        d['sequence_ids'] = [None] + [0] * text_length + [None] * 2
                    if return_token_type_ids:
                        d['token_type_ids'] = [0] * real_length
                    if return_special_tokens_mask:
                        d['special_tokens_mask'] = [1] + [0] * text_length + [1]
                    if return_offsets_mapping:
                        d['offset_mapping'] = [(0, 0)] + d['offset_mapping'] + [(0, 0)]
                    else:
                        d.pop('offset_mapping')
                    if not return_overflowing_tokens:
                        d.pop('overflow_to_sample_mapping')
                    batch_outputs.append(d)

        batch_outputs = self.tokenizer.pad(batch_outputs,
                                           padding=padding_strategy.value,
                                           max_length=max_length,
                                           pad_to_multiple_of=pad_to_multiple_of,
                                           return_attention_mask=return_attention_mask, )
        if return_length:
            batch_outputs['length'] = [len(ids) for ids in batch_outputs['input_ids']]
        # 剔除冗余的0维。
        if return_tensors is None and not return_overflowing_tokens:
            batch_outputs = {key: value[0] if len(value) > 0 and isinstance(value[0], list) else value
                               for key, value in batch_outputs.items()}
        return BatchEncoding(batch_outputs, tensor_type=return_tensors)

    def exam(self, text, text_pair, boxes):
        """
        copy from ernie layout tokenizer.
        Args:
            text:
            text_pair:
            boxes:

        Returns:

        """
        def _is_valid_text_input(t):
            if isinstance(t, str):
                # Strings are fine
                return True
            elif isinstance(t, (list, tuple)):
                # List are fine as long as they are...
                if len(t) == 0:
                    # ... empty
                    return True
                elif isinstance(t[0], str):
                    # ... list of strings
                    return True
                elif isinstance(t[0], (list, tuple)):
                    # ... list with an empty list or with a list of strings
                    return len(t[0]) == 0 or isinstance(t[0][0], str)
                else:
                    return False
            else:
                return False

        if text_pair is not None:
            # in case text + text_pair are provided, text = questions, text_pair = words
            if not _is_valid_text_input(text):
                raise ValueError("text input must of type `str` (single example) or `List[str]` (batch of examples). ")
            if not isinstance(text_pair, (list, tuple)):
                raise ValueError(
                    "words must of type `List[str]` (single pretokenized example), "
                    "or `List[List[str]]` (batch of pretokenized examples)."
                )
        else:
            # in case only text is provided => must be words
            if not isinstance(text, (list, tuple)):
                raise ValueError(
                    "Words must of type `List[str]` (single pretokenized example), "
                    "or `List[List[str]]` (batch of pretokenized examples)."
                )

        if text_pair is not None:
            is_batched = isinstance(text, (list, tuple))
        else:
            is_batched = isinstance(text, (list, tuple)) and text and isinstance(text[0], (list, tuple))

        words = text if text_pair is None else text_pair
        if boxes is None:
            raise ValueError("You must provide corresponding bounding boxes")
        if is_batched:
            if len(words) != len(boxes):
                raise ValueError("You must provide words and boxes for an equal amount of examples")
            for words_example, boxes_example in zip(words, boxes):
                if len(words_example) != len(boxes_example):
                    raise ValueError("You must provide as many words as there are bounding boxes")
        else:
            if len(words) != len(boxes):
                raise ValueError("You must provide as many words as there are bounding boxes")

        if is_batched:
            if text_pair is not None and len(text) != len(text_pair):
                raise ValueError(
                    f"batch length of `text`: {len(text)} does not match batch length of `text_pair`:"
                    f" {len(text_pair)}."
                )
        return is_batched
    # def __call__(
    #     self,
    #     images,
    #     text: Union[TextInput, PreTokenizedInput, List[TextInput], List[PreTokenizedInput]] = None,
    #     text_pair: Optional[Union[PreTokenizedInput, List[PreTokenizedInput]]] = None,
    #     boxes: Union[List[List[int]], List[List[List[int]]]] = None,
    #     word_labels: Optional[Union[List[int], List[List[int]]]] = None,
    #     add_special_tokens: bool = True,
    #     padding: Union[bool, str, PaddingStrategy] = False,
    #     truncation: Union[bool, str, TruncationStrategy] = False,
    #     max_length: Optional[int] = None,
    #     stride: int = 0,
    #     pad_to_multiple_of: Optional[int] = None,
    #     return_token_type_ids: Optional[bool] = None,
    #     return_attention_mask: Optional[bool] = None,
    #     return_overflowing_tokens: bool = False,
    #     return_special_tokens_mask: bool = False,
    #     return_offsets_mapping: bool = False,
    #     return_length: bool = False,
    #     verbose: bool = True,
    #     return_tensors: Optional[Union[str, TensorType]] = None,
    #     **kwargs
    # ) -> BatchEncoding:
    #     """
    #     This method first forwards the `images` argument to [`~ErnieLayoutFeatureExtractor.__call__`]. In case
    #     [`ErnieLayoutFeatureExtractor`] was initialized with `apply_ocr` set to `True` or added key word param
    #     `apply_ocr` is `True`, it passes the obtained words and bounding boxes along with the additional arguments
    #     to [`~LayoutXLMTokenizer.__call__`] and returns the output, together with resized `images`.
    #     In case [`ErnieLayoutFeatureExtractor`] was initialized with `apply_ocr` set to `False` or added key word param
    #     `apply_ocr` is `False`, it passes the words
    #      (`text`/``text_pair`) and `boxes` specified by the user along with the additional
    #     arguments to [`~LayoutXLMTokenizer.__call__`] and returns the output, together with resized `images``.
    #     Note: the priority of keyword param `apply_ocr` is higher than `apply_ocr` attr of `ErnieLayoutFeatureExtractor`
    #     ,but would not revise the value of `apply_ocr` attr.
    #     """
    #     # 处理keyword param `apply_ocr`
    #     apply_ocr = self.feature_extractor.apply_ocr
    #     if 'apply_ocr' in kwargs:
    #         apply_ocr = kwargs.pop('apply_ocr')
    #         assert isinstance(apply_ocr, bool), "the value of keyword param 'apply_ocr' must be instance of 'bool'."
    #     # verify input
    #     if apply_ocr and (boxes is not None):
    #         raise ValueError(
    #             "You cannot provide bounding boxes "
    #             "if you initialized the feature extractor with apply_ocr set to True."
    #         )
    #
    #     if apply_ocr and (word_labels is not None):
    #         raise ValueError(
    #             "You cannot provide word labels if you initialized the feature extractor with apply_ocr set to True."
    #         )
    #
    #     # first, apply the feature extractor
    #     features = self.feature_extractor(images=images, return_tensors=return_tensors, apply_ocr=apply_ocr)
    #
    #     # second, apply the tokenizer
    #     if text is not None and apply_ocr and text_pair is None:
    #         if isinstance(text, str):
    #             text = [text]  # add batch dimension (as the feature extractor always adds a batch dimension)
    #         text_pair = features["words"]
    #
    #     encoded_inputs = self.tokenizer(
    #         text=text if text is not None else features["words"],
    #         text_pair=text_pair if text_pair is not None else None,
    #         boxes=boxes if boxes is not None else features["boxes"],
    #         word_labels=word_labels,
    #         add_special_tokens=add_special_tokens,
    #         padding=padding,
    #         truncation=truncation,
    #         max_length=max_length,
    #         stride=stride,
    #         pad_to_multiple_of=pad_to_multiple_of,
    #         return_token_type_ids=return_token_type_ids,
    #         return_attention_mask=return_attention_mask,
    #         return_overflowing_tokens=return_overflowing_tokens,
    #         return_special_tokens_mask=return_special_tokens_mask,
    #         return_offsets_mapping=return_offsets_mapping,
    #         return_length=return_length,
    #         verbose=verbose,
    #         return_tensors=return_tensors,
    #         **kwargs,
    #     )
    #
    #     # add pixel values
    #     images = features.pop("pixel_values")
    #     if return_overflowing_tokens is True:
    #         images = self.get_overflowing_images(images, encoded_inputs["overflow_to_sample_mapping"])
    #     encoded_inputs["image"] = images
    #
    #     return encoded_inputs

    def get_overflowing_images(self, images, overflow_to_sample_mapping):
        # in case there's an overflow, ensure each `input_ids` sample is mapped to its corresponding image
        images_with_overflow = []
        for sample_idx in overflow_to_sample_mapping:
            images_with_overflow.append(images[sample_idx])

        if len(images_with_overflow) != len(overflow_to_sample_mapping):
            raise ValueError(
                "Expected length of images to be the same as the length of `overflow_to_sample_mapping`, but got"
                f" {len(images_with_overflow)} and {len(overflow_to_sample_mapping)}"
            )

        return images_with_overflow

    def batch_decode(self, *args, **kwargs):
        """
        This method forwards all its arguments to PreTrainedTokenizer's [`~PreTrainedTokenizer.batch_decode`]. Please
        refer to the docstring of this method for more information.
        """
        return self.tokenizer.batch_decode(*args, **kwargs)

    def decode(self, *args, **kwargs):
        """
        This method forwards all its arguments to PreTrainedTokenizer's [`~PreTrainedTokenizer.decode`]. Please refer
        to the docstring of this method for more information.
        """
        return self.tokenizer.decode(*args, **kwargs)


if __name__ == '__main__':
    pass
    from PIL import Image
    img = Image.open(r'C:\Users\<USER>\Desktop\不动产登记申请审批表\img\201911281030920.jpg').convert('RGB')
    processor = ErnieLayoutProcessor(ErnieLayoutFeatureExtractor.from_pretrained('microsoft/layoutxlm-base',
                                                                                 apply_ocr=False),
                                     ErnieLayoutTokenizerFast.from_pretrained('microsoft/layoutxlm-base'))
    print(processor(images=img,
                    text=['请问您'],
                    boxes=[[1, 1, 1, 1]],
                    max_length=512,
                    padding=True,
                    # return_offsets_mapping=True,
                    # return_token_type_ids=True,
                    return_length=True,
                    # return_special_tokens_mask=True,
                    # return_word_ids=True,
                    # return_sequence_ids=True,
                    # return_tensors='pt',
                    apply_ocr=True,
                    ))
    # from uiex_p import Taskflow
    # docprompt = Taskflow('document_intelligence', device_id=-1, topn=10, batch_size=2)
    # print(docprompt([{"doc": r"C:\Users\<USER>\Desktop\不动产登记申请审批表\img\202012191030092.jpg", "prompt": ['通讯地址']}]))
