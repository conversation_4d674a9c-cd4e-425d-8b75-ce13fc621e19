# Copyright (c) 2022 PaddlePaddle Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import argparse
import time
import sys
import os

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))
# print(sys.path)
import shutil
from functools import partial
import jsonlines
import re
from itertools import accumulate
from copy import copy
from typing import List, Dict
import random
import numpy as np
import torch
from torch.utils.data import DataLoader, Dataset
from torch.optim import Adam<PERSON>
from torch.nn.parallel import DistributedDataParallel
from transformers import AutoTokenizer
from PIL import Image

from kbnlp.scripts.information_extraction.metric import SpanEvaluator
from kbnlp.scripts.information_extraction.my_convert import AIDPConvertorForUIEX, AIDPDrawBoxConvertorForUIEX
from kbnlp.taskflow.models.information_extraction.information_extraction_model import UIE
from kbnlp.pretrained.ernie.configuration_ernie import ErnieConfig
from kbnlp.scripts.information_extraction.evaluate import evaluate
from kbnlp.scripts.information_extraction.utils import isInside, box_coverage_area
from kbnlp.scripts.information_extraction.utils import dbc2sbc


torch._C._jit_set_profiling_mode(False)  # 需要在最开始处执行，解决torch1.9~1.12script模型可能执行不正常的bug
# 目前将所有的文本类别拼接到一起。
p = re.compile('\[[.+,]*.+\]')


def print_(info, file_path=None):
    if file_path:
        _file = os.path.join(file_path, 'log.txt')
        if not os.path.exists(file_path):
            os.makedirs(file_path)
        with open(_file, "a+", encoding='utf-8') as fw:
            fw.writelines(str(info) + '\n')
        print(info)
    else:
        print(info)


def _callback(*args, **kwargs):
    return


def _setup_post_callback():
    if os.environ.get('SLURM_PROCID', '0') != '0':
        return
    job_id = int(os.environ.get('SLURM_JOB_ID', '-1'))
    import requests
    url = os.environ.get('CALLBACK_URL', '')
    global _callback
    if not url:
        return

    def callback(*args, **kwargs):
        res = requests.post(url, json={'msg': args, 'detail': kwargs, 'JobId': job_id}, timeout=10)
        if res.ok:
            j = res.json()  # ; print(j)
        else:
            print('NOTE callback code={} error={}'.format(res.status_code, res.text))
        return

    _callback = callback


def seed_everything(seed):
    torch.manual_seed(seed)       # Current CPU
    torch.cuda.manual_seed(seed)  # Current GPU
    np.random.seed(seed)          # Numpy module
    random.seed(seed)             # Python random module
    # torch.backends.cudnn.benchmark = False    # Close optimization
    # torch.backends.cudnn.deterministic = True  # Close optimization
    torch.cuda.manual_seed_all(seed)  # All GPU (Optional)


# TODO:1,关系的提示符转化为id；2，数据的特殊字符的转换。3,BCELoss。
def do_train(args):
    print = partial(print_, file_path=args.save_dir)
    _setup_post_callback()
    _callback(step='starting program', workdir=args.save_dir, checkpoint='checkpoint.pth')
    # accelerator = Accelerator(cpu=True if args.device == 'cpu' else False)
    # accelerator.print(args)
    print(args.__dict__)
    # seed_everything(args.seed)
    # device = accelerator.device
    device = args.device if torch.cuda.is_available() else 'cpu'
    print(f'device:{device}')
    # print('process id: %d, current device: %s' % (accelerator.process_index, device))
    _callback(step='loading dataset.')
    options = []
    if args.task_type == 'document_record':
        options = ['否', '是']
    prompt_prefix = '文档包含著录项'
    # 这里的transform仅仅是用来获取训练集中options和著录项的，属于历史遗留问题。
    if args.data_source == 'aidp3.0':
        def transform(line):
            """
                    将aidp导出的数据处理成文档信息抽取脚本（uie、uiex）能处理的doccano格式。如果读取数据已经是文档信息抽取可以处理的doccano格式
                数据,则把数据原样返回；如果该doccano格式数据中没有cls_label,则cls_label被创建为空列表 。

                # 文档标注导出数据格式
                数据导出成jsonlines格式，每行一个样本，图片放在jsonl文件的同级目录下的images/file_name文件夹下。在jsonl文件中，每个样本是一个字典，包含字段：
                'word': List[str], ocr得到的文字列表。
                'bbox': List[List[int, int, int, int]], ocr得到的所有文本框的bbox列表。
                'image': str, 记录jsonl文件的同级images目录到该图片文件的路径信息）,如，某张图片名为'pig1.jgp',其放在images/pig目录下，该本字段记录为'images/pig/pig1.jpg'。
                'entities': 如下示例，id代表该图片中标注的实体id,从0开始，依次递增，label代表实体标签（著录项），fragment记录用户对该著录项的标注结果，
                            该示例中fragment列表中的第一个著录项'住址著录项'对应的标注步骤如下：
                                1，用户首先在box_id为0的文本框中选中‘上海市’，’上海市‘在该文本框中的start、end值为(2, 5);
                                2，然后在该文本框中选中'SOHO东海广场',其在文本框中的start、end值为(8, 16)；
                                3，最后在box_id为3的文本框中选中了'2208室'，其在3号文本框中的start、end值为(7, 12)。
                            '公司名称著录项'著录像类似。
                            例子：
                            [{'fragment': [{'box_id': 0, 'mention': ['上海市', 'SOHO东海广场'], 'offset_map': [(2, 5), (8, 16)]},
                                           {'box_id': 3, 'mention': ['2208室'], 'offset_map': [(7, 12)]}],
                              'id': 0,
                              'label': '住址著录项'},
                             {'fragment': [{'box_id': 5, 'mention': ['北京酷豹（上海数豹）科技'], 'offset_map': [(3, 15)]},
                                           {'box_id': 6, 'mention': ['有限公司'], 'offset_map': [(0, 4)]}],
                              'id': 1,
                              'label': '公司名称著录项'},
                             {'fragment': [{'box_id': 21, 'mention': ['上海市南京西路'], 'offset_map': [(11, 18)]}],
                              'id': 8,
                              'label': '住址著录项'}]
                            注意：如果一个实体类型（著录项）对应不不止一个实体实例（著录结果），如'住址著录项'包括’上海市SOHO东海广场‘、’上海市南京西路‘，
                            则他们需要要在entities中分别占据一项记录。
                relations: 一个空列表（不是None）。目前不做关系抽取。
                #按照目前设计逻辑，从AIDP导出的数据需要做文本图片做分类。所以task_type参数无效。

                文档信息抽取模型所需doccano格式数据：jsonlines文件，每行是一个样本，每个样本都是一个字典。有text、word、bbox、image、entities、
                relations字段。image和aidp格式数据一致。

            """
            example = copy(line)
            if 'text' not in line and 'word' in line:
                example['text'] = ''.join(line['word'])
            if not line['entities'] and not line['relations']:
                if 'cls_label' not in example:
                    example['cls_label'] = ['否']
                return example

            entities = []
            relations = []
            entity_id = 0
            relation_id = 0
            word_lens = [0] + list(accumulate(map(len, line['word'])))
            for entity in line['entities']:
                if 'fragment' not in entity:  # 直接加载doccano格式数据
                    entities.append(entity)
                else:
                    frags = entity['fragment']
                    label = entity['label']
                    whole_entity = []
                    for frag in frags:
                        box_id = frag['box_id']
                        frag_entities = []  # 一个fragment中所有的实体片段。
                        for mention, offset in zip(frag['mention'], frag['offset_map']):
                            start_offset = word_lens[box_id] + offset[0]
                            end_offset = word_lens[box_id] + offset[1]
                            frag_entities.append({'id': entity_id,
                                                  'label': label,
                                                  'start_offset': start_offset,
                                                  'end_offset': end_offset})
                            entity_id += 1
                        whole_entity.extend(frag_entities)
                    entities.extend(whole_entity)
                    # 暂时不支持不连续实体功能。
                    # if len(whole_entity) >= 2:
                    #     for e_i in range(len(whole_entity) - 1):
                    #         from_id = whole_entity[e_i]['id']
                    #         to_id = whole_entity[e_i + 1]['id']
                    #         relations.append({'id': relation_id,
                    #                           'from_id': from_id,
                    #                           'to_id': to_id,
                    #                           'type': '后续'})
                    #         relation_id += 1
            # 将原本有的关系保留（之后处理不连续实体，或者相邻文本框被分开的实体，处理已有的关系需要重新考虑。）
            for rel in line['relations']:
                relations.append(rel)
            example['entities'] = entities
            example['relations'] = relations  # 之后标注平台支持关系标注后，则需要处理已有的relations。
            # example = deal_next_relation2([example])[0]  # 该方法有待检查
            # 利用图片中包含的著录项做图片标签。（经验证，直接这么使用不太可行。）
            # example['cls_label'] = ['|'.join(sorted(set([e['label'] for e in example['entities']])))] \
            #     if example['entities'] else []
            example['cls_label'] = ['是' if example['entities'] else '否']
            return example
    else:
        from zhu_ocr import OCRSystem
        doc_parser = OCRSystem(det_model_path='dtm/001.pt',
                               cls_model_path='dtm/003.pt', cls_vertical_model_path='dtm/006.pt',
                               rec_model_path='dtm/tp1t7.pt',
                               rec_char_dict_path='dtm/ppocr_keys_v1.txt')

        def transform(line):
            """
                # AIDP3.1拉框版文档标注导出数据格式
                数据导出成jsonlines格式，每行一个样本，记录一张图片的信息。对应的真实图片放在jsonl文件的同级目录下的images/file_name文件夹下。在jsonl文件中，每个样本是一个字典，包含字段：
                    'fileBookName':  # 如，同北京设计一致，需要确保同一案卷同一案件内的所有图片的该字段值相同。
                    'volume_id': # 案卷id，用以区分整批数据中的不同案卷。
                    'file_id': # 案件id，用以区分同一案卷中的不同案件
                    'word': List[str], ocr得到的文字列表。
                    'bbox': List[List[int, int, int, int]], ocr得到的所有文本框的bbox列表。
                    'image': str, 记录jsonl文件的同级images目录到该图片文件的路径信息）,如，本条样本对应的图片名为'pig1.jgp',其放在images/pig目录下，则本字段记录为'images/pig/pig1.jpg'。
                    'entities': 如下示例，
                                id代表该图片中标注的实体id,从0开始，依次递增。
                                label代表实体标签（著录项）。
                                fragment字段记录了该著录项的值，该值是一个列表，列表中每个元素都是一个字典。
                                    字典中bbox表示用户拉框的坐标（长为4的整数列表，分别代表左上角x坐标、左上角y坐标、右下角x坐标、右下角y坐标）；
                                    mention表示拉框ocr的结果。列表中元素的顺序和用户拉框顺序一致。
                                例子：
                                 [{'fragment': [{'bbox': [1200, 23, 2450, 67], 'mention': '上海市SOHO东'},
                                               {'bbox': [90, 80, 358, 159], 'mention': '海广场'}],
                                   'id': 0,
                                   'record_type': 'volume',
                                   'label': '住址著录项'},
                                  {'fragment': [{'bbox': [600, 63, 1040, 120], 'mention': '北京酷豹（上海数豹）科技有限公司'}],
                                   'id': 1,
                                   'record_type': 'file',
                                   'label': '公司名称著录项'}]
                    relations: 一个空列表（不是None）。目前不做关系抽取。

            """
            example = copy(line)
            path = os.path.join(args.train_image_base_path,
                                *re.split(r'\\|/', example['image']))
            try:
                img = Image.open(path).convert("RGB")
            except Exception as e:
                print(f'{path}文件无法正常读取。')
                example['image_width'] = 1010
                example['image_height'] = 1010
                example['word'] = ['']
                example['text'] = ''
                example['bbox'] = [[0, 0, 0, 0]]
                example['entities'] = []
                example['relations'] = []
                example['cls_label'] = ['否']
            else:
                example['image_width'] = img.width
                example['image_height'] = img.height

                example['text'] = ''.join(line['word'])
                if not line['entities'] and not line['relations']:
                    if 'cls_label' not in example:
                        example['cls_label'] = ['否']
                    return example

                entities = []
                relations = []
                entity_id = 0
                relation_id = 0
                if len(line['entities']) > 0:
                    ocr_res = doc_parser.ocr(img)[0]
                    ocr_res = [[[min(box[0][0], box[2][0]), min(box[0][1], box[2][1]),
                                 max(box[0][0], box[2][0]), max(box[0][1], box[2][1])], (t_s_c[0], t_s_c[1], t_s_c[2])]
                               for box, t_s_c in ocr_res if t_s_c[0].strip()]  # 这里只将strip后是空字符串的丢掉。strip后不是空的吧未strip的原字符串返回。

                    # 如果需要重新对bbox排序，需要在此处理ocr_res,而不是直接对下面的bbox，word排序。

                    word = []
                    bbox = []
                    for box, t_s_c in ocr_res:
                        bbox.append(box)
                        word.append(t_s_c[0])
                    example['word'] = word
                    example['text'] = ''.join(word)
                    example['bbox'] = bbox
                    word_lens = [0] + list(accumulate(map(len, example['word'])))
                    for entity in line['entities']:
                        label = entity['label']
                        record_type = entity['record_type']  # volume, file
                        entities_frag = []
                        for frag in entity['fragment']:
                            draw_box = frag['bbox']
                            mention = frag['mention']
                            for box_id, box_res in enumerate(ocr_res):
                                box, t_s_c = box_res
                                draw_box = [min(draw_box[0], draw_box[2]), min(draw_box[1], draw_box[3]),
                                            max(draw_box[0], draw_box[2]), max(draw_box[1], draw_box[3])]
                                text, score, char_coords = t_s_c
                                if box_coverage_area(box, draw_box) > 0:
                                    last_inside = False
                                    start_offset = -1
                                    end_offset = -1
                                    for i, coord in enumerate(char_coords):
                                        cur_inside = isInside(draw_box[0], draw_box[1], draw_box[2], draw_box[1],
                                                              draw_box[2], draw_box[3], draw_box[0], draw_box[3],
                                                              coord[0], coord[1])
                                        if cur_inside:
                                            if not last_inside:
                                                start_offset = i
                                        else:
                                            if last_inside:
                                                end_offset = i
                                                entities_frag.append({'id': entity_id,
                                                                      'box_id': box_id,
                                                                      'label': label,
                                                                      'start_offset': start_offset + word_lens[box_id],
                                                                      'end_offset': end_offset + word_lens[box_id]})
                                                entity_id += 1
                                                start_offset = -1
                                                end_offset = -1
                                        last_inside = cur_inside
                                    if start_offset != -1 and end_offset == -1:
                                        entities_frag.append({'id': entity_id,
                                                              'box_id': box_id,
                                                              'label': label,
                                                              'start_offset': start_offset + word_lens[box_id],
                                                              'end_offset': len(char_coords) + word_lens[box_id]})
                                        entity_id += 1
                        if entities_frag:
                            entities.append(entities_frag)
                    entities_ = []
                    for entities_frag in entities:
                        if entities_frag:
                            entities_frag_ = [entities_frag[0]]
                            for e in entities_frag[1:]:
                                last_e = entities_frag_[-1]
                                if e['start_offset'] == last_e['end_offset']:
                                    last_e['end_offset'] = e['end_offset']
                                    last_e['box_id'] = e['box_id']
                                else:
                                    entities_frag_.append(e)
                            entities_.extend(entities_frag_)
                    for e in entities_:
                        e.pop('box_id')
                    entities = entities_
                # 将原本有的关系保留（之后处理不连续实体，或者相邻文本框被分开的实体，处理已有的关系需要重新考虑。）
                for rel in line['relations']:
                    relations.append(rel)
                example['entities'] = entities
                example['relations'] = relations  # 之后标注平台支持关系标注后，则需要处理已有的relations。
                if 'cls_label' not in example:
                    example['cls_label'] = ['是' if example['entities'] else '否']
            return example

    if args.data_source == 'aidp3.0':
        convertor_class = AIDPConvertorForUIEX
    else:
        convertor_class = AIDPDrawBoxConvertorForUIEX

    if args.train_path_processed:
        print("从'train_path_processed'中加载数据， 'fewshot','train_top','negative_ratio'参数无效。")
        with jsonlines.open(args.train_path_processed, mode='r') as jr:
            train_dataset = []
            for d in jr:  # 兼容以前的数据，以后统一用text。
                if 'content' in d:
                    d['text'] = d.pop('content')
                if not options:
                    match = re.search(p, d['prompt'])
                    if match is not None:
                        options = match.group()[1:-1].split(',')
                train_dataset.append(d)
            if args.shuffle:
                ids = np.random.permutation(len(train_dataset))
                train_dataset = [train_dataset[i] for i in ids]
        options = sorted(list(set(options)))
        classes2document_record_schema = {None: []}  # TODO
        for data in train_dataset:
            if re.search(p, data['prompt']) is None:
                classes2document_record_schema[None].append(data['prompt'])
        print('对于已经处理成提示类型的数据，暂时不支持自动搜索文档类别到特定著录项的映射。')

    elif args.train_path:
        train_dataset = []
        classes2document_record_schema = {None: []}
        with jsonlines.open(args.train_path) as jr:
            for data in jr:
                data = transform(data)
                train_dataset.append(data)
                if 'cls_label' in data:
                    for cls_l in data['cls_label']:
                        if cls_l not in options:
                            options.append(cls_l)
                        if cls_l not in classes2document_record_schema:
                            classes2document_record_schema[cls_l] = []
                        for entity in data['entities']:
                            e_l = entity['label']
                            if e_l not in classes2document_record_schema[None]:
                                classes2document_record_schema[None].append(e_l)
                            if e_l not in classes2document_record_schema[cls_l]:
                                classes2document_record_schema[cls_l].append(e_l)
                else:
                    for entity in data['entities']:
                        e_l = entity['label']
                        if e_l not in classes2document_record_schema[None]:
                            classes2document_record_schema[None].append(e_l)
        options = sorted(list(set(options)))
        train_convertor = convertor_class(file_path=args.train_path,
                                          image_base_path=args.train_image_base_path,
                                          splits=[1., 0, 0],
                                          seed=None,
                                          is_shuffle=False,
                                          anno_type='text',
                                          separator=args.separator,
                                          negative_ratio=args.negative_ratio,
                                          task_type=args.task_type,
                                          options=options,
                                          prompt_prefix=prompt_prefix,
                                          verbose=False,
                                          )
        train_dataset, _, _ = train_convertor.convert()
        # train_dataset, _, _ = convert_example_to_prompt_format(datas=train_dataset,
        #                                                        transform=None,
        #                                                        fewshot=args.fewshot,
        #                                                        top=args.train_top,
        #                                                        negative_ratio=args.negative_ratio,
        #                                                        splits=[1., 0., 0.],
        #                                                        task_type=args.task_type,
        #                                                        options=options,
        #                                                        prompt_prefix=prompt_prefix,
        #                                                        shuffle=args.shuffle,
        #                                                        seed=args.seed,
        #                                                        separator=args.separator,
        #                                                        )
    else:
        raise ValueError("必须提供'train_path_processed'和'train_path'中的一个。")

    assert len(train_dataset) > 0, '训练数据为空。'
    print('prompt_prefix:' + prompt_prefix)
    if options:
        print('document_classes_schema:' + '[' + ','.join(options) + ']')
    else:
        print('数据中没有提供文档类别信息。')
    print('classes2document_record_schema:' + str(classes2document_record_schema))

    if args.dev_path_processed:
        print("从'dev_path_processed'中加载数据， 'negative_ratio','dev_top'参数无效。")
        with jsonlines.open(args.dev_path_processed, mode='r') as jr:
            dev_dataset = []
            for d in jr:
                if 'content' in d:
                    d['text'] = d.pop('content')
                dev_dataset.append(d)
        assert len(dev_dataset) > 0, '提供的评估数据为空。'
    elif args.dev_path:
        dev_convertor = convertor_class(file_path=args.dev_path,
                                        image_base_path=args.dev_image_base_path,
                                        splits=[0, 1.0, 0],
                                        seed=None,
                                        is_shuffle=False,
                                        anno_type='text',
                                        separator=args.separator,
                                        negative_ratio=args.negative_ratio,
                                        task_type=args.task_type,
                                        options=options,
                                        prompt_prefix=prompt_prefix,
                                        verbose=False,
                                        )
        _, dev_dataset, _ = dev_convertor.convert()
        # _, dev_dataset, _ = convert_example_to_prompt_format(
        #     args.dev_path,
        #     transform=transform,
        #     top=args.dev_top,
        #     negative_ratio=args.negative_ratio,
        #     splits=[0., 1.0, 0.],
        #     task_type=args.task_type,
        #     options=options,
        #     prompt_prefix=prompt_prefix,
        #     shuffle=True,
        #     seed=args.seed,
        #     separator=args.separator,
        # )
        assert len(dev_dataset) > 0, '提供的评估数据为空。'
    else:
        dev_dataset = []
        print('no eval dataset.')
    _callback(step='loading tokenizer and model.')
    tokenizer = AutoTokenizer.from_pretrained(args.tokenizer if args.tokenizer else args.uie_model_path)
    assert not (args.uie_model_path and args.checkpoint), "不能同时从uie模型、checkpoint中初始化模型。"
    if args.uie_model_path:
        model = UIE.from_pretrained(args.uie_model_path).to(device)
    elif args.checkpoint:
        model = UIE.from_pretrained(args.checkpoint).to(device)
    else:
        # accelerator.print("Without 'uie_model_path' and 'checkpoint', random init the AutoPromptUIE model params. This is not recommended.")
        print("Without 'uie_model_path' and 'checkpoint', random init the AutoPromptUIE model params. "
              "This is not recommended.")
        model = UIE(ErnieConfig()).to(device)

    # accelerator.print('train_dataset: ', len(train_dataset))
    # accelerator.print('dev_dataset: ', len(dev_dataset))
    print('train_dataset: ' + str(len(train_dataset)))
    print('dev_dataset: ' + str(len(dev_dataset)))

    def normalize(data):
        data['text'] = dbc2sbc(data['text'])
        return data

    if args.normal:
        train_dataset = [normalize(d) for d in train_dataset]
        dev_dataset = [normalize(d) for d in dev_dataset]

    class UIEDataset(Dataset):

        def __init__(self, data: List[Dict]):
            super().__init__()
            self.data = data

        def __getitem__(self, index):
            return self.data[index]

        def __len__(self):
            return len(self.data)

    train_dataset = UIEDataset(train_dataset)
    train_loader = DataLoader(train_dataset, batch_size=args.batch_size,
                              collate_fn=partial(model.collate, tokenizer=tokenizer, max_seq_len=args.max_seq_len))

    dev_dataset = UIEDataset(dev_dataset)
    dev_loader = DataLoader(dev_dataset, batch_size=args.batch_size,
                            collate_fn=partial(model.collate, tokenizer=tokenizer, max_seq_len=args.max_seq_len))
    optimizer = AdamW(params=model.parameters(), lr=args.learning_rate, weight_decay=args.weight_decay)
    # model, optimizer, train_loader = accelerator.prepare(model, optimizer, train_loader)
    criterion = torch.nn.BCELoss()
    metric = SpanEvaluator()

    checkpoint_name_list = []
    global_step = 0
    best_epoch = 0
    best_f1 = 0
    best_precision, best_recall = 0, 0
    start_time = time.time()
    model.train()
    _callback(step='start training',
              device=args.device, cuda_available=str(torch.cuda.is_available()),
              start_epoch=0,
              epochs=args.num_epochs)
    for epoch in range(1, args.num_epochs + 1):
        _callback(step='begin epoch', epoch=epoch)
        loss_list = []
        time_temp = time.time()
        for batch in train_loader:
            optimizer.zero_grad()
            batch = dict((k, v.to(device) if isinstance(v, torch.Tensor) else v) for k, v in batch.items())
            start_ids, end_ids = batch.pop('start_ids'), batch.pop('end_ids')
            start_prob, end_prob = model(**batch)[:2]
            loss_start = criterion(start_prob, start_ids)
            loss_end = criterion(end_prob, end_ids)
            loss = (loss_start + loss_end) / 2.0
            loss.backward()
            # accelerator.backward(loss)
            optimizer.step()

            loss_list.append(float(loss))
            global_step += 1
            if global_step % args.logging_steps == 0:
                time_diff = time.time() - time_temp
                # TODO: 不要sum，直接累加
                loss_avg = sum(loss_list) / len(loss_list)
                # accelerator.print(
                #     "global step %d, epoch: %d, loss: %.5f, speed: %.2f step/s" % (global_step, epoch, loss_avg,
                #                                                                    args.logging_steps / time_diff))
                print(
                    "global step %d, epoch: %d, loss: %.5f, speed: %.2f step/s" % (global_step, epoch, loss_avg,
                                                                                   args.logging_steps / time_diff))
                time_temp = time.time()

        # if accelerator.is_main_process and args.save_checkpoint and len(dev_dataset) > 0:
        if len(dev_dataset) > 0:
            checkpoint_name = "model_%d" % global_step
            checkpoint_name_list.append(checkpoint_name)
            if len(checkpoint_name_list) > args.max_checkpoint_num:
                checkpoint_to_delete = checkpoint_name_list.pop(0)
                delete(args.save_dir, checkpoint_to_delete)
            if args.save_checkpoint:
                save(args.save_dir, checkpoint_name, model, tokenizer)
            precision, recall, f1 = evaluate(model, metric, dev_loader, device=device)
            # accelerator.print("Evaluation precision: %.5f, recall: %.5f, F1: %.5f" % (precision, recall, f1))
            print("Evaluation precision: %.5f, recall: %.5f, F1: %.5f" % (precision, recall, f1))
            if f1 > best_f1:
                # accelerator.print(f"********** best F1 performence has been updated: {best_f1:.5f} --> {f1:.5f}")
                print(f"********** best F1 performence has been updated: {best_f1:.5f} --> {f1:.5f}")
                best_epoch, best_precision, best_recall, best_f1 = epoch, precision, recall, f1
                save(args.save_dir, 'model_best', model, tokenizer)
        _val = _callback(step='end epoch', epoch=epoch)
        if _val:
            print('Issued exit by callback reply={}'.format(_val))
            break
    # if accelerator.is_local_main_process:
    #     save(args.save_dir, checkpoint_name='model', model=model, tokenizer=tokenizer)
    save(args.save_dir, checkpoint_name='model', model=model, tokenizer=tokenizer)

    # accelerator.print("Best F1: %.5f, the epoch is %d, precision is %.5f, recall is %.5f."
    #                   % (best_f1, best_epoch, best_precision, best_recall))
    # accelerator.print('Finish. Train eval time cost: %.1fs.' % (time.time() - start_time))
    print("Best F1: %.5f, the epoch is %d, precision is %.5f, recall is %.5f."
          % (best_f1, best_epoch, best_precision, best_recall))
    print('Finish. Train eval time cost: %.1fs.' % (time.time() - start_time))
    _callback(step='finish training', artifact='model_best')


def delete(save_dir, checkpoint_name):
    dir_to_delete = os.path.join(save_dir, checkpoint_name)
    if os.path.exists(dir_to_delete):
        shutil.rmtree(dir_to_delete)


def save(save_dir, checkpoint_name='model', model=None, tokenizer=None):
    save_dir = os.path.join(save_dir, checkpoint_name)
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)
    model_to_save = model
    if isinstance(model, DistributedDataParallel):
        model_to_save = model.module
    model_to_save.save_pretrained(save_dir)
    tokenizer.save_pretrained(save_dir)


class Kv:
    def __init__(self, **kwargs):
        for k, v in kwargs.items():
            if not hasattr(self, k):
                setattr(self, k, v)


def run(data_source='aidp3.0', train_image_base_path=None, dev_image_base_path=None, train_path=None,
        train_path_processed=None, train_top=0, fewshot=0, dev_path=None,
        dev_path_processed=None, dev_top=0, negative_ratio=3, task_type='document_record',
        separator='##', save_dir=None, num_epochs=100, batch_size=8, max_seq_len=512,
        learning_rate=1e-5, weight_decay=0., device='cpu', logging_steps=10, uie_model_path=None,
        tokenizer=None, checkpoint=None, save_checkpoint=None, max_checkpoint_num=1, shuffle=False, seed=0,
        normal=False, doccano=False, **kwargs):
    kwargs_ = locals()
    kwargs_.pop('kwargs')
    kwargs_.update(**kwargs)
    do_train(Kv(**kwargs_))


if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument("--data_source", default='aidp3.0', type=str, help="数据来源，可选值有aidp3.0,aidp3.1。后者是拉框标注法。")
    parser.add_argument("--train_image_base_path", default=None, type=str,
                        help="训练集图片存放位置，该参数和训练集样本中image字段拼接代表该图片的地址。")
    parser.add_argument("--dev_image_base_path", default=None, type=str,
                        help="训练集图片存放位置，该参数和测试集样本中image字段拼接代表该图片的地址。")
    parser.add_argument("--train_path", default=None, type=str, help="The path of train set.")
    parser.add_argument("--train_path_processed", default=None, type=str, help="经过doccano.py处理的结果，免去中间步骤.")
    parser.add_argument("--train_top", default=0, type=int, help="Select top k of train set to train.")
    parser.add_argument("--fewshot", default=0, type=int, help='读取数据时保证的few-shot number,达到该值后,不在读取数据。该参数仅对训练集有效。')
    parser.add_argument("--dev_path", default=None, type=str, help="The path of dev set.")
    parser.add_argument("--dev_path_processed", default=None, type=str, help="经过doccano.py处理的结果，免去中间步骤.")
    parser.add_argument("--dev_top", default=0, type=int, help="Select top k of dev set to evaluate.")
    parser.add_argument("--negative_ratio", default=3, type=int,
                        help="Used only for the extraction taskflow, the ratio of positive and negative samples, number of "
                             "negative samples = negative_ratio * number of positive samples.该参数仅对训练集有效，验证集全负采样 ")
    parser.add_argument("--task_type", default="document_record", type=str,
                        help="文档著录的task_type只能为document_record.")
    # parser.add_argument("--options", default=["正向", "负向"], type=str, nargs="+",
    #                     help="Used only for the classification taskflow, the options for classification")
    # parser.add_argument("--prompt_prefix", default="情感倾向", type=str,
    #                     help="Used only for the classification taskflow, the prompt prefix for classification")
    parser.add_argument("--separator", type=str, default='##',
                        help="Used only for entity/aspect-level classification taskflow, "
                             "separator for entity label and classification label")
    # parser.add_argument("--splits", default=[0.8, 0.2, 0.], type=float, nargs="*",
    #                     help="The ratio of samples in datasets. [0.6, 0.4, 0.] means 60% samples used for training, "
    #                          "40% for evaluation and 0% for test.该参数长度必须为0或者3.如果为3，则dev_path必须为空。目前测试集上必须是0.")

    parser.add_argument("--save_dir", default='./checkpoint', type=str,
                        help="The output directory where the model checkpoints will be written. ")

    parser.add_argument("--num_epochs", default=100, type=int, help="Total number of training epochs to perform.")
    parser.add_argument("--batch_size", default=8, type=int, help="Batch size per GPU/CPU for training.")
    parser.add_argument("--max_seq_len", default=512, type=int, help="The maximum input sequence length, "
                                                                     "Sequences longer than this will be split_trim_underline_fake_relation automatically.")
    parser.add_argument("--learning_rate", default=1e-5, type=float, help="The initial learning rate for AdamW.")
    parser.add_argument("--weight_decay", default=0., type=float, help="The weight_decay for AdamW.")
    parser.add_argument('--device', default="cuda",
                        help="Select which device to train model, defaults to cuda 0.")
    parser.add_argument("--logging_steps", default=10, type=int, help="The interval steps to logging.")

    parser.add_argument("--uie_model_path", default=None, help='Select the pretrained model.')
    parser.add_argument("--tokenizer", default=None, type=str,
                        help='tokenizer的路径，默认不填写与uie_model_path or checkpoint存放在同一路径下。')

    parser.add_argument("--checkpoint", default=None, type=str,
                        help="该参数与uie_model_path参数互斥。当使用本参数时，type_auto_prompt_size和auto_prompt_len无效。")
    parser.add_argument("--save_checkpoint", action='store_true', help='是否保存checkpoint，使用该参数表示保存。')
    parser.add_argument("--max_checkpoint_num", default=5, type=int, help="最多能保存的checkpoint, 设置过大会比较耗费磁盘存储空间。")
    parser.add_argument("--shuffle", action='store_true',
                        help="Whether to shuffle the dataset, defaults to True.")
    parser.add_argument("--seed", default=42, type=int, help="Random seed for initialization")
    parser.add_argument("--normal", action='store_true', help="全角转半角。")
    parser.add_argument("--doccano", action='store_true', help="使用该参数后可以加载doccano格式的数据，否则加载aidp导出的数据。")
    # params of doccano format data process fun

    # parser.add_argument("--file_path", default="../../raw_data/baidu_rte/doccano_ext_demo.jsonl", type=str,
    #                     help="The jsonline file exported from doccano platform.")

    args = parser.parse_args()
    run(**args.__dict__)
    # do_train()

