from datetime import datetime
from sqlalchemy import <PERSON><PERSON><PERSON>, Column, Integer, String, TEXT, DateTime
from common.database import Base


# 定义 User 类
class Map(Base):
    __tablename__ = 'map'  # 定义表名
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    name = Column(String(128), unique=True)
    dataNums = Column(Integer, default=0)
    surplus = Column(Integer, default=0)
    nodeNums = Column(Integer, default=0)
    relationNums = Column(Integer, default=0)
    config = Column(TEXT)
    map_type = Column(Integer, default=0)   # 0 通用图谱   1 专题图谱
    construction_method = Column(String(128), default="自动")   # 自动（通用图谱） 手动（专题图谱）
    status = Column(Integer, default=0)  # 运行状态  0 初始化  1 运行中  2 运行失败  3 完成
    is_active = Column(Boolean, default=False)
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now)


class GraphItem(Base):
    __tablename__ = 'graph_item'  # 定义表名
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    dh = Column(String(256), unique=True)
    tm = Column(String(256))
    qzh = Column(String(256))
    qzmc = Column(String(256))
    wh = Column(String(256))
    ys = Column(String(256))
    mj = Column(String(256))
    bgqx = Column(String(256))
    cwsj = Column(String(256))
    is_delete = Column(Boolean, default=False)
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    __table_args__ = {
        'mysql_charset': 'utf8'
    }


class GraphImage(Base):
    __tablename__ = 'graph_image'  # 定义表名
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    data_id = Column(Integer)
    file_name = Column(String(256))
    file_path = Column(TEXT)
    content = Column(TEXT)
    is_delete = Column(Boolean, default=False)
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    __table_args__ = {
        'mysql_charset': 'utf8'
    }


class GraphWord(Base):
    __tablename__ = 'graph_word'  # 定义表名
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    image_id = Column(TEXT)
    name = Column(String(256))
    epistatic_word = Column(TEXT)  # 上位词
    apposition_word = Column(TEXT)  # 同位词
    inferior_word = Column(TEXT)  # 下位词
    is_delete = Column(Boolean, default=False)
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    __table_args__ = {
        'mysql_charset': 'utf8'
    }
