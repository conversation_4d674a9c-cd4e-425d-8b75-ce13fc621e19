
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named 'win32com.gen_py' - imported by win32com (conditional, optional), C:\ProgramData\Anaconda3\envs\py3.9_torch\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_win32comgenpy.py (top-level)
missing module named 'typing.io' - imported by importlib.resources (top-level)
missing module named pep517 - imported by importlib.metadata (delayed)
missing module named 'org.python' - imported by pickle (optional), xml.sax (delayed, conditional)
missing module named posix - imported by os (conditional, optional), shutil (conditional), importlib._bootstrap_external (conditional)
missing module named resource - imported by posix (top-level), psutil._pslinux (optional), test.support (delayed, conditional, optional), joblib.externals.loky.backend.fork_exec (delayed, optional)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named pyimod02_importers - imported by C:\ProgramData\Anaconda3\envs\py3.9_torch\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgutil.py (top-level), C:\ProgramData\Anaconda3\envs\py3.9_torch\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgres.py (top-level)
missing module named jinja2 - imported by huggingface_hub.repocard (delayed, conditional), pyparsing.diagram (top-level), pkg_resources._vendor.pyparsing.diagram (top-level), setuptools._vendor.pyparsing.diagram (top-level)
missing module named railroad - imported by pyparsing.diagram (top-level), pkg_resources._vendor.pyparsing.diagram (top-level), setuptools._vendor.pyparsing.diagram (top-level)
missing module named pwd - imported by posixpath (delayed, conditional), subprocess (optional), shutil (optional), tarfile (optional), pathlib (delayed, conditional, optional), netrc (delayed, conditional), getpass (delayed), http.server (delayed, optional), webbrowser (delayed), psutil (optional), distutils.util (delayed, conditional, optional), distutils.archive_util (optional), setuptools._distutils.archive_util (optional), setuptools._distutils.util (delayed, conditional, optional)
missing module named termios - imported by getpass (optional), tty (top-level), psutil._compat (delayed, optional), tqdm.utils (delayed, optional)
missing module named _posixsubprocess - imported by subprocess (optional), multiprocessing.util (delayed)
missing module named grp - imported by subprocess (optional), shutil (optional), tarfile (optional), pathlib (delayed, optional), distutils.archive_util (optional), setuptools._distutils.archive_util (optional)
missing module named readline - imported by cmd (delayed, conditional, optional), code (delayed, conditional, optional), pdb (delayed, optional), site (delayed, optional), rlcompleter (optional)
missing module named _manylinux - imported by packaging._manylinux (delayed, optional), setuptools._vendor.packaging._manylinux (delayed, optional), pkg_resources._vendor.packaging._manylinux (delayed, optional)
missing module named 'pkg_resources.extern.pyparsing' - imported by pkg_resources._vendor.packaging.markers (top-level), pkg_resources._vendor.packaging.requirements (top-level)
missing module named 'pkg_resources.extern.importlib_resources' - imported by pkg_resources._vendor.jaraco.text (optional)
missing module named 'pkg_resources.extern.more_itertools' - imported by pkg_resources._vendor.jaraco.functools (top-level)
missing module named 'com.sun' - imported by torch._appdirs (delayed, conditional, optional), pkg_resources._vendor.appdirs (delayed, conditional, optional)
missing module named _winreg - imported by platform (delayed, optional), pkg_resources._vendor.appdirs (delayed, conditional)
missing module named pkg_resources.extern.packaging - imported by pkg_resources.extern (top-level), pkg_resources (top-level)
missing module named pkg_resources.extern.appdirs - imported by pkg_resources.extern (top-level), pkg_resources (top-level)
missing module named 'pkg_resources.extern.jaraco' - imported by pkg_resources (top-level), pkg_resources._vendor.jaraco.text (top-level)
missing module named 'java.lang' - imported by platform (delayed, optional), xml.sax._exceptions (conditional)
missing module named vms_lib - imported by platform (delayed, optional)
missing module named java - imported by platform (delayed)
missing module named usercustomize - imported by site (delayed, optional)
missing module named sitecustomize - imported by site (delayed, optional)
missing module named 'docutils.nodes' - imported by setuptools._distutils.command.check (top-level)
missing module named 'docutils.frontend' - imported by setuptools._distutils.command.check (top-level)
missing module named 'docutils.parsers' - imported by setuptools._distutils.command.check (top-level)
missing module named docutils - imported by setuptools._distutils.command.check (top-level)
missing module named _posixshmem - imported by multiprocessing.resource_tracker (conditional), multiprocessing.shared_memory (conditional)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level), joblib.externals.loky.backend.context (top-level), transformers.models.wav2vec2_with_lm.processing_wav2vec2_with_lm (top-level)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level), joblib._parallel_backends (conditional)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.Queue - imported by multiprocessing (top-level), transformers.benchmark.benchmark_utils (top-level)
missing module named multiprocessing.Pipe - imported by multiprocessing (top-level), transformers.benchmark.benchmark_utils (top-level)
missing module named multiprocessing.cpu_count - imported by multiprocessing (top-level), transformers.data.processors.squad (top-level), jieba (delayed), kbnlp.task (top-level)
missing module named multiprocessing.Pool - imported by multiprocessing (top-level), transformers.data.processors.squad (top-level), jieba (delayed, conditional)
missing module named multiprocessing.Process - imported by multiprocessing (top-level), caffe2.python.workspace (top-level), transformers.benchmark.benchmark_utils (top-level)
missing module named multiprocessing.RLock - imported by multiprocessing (delayed, conditional, optional), tqdm.std (delayed, conditional, optional)
missing module named asyncio.DefaultEventLoopPolicy - imported by asyncio (delayed, conditional), asyncio.events (delayed, conditional)
missing module named 'setuptools.extern.pyparsing' - imported by setuptools._vendor.packaging.requirements (top-level), setuptools._vendor.packaging.markers (top-level)
missing module named 'setuptools.extern.jaraco' - imported by setuptools._reqs (top-level), setuptools._entry_points (top-level), setuptools.command.egg_info (top-level), setuptools._vendor.jaraco.text (top-level)
missing module named setuptools.extern.importlib_resources - imported by setuptools.extern (conditional), setuptools._importlib (conditional), setuptools._vendor.jaraco.text (optional)
missing module named setuptools.extern.tomli - imported by setuptools.extern (delayed), setuptools.config.pyprojecttoml (delayed)
missing module named setuptools.extern.importlib_metadata - imported by setuptools.extern (conditional), setuptools._importlib (conditional)
missing module named setuptools.extern.ordered_set - imported by setuptools.extern (top-level), setuptools.dist (top-level)
missing module named setuptools.extern.packaging - imported by setuptools.extern (top-level), setuptools.dist (top-level), setuptools.command.egg_info (top-level), setuptools.depends (top-level)
missing module named 'setuptools.extern.more_itertools' - imported by setuptools.dist (top-level), setuptools.config.expand (delayed), setuptools._itertools (top-level), setuptools._entry_points (top-level), setuptools.msvc (top-level), setuptools._vendor.jaraco.functools (top-level)
missing module named 'setuptools.extern.packaging.version' - imported by setuptools.config.setupcfg (top-level), setuptools.msvc (top-level)
missing module named 'setuptools.extern.packaging.utils' - imported by setuptools.wheel (top-level)
missing module named 'setuptools.extern.packaging.tags' - imported by setuptools.wheel (top-level)
missing module named trove_classifiers - imported by setuptools.config._validate_pyproject.formats (optional)
missing module named 'setuptools.extern.packaging.specifiers' - imported by setuptools.config.setupcfg (top-level), setuptools.config._apply_pyprojecttoml (delayed)
missing module named 'setuptools.extern.packaging.requirements' - imported by setuptools.config.setupcfg (top-level)
missing module named importlib_metadata - imported by transformers.utils.versions (conditional), huggingface_hub.utils._runtime (conditional), accelerate.utils.imports (conditional), setuptools._importlib (delayed, optional)
missing module named _scproxy - imported by urllib.request (conditional), future.backports.urllib.request (conditional)
missing module named simplejson - imported by requests.compat (conditional, optional), huggingface_hub.utils._fixes (optional)
missing module named dummy_threading - imported by psutil._compat (optional), requests.cookies (optional), future.backports.http.cookiejar (optional), joblib.compressor (optional)
missing module named StringIO - imported by urllib3.packages.six (conditional), six (conditional)
runtime module named urllib3.packages.six.moves - imported by http.client (top-level), urllib3.util.response (top-level), urllib3.connectionpool (top-level), 'urllib3.packages.six.moves.urllib' (top-level), urllib3.util.queue (top-level)
missing module named brotli - imported by urllib3.util.request (optional), urllib3.response (optional)
missing module named brotlicffi - imported by urllib3.util.request (optional), urllib3.response (optional)
missing module named Queue - imported by urllib3.util.queue (conditional)
missing module named "'urllib3.packages.six.moves.urllib'.parse" - imported by urllib3.request (top-level), urllib3.poolmanager (top-level)
missing module named socks - imported by urllib3.contrib.socks (optional)
missing module named cryptography - imported by urllib3.contrib.pyopenssl (top-level), requests (conditional, optional)
missing module named 'OpenSSL.crypto' - imported by urllib3.contrib.pyopenssl (delayed)
missing module named 'cryptography.x509' - imported by urllib3.contrib.pyopenssl (delayed, optional)
missing module named 'cryptography.hazmat' - imported by urllib3.contrib.pyopenssl (top-level)
missing module named 'OpenSSL.SSL' - imported by urllib3.contrib.pyopenssl (top-level)
missing module named OpenSSL - imported by urllib3.contrib.pyopenssl (top-level)
missing module named chardet - imported by requests.compat (optional), requests (optional), requests.packages (optional)
missing module named unicodedata2 - imported by charset_normalizer.utils (optional)
missing module named urllib3_secure_extra - imported by urllib3 (optional)
missing module named nlp_project - imported by kbnlp (conditional)
missing module named 'kbnlp.mlt.multi_task_learning' - imported by kbnlp (optional)
missing module named 'IPython.display' - imported by tqdm.notebook (conditional, optional), huggingface_hub._login (delayed, optional), transformers.utils.notebook (top-level)
missing module named 'IPython.html' - imported by tqdm.notebook (conditional, optional)
missing module named IPython - imported by tqdm.notebook (conditional, optional)
missing module named ipywidgets - imported by tqdm.notebook (conditional, optional)
missing module named fcntl - imported by psutil._compat (delayed, optional), tqdm.utils (delayed, optional), filelock._unix (conditional, optional)
missing module named setuptools_scm - imported by tqdm.version (optional)
missing module named 'pandas.core' - imported by tqdm.std (delayed, optional)
missing module named pandas - imported by tqdm.std (delayed, optional), transformers.pipelines.table_question_answering (delayed), transformers.models.tapas.tokenization_tapas (conditional), transformers.models.tapex.tokenization_tapex (conditional)
missing module named 'matplotlib.pyplot' - imported by tqdm.gui (delayed), torch.utils.tensorboard._utils (delayed)
missing module named matplotlib - imported by tqdm.gui (delayed)
missing module named importlib_resources - imported by tqdm.cli (delayed, conditional, optional)
missing module named 'paddlenlp.transformers' - imported by kbnlp.pretrained.ernie_layout.modeling_ernie_layout (conditional)
missing module named paddle - imported by jieba._compat (delayed, optional), jieba.lac_small.predict (top-level), jieba.lac_small.creator (top-level), jieba.lac_small.reader_small (top-level), kbnlp.pretrained.ernie_layout.visual_model (conditional), kbnlp.pretrained.ernie_layout.modeling_ernie_layout (conditional)
missing module named paddlenlp - imported by kbnlp.pretrained.ernie_layout.visual_model (conditional)
missing module named torch.multiprocessing._prctl_pr_set_pdeathsig - imported by torch.multiprocessing (top-level), torch.multiprocessing.spawn (top-level)
missing module named 'tensorboard.summary' - imported by torch.utils.tensorboard.writer (top-level), torch.utils.tensorboard (top-level)
missing module named 'google.protobuf' - imported by caffe2.proto.caffe2_pb2 (top-level), caffe2.proto.metanet_pb2 (top-level), caffe2.proto.torch_pb2 (top-level), caffe2.python.workspace (top-level), caffe2.python.utils (top-level), torch.utils.tensorboard.summary (top-level), torch.utils.tensorboard.writer (delayed), transformers.utils.sentencepiece_model_pb2 (top-level)
missing module named 'caffe2.python.caffe2_pybind11_state_hip' - imported by caffe2.python._import_c_extension (optional)
missing module named 'caffe2.python.caffe2_pybind11_state_gpu' - imported by caffe2.python._import_c_extension (optional)
missing module named __builtin__ - imported by future.builtins.misc (conditional), future.builtins.new_min_max (conditional), future.utils (conditional), past.types (conditional), past.builtins.noniterators (conditional), past.builtins (conditional), past.builtins.misc (conditional)
missing module named _dbm - imported by dbm.ndbm (top-level)
missing module named gdbm - imported by anydbm (top-level), future.moves.dbm.gnu (conditional)
missing module named _gdbm - imported by dbm.gnu (top-level)
missing module named dumbdbm - imported by anydbm (top-level), future.moves.dbm.dumb (conditional)
missing module named anydbm - imported by future.moves.dbm (conditional)
missing module named dbhash - imported by anydbm (top-level)
missing module named whichdb - imported by future.moves.dbm (conditional), anydbm (top-level)
missing module named 'test.test_support' - imported by future.moves.test.support (conditional)
missing module named future_builtins - imported by future.builtins.misc (conditional)
missing module named dummy_thread - imported by future.backports.misc (conditional, optional)
missing module named thread - imported by future.backports.misc (conditional, optional)
missing module named _dummy_thread - imported by numpy.core.arrayprint (optional), future.backports.misc (conditional, optional)
missing module named google - imported by caffe2.proto.caffe2_pb2 (top-level)
missing module named 'caffe2.caffe2' - imported by caffe2.proto (optional)
missing module named 'tornado.wsgi' - imported by caffe2.python.mint.app (top-level)
missing module named tornado - imported by caffe2.python.mint.app (top-level), joblib.externals.cloudpickle.cloudpickle (delayed)
missing module named nvd3 - imported by caffe2.python.mint.app (top-level)
missing module named flask - imported by caffe2.python.mint.app (top-level)
missing module named moviepy - imported by torch.utils.tensorboard.summary (delayed, optional)
missing module named olefile - imported by PIL.FpxImagePlugin (top-level), PIL.MicImagePlugin (top-level)
missing module named cffi - imported by PIL.Image (optional), PIL.PyAccess (optional), PIL.ImageTk (delayed, conditional, optional)
missing module named defusedxml - imported by PIL.Image (optional)
missing module named PIL._imagingagg - imported by PIL (delayed, conditional, optional), PIL.ImageDraw (delayed, conditional, optional)
missing module named six.moves.range - imported by six.moves (top-level), torch.utils.tensorboard.summary (top-level)
runtime module named six.moves - imported by torch.utils.tensorboard.summary (top-level)
missing module named 'matplotlib.backends' - imported by torch.utils.tensorboard._utils (delayed)
missing module named onnx - imported by torch.utils.tensorboard._onnx_graph (delayed), transformers.onnx.convert (delayed)
missing module named 'tensorboard.plugins' - imported by torch.utils.tensorboard.writer (top-level), torch.utils.tensorboard._embedding (top-level), torch.utils.tensorboard.summary (top-level)
missing module named 'tensorboard.compat' - imported by torch.utils.tensorboard.writer (top-level), torch.utils.tensorboard._embedding (top-level), torch.utils.tensorboard._onnx_graph (top-level), torch.utils.tensorboard._pytorch_graph (top-level), torch.utils.tensorboard._proto_graph (top-level), torch.utils.tensorboard.summary (top-level), torch.utils.tensorboard._caffe2_graph (top-level)
missing module named tensorboard - imported by torch.utils.tensorboard (top-level)
missing module named pynvml - imported by torch.cuda.memory (delayed, optional)
missing module named torch.device - imported by torch (top-level), torch.cuda (top-level), torch.nn.modules.module (top-level), transformers.modeling_utils (top-level)
missing module named torch.norm_except_dim - imported by torch (top-level), torch.nn.utils.weight_norm (top-level)
missing module named torch._weight_norm - imported by torch (top-level), torch.nn.utils.weight_norm (top-level)
missing module named torch.Size - imported by torch (top-level), torch.nn.modules.normalization (top-level)
missing module named 'torch._C._autograd' - imported by torch.autograd (top-level)
missing module named 'torch._C._distributed_c10d' - imported by torch.distributed (conditional), torch.distributed.constants (top-level), torch.distributed.distributed_c10d (top-level), torch.distributed.rpc (conditional)
missing module named torch.dtype - imported by torch (top-level), torch.nn.modules.module (top-level)
missing module named 'torch._C._distributed_autograd' - imported by torch.distributed.autograd (conditional)
missing module named 'torch._C._distributed_rpc' - imported by torch.distributed.rpc (conditional), torch.distributed.rpc.api (top-level), torch.distributed.rpc.internal (top-level), torch.distributed.rpc.constants (top-level), torch.distributed.rpc.options (top-level)
missing module named sentencepiece_model_pb2 - imported by tokenizers.implementations.sentencepiece_unigram (delayed, optional)
missing module named deepspeed - imported by accelerate.utils.deepspeed (conditional), accelerate.utils.other (conditional), accelerate.accelerator (conditional), transformers.deepspeed (delayed), transformers.modeling_utils (delayed, conditional), transformers.training_args (delayed, conditional), transformers.models.distilbert.modeling_distilbert (delayed, conditional), transformers.models.fsmt.modeling_fsmt (delayed, conditional), transformers.models.hubert.modeling_hubert (delayed, conditional), transformers.models.sew.modeling_sew (delayed, conditional), transformers.models.sew_d.modeling_sew_d (delayed, conditional), transformers.models.unispeech.modeling_unispeech (delayed, conditional), transformers.models.unispeech_sat.modeling_unispeech_sat (delayed, conditional), transformers.models.wav2vec2.modeling_wav2vec2 (delayed, conditional), transformers.models.wav2vec2_conformer.modeling_wav2vec2_conformer (delayed, conditional), transformers.models.wavlm.modeling_wavlm (delayed, conditional)
missing module named 'smdistributed.dataparallel' - imported by transformers.training_args (delayed, conditional)
missing module named 'deepspeed.utils' - imported by transformers.deepspeed (delayed)
missing module named boto3 - imported by accelerate.commands.config.sagemaker (conditional)
missing module named 'torch_xla.distributed' - imported by accelerate.launchers (delayed, conditional), transformers.trainer (conditional)
missing module named 'torch.distributed.fsdp' - imported by accelerate.utils.dataclasses (delayed), accelerate.accelerator (delayed, conditional), transformers.trainer (delayed, conditional)
missing module named comet_ml - imported by accelerate.tracking (conditional), transformers.integrations (conditional, optional), transformers.trainer_tf (conditional)
missing module named wandb - imported by accelerate.tracking (conditional), transformers.integrations (delayed, conditional), transformers.trainer (delayed, conditional), transformers.trainer_tf (conditional)
missing module named 'torchvision.transforms' - imported by torch.utils.data.datapipes.iter.callable (delayed, optional)
missing module named dill - imported by torch.utils.data.datapipes.iter.callable (optional)
missing module named scipy - imported by torch.utils.data.datapipes.utils.decoder (delayed, optional), transformers.models.fnet.modeling_fnet (conditional)
missing module named torchaudio - imported by torch.utils.data.datapipes.utils.decoder (delayed, optional), transformers.pipelines.automatic_speech_recognition (delayed, conditional), transformers.models.mctct.feature_extraction_mctct (top-level)
missing module named torchvision - imported by torch.utils.data.datapipes.utils.decoder (delayed, optional)
missing module named psutil._psutil_aix - imported by psutil (top-level), psutil._psaix (top-level)
missing module named psutil._psutil_sunos - imported by psutil (top-level), psutil._pssunos (top-level)
missing module named psutil._psutil_bsd - imported by psutil (top-level), psutil._psbsd (top-level)
missing module named psutil._psutil_osx - imported by psutil (top-level), psutil._psosx (top-level)
missing module named psutil._psutil_linux - imported by psutil (top-level), psutil._pslinux (top-level)
missing module named _curses - imported by curses (top-level), curses.has_key (top-level)
missing module named apex - imported by accelerate.utils.deepspeed (conditional), transformers.trainer (conditional)
missing module named torch_ccl - imported by accelerate.utils.imports (optional)
missing module named codecarbon - imported by transformers.integrations (delayed)
missing module named neptune - imported by transformers.integrations (delayed)
missing module named mlflow - imported by transformers.integrations (delayed)
missing module named azureml - imported by transformers.integrations (delayed)
missing module named intel_extension_for_pytorch - imported by transformers.trainer (delayed)
missing module named 'bitsandbytes.optim' - imported by transformers.trainer (delayed, conditional, optional)
missing module named 'apex.optimizers' - imported by transformers.trainer (delayed, conditional, optional)
missing module named bitsandbytes - imported by transformers.trainer (delayed, conditional)
missing module named 'torchdynamo.optimizations' - imported by transformers.trainer (delayed, conditional)
missing module named torchdynamo - imported by transformers.trainer (delayed, conditional)
missing module named 'torch_xla.amp' - imported by transformers.trainer (delayed, conditional, optional)
missing module named 'fairscale.optim' - imported by transformers.trainer (conditional)
missing module named 'fairscale.nn' - imported by transformers.trainer (conditional)
missing module named fairscale - imported by transformers.trainer (conditional)
missing module named 'torch_xla.debug' - imported by transformers.trainer (conditional), transformers.benchmark.benchmark (delayed, conditional, optional)
missing module named 'jax.core' - imported by transformers.utils.generic (delayed, conditional)
missing module named tabulate - imported by torch.fx.graph (delayed, optional)
missing module named 'torch._C._fx' - imported by torch.fx.symbolic_trace (top-level)
missing module named torch_xla - imported by transformers.utils.import_utils (delayed, conditional, optional)
missing module named 'scipy.stats' - imported by transformers.data.metrics (conditional)
missing module named sklearn - imported by transformers.data.metrics (conditional)
missing module named tensorboardX - imported by transformers.integrations (delayed, conditional, optional)
missing module named sigopt - imported by transformers.integrations (delayed, conditional)
missing module named 'datasets.load' - imported by transformers.integrations (delayed, conditional)
missing module named 'ray.tune' - imported by transformers.integrations (delayed, conditional)
missing module named ray - imported by transformers.integrations (delayed), transformers.trainer (delayed, conditional), transformers.trainer_utils (delayed)
missing module named optuna - imported by transformers.integrations (delayed, conditional), transformers.trainer (delayed, conditional)
missing module named 'smdistributed.modelparallel' - imported by transformers.modeling_utils (conditional), transformers.trainer_pt_utils (conditional), transformers.trainer (conditional), transformers.training_args (conditional)
missing module named 'torch_xla.core' - imported by accelerate.utils.imports (optional), accelerate.state (conditional), accelerate.utils.operations (conditional), accelerate.optimizer (conditional), accelerate.utils.other (conditional), accelerate.utils.random (conditional), accelerate.checkpointing (conditional), accelerate.data_loader (conditional), transformers.trainer_pt_utils (delayed, conditional), transformers.trainer (conditional), transformers.trainer_utils (delayed, conditional), transformers.training_args (conditional), transformers.benchmark.benchmark_args (conditional)
missing module named datasets - imported by transformers.modeling_tf_utils (delayed), transformers.trainer (conditional), transformers.modelcard (delayed, conditional), transformers.models.rag.retrieval_rag (conditional)
missing module named 'tensorflow.compiler' - imported by transformers.generation_tf_utils (top-level), transformers.models.t5.modeling_tf_t5 (top-level)
missing module named keras - imported by transformers.modeling_tf_utils (top-level)
missing module named fastai - imported by huggingface_hub.fastai_utils (delayed)
missing module named toml - imported by huggingface_hub.fastai_utils (delayed, optional)
missing module named 'ipywidgets.widgets' - imported by huggingface_hub._login (delayed, optional)
missing module named 'InquirerPy.separator' - imported by huggingface_hub.commands.delete_cache (optional)
missing module named 'InquirerPy.base' - imported by huggingface_hub.commands.delete_cache (optional)
missing module named InquirerPy - imported by huggingface_hub.commands.delete_cache (optional)
missing module named 'tensorflow.python' - imported by transformers.modeling_tf_pytorch_utils (delayed, optional), transformers.modeling_tf_utils (top-level), transformers.benchmark.benchmark_utils (conditional), transformers.benchmark.benchmark_tf (conditional), transformers.trainer_tf (top-level)
missing module named h5py - imported by transformers.modeling_tf_utils (top-level)
missing module named tf2onnx - imported by transformers.onnx.convert (delayed)
missing module named astunparse - imported by torch.jit.frontend (optional)
missing module named 'monkeytype.tracing' - imported by torch.jit._monkeytype_config (optional)
missing module named 'monkeytype.config' - imported by torch.jit._monkeytype_config (optional)
missing module named 'monkeytype.db' - imported by torch.jit._monkeytype_config (optional)
missing module named monkeytype - imported by torch.jit._monkeytype_config (optional)
missing module named 'torch._C._jit_tree_views' - imported by torch.jit.frontend (top-level)
missing module named onnxruntime - imported by transformers.onnx.convert (delayed, optional)
missing module named rjieba - imported by transformers.models.roformer.tokenization_roformer (delayed, optional), transformers.models.roformer.tokenization_utils (delayed, optional)
missing module named sentencepiece - imported by transformers.convert_slow_tokenizer (delayed), transformers.models.albert.tokenization_albert (top-level), transformers.models.barthez.tokenization_barthez (top-level), transformers.models.bartpho.tokenization_bartpho (top-level), transformers.models.bert_generation.tokenization_bert_generation (top-level), transformers.models.big_bird.tokenization_big_bird (top-level), transformers.models.camembert.tokenization_camembert (top-level), transformers.models.xlnet.tokenization_xlnet (top-level), transformers.models.deberta_v2.tokenization_deberta_v2 (top-level), transformers.models.fnet.tokenization_fnet (top-level), transformers.models.layoutxlm.tokenization_layoutxlm (top-level), transformers.models.xlm_roberta.tokenization_xlm_roberta (top-level), transformers.models.m2m_100.tokenization_m2m_100 (top-level), transformers.models.marian.tokenization_marian (top-level), transformers.models.mbart.tokenization_mbart (top-level), transformers.models.mbart50.tokenization_mbart50 (top-level), transformers.models.mluke.tokenization_mluke (top-level), transformers.models.t5.tokenization_t5 (top-level), transformers.models.nllb.tokenization_nllb (top-level), transformers.models.pegasus.tokenization_pegasus (top-level), transformers.models.plbart.tokenization_plbart (top-level), transformers.models.reformer.tokenization_reformer (top-level), transformers.models.rembert.tokenization_rembert (top-level), transformers.models.speech_to_text.tokenization_speech_to_text (top-level), transformers.models.xglm.tokenization_xglm (top-level), transformers.models.xlm_prophetnet.tokenization_xlm_prophetnet (delayed, optional)
missing module named 'jax.lax' - imported by transformers.generation_flax_logits_process (top-level)
missing module named 'jax.random' - imported by transformers.modeling_flax_utils (top-level), transformers.models.encoder_decoder.modeling_flax_encoder_decoder (top-level), transformers.models.bart.modeling_flax_bart (top-level), transformers.models.blenderbot_small.modeling_flax_blenderbot_small (top-level), transformers.models.blenderbot.modeling_flax_blenderbot (top-level), transformers.models.longt5.modeling_flax_longt5 (top-level), transformers.models.marian.modeling_flax_marian (top-level), transformers.models.mbart.modeling_flax_mbart (top-level), transformers.models.t5.modeling_flax_t5 (top-level), transformers.models.opt.modeling_flax_opt (top-level), transformers.models.pegasus.modeling_flax_pegasus (top-level), transformers.models.speech_encoder_decoder.modeling_flax_speech_encoder_decoder (top-level), transformers.models.vision_encoder_decoder.modeling_flax_vision_encoder_decoder (top-level), transformers.models.xglm.modeling_flax_xglm (top-level)
missing module named 'flax.serialization' - imported by transformers.modeling_flax_utils (top-level)
missing module named msgpack - imported by transformers.modeling_flax_utils (top-level)
missing module named 'flax.traverse_util' - imported by transformers.modeling_flax_pytorch_utils (top-level), transformers.models.bert.modeling_flax_bert (top-level), transformers.modeling_flax_utils (top-level), transformers.models.roformer.modeling_flax_roformer (top-level), transformers.models.encoder_decoder.modeling_flax_encoder_decoder (top-level), transformers.models.albert.modeling_flax_albert (top-level), transformers.models.bart.modeling_flax_bart (top-level), transformers.models.beit.modeling_flax_beit (top-level), transformers.models.big_bird.modeling_flax_big_bird (top-level), transformers.models.roberta.modeling_flax_roberta (top-level), transformers.models.blenderbot_small.modeling_flax_blenderbot_small (top-level), transformers.models.blenderbot.modeling_flax_blenderbot (top-level), transformers.models.clip.modeling_flax_clip (top-level), transformers.models.gpt2.modeling_flax_gpt2 (top-level), transformers.models.distilbert.modeling_flax_distilbert (top-level), transformers.models.electra.modeling_flax_electra (top-level), transformers.models.gpt_neo.modeling_flax_gpt_neo (top-level), transformers.models.gptj.modeling_flax_gptj (top-level), transformers.models.longt5.modeling_flax_longt5 (top-level), transformers.models.marian.modeling_flax_marian (top-level), transformers.models.mbart.modeling_flax_mbart (top-level), transformers.models.t5.modeling_flax_t5 (top-level), transformers.models.opt.modeling_flax_opt (top-level), transformers.models.pegasus.modeling_flax_pegasus (top-level), transformers.models.speech_encoder_decoder.modeling_flax_speech_encoder_decoder (top-level), transformers.models.vision_encoder_decoder.modeling_flax_vision_encoder_decoder (top-level), transformers.models.vit.modeling_flax_vit (top-level), transformers.models.xglm.modeling_flax_xglm (top-level)
missing module named 'flax.core' - imported by transformers.models.bert.modeling_flax_bert (top-level), transformers.modeling_flax_utils (top-level), transformers.models.roformer.modeling_flax_roformer (top-level), transformers.models.encoder_decoder.modeling_flax_encoder_decoder (top-level), transformers.models.albert.modeling_flax_albert (top-level), transformers.models.bart.modeling_flax_bart (top-level), transformers.models.beit.modeling_flax_beit (top-level), transformers.models.big_bird.modeling_flax_big_bird (top-level), transformers.models.roberta.modeling_flax_roberta (top-level), transformers.models.blenderbot_small.modeling_flax_blenderbot_small (top-level), transformers.models.blenderbot.modeling_flax_blenderbot (top-level), transformers.models.clip.modeling_flax_clip (top-level), transformers.models.gpt2.modeling_flax_gpt2 (top-level), transformers.models.distilbert.modeling_flax_distilbert (top-level), transformers.models.electra.modeling_flax_electra (top-level), transformers.models.gpt_neo.modeling_flax_gpt_neo (top-level), transformers.models.gptj.modeling_flax_gptj (top-level), transformers.models.longt5.modeling_flax_longt5 (top-level), transformers.models.marian.modeling_flax_marian (top-level), transformers.models.mbart.modeling_flax_mbart (top-level), transformers.models.t5.modeling_flax_t5 (top-level), transformers.models.opt.modeling_flax_opt (top-level), transformers.models.pegasus.modeling_flax_pegasus (top-level), transformers.models.speech_encoder_decoder.modeling_flax_speech_encoder_decoder (top-level), transformers.models.vision_encoder_decoder.modeling_flax_vision_encoder_decoder (top-level), transformers.models.vit.modeling_flax_vit (top-level), transformers.models.xglm.modeling_flax_xglm (top-level)
missing module named 'jax.numpy' - imported by transformers.utils.generic (delayed), transformers.feature_extraction_utils (delayed, conditional), transformers.tokenization_utils_base (delayed, conditional), transformers.modeling_flax_pytorch_utils (top-level), transformers.models.bert.modeling_flax_bert (top-level), transformers.modeling_flax_outputs (top-level), transformers.modeling_flax_utils (top-level), transformers.generation_flax_utils (top-level), transformers.generation_flax_logits_process (top-level), transformers.models.roformer.modeling_flax_roformer (top-level), transformers.models.encoder_decoder.modeling_flax_encoder_decoder (top-level), transformers.models.albert.modeling_flax_albert (top-level), transformers.models.bart.modeling_flax_bart (top-level), transformers.models.beit.modeling_flax_beit (top-level), transformers.models.big_bird.modeling_flax_big_bird (top-level), transformers.models.roberta.modeling_flax_roberta (top-level), transformers.models.blenderbot_small.modeling_flax_blenderbot_small (top-level), transformers.models.blenderbot.modeling_flax_blenderbot (top-level), transformers.models.clip.modeling_flax_clip (top-level), transformers.models.gpt2.modeling_flax_gpt2 (top-level), transformers.models.distilbert.modeling_flax_distilbert (top-level), transformers.models.electra.modeling_flax_electra (top-level), transformers.models.gpt_neo.modeling_flax_gpt_neo (top-level), transformers.models.gptj.modeling_flax_gptj (top-level), transformers.models.longt5.modeling_flax_longt5 (top-level), transformers.models.marian.modeling_flax_marian (top-level), transformers.models.mbart.modeling_flax_mbart (top-level), transformers.models.t5.modeling_flax_t5 (top-level), transformers.models.opt.modeling_flax_opt (top-level), transformers.models.owlvit.processing_owlvit (delayed, conditional), transformers.models.pegasus.modeling_flax_pegasus (top-level), transformers.models.speech_encoder_decoder.modeling_flax_speech_encoder_decoder (top-level), transformers.models.vision_encoder_decoder.modeling_flax_vision_encoder_decoder (top-level), transformers.models.vit.modeling_flax_vit (top-level), transformers.models.wav2vec2.tokenization_wav2vec2 (conditional), transformers.models.wav2vec2_phoneme.tokenization_wav2vec2_phoneme (conditional), transformers.models.xglm.modeling_flax_xglm (top-level)
missing module named jax - imported by transformers.utils.generic (delayed, conditional), transformers.modeling_flax_pytorch_utils (top-level), transformers.models.bert.modeling_flax_bert (top-level), transformers.modeling_flax_utils (top-level), transformers.generation_flax_utils (top-level), transformers.generation_flax_logits_process (top-level), transformers.models.roformer.modeling_flax_roformer (top-level), transformers.models.encoder_decoder.modeling_flax_encoder_decoder (top-level), transformers.models.albert.modeling_flax_albert (top-level), transformers.models.bart.modeling_flax_bart (top-level), transformers.models.beit.modeling_flax_beit (top-level), transformers.models.big_bird.modeling_flax_big_bird (top-level), transformers.models.roberta.modeling_flax_roberta (top-level), transformers.models.blenderbot_small.modeling_flax_blenderbot_small (top-level), transformers.models.blenderbot.modeling_flax_blenderbot (top-level), transformers.models.clip.modeling_flax_clip (top-level), transformers.models.gpt2.modeling_flax_gpt2 (top-level), transformers.models.distilbert.modeling_flax_distilbert (top-level), transformers.models.electra.modeling_flax_electra (top-level), transformers.models.gpt_neo.modeling_flax_gpt_neo (top-level), transformers.models.gptj.modeling_flax_gptj (top-level), transformers.models.longt5.modeling_flax_longt5 (top-level), transformers.models.marian.modeling_flax_marian (top-level), transformers.models.mbart.modeling_flax_mbart (top-level), transformers.models.t5.modeling_flax_t5 (top-level), transformers.models.opt.modeling_flax_opt (top-level), transformers.models.pegasus.modeling_flax_pegasus (top-level), transformers.models.speech_encoder_decoder.modeling_flax_speech_encoder_decoder (top-level), transformers.models.vision_encoder_decoder.modeling_flax_vision_encoder_decoder (top-level), transformers.models.vit.modeling_flax_vit (top-level), transformers.models.xglm.modeling_flax_xglm (top-level)
missing module named 'flax.linen' - imported by transformers.models.bert.modeling_flax_bert (top-level), transformers.modeling_flax_utils (top-level), transformers.models.roformer.modeling_flax_roformer (top-level), transformers.models.encoder_decoder.modeling_flax_encoder_decoder (top-level), transformers.models.albert.modeling_flax_albert (top-level), transformers.models.bart.modeling_flax_bart (top-level), transformers.models.beit.modeling_flax_beit (top-level), transformers.models.big_bird.modeling_flax_big_bird (top-level), transformers.models.roberta.modeling_flax_roberta (top-level), transformers.models.blenderbot_small.modeling_flax_blenderbot_small (top-level), transformers.models.blenderbot.modeling_flax_blenderbot (top-level), transformers.models.clip.modeling_flax_clip (top-level), transformers.models.gpt2.modeling_flax_gpt2 (top-level), transformers.models.distilbert.modeling_flax_distilbert (top-level), transformers.models.electra.modeling_flax_electra (top-level), transformers.models.gpt_neo.modeling_flax_gpt_neo (top-level), transformers.models.gptj.modeling_flax_gptj (top-level), transformers.models.longt5.modeling_flax_longt5 (top-level), transformers.models.marian.modeling_flax_marian (top-level), transformers.models.mbart.modeling_flax_mbart (top-level), transformers.models.t5.modeling_flax_t5 (top-level), transformers.models.opt.modeling_flax_opt (top-level), transformers.models.pegasus.modeling_flax_pegasus (top-level), transformers.models.speech_encoder_decoder.modeling_flax_speech_encoder_decoder (top-level), transformers.models.vision_encoder_decoder.modeling_flax_vision_encoder_decoder (top-level), transformers.models.vit.modeling_flax_vit (top-level), transformers.models.xglm.modeling_flax_xglm (top-level)
missing module named flax - imported by transformers.modeling_flax_pytorch_utils (top-level), transformers.models.bert.modeling_flax_bert (top-level), transformers.modeling_flax_outputs (top-level), transformers.generation_flax_utils (top-level), transformers.models.albert.modeling_flax_albert (top-level), transformers.models.beit.modeling_flax_beit (top-level), transformers.models.big_bird.modeling_flax_big_bird (top-level), transformers.models.clip.modeling_flax_clip (top-level), transformers.models.electra.modeling_flax_electra (top-level)
missing module named tensorflow_text - imported by transformers.models.bert.tokenization_bert_tf (top-level), transformers.models.bert_generation.modeling_bert_generation (delayed, optional)
missing module named fast_lsh_cumulation - imported by transformers.models.yoso.modeling_yoso (delayed, optional)
missing module named dl - imported by setuptools.command.build_ext (conditional, optional)
missing module named Cython - imported by setuptools.command.build_ext (optional)
missing module named com - imported by torch._appdirs (delayed)
missing module named pycocotools - imported by transformers.models.detr.feature_extraction_detr (delayed, optional), transformers.models.yolos.feature_extraction_yolos (delayed, optional)
missing module named 'scipy.optimize' - imported by transformers.models.detr.modeling_detr (conditional), transformers.models.maskformer.modeling_maskformer (conditional), transformers.models.yolos.modeling_yolos (conditional)
missing module named 'paddle.fluid' - imported by jieba.lac_small.predict (top-level), jieba.lac_small.utils (top-level), jieba.lac_small.creator (top-level), jieba.lac_small.nets (top-level), jieba.lac_small.reader_small (top-level)
missing module named pythainlp - imported by transformers.models.xlm.tokenization_xlm (delayed, conditional, optional)
missing module named Mykytea - imported by transformers.models.xlm.tokenization_xlm (delayed, conditional, optional)
missing module named regex.DEFAULT_VERSION - imported by regex (delayed, optional), regex.regex (delayed, optional)
missing module named pickle5 - imported by numpy.compat.py3k (optional), joblib.externals.cloudpickle.compat (conditional, optional)
missing module named viztracer - imported by joblib.externals.loky.initializers (delayed, optional)
missing module named 'tornado.gen' - imported by joblib._dask (conditional, optional)
missing module named 'distributed.utils' - imported by joblib._dask (conditional, optional)
missing module named 'dask.distributed' - imported by joblib._dask (conditional)
missing module named 'dask.sizeof' - imported by joblib._dask (conditional)
missing module named 'dask.utils' - imported by joblib._dask (conditional)
missing module named distributed - imported by joblib._parallel_backends (delayed, optional), joblib._dask (optional)
missing module named dask - imported by joblib._dask (optional)
missing module named numpy.core.integer - imported by numpy.core (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.conjugate - imported by numpy.core (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.ufunc - imported by numpy.core (top-level), numpy.lib.utils (top-level)
missing module named numpy.core.ones - imported by numpy.core (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.hstack - imported by numpy.core (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.atleast_1d - imported by numpy.core (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.atleast_3d - imported by numpy.core (top-level), numpy.lib.shape_base (top-level)
missing module named numpy.core.vstack - imported by numpy.core (top-level), numpy.lib.shape_base (top-level)
missing module named numpy.core.linspace - imported by numpy.core (top-level), numpy.lib.index_tricks (top-level)
missing module named numpy.core.transpose - imported by numpy.core (top-level), numpy.lib.function_base (top-level)
missing module named numpy.core.result_type - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.float_ - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.number - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.bool_ - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.inf - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.array2string - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.signbit - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.isscalar - imported by numpy.core (delayed), numpy.testing._private.utils (delayed), numpy.lib.polynomial (top-level)
missing module named numpy.core.isinf - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.isnat - imported by numpy.core (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.ndarray - imported by numpy.core (top-level), numpy.testing._private.utils (top-level), numpy.lib.utils (top-level)
missing module named numpy.core.array_repr - imported by numpy.core (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.arange - imported by numpy.core (top-level), numpy.testing._private.utils (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.float32 - imported by numpy.core (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.iinfo - imported by numpy.core (top-level), numpy.lib.twodim_base (top-level)
missing module named numpy.core.reciprocal - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.sort - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.argsort - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.sign - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.isnan - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.count_nonzero - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.divide - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.swapaxes - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.matmul - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.object_ - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.asanyarray - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.intp - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.atleast_2d - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.product - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.amax - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.amin - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.moveaxis - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.geterrobj - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.errstate - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.finfo - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.isfinite - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.sum - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.fastCopyAndTranspose - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.sqrt - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.multiply - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.add - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.dot - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.Inf - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.all - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.newaxis - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.complexfloating - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.inexact - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.cdouble - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.csingle - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.double - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.single - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.intc - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.empty_like - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.empty - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.zeros - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.asarray - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.lib.utils (top-level), numpy.fft._pocketfft (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.array - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (top-level), numpy.lib.polynomial (top-level)
missing module named _ufunc - imported by numpy._typing (conditional)
missing module named numpy.bytes_ - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.str_ - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.void - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.object_ - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.datetime64 - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.timedelta64 - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.number - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.complexfloating - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.floating - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.integer - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.ctypeslib (top-level)
missing module named numpy.unsignedinteger - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.bool_ - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.ma.core (top-level), numpy.ma.mrecords (top-level)
missing module named numpy.generic - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.dtype - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.array_api._typing (top-level), numpy.ma.mrecords (top-level), numpy.ctypeslib (top-level)
missing module named numpy.ndarray - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.ma.core (top-level), numpy.ma.extras (top-level), numpy.ma.mrecords (top-level), numpy.ctypeslib (top-level)
missing module named numpy.ufunc - imported by numpy (top-level), numpy._typing (top-level)
missing module named numpy.float64 - imported by numpy (top-level), numpy.array_api._typing (top-level)
missing module named numpy.float32 - imported by numpy (top-level), numpy.array_api._typing (top-level)
missing module named numpy.uint64 - imported by numpy (top-level), numpy.array_api._typing (top-level)
missing module named numpy.uint32 - imported by numpy (top-level), numpy.array_api._typing (top-level)
missing module named numpy.uint16 - imported by numpy (top-level), numpy.array_api._typing (top-level)
missing module named numpy.uint8 - imported by numpy (top-level), numpy.array_api._typing (top-level)
missing module named numpy.int64 - imported by numpy (top-level), numpy.array_api._typing (top-level)
missing module named numpy.int32 - imported by numpy (top-level), numpy.array_api._typing (top-level)
missing module named numpy.int16 - imported by numpy (top-level), numpy.array_api._typing (top-level)
missing module named numpy.int8 - imported by numpy (top-level), numpy.array_api._typing (top-level)
missing module named numpy.array - imported by numpy (top-level), numpy.ma.core (top-level), numpy.ma.extras (top-level), numpy.ma.mrecords (top-level)
missing module named numpy.recarray - imported by numpy (top-level), numpy.ma.mrecords (top-level)
missing module named numpy.expand_dims - imported by numpy (top-level), numpy.ma.core (top-level)
missing module named numpy.iscomplexobj - imported by numpy (top-level), numpy.ma.core (top-level)
missing module named numpy.amin - imported by numpy (top-level), numpy.ma.core (top-level)
missing module named numpy.amax - imported by numpy (top-level), numpy.ma.core (top-level)
missing module named numpy.histogramdd - imported by numpy (delayed), numpy.lib.twodim_base (delayed)
missing module named numpy.eye - imported by numpy (delayed), numpy.core.numeric (delayed)
missing module named 'lz4.frame' - imported by joblib.compressor (optional)
missing module named lz4 - imported by joblib.compressor (optional)
missing module named 'pyctcdecode.constants' - imported by transformers.models.wav2vec2_with_lm.processing_wav2vec2_with_lm (delayed)
missing module named 'pyctcdecode.alphabet' - imported by transformers.models.wav2vec2_with_lm.processing_wav2vec2_with_lm (delayed)
missing module named pyctcdecode - imported by transformers.pipelines (delayed, conditional, optional), transformers.models.wav2vec2_with_lm.processing_wav2vec2_with_lm (delayed, conditional)
missing module named 'phonemizer.separator' - imported by transformers.models.wav2vec2_phoneme.tokenization_wav2vec2_phoneme (delayed)
missing module named phonemizer - imported by transformers.models.wav2vec2_phoneme.tokenization_wav2vec2_phoneme (delayed)
missing module named processing_visiotn_text_dual_encoder - imported by transformers.models.vision_text_dual_encoder (conditional)
missing module named tensorflow_probability - imported by transformers.pipelines.table_question_answering (conditional), transformers.models.tapas.modeling_tf_tapas (conditional, optional)
missing module named torch_scatter - imported by transformers.models.tapas.modeling_tapas (conditional, optional)
missing module named 'apex.normalization' - imported by transformers.models.longt5.modeling_longt5 (optional), transformers.models.t5.modeling_t5 (optional)
missing module named 'torchaudio.compliance' - imported by transformers.models.speech_to_text.feature_extraction_speech_to_text (top-level)
missing module named scann - imported by transformers.models.realm.retrieval_realm (delayed)
missing module named 'tensorflow.compat' - imported by transformers.models.bert_generation.modeling_bert_generation (delayed, optional), transformers.models.realm.retrieval_realm (delayed)
missing module named faiss - imported by transformers.models.rag.retrieval_rag (conditional)
missing module named 'pytorch_quantization.nn' - imported by transformers.models.qdqbert.modeling_qdqbert (conditional, optional)
missing module named pytorch_quantization - imported by transformers.models.qdqbert.modeling_qdqbert (conditional, optional)
missing module named spacy - imported by transformers.models.openai.tokenization_openai (delayed, optional)
missing module named ftfy - imported by transformers.models.clip.tokenization_clip (delayed, optional), transformers.models.openai.tokenization_openai (delayed, optional)
missing module named transformers.models.mbart.MBart50Tokenizer - imported by transformers.models.mbart (conditional, optional), transformers (conditional, optional)
missing module named pytesseract - imported by transformers.models.layoutlmv2.feature_extraction_layoutlmv2 (conditional), transformers.models.layoutlmv3.feature_extraction_layoutlmv3 (conditional)
missing module named 'detectron2.modeling' - imported by transformers.models.layoutlmv2.modeling_layoutlmv2 (conditional)
missing module named detectron2 - imported by transformers.models.layoutlmv2.configuration_layoutlmv2 (conditional), transformers.models.layoutlmv2.modeling_layoutlmv2 (conditional)
missing module named kenlm - imported by transformers.pipelines (delayed, conditional, optional)
missing module named timm - imported by transformers.models.detr.modeling_detr (conditional)
missing module named emoji - imported by transformers.models.bertweet.tokenization_bertweet (delayed, optional)
missing module named unidic - imported by transformers.models.bert_japanese.tokenization_bert_japanese (delayed, conditional, optional)
missing module named unidic_lite - imported by transformers.models.bert_japanese.tokenization_bert_japanese (delayed, conditional, optional)
missing module named ipadic - imported by transformers.models.bert_japanese.tokenization_bert_japanese (delayed, conditional, optional)
missing module named fugashi - imported by transformers.models.bert_japanese.tokenization_bert_japanese (delayed, optional)
missing module named tensorflow_hub - imported by transformers.models.bert_generation.modeling_bert_generation (delayed, optional)
missing module named tensorflow - imported by transformers.utils.generic (delayed, conditional), huggingface_hub.keras_mixin (delayed, conditional), transformers.feature_extraction_utils (delayed, conditional), transformers.tokenization_utils_base (delayed, conditional), transformers.modeling_tf_pytorch_utils (delayed, optional), transformers.modeling_tf_utils (top-level), transformers.activations_tf (top-level), transformers.generation_tf_utils (top-level), transformers.generation_tf_logits_process (top-level), transformers.tf_utils (top-level), transformers.trainer_utils (conditional), transformers.data.data_collator (delayed, conditional), transformers.data.processors.utils (delayed, conditional), transformers.data.processors.glue (conditional), transformers.data.processors.squad (conditional), transformers.modelcard (delayed, conditional), transformers.models.bert.modeling_bert (delayed, optional), transformers.models.bert.modeling_tf_bert (top-level), transformers.modeling_tf_outputs (top-level), transformers.models.bert.tokenization_bert_tf (top-level), transformers.models.roformer.modeling_roformer (delayed, optional), transformers.models.roformer.modeling_tf_roformer (top-level), transformers.models.encoder_decoder.modeling_tf_encoder_decoder (top-level), transformers.onnx.convert (delayed), transformers.models.albert.modeling_albert (delayed, optional), transformers.models.albert.modeling_tf_albert (top-level), transformers.models.bart.modeling_tf_bart (top-level), transformers.models.big_bird.modeling_big_bird (delayed, optional), transformers.models.roberta.modeling_tf_roberta (top-level), transformers.pipelines.base (conditional), transformers.pipelines.conversational (conditional), transformers.pipelines.fill_mask (conditional), transformers.pipelines.image_classification (conditional), transformers.pipelines.question_answering (conditional), transformers.pipelines.table_question_answering (conditional), transformers.pipelines.text2text_generation (conditional), transformers.pipelines.text_generation (conditional), transformers.pipelines.zero_shot_image_classification (conditional), transformers.pipelines (conditional), transformers.models.blenderbot_small.modeling_tf_blenderbot_small (top-level), transformers.models.blenderbot.modeling_tf_blenderbot (top-level), transformers.models.canine.modeling_canine (delayed, optional), transformers.models.clip.modeling_tf_clip (top-level), transformers.models.codegen.tokenization_codegen (conditional), transformers.models.codegen.tokenization_codegen_fast (conditional), transformers.models.convbert.modeling_convbert (delayed, optional), transformers.models.convbert.modeling_tf_convbert (top-level), transformers.models.xlnet.modeling_xlnet (delayed, optional), transformers.models.xlnet.modeling_tf_xlnet (top-level), transformers.models.ctrl.modeling_tf_ctrl (top-level), transformers.models.data2vec.modeling_tf_data2vec_vision (top-level), transformers.models.gpt2.modeling_gpt2 (delayed, optional), transformers.models.gpt2.modeling_tf_gpt2 (top-level), transformers.models.deberta.modeling_tf_deberta (top-level), transformers.models.deberta_v2.modeling_tf_deberta_v2 (top-level), transformers.models.decision_transformer.modeling_decision_transformer (delayed, optional), transformers.models.deit.modeling_tf_deit (top-level), transformers.models.distilbert.modeling_tf_distilbert (top-level), transformers.models.dpr.modeling_tf_dpr (top-level), transformers.models.electra.modeling_electra (delayed, optional), transformers.models.electra.modeling_tf_electra (top-level), transformers.models.xlm.modeling_tf_xlm (top-level), transformers.models.flaubert.modeling_tf_flaubert (top-level), transformers.models.funnel.modeling_funnel (delayed, optional), transformers.models.funnel.modeling_tf_funnel (top-level), transformers.models.gpt_neo.modeling_gpt_neo (delayed, optional), transformers.models.gptj.modeling_tf_gptj (top-level), transformers.models.hubert.modeling_tf_hubert (top-level), transformers.models.imagegpt.modeling_imagegpt (delayed, optional), transformers.models.layoutlm.modeling_tf_layoutlm (top-level), transformers.models.led.modeling_tf_led (top-level), transformers.models.longformer.modeling_tf_longformer (top-level), transformers.models.lxmert.modeling_lxmert (delayed, optional), transformers.models.lxmert.modeling_tf_lxmert (top-level), transformers.models.marian.modeling_tf_marian (top-level), transformers.models.swin.modeling_tf_swin (top-level), transformers.models.mbart.modeling_tf_mbart (top-level), transformers.models.megatron_bert.modeling_megatron_bert (delayed, optional), transformers.models.mobilebert.modeling_mobilebert (delayed, optional), transformers.models.mobilebert.modeling_tf_mobilebert (top-level), transformers.models.mpnet.modeling_tf_mpnet (top-level), transformers.models.t5.modeling_t5 (delayed, optional), transformers.models.t5.modeling_tf_t5 (top-level), transformers.models.nezha.modeling_nezha (delayed, optional), transformers.models.openai.modeling_tf_openai (top-level), transformers.models.opt.modeling_tf_opt (top-level), transformers.models.owlvit.processing_owlvit (delayed, conditional), transformers.models.pegasus.modeling_tf_pegasus (top-level), transformers.models.qdqbert.modeling_qdqbert (delayed, optional), transformers.models.rag.modeling_tf_rag (top-level), transformers.models.realm.modeling_realm (delayed, optional), transformers.models.regnet.modeling_tf_regnet (top-level), transformers.models.rembert.modeling_rembert (delayed, optional), transformers.models.rembert.modeling_tf_rembert (top-level), transformers.models.resnet.modeling_tf_resnet (top-level), transformers.models.segformer.modeling_tf_segformer (top-level), transformers.models.speech_to_text.modeling_tf_speech_to_text (top-level), transformers.models.tapas.modeling_tapas (delayed, optional), transformers.models.tapas.modeling_tf_tapas (top-level), transformers.models.trajectory_transformer.modeling_trajectory_transformer (delayed, optional), transformers.models.transfo_xl.modeling_transfo_xl (delayed, optional), transformers.models.transfo_xl.modeling_tf_transfo_xl (top-level), transformers.models.transfo_xl.modeling_tf_transfo_xl_utilities (top-level), transformers.models.vision_encoder_decoder.modeling_tf_vision_encoder_decoder (top-level), transformers.models.vit.modeling_tf_vit (top-level), transformers.models.vit_mae.modeling_tf_vit_mae (top-level), transformers.models.wav2vec2.tokenization_wav2vec2 (conditional), transformers.models.wav2vec2.modeling_tf_wav2vec2 (top-level), transformers.models.wav2vec2_phoneme.tokenization_wav2vec2_phoneme (conditional), transformers.training_args_tf (conditional), transformers.benchmark.benchmark_args_tf (conditional), transformers.benchmark.benchmark_tf (conditional), transformers.keras_callbacks (top-level), transformers.optimization_tf (top-level), transformers.trainer_tf (top-level)
missing module named torch._softmax_backward_data - imported by torch (top-level), transformers.pytorch_utils (top-level)
missing module named smdistributed - imported by transformers.modeling_utils (conditional)
missing module named modeling_tf_layoutlm - imported by transformers (conditional, optional)
missing module named 'tensorflow.keras' - imported by transformers.keras_callbacks (top-level)
missing module named 'py3nvml.py3nvml' - imported by transformers.benchmark.benchmark (conditional), transformers.benchmark.benchmark_tf (conditional)
missing module named py3nvml - imported by transformers.benchmark.benchmark_utils (conditional)
missing module named 'transformers.utils.dummies_sentencepiece_and_tokenizers_objects' - imported by transformers (conditional, optional)
missing module named torch.Generator - imported by torch (top-level), torch.utils.data.dataset (top-level)
missing module named torch.default_generator - imported by torch (top-level), torch.utils.data.dataset (top-level)
missing module named torch.randperm - imported by torch (top-level), torch.utils.data.dataset (top-level)
missing module named 'torch._C._VariableFunctions' - imported by torch (conditional)
missing module named 'torch._dl' - imported by torch (conditional, optional)
missing module named DLFCN - imported by torch (conditional, optional)
missing module named org - imported by copy (optional)
