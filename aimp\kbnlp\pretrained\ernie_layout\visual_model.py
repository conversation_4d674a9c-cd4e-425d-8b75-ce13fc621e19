#-*- coding: utf-8 -*-
# copyright (c) 2022 PaddlePaddle Authors. All Rights Reserve.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.nn import Conv2d, BatchNorm2d, MaxPool2d
# import paddle
# from paddle import ParamAttr
# import paddle.nn as nn
# import paddle.nn.functional as F
# from paddle.nn import Conv2D, BatchNorm
# from paddle.nn import MaxPool2D


class ConvBNLayer(nn.Module):

    def __init__(self,
                 num_channels,
                 num_filters,
                 filter_size,
                 stride=1,
                 groups=1,
                 act=None,
                 name=None,
                 data_format="NCHW"):
        super(ConvB<PERSON>ayer, self).__init__()
        self.act = None if act is None else getattr(torch, 'relu')
        self._conv = Conv2d(in_channels=num_channels,
                            out_channels=num_filters,
                            kernel_size=filter_size,
                            stride=stride,
                            padding=(filter_size - 1) // 2,
                            groups=groups,
                            # weight_attr=ParamAttr(name=name + "_weights"),
                            # bias_attr=False,
                            # data_format=data_format)
                            bias=False)
        if name == "conv1":
            bn_name = "bn_" + name
        else:
            bn_name = "bn" + name[3:]
        self._batch_norm = BatchNorm2d(num_filters,)
                                     # act=act,
                                     # param_attr=ParamAttr(name=bn_name +
                                     #                      "_scale"),
                                     # bias_attr=ParamAttr(bn_name + "_offset"),
                                     # moving_mean_name=bn_name + "_mean",
                                     # moving_variance_name=bn_name + "_variance",
                                     # data_layout=data_format)

    def forward(self, inputs):
        y = self._conv(inputs)
        y = self._batch_norm(y)
        if self.act:
            y = self.act(y)
        return y


class BottleneckBlock(nn.Module):

    def __init__(self,
                 num_channels,
                 num_filters,
                 stride,
                 shortcut=True,
                 name=None,
                 data_format="NCHW"):
        super(BottleneckBlock, self).__init__()
        self.conv0 = ConvBNLayer(num_channels=num_channels,
                                 num_filters=num_filters,
                                 filter_size=1,
                                 act="relu",
                                 name=name + "_branch2a",
                                 data_format=data_format)
        self.conv1 = ConvBNLayer(num_channels=num_filters,
                                 num_filters=num_filters,
                                 filter_size=3,
                                 stride=stride,
                                 act="relu",
                                 name=name + "_branch2b",
                                 data_format=data_format)
        self.conv2 = ConvBNLayer(num_channels=num_filters,
                                 num_filters=num_filters * 4,
                                 filter_size=1,
                                 act=None,
                                 name=name + "_branch2c",
                                 data_format=data_format)

        if not shortcut:
            self.short = ConvBNLayer(num_channels=num_channels,
                                     num_filters=num_filters * 4,
                                     filter_size=1,
                                     stride=stride,
                                     name=name + "_branch1",
                                     data_format=data_format)

        self.shortcut = shortcut

        self._num_channels_out = num_filters * 4

    def forward(self, inputs):
        y = self.conv0(inputs)
        conv1 = self.conv1(y)
        conv2 = self.conv2(conv1)

        if self.shortcut:
            short = inputs
        else:
            short = self.short(inputs)

        y = torch.add(short, conv2)
        y = F.relu(y)
        return y


class BasicBlock(nn.Module):

    def __init__(self,
                 num_channels,
                 num_filters,
                 stride,
                 shortcut=True,
                 name=None,
                 data_format="NCHW"):
        super(BasicBlock, self).__init__()
        self.stride = stride
        self.conv0 = ConvBNLayer(num_channels=num_channels,
                                 num_filters=num_filters,
                                 filter_size=3,
                                 stride=stride,
                                 act="relu",
                                 name=name + "_branch2a",
                                 data_format=data_format)
        self.conv1 = ConvBNLayer(num_channels=num_filters,
                                 num_filters=num_filters,
                                 filter_size=3,
                                 act=None,
                                 name=name + "_branch2b",
                                 data_format=data_format)

        if not shortcut:
            self.short = ConvBNLayer(num_channels=num_channels,
                                     num_filters=num_filters,
                                     filter_size=1,
                                     stride=stride,
                                     name=name + "_branch1",
                                     data_format=data_format)

        self.shortcut = shortcut

    def forward(self, inputs):
        y = self.conv0(inputs)
        conv1 = self.conv1(y)

        if self.shortcut:
            short = inputs
        else:
            short = self.short(inputs)
        y = torch.add(short, conv1)
        y = F.relu(y)
        return y


class ResNet(nn.Module):

    def __init__(self,
                 layers=50,
                 class_dim=1000,
                 input_image_channel=3,
                 data_format="NCHW"):
        super(ResNet, self).__init__()

        self.layers = layers
        self.data_format = data_format
        self.input_image_channel = input_image_channel

        supported_layers = [18, 34, 50, 101, 152]
        assert layers in supported_layers, \
            "supported layers are {} but input layer is {}".format(
                supported_layers, layers)

        if layers == 18:
            depth = [2, 2, 2, 2]
        elif layers == 34 or layers == 50:
            depth = [3, 4, 6, 3]
        elif layers == 101:
            depth = [3]
        elif layers == 152:
            depth = [3, 8, 36, 3]
        num_channels = [64, 256, 512, 1024
                        ] if layers >= 50 else [64, 64, 128, 256]
        num_filters = [64, 128, 256, 512]

        self.conv = ConvBNLayer(num_channels=self.input_image_channel,
                                num_filters=64,
                                filter_size=7,
                                stride=2,
                                act="relu",
                                name="conv1",
                                data_format=self.data_format)
        self.pool2d_max = MaxPool2d(kernel_size=3,
                                    stride=2,
                                    padding=1,
                                    )

        self.block_list = []
        if layers >= 50:
            for block in range(len(depth)):
                shortcut = False
                for i in range(depth[block]):
                    if layers in [101, 152] and block == 2:
                        if i == 0:
                            conv_name = "res" + str(block + 2) + "a"
                        else:
                            conv_name = "res" + str(block + 2) + "b" + str(i)
                    else:
                        conv_name = "res" + str(block + 2) + chr(97 + i)
                    bb = BottleneckBlock(num_channels=num_channels[block] if i == 0 else num_filters[block] * 4,
                                         num_filters=num_filters[block],
                                         stride=2 if i == 0 and block != 0 else 1,
                                         shortcut=shortcut,
                                         name=conv_name,
                                         data_format=self.data_format)
                    self.add_module(
                        conv_name,
                        bb)
                    self.block_list.append(bb)
                    shortcut = True
        else:
            for block in range(len(depth)):
                shortcut = False
                for i in range(depth[block]):
                    conv_name = "res" + str(block + 2) + chr(97 + i)
                    bb = BasicBlock(num_channels=num_channels[block] if i == 0 else num_filters[block],
                                    num_filters=num_filters[block],
                                    stride=2 if i == 0 and block != 0 else 1,
                                    shortcut=shortcut,
                                    name=conv_name,
                                    data_format=self.data_format)
                    self.add_module(
                        conv_name,
                        bb)
                    self.block_list.append(bb)
                    shortcut = True

    def forward(self, inputs):
        y = self.conv(inputs)
        y = self.pool2d_max(y)

        for block in self.block_list:
            y = block(y)
        return y


if __name__ == '__main__':
    import paddle
    from collections import OrderedDict
    import numpy as np
    import math
    resnet_t = ResNet(101)
    state_ori = resnet_t.state_dict()
    from paddlenlp.transformers.ernie_layout.visual_backbone import ResNet as ResNet2
    resnet_p = ResNet2(101)
    state_p = resnet_p.state_dict()
    state_t = OrderedDict()
    for k, v in state_p.items():
        if '._mean' in k:
            k_ = k.replace('_mean', 'running_mean')
        elif '._variance' in k:
            k_ = k.replace('_variance', 'running_var')
        else:
            k_ = k
        v_ = torch.tensor(v.numpy())
        state_t[k_] = v_
    print(resnet_t.load_state_dict(state_t, strict=False))
    input_ = np.random.randn(5, 3, 224, 224).astype(np.float32)
    input_t = torch.tensor(input_)
    input_p = paddle.to_tensor(input_)
    resnet_t.eval()
    resnet_p.eval()
    with torch.no_grad():
        out_t = resnet_t(input_t).numpy()
    with paddle.no_grad():
        out_p = resnet_p(input_p).numpy()
    acc = 4
    acc_ = math.pow(10, -acc)
    acc_guaranteed = np.all(np.abs(out_p -
                                   out_t) < acc_)
    print('保证小数点后%d位精度：' % acc, acc_guaranteed)
