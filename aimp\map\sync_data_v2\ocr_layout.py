import os
import fitz
import re
import numpy as np
import cv2
import torch, torchvision
from pathlib import Path
from PIL import Image
from kbnlp.scripts.information_extraction.zhu_ocr import OCRSystem
from collections import defaultdict

torch._C._jit_set_profiling_mode(False)


model_dir_path = Path(__file__).parent.parent.parent

ocr = OCRSystem(det_model_path=os.path.join(str(model_dir_path), "dtm/001.pt"),
                cls_model_path=os.path.join(str(model_dir_path), "dtm/003.pt"), cls_vertical_model_path=os.path.join(str(model_dir_path), "dtm/006.pt"),
                rec_model_path=os.path.join(str(model_dir_path), "dtm/tp1t7.pt"), rec_image_shape='3,32,480',
                rec_char_dict_path=os.path.join(str(model_dir_path), "dtm/ppocr_keys_v1.txt"))  # det_max_side_len=1152


@torch.no_grad()
def ocr_layout(file_path):
    """
    ocr and layout
    """
    suffix = os.path.splitext(file_path)[1]
    ret = defaultdict(lambda: [])
    if suffix == ".pdf":
        # 使用正则表达式来查找图片
        checkXO = r"/Type(?= */XObject)"
        checkIM = r"/Subtype(?= */Image)"
        # 打开pdf
        doc = fitz.open(file_path)
        # 图片计数
        imgcount = 0
        # 获取对象数量长度
        lenXREF = doc.xref_length()

        # 打印PDF的信息
        # print("文件名:{}, 页数: {}, 对象: {}".format(path, len(doc), lenXREF - 1))

        # 遍历每一个图片对象
        for i in range(1, lenXREF):
            # 定义对象字符串
            text = doc.xref_object(i)
            #         print(i,text)
            isXObject = re.search(checkXO, text)
            # 使用正则表达式查看是否是图片
            isImage = re.search(checkIM, text)
            # 如果不是对象也不是图片，则continue
            if not isXObject or not isImage:
                continue
            imgcount += 1
            # 根据索引生成图像
            pix = fitz.Pixmap(doc, i)
            _img = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)
            img = cv2.cvtColor(np.asarray(_img.convert('RGB')), cv2.COLOR_RGB2BGR)
            # print(img, type(img))
            img = torch.as_tensor(img, device=ocr.device)[None, :].permute([0, 3, 1, 2]).to(torch.float32)
            boxes, rects = ocr.text_detect(img)
            if len(boxes) > 1:
                rec_res = ocr.text_rec_classic(img, boxes, rects)
                result = "".join([item[0] for item in rec_res if item[1] > 0.5])
                ret[file_path].append(result)

    else:
        with Image.open(file_path, 'r') as img:
            img = cv2.cvtColor(np.asarray(img.convert('RGB')), cv2.COLOR_RGB2BGR)
        img = torch.as_tensor(img, device=ocr.device)[None, :].permute([0, 3, 1, 2]).to(torch.float32)
        boxes, rects = ocr.text_detect(img)
        if len(boxes) > 1:
            rec_res = ocr.text_rec_classic(img, boxes, rects)
            # print(rec_res)
            result = "".join([item[0] for item in rec_res if item[1] > 0.5])
            ret[file_path].append(result)
    return ret


if __name__ == '__main__':
    path = r'C:\Users\<USER>\Desktop\0001.pdf'
    ocr_layout(path)


# ocr = OCRSystem(det_model_path='dtm/001.pt',
#         cls_model_path='dtm/003.pt', cls_vertical_model_path='dtm/006.pt',
#         rec_model_path='dtm/tp1t7.pt', rec_image_shape='3,32,480',
#         rec_char_dict_path='dtm/ppocr_keys_v1.txt') #, det_max_side_len=1152)
#
# with Image.open(r'C:\Users\<USER>\Desktop\解放军档案馆图片\中国人民志愿军烈士英名录第1卷_正文002-正文006\正文002.jpg', 'r') as img:
#     img = cv2.cvtColor(np.asarray(img.convert('RGB')), cv2.COLOR_RGB2BGR)
# img = torch.as_tensor(img, device=ocr.device)[None,:].permute([0,3,1,2]).to(torch.float32)
# boxes, rects = ocr.text_detect(img)
# if len(boxes) < 1:
#     exit()
# rec_res = ocr.text_rec_classic(img, boxes, rects)
# print(rec_res)
#
# model = torch.jit.load('dtm/la_dp09.pt', map_location=ocr.device)
# from time import time
# for i in range(5):
#     t = time()
#     result = model([img[0]])[1][0]
#     print('layout time', time()-t)
# result = {k:v.cpu().numpy() for k, v in result.items()}
# print(result)
