# encoding=utf-8

from transformers import LayoutX<PERSON>TokenizerFast, LayoutXLMTokenizer
layoutxlm_fast = LayoutXLMTokenizerFast.from_pretrained('microsoft/layoutxlm-base')
print(layoutxlm_fast(['你好', '上海'], boxes=[[1, 1, 1, 1], [2, 2, 2, 2]], max_length=8, padding='max_length',
                     return_offsets_mapping=True, return_token_type_ids=True, return_length=True,
                     return_special_tokens_mask=True,
                     return_tensors=None))
