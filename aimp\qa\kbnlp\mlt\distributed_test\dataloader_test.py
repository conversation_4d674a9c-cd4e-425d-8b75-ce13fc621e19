import torch
from accelerate import Accelerator
from torch.utils.data.dataloader import <PERSON><PERSON><PERSON><PERSON>, Dataset, Sampler
import itertools
from copy import copy
from collections import OrderedDict
from abc import ABC, abstractmethod
from typing import List

accelerator = Accelerator(device_placement=False)
device = accelerator.device
# print(device)


class SortingSampler(Sampler):
    # noinspection PyMissingConstructor
    def __init__(self, lengths: List[int], batch_size=None, batch_max_tokens=None, use_effective_tokens=False,
                 shuffle=False, generator=None, accelerator=None, data=None, task_id=None) -> None:
        """A sampler which sort samples according to their lengths. It takes packagea continuous chunk of sorted samples to
        make packagea batch.

        Args:
            lengths: Lengths of each sample, usually measured by number of tokens.
            batch_max_tokens: Maximum tokens per batch.
            use_effective_tokens: Whether to calculate effective number of tokens when applying the `batch_max_tokens`.
            batch_size: Maximum samples per batch.
            shuffle: ``True`` to shuffle batches and samples in packagea batch. 评估和预测时该参数设置为False。
            generator： 随机数生成器。如果使用分布式训练，需要确保不同进程的generator通过manual_seed方法设置相同的种子。当shuffle为True时有效。
        """
        # assert any([batch_size, batch_max_tokens]), 'At least one of batch_size and batch_max_tokens is required'
        self.shuffle = shuffle
        self.batch_size = batch_size
        # self.batch_max_tokens = batch_max_tokens
        self.batch_indices = []
        self.generator = generator
        self.drop_last = False  # Accelerate包要求BatchSampler必须提供此属性。
        self.accelerator = accelerator
        self.data = data
        self.task_id = task_id
        num_tokens = 0
        mini_batch = []
        for i in torch.argsort(torch.tensor(lengths), descending=True).tolist():
            # if batch_max_tokens:
            effective_tokens = lengths[i] if (not mini_batch or not use_effective_tokens) else lengths[mini_batch[0]]
            if (batch_max_tokens is None or num_tokens + effective_tokens <= batch_max_tokens) and (
                    batch_size is None or len(mini_batch) < batch_size):
                mini_batch.append(i)
                num_tokens += effective_tokens
            else:
                if not mini_batch:  # this sequence is longer than  batch_max_tokens
                    mini_batch.append(i)
                    self.batch_indices.append(mini_batch)
                    mini_batch = []
                    num_tokens = 0
                else:
                    self.batch_indices.append(mini_batch)
                    mini_batch = [i]
                    num_tokens = effective_tokens
        if mini_batch:
            self.batch_indices.append(mini_batch)
        # print(len(max(self.batch_indices, key=len)))

    def __iter__(self):
        fw = open('%d.txt' % self.accelerator.local_process_index, mode='a', encoding='utf-8')
        if self.shuffle:
            generator = self.generator
            if generator is None:
                generator = torch.Generator()
                generator.manual_seed(int(torch.empty((), dtype=torch.int64).random_().item()))
            l = torch.randperm(len(self), generator=generator).tolist()
            fw.write('  '.join(['process:%d' % self.accelerator.local_process_index, 'task:%d' % self.task_id, 'data:', repr([self.data[self.batch_indices[i]].tolist() for i in l])]) + '\n')
            for i in l:
                yield self.batch_indices[i]
        else:
            for batch in self.batch_indices:
                yield batch

    def __len__(self) -> int:
        return len(self.batch_indices)


class SamplerBuilder(ABC):
    @staticmethod
    @abstractmethod
    def build(lengths: List[int], batch_size=None, batch_max_tokens=None, shuffle=False, gradient_accumulation=1,
              generator=None, accelerator=None, data=None, task_id=None,
              **kwargs) -> Sampler:
        """Build packagea ``Sampler`` given statistics of samples and other arguments.

        Args:
            lengths: The lengths of samples.
            shuffle: ``True`` to shuffle batches. Note samples in each mini-batch are not necessarily shuffled.
            gradient_accumulation: Number of mini-batches per update step.
            **kwargs: Other arguments to be passed to the constructor of the sampler.
            generator:
        """
        pass

    @staticmethod
    def scale(batch_size, batch_max_tokens, gradient_accumulation):
        r"""Scale down the ``batch_size`` and ``batch_max_tokens`` to :math:`\frac{1}{\text{gradient_accumulation}}`
        of them respectively.

        Args:
            gradient_accumulation: Number of mini-batches per update step.

        Returns:
            tuple(int,int): batch_size, batch_max_tokens
        """
        if gradient_accumulation:
            if batch_size:
                batch_size //= gradient_accumulation
            if batch_max_tokens:
                batch_max_tokens //= gradient_accumulation
        return batch_size, batch_max_tokens


class SortingSamplerBuilder(SamplerBuilder):
    @staticmethod
    def build(lengths: List[int], batch_size=None, batch_max_tokens=None, use_effective_tokens=False, shuffle=False, gradient_accumulation=1,
              generator=None, accelerator=None, data=None, task_id=None,
              **kwargs) -> Sampler:
        batch_size, batch_max_tokens = SortingSamplerBuilder.scale(batch_size, batch_max_tokens, gradient_accumulation)
        return SortingSampler(lengths, batch_size, batch_max_tokens, use_effective_tokens, shuffle, generator, accelerator, data, task_id)


class MyDataset(Dataset):
    def __init__(self, data):
        super(MyDataset, self).__init__()
        self.data = data

    def __getitem__(self, index):
        return self.data[index]

    def __len__(self):
        return len(self.data)


class MyDataloader(DataLoader):
    def __init__(self, tau=0.8, accelerator=None):
        super(MyDataloader, self).__init__(dataset=None)
        self.dataloaders = {}
        self.tau = tau
        self.generator = torch.Generator()  # 用来生成随机种子的generator，通过manual_seed方法保证每个进程产生相同的随机数种子。
        self.generator.manual_seed(5125636612826581487)
        self.accelerator = accelerator

    def __len__(self) -> int:
        if self.dataloaders:
            return sum(self.sizes)
        return 0

    def __iter__(self):
        if len(self) == 0:
            return None, None

        step_id = 0
        # generator = torch.Generator()  # 每个epoch，各个进程都利用self.generator产生相同的随机数。这样保证不同epoch的执行的任务顺序是变化的，同时保证在任意一个epoch内，不同进程每个step做的任务是相同的。（注意虽然每个step，不同进程的任务头相同，但是不同进程获取的数据是随机的。）
        # generator.manual_seed(int(torch.empty((), dtype=torch.int64).random_(generator=self.generator).item()))

        sampling_weights, total_size = self.sampling_weights
        task_names = list(self.dataloaders.keys())
        iterators = dict((k, itertools.cycle(v)) for k, v in self.dataloaders.items())
        # if self.accelerator:
        #     print('local_process_index:%d.' %
        #           self.accelerator.local_process_index)
        for task_id in torch.multinomial(torch.tensor(sampling_weights), total_size, replacement=True,
                                         generator=self.generator).tolist():
            step_id += 1
            # if self.accelerator:
            #     print('step_id:%d, task_id:%d.' % (step_id, task_id))
            batch = copy(next(iterators[task_names[task_id]]))
            yield self.accelerator.local_process_index, step_id, task_names[task_id], batch

    @property
    def sampling_weights(self):
        sampling_weights = self.sizes
        total_size = sum(sampling_weights)
        Z = sum(pow(v, self.tau) for v in sampling_weights)
        sampling_weights = [pow(v, self.tau) / Z for v in sampling_weights]
        return sampling_weights, total_size

    @property
    def sizes(self):
        return [len(v) for v in self.dataloaders.values()]


generator1 = torch.Generator()  # 用来生成随机种子的generator，通过manual_seed方法保证每个进程产生相同的随机数种子。
generator1.manual_seed(1679812325489)
data1 = torch.arange(64)
dataset1 = MyDataset(data1)
dataloader1 = DataLoader(dataset1, batch_sampler=SortingSamplerBuilder.build(lengths=data1.tolist(),
                                                                             batch_size=4,
                                                                             shuffle=True,
                                                                             generator=generator1,
                                                                             accelerator=accelerator,
                                                                             data=data1,
                                                                             task_id=0))

generator2 = torch.Generator()  # 用来生成随机种子的generator，通过manual_seed方法保证每个进程产生相同的随机数种子。
generator2.manual_seed(1679812325489)
data2 = torch.arange(100, 164)
dataset2 = MyDataset(data2)
dataloader2 = DataLoader(dataset2, batch_sampler=SortingSamplerBuilder.build(lengths=data2.tolist(),
                                                                             batch_size=4,
                                                                             shuffle=True,
                                                                             generator=generator2,
                                                                             accelerator=accelerator,
                                                                             data=data2,
                                                                             task_id=1))

generator3 = torch.Generator()  # 用来生成随机种子的generator，通过manual_seed方法保证每个进程产生相同的随机数种子。
generator3.manual_seed(1679812325489)
data3 = torch.arange(1000, 1064)
dataset3 = MyDataset(data3)
dataloader3 = DataLoader(dataset3, batch_sampler=SortingSamplerBuilder.build(lengths=data3.tolist(),
                                                                             batch_size=4,
                                                                             shuffle=True,
                                                                             generator=generator3,
                                                                             accelerator=accelerator,
                                                                             data=data3,
                                                                             task_id=2))

generator4 = torch.Generator()  # 用来生成随机种子的generator，通过manual_seed方法保证每个进程产生相同的随机数种子。
generator4.manual_seed(1679812325489)
data4 = torch.arange(10000, 10064)
dataset4 = MyDataset(data4)
dataloader4 = DataLoader(dataset4, batch_sampler=SortingSamplerBuilder.build(lengths=data4.tolist(),
                                                                             batch_size=4,
                                                                             shuffle=True,
                                                                             generator=generator4,
                                                                             accelerator=accelerator,
                                                                             data=data4,
                                                                             task_id=3))


dataloader1, dataloader2, dataloader3, dataloader4 = accelerator.prepare(dataloader1, dataloader2, dataloader3, dataloader4)

dataloader = MyDataloader(accelerator=accelerator)

dataloader.dataloaders = OrderedDict([(0, dataloader1), (1, dataloader2), (2, dataloader3), (3, dataloader4)])

fw = open(file='log%d.txt' % accelerator.local_process_index, mode='w', encoding='utf-8')
for i in range(2):
    if i == 1:
        pass
    for process_index, step_id, task_id, batch in dataloader:
        batch = batch.to(device)
        fw.write('  '.join(['step:%d' % step_id, 'process:%d' % process_index, "task: %d" % task_id, repr(batch)]) + '\n')
    fw.write('\n')

