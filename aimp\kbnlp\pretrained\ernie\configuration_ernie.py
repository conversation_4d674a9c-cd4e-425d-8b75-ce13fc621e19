from transformers import BertConfig


class ErnieConfig(BertConfig):

    # model_type = 'ernie'  # 不要添加这一句，否则AutoConfig.from_pretrained方法不能使用。把ernie看作bert。

    def __init__(
            self,
            vocab_size=40000,
            hidden_size=768,
            num_hidden_layers=12,
            num_attention_heads=12,
            intermediate_size=3072,
            hidden_act="gelu",
            hidden_dropout_prob=0.1,
            attention_probs_dropout_prob=0.1,
            max_position_embeddings=512,
            type_vocab_size=4,
            initializer_range=0.02,
            layer_norm_eps=1e-12,  # baidu ernie官方默认参数值
            pad_token_id=0,
            gradient_checkpointing=False,
            position_embedding_type="absolute",
            use_cache=True,
            task_type_vocab_size=3,
            task_id=0,
            use_task_id=False,
            **kwargs):
        super().__init__(vocab_size=vocab_size,
                         hidden_size=hidden_size,
                         num_hidden_layers=num_hidden_layers,
                         num_attention_heads=num_attention_heads,
                         intermediate_size=intermediate_size,
                         hidden_act=hidden_act,
                         hidden_dropout_prob=hidden_dropout_prob,
                         attention_probs_dropout_prob=attention_probs_dropout_prob,
                         max_position_embeddings=max_position_embeddings,
                         type_vocab_size=type_vocab_size,
                         initializer_range=initializer_range,
                         layer_norm_eps=layer_norm_eps,
                         pad_token_id=pad_token_id,
                         gradient_checkpointing=gradient_checkpointing,
                         position_embedding_type=position_embedding_type,
                         use_cache=use_cache,
                         **kwargs)

        self.task_type_vocab_size = task_type_vocab_size
        self.task_id = task_id
        self.use_task_id = use_task_id


if __name__ == '__main__':
    from pprint import pprint
    config = ErnieConfig().__dict__
    config['type_auto_prompt_size'] = 1000
    config['auto_prompt_len'] = 5
    config['auto_prompt_vocab'] = ['人名']
    pprint(config)
    from kbnlp.taskflow.models.information_extraction.information_extraction_model import AutoPromptUIE, AutoPromptUIEConfig
    auie = AutoPromptUIE(AutoPromptUIEConfig(**config))
    print('*' * 20)
    pprint(auie.config.__dict__)

