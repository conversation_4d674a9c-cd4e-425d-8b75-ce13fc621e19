import copy
import json
import os
import re
from fastapi import APIRouter, Depends
from pydantic import BaseModel, validator
from .models import QA
from common.customResponse import resp
from datetime import datetime
from typing import List
from sqlalchemy.orm import Session
from common.database import get_db
from .ymlkbqa.chat_bot_graph import ChatBotGraph
from .extensions import qa_Cache
from sqlalchemy import func
from common.logger import logger


router = APIRouter(
    prefix="/knowledge",
    tags=["knowledge"],
    responses={404: {"description": "Not found"}}
)

default_handler = ChatBotGraph()


class QaInnerSchema(BaseModel):
    id: int
    name: str
    knowledge_graph: int
    intentionNums: int
    config: str
    is_active: bool
    create_time: datetime
    update_time: datetime

    class Config:
        orm_mode = True

    @validator('config')
    def json_to_dict(cls, v):
        return json.loads(v)

    @validator('create_time')
    def format_time(cls, v):
        return v.strftime('%Y-%m-%d %H:%M:%S')


class QaOutSchema(BaseModel):
    data: List[QaInnerSchema]
    total: int


class Param(BaseModel):
    id: int = None
    name: str
    knowledge_graph: int
    config: list


@router.get("/qa", response_model=QaOutSchema)
@logger.catch
def qa(page=1, pageSize=9, db: Session = Depends(get_db)):
    """
    获取问答服务列表
    """
    count = db.query(func.count(QA.id)).scalar()
    ret = db.query(QA).order_by(QA.create_time.desc()).limit(pageSize).offset((int(page) - 1) * int(pageSize)).all()
    return {
        "data": ret,
        "total": count
    }


@router.get("/qa_detail", response_model=QaInnerSchema)
@logger.catch
def qa_detail(id, db: Session = Depends(get_db)):
    """
    获取问答服务详情
    """
    ret = db.query(QA).filter(QA.id == id).first()
    print(ret, type(ret))
    return ret


@router.get("/qa_check_name")
@logger.catch
def qa_check_name(qa_id: str, name: str, db: Session = Depends(get_db)):
    """
    检查问答服务名称
    """
    isAllow = True
    if qa_id:
        ret = db.query(QA).filter(QA.name == name, QA.id != qa_id).first()
        if ret:
            isAllow = False
    else:
        ret = db.query(QA).filter(QA.name == name).first()
        if ret:
            isAllow = False
    return isAllow


@router.post("/create_qa")
@logger.catch
def create_qa(param: Param, db: Session = Depends(get_db)):
    """
    创建问答服务
    """
    id = param.id
    config = param.config
    if id:
        db.query(QA).filter(QA.id == id).update({
            "name": param.name,
            "knowledge_graph": param.knowledge_graph,
            "intentionNums": len(config),
            "config": json.dumps(config, ensure_ascii=False)
        })
        db.commit()
    else:
        qa_row = QA(
            name=param.name,
            knowledge_graph=param.knowledge_graph,
            intentionNums=len(config),
            config=json.dumps(config, ensure_ascii=False)
        )
        db.add(qa_row)
        db.commit()
    for item_config in config:
        qa_model = json.loads(item_config.get("qaModel"))
        qa_model_path = qa_model.get("versions")[0]["path"]
        handler = ChatBotGraph()
        handler.classifier.change_ner(ner_path=qa_model_path)
        qa_Cache.qa_cache.update({id: handler})
        break

    return resp()


class IdListParam(BaseModel):
    id: List


@router.post("/delete_qa")
@logger.catch
def delete_qa(param: IdListParam, db: Session = Depends(get_db)):
    """
    删除问答服务
    """
    id = param.id
    db.query(QA).filter(QA.id.in_(id)).delete()
    db.commit()
    return resp()


class IdParam(BaseModel):
    id: int


@router.post("/start_qa")
@logger.catch
def start_qa(param: IdParam, db: Session = Depends(get_db)):
    id = param.id
    qa = db.query(QA).filter(QA.id == id)
    qa.update({
        "is_active": True
    })
    db.commit()
    return resp()


@router.post("/stop_qa")
@logger.catch
def stop_qa(param: IdParam, db: Session = Depends(get_db)):
    id = param.id
    db.query(QA).filter(QA.id == id).update({
        "is_active": False
    })
    db.commit()
    return resp()


class KeywordParam(BaseModel):
    page: int = 1
    pageSize: int = 9
    keyword: str


@router.post("/search_qa", response_model=QaOutSchema)
@logger.catch
def search_qa(param: KeywordParam, db: Session = Depends(get_db)):
    page = param.page
    pageSize = param.pageSize
    keyword = param.keyword
    count = db.query(func.count(QA.id)).scalar()
    ret = db.query(QA).filter(QA.name.like(f"%{keyword}%")).order_by(QA.create_time.desc()).limit(pageSize).offset((int(page) - 1) * int(pageSize)).all()
    return {
        "data": ret,
        "total": count
    }


Field_Mapping = {
    "实体类型": "entity_type",
    "实体实例": "entity_name",
    "属性": "attr",
    "属性值": "attr_value",
}


def splicing_statement(_fields, optional_result, optional_na_result):
    if isinstance(_fields, list):
        # 提取模板中需input_fields替换的部分
        _result = []
        for x in _fields:
            for k, v in x.items():
                if not v:
                    optional_result = optional_na_result
            # 提取模板中需input_fields替换的部分
            k_v = [item for item in x.keys() if item in optional_result]
            item_pattern = re.compile("(.*?)".join(k_v) if len(k_v) > 1 else f"{k_v[0]}(.*)")
            pattern_search = item_pattern.search(optional_result)
            if not pattern_search:
                k_v.reverse()
                item_pattern = re.compile("(.*?)".join(k_v))
                pattern_search = item_pattern.search(optional_result)
            if pattern_search:
                origin_pattern = pattern_search.group(0)
                pattern_result = pattern_search.group(1)
                new_k_v = [f"@{item}@" if len(k_v) > 1 else f"@{item}@{pattern_result}" for item in k_v]
                new_pattern_result = f"{pattern_result}".join(new_k_v)
                # if "entity_name" in x:
                #     x.update({
                #         "entity_name": f"<a href='/{x['entity_type']}/{x['id']}'>{x['entity_name']}</a>"
                #     })
                item_pattern = re.compile("|".join([rf"@{item}@"for item in x.keys()]))
                item_result = item_pattern.sub(
                    lambda m: x[m.group(0).replace("@", "").replace("@", "")],
                    new_pattern_result)
                _result.append(item_result)
        _result = "，".join(_result)
    else:
        # 提取模板中需input_fields替换的部分
        for k, v in _fields.items():
            if not v:
                optional_result = optional_na_result
        k_v = [item for item in _fields.keys() if item in optional_result]
        item_pattern = re.compile("(.*?)".join(k_v) if len(k_v) > 1 else f"{k_v[0]}(.*)")
        pattern_search = item_pattern.search(optional_result)
        if not pattern_search:
            k_v.reverse()
            item_pattern = re.compile("(.*?)".join(k_v))
            pattern_search = item_pattern.search(optional_result)
        _result = ""
        if pattern_search:
            origin_pattern = pattern_search.group(0)
            pattern_result = pattern_search.group(1)
            new_k_v = [f"@{item}@" if len(k_v) > 1 else f"@{item}@{pattern_result}" for item in k_v]
            new_pattern_result = f"{pattern_result}".join(new_k_v)
            # if "entity_name" in _fields:
            #     _fields.update({
            #         "entity_name": f"<a href='/{_fields['entity_type']}/{_fields['id']}'>{_fields['entity_name']}</a>"
            #     })
            _pattern = re.compile("|".join([rf"@{item}@"for item in _fields.keys()]))
            _result = _pattern.sub(
                lambda m: _fields[m.group(0).replace("@", "").replace("@", "")],
                new_pattern_result)

    return optional_result, origin_pattern, _result


def qa_run(qa_template_type, qa_template, new_ai_ret, limit):
    if qa_template_type == "entity":
        qa_na_template = qa_template.replace("[实体示例]", "暂无资料")
    else:
        qa_na_template = qa_template.replace("[属性值]", "暂无资料")
    # problemIntention = config.get("problemIntention")
    # 模板转化语句
    optional_pattern = re.compile("|".join([rf"\[{item}\]" for item in Field_Mapping.keys()]))
    # qa_template
    optional_result = optional_pattern.sub(
        lambda m: Field_Mapping[m.group(0).replace("[", "").replace("]", "")],
        qa_template)
    # qa_template
    optional_na_result = optional_pattern.sub(
        lambda m: Field_Mapping[m.group(0).replace("[", "").replace("]", "")],
        qa_na_template)

    statement_beautification = []
    if qa_template_type != "attribute":
        temp_pattern_search = ""
        pattern_search2 = ""
        _result2_list = []
        for item in new_ai_ret:
            input_fields = item["input_fields"]
            output_fields = item["output_fields"]
            _template1, pattern_search1, _result1 = splicing_statement(input_fields, optional_result,
                                                                       optional_na_result)
            _template2, pattern_search2, _result2 = splicing_statement(output_fields, optional_result,
                                                                       optional_na_result)
            temp_pattern_search = _template2.replace(pattern_search1, _result1)
            _result2_list.append(_result2)
        common_str = os.path.commonprefix(_result2_list)
        new_result2_list = [item.replace(common_str, "") for item in _result2_list if item.replace(common_str, "")]
        if len(_result2_list) < limit:
            _result2_str = common_str + "、".join(new_result2_list)
        else:
            _result2_str = common_str + "、".join(new_result2_list[:limit]) + "等"
        item_result = temp_pattern_search.replace(pattern_search2, _result2_str)
        if not item_result:
            item_result = "机器人不能理解您的问题，请换一种询问方式或询问新的问题！"
        else:
            p_nums = len(_result2_list)
            if len(new_ai_ret) == 1 and not new_ai_ret[0]["output_fields"]["entity_name"]:
                p_nums = 0
            item_result += f"共计{p_nums}人。"
        statement_beautification.append(item_result)
        answer = statement_beautification[:limit] if statement_beautification else []
    else:
        for item in new_ai_ret:
            input_fields = item["input_fields"]
            output_fields = item["output_fields"]
            _template1, pattern_search1, _result1 = splicing_statement(input_fields, optional_result,
                                                                       optional_na_result)
            _template2, pattern_search2, _result2 = splicing_statement(output_fields, optional_result,
                                                                       optional_na_result)
            item_result = _template2.replace(pattern_search1, _result1).replace(pattern_search2, _result2)
            statement_beautification.append(item_result)
        answer = statement_beautification[:limit] if statement_beautification else ["机器人不能理解您的问题，请换一种询问方式或询问新的问题！"]

    return answer


class IdParam(BaseModel):
    id: int
    question: str


@router.post("/qa_service")
@logger.catch
def qa_service(param: IdParam, db: Session = Depends(get_db)):
    try:
        id = param.id
        question = param.question
        if not question:
            return resp(data={"answer": ["问题不能为空！"]}, msg="问题不能为空！", code=400)
        ret = db.query(QA).filter(QA.id == id, QA.is_active.is_(True)).first()
        if not ret:
            return resp(data={"answer": ["请确认此问答服务是否启动！"]}, msg="请确认此问答服务是否启动！", code=400)
        config_list = json.loads(ret.config)
        # ai问答结果
        handler = qa_Cache.qa_cache.get(id)
        if handler is None:
            item_config = config_list[0]
            handler = default_handler
            qa_model = item_config.get("qaModel")
            if qa_model is not None:
                qa_model_dict = json.loads(qa_model)
                qa_model_path = qa_model_dict.get("versions")[0]["path"]
                handler.classifier.change_ner(ner_path=qa_model_path)
            qa_Cache.qa_cache.update({id: handler})
        ai_ret = handler.chat_main(question)
        result = {
            "query_result": ai_ret
        }
        intent = str(ai_ret[0].get("intent")) if ai_ret else "1"
        new_ai_ret = copy.deepcopy(ai_ret)
        # 获取问答模板
        config_dict = dict()
        for item in config_list:
            if item.get("intentionType") == "entity":
                config_dict["2"] = item
            if item.get("intentionType") == "attribute":
                config_dict["1"] = item
        config = config_dict.get(intent)
        if not config:
            return resp(data={"answer": ["此问答服务没有配置该类问题的意图模板！"]}, msg="此问答服务没有配置该类问题的意图模板！", code=400)
        limit = config.get("retLimit")
        qa_template_type = config.get("intentionType")
        qa_template = config.get("templateValueStr")
        fold_qa_template = config.get("foldQaTemplateValueStr")
        not_fold_qa_template = config.get("notFoldQaTemplateValueStr")

        if fold_qa_template or not_fold_qa_template:
            if fold_qa_template:
                answer = qa_run(qa_template_type, fold_qa_template, new_ai_ret, limit)
                result.update({"fold_answer": answer})
            if not_fold_qa_template:
                answer = qa_run(qa_template_type, not_fold_qa_template, new_ai_ret, limit)
                result.update({"not_fold_answer": answer})
        else:
            if qa_template:
                answer = qa_run(qa_template_type, qa_template, new_ai_ret, limit)
                result.update({"answer": answer})
    except Exception as e:
        import traceback
        print(traceback.format_exc())
        raise e
    return resp(data=result)
