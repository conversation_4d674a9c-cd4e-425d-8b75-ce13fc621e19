import torch
from torch.utils.data.dataloader import DataLoader
from torch.nn.utils.rnn import pad_sequence
from transformers import AutoTokenizer, PreTrainedTokenizer
from kbnlp.mlt.common.vocab import Vocab
from kbnlp.mlt.data_transform import TransformList, NormalizeCharacter, NamedTransform, VocabDict
from kbnlp.mlt.dataset import BaseDataset, SortingSamplerBuilder, pad_data
from kbnlp.mlt.util.tensor_util import lengths_to_mask
from kbnlp.mlt.task import Task
from kbnlp.mlt.common.metric import F1
from kbnlp.mlt.util import merge_locals_kwargs, ids_to_tags
from kbnlp.mlt.util.tensor_util import pick_tensor_for_each_token
from kbnlp.mlt.layers.decoder.linear_decoder import LinearCRFDecoder
from functools import partial
from typing import Union, Iterable, Dict, Optional, List
from itertools import permutations


def read_cws_tsv_data(path):
    with open(path, mode='r', encoding='utf-8') as fr:
        data = []
        for line in fr:
            words_list = line.strip().split()
            if words_list:
                data.append(words_list)
    return data


class SegmentDataset(BaseDataset):
    def __init__(self, data, transform_list: TransformList = None):
        super(SegmentDataset, self).__init__(data, transform_list)


class Segment(Task):

    def __init__(self,
                 trn=None,
                 dev=None,
                 tst=None,
                 batch_size=None,
                 batch_max_tokens=None,
                 eval_batch_size=None,
                 eval_batch_max_tokens=None,
                 # dependencies: str = None,
                 lr=None,
                 separate_optimizer=False,
                 cls_is_bos=False,
                 sep_is_eos=False,
                 **kwargs,
                 ):
        super(Segment, self).__init__(**merge_locals_kwargs(locals(), kwargs, excludes=['self', 'kwargs', '__class__']))
        self.vocabs = VocabDict()  # 至少需要一个tag的vocab

    def build_model(self, encoder_size, task_name, **kwargs):
        return LinearCRFDecoder(encoder_size, num_labels=len(self.vocabs['%s_tag' % task_name]), crf=False)

    def build_sample(self, data):
        examples = []
        for i, exa in enumerate(data):
            examples.append({'original_input': exa})
            examples[-1]['_id'] = i
            if isinstance(exa, list):
                examples[-1]['token'] = exa
        return examples

    def build_transformList(self, mode, task_name, **kwargs):
        if mode == 'train' or mode == 'eval':
            return TransformList(partial(generate_tags_for_subtokens, task_name=task_name, tagging_scheme='BMES'),
                                 self.vocabs)
        else:  # 预测时，且首任务是seg（提供分好词的列表，且指定参数is_split_into_words为False首任务才会是seg）才会进入该分支。
            def get_force_start(sample: dict):
                token_tail = sample.pop('token_tail', None)  # 这里token_span其实只是group_span,，并不一定会让一个span内的子词组成最终的token，所有将其删除。
                # if成立表示预测是传入的是List[str]，且首任务是分词。否则，传入的是List[List[str]]且指定参数is_split_into_words为False。
                # 若传入的是List[List[str]]且指定参数is_split_into_words为True时,首任务不是分词任务，也就不会调用本方法。所以可以利用是否包含
                # token_tail来区分前两种情况。
                if token_tail is None:
                    return sample
                force_start = [0] * (sample['subtoken_len'] - 2)
                for tail in token_tail[: -2]:
                    force_start[tail] = 1  # force_start[tail + 1 - 1] = 1
                sample['force_start'] = force_start
                return sample
            return TransformList(get_force_start)

    def build_dataset(self, data, transform_list, **kwargs):
        dataset = SegmentDataset(data, transform_list=transform_list)
        return dataset

    def build_dataloader(self, dataset, batch_size=None, batch_max_tokens=None, shuffle=False, generator=None, gradient_accumulation=1,
                         use_effective_tokens=False, **kwargs):
        return DataLoader(dataset=dataset,
                          batch_sampler=SortingSamplerBuilder.build([data['subtoken_len'] for data in dataset],
                                                                    batch_size, batch_max_tokens, use_effective_tokens,
                                                                    shuffle, generator, gradient_accumulation),
                          collate_fn=partial(SegmentDataset.collate_fn, padding_value=0))

    def build_criterion(self, reduction='mean', **kwargs):
        return torch.nn.CrossEntropyLoss(reduction=reduction)

    def build_optimizer(self, decoder, **kwargs):
        raise NotImplementedError('该任务没有实现独立的优化器。')

    def compute_loss(self, task_name, batch, out, mask, criterion):
        y = batch['%s_tag_ids' % task_name]
        loss = criterion(out[mask], y[mask])
        return loss

    def build_metric(self, **kwargs):
        return F1()

    def update_metric(self, prediction, batch, metric, task_name):
        gold_tags = batch['%s_tag' % task_name]
        gold_subtoken_interval = []
        for tags in gold_tags:
            gold_subtoken_interval.append(bmes_to_interval(tags))
        for pred, gold in zip(prediction, gold_subtoken_interval):
            metric(set(pred), set(gold))

    def feed_batch(self,
                   mode,
                   h: torch.FloatTensor,
                   batch: Dict,
                   output_dict,
                   decoder: torch.nn.Module,
                   task_name):
        mask = batch['subtoken_input_ids'].ne(0)[:, 2:]
        # mask = lengths_to_mask(batch['subtoken_len'] - 2)
        logits = decoder(h, batch=batch, mask=mask)
        output_dict[task_name] = {
            'output': logits,
            'mask': mask
        }

    def decode_output(self, mode, output_dict, batch, model, task_name, **kwargs):
        output_per_task = output_dict[task_name]
        output = output_per_task['output']
        mask = output_per_task['mask']
        predict_tag_ids = torch.argmax(output, dim=-1)
        predict_tags = ids_to_tags(predict_tag_ids.tolist(), mask.sum(1).tolist(), self.vocabs['%s_tag' % task_name])
        token_interval = []
        for tags, force_start in zip(predict_tags, batch.get('force_start', [None] * len(predict_tags))):
            token_interval.append(bmes_to_interval(tags, force_start=force_start))
        output_per_task['prediction'] = token_interval

    def prediction_to_result(self, pred, vocab, batch, output_dict, task_name, **kwargs):
        tokens = self.intervals_to_tokens(pred, batch, **kwargs)
        if isinstance(tokens, tuple):
            return list(zip(*tokens))
        else:
            return tokens

    def post_transform(self, task_name, batch, output_dict, device, **kwargs):
        """
        为其它任务做准备
        :param task_name:
        :param batch:
        :param output_dict:
        :param device:
        :param kwargs:
        :return:
        """
        tokens = output_dict[task_name]['result']
        if isinstance(tokens[0], tuple):  # 返回了二元组，(token, token_group)
            batch['token'], batch['token_group'] = tuple(zip(*tokens))
        else:
            batch['token'] = tokens
        batch['token_len'] = torch.tensor([len(res) + 2 for res in batch['token']], dtype=torch.long,
                                          device=device)
        token_tail = []
        batch_prediction = output_dict[task_name]['prediction']
        for length, prediction in zip(batch['subtoken_len'].tolist(), batch_prediction):
            token_tail.append([0] + [r for (l, r) in prediction] + [length-1])
        batch['token_tail'] = pad_data(token_tail, pad=0, dtype=torch.long, device=device)
        output_dict['hidden'] = pick_tensor_for_each_token(h=output_dict['hidden'], token_tail=batch['token_tail'],
                                                           average_subwords=True)
        # TODO: 根据后续任务删除不需要的值。
        output_dict.pop(task_name)

    def intervals_to_tokens(self, intervals, batch, return_group_info=False):  # return_group_info为True则返回一个二元组，多了一个token_group,记录每个group由那几个单词组成。
        batch_tokens = []
        batch_token_groups = []
        for intervals_per_sent, sub_tokens, force_s in zip(intervals, batch['subtoken'], batch.get('force_start', [None] * len(intervals))):
            token_groups = []
            if force_s is None:
                tokens = [''.join(sub_tokens[interval[0]:interval[1]]) for interval in intervals_per_sent]
            else:  # 对应于输入为List[List[str]]且is_split_into_words参数为False。bmes_to_interval方法会根据force_start参数修改bmes的label列表。
                tokens = []
                token_per_group = []
                pre_group_end = 0
                length = len(force_s)  # 子词个数。
                for interval in intervals_per_sent:
                    token_per_group.append(''.join(sub_tokens[interval[0]: interval[1]]))
                    if interval[1] >= length or force_s[interval[1]]:
                        tokens.extend(token_per_group)
                        if return_group_info:
                            token_groups.append((pre_group_end, pre_group_end + len(token_per_group)))
                            pre_group_end += len(token_per_group)
                        token_per_group = []
            batch_tokens.append(tokens)
            if token_groups:
                batch_token_groups.append(token_groups)
        return batch_tokens if not return_group_info else (batch_tokens, batch_token_groups)


def bmes_to_interval(tags=0, force_start: Optional[List[int]] = None):
    """
    :param tags: 子词的bems标注结果
    :param force_start: 默认为None.可以是一个和tags长度相同的整型列表，值为0或者1.如果为1表示对应的子词为一个单词的开始。
    :return: 根据子词的bems标注结果，得到一个代表token的区间。不考虑cls和sep。第一个子词的位置用0表示。左闭右开。
    """
    result = []
    offset = 0
    pre_offset = 0
    if force_start is None:
        force_start = [0] * len(tags)
    for t, force_s in zip(tags[1:], force_start[1:]):
        offset += 1
        if t == 'B' or t == 'S' or force_s:
            result.append((pre_offset, offset))
            pre_offset = offset
    if offset != len(tags):
        result.append((pre_offset, len(tags)))
    return result


def generate_tags_for_subtokens(sample: dict, task_name, tagging_scheme='BMES'):
    token_tail = sample.get('token_tail', None)
    if token_tail:
        if tagging_scheme == 'BMES':
            word_size = [tail - pre_tail for tail, pre_tail in zip(token_tail[1: -1], token_tail)]
            sample['%s_tag' % task_name] = word_size_to_bmes(word_size)
        elif tagging_scheme == 'BI':
            raise NotImplementedError(f'Unsupported tagging scheme {tagging_scheme}.')
            # sample['tag'] = words_to_bi(subtokens_group)
        else:
            raise NotImplementedError(f'Unsupported tagging scheme {tagging_scheme}.')
    return sample


# def offsets_to_subtokens(tokens, token_subtoken_offsets, token_input_tokens_group):
#     results = []
#     if token_input_tokens_group:
#         for subtokens, token in zip(token_input_tokens_group, tokens):
#             for b, e in subtokens:
#                 results.append(token[b:e])
#     else:
#         offset = -1  # BERT produces 'ᄒ', '##ᅡ', '##ᆫ' for '한' and they share the same span
#         for b, e in token_subtoken_offsets:
#             if b < offset:
#                 continue
#             offset = e
#             results.append(tokens[b:e])
#     return results


def word_size_to_bmes(word_size):
    tags = []
    for size in word_size:
        if not size:
            raise ValueError("can't contains None or zero-length word.")
        if size == 1:
            tags.append('S')
        else:
            tags.extend(['B'] + ['M'] * (size - 2) + ['E'])
    return tags


if __name__ == '__main__':
    print('finish.')
