import time

from django.db import models


# Create your models here.

class BaseClass(models.Model):
    create_time = models.IntegerField(default=0)  # 创建时间
    update_time = models.IntegerField(default=0)  # 更新时间

    def __init__(self):
        _time = int(time.time())
        self._create_time = _time
        self._update_time = _time
    # 抽象类，不创建实际数据库
    class Meta:
        abstract = True


class DataSet(BaseClass,models.Model):
    dataset_name = models.CharField(max_length=255)  # 数据集名称
    create_user = models.CharField(max_length=32,null=True)  # 创建人
    dataset_type = models.IntegerField(default=301)  # 数据集类型
    # 数据集类型
    path = models.CharField(max_length=255,null=True) # 路径

    def __init__(self, *args, **kwargs):
        "重写__init__方法"
        super(BaseClass, self).__init__(*args, **kwargs)
        super(DataSet, self).__init__()


    def save(self, *args, **kwargs):
        if not self.create_time:
            self.create_time = self._create_time
        self.update_time=self.update_time
        super(DataSet, self).save(*args, **kwargs)

class RawText(models.Model):
    text = models.TextField()  # 文本内容
    raw_status = models.IntegerField(default=1)
    tesing_user = models.CharField(max_length=32, default="root")  # 质检的用户
    data_id = models.ForeignKey(to="DataSet", to_field="id", on_delete=models.CASCADE)

#
# class DataDict(models.Model):
#     param_name = models.CharField(max_length=255) # 需要展示的参数名称
#     pid = models.IntegerField(null=True) # 参数的id
#     tid = models.IntegerField(null=True) # 参数的类型
#     is_open = models.IntegerField(default=2) # 默认值为1,既为开启


class Tag(models.Model):
    tag_name = models.CharField(max_length=255) # 标签名称


class DatasetTag(models.Model):
    color = models.CharField(max_length=255)  # 标签颜色
    shoutcut_key = models.CharField(max_length=255)  # 快捷键
    data_id = models.ForeignKey(to="DataSet", to_field="id", on_delete=models.CASCADE)
    tag_id = models.ForeignKey(to="Tag", to_field="id", on_delete=models.CASCADE)


# 标签，数据库
class TagIntention(models.Model):
    data_id = models.ForeignKey(to="DataSet", to_field="id", on_delete=models.CASCADE)
    tag_id = models.ForeignKey(to="Tag", to_field="id", on_delete=models.CASCADE)


class RawIntention(models.Model):
    raw = models.ForeignKey(to="RawText",to_field="id", on_delete=models.CASCADE)
    tag_type = models.ForeignKey(to="TagIntention",to_field="id",on_delete=models.CASCADE)


class TagSlot(models.Model):
    slot = models.CharField(max_length=255)
    tagintentions = models.ForeignKey(to="TagIntention", to_field="id", on_delete=models.CASCADE,default=1)


class SlotText(models.Model):
    slot = models.ForeignKey(to="TagSlot",to_field="id", on_delete=models.CASCADE)
    raw = models.ForeignKey(to="RawText",to_field="id", on_delete=models.CASCADE)
    tag_begin_x = models.IntegerField(default=0)  # 开始
    tag_end_x = models.IntegerField(default=0)  # 纵坐标位置
    tag_begin_width = models.IntegerField(default=0)  # 结束
    tag_end_width = models.IntegerField(default=0)  # 纵坐标位置
    tag_line_pos = models.IntegerField(default=0)  # 行位置
    tag_line = models.IntegerField(default=0)  # 行数
    tag_begin_line = models.IntegerField(default=0)  # 行数
    tag_end_line = models.IntegerField(default=0)  # 行数
    tag_begin_line_pos = models.IntegerField(default=0)
    tag_end_line_pos = models.IntegerField(default=0)
    tag_text_begin = models.IntegerField(default=0)
    tag_text_end = models.IntegerField(default=0)


class TagText(models.Model):
    rawtext = models.ForeignKey(to="RawText", to_field="id",on_delete=models.CASCADE)
    tag = models.ForeignKey(to="Tag", to_field="id",on_delete=models.CASCADE)
    tag_begin_x = models.IntegerField(default=0) # 开始
    tag_end_x = models.IntegerField(default=0) # 纵坐标位置
    tag_begin_width = models.IntegerField(default=0)  # 结束
    tag_end_width = models.IntegerField(default=0)  # 纵坐标位置
    tag_line_pos = models.IntegerField(default=0) # 行位置
    tag_line = models.IntegerField(default=0) # 行数
    tag_begin_line = models.IntegerField(default=0)  # 行数
    tag_end_line = models.IntegerField(default=0)  # 行数
    tag_begin_line_pos = models.IntegerField(default=0)
    tag_end_line_pos = models.IntegerField(default=0)
    tag_text_begin = models.IntegerField(default=0)
    tag_text_end = models.IntegerField(default=0)


class Relation(models.Model):
    relation_name = models.CharField(max_length=255) # 关系名称
    tag_begin = models.ForeignKey(to="Tag", to_field="id", on_delete=models.CASCADE,related_name="tag_begin") # 标签前后
    tag_end = models.ForeignKey(to="Tag", to_field="id", on_delete=models.CASCADE,related_name="tag_end") # 结束内容
    data_id = models.ForeignKey(to="DataSet", to_field="id", on_delete=models.CASCADE) # 数据集


class RelationText(models.Model):
    relation_id = models.ForeignKey(to="Relation", to_field="id", on_delete=models.CASCADE)
    relation_begin = models.ForeignKey(to="TagText", to_field="id", on_delete=models.CASCADE,related_name="relation_begin")
    relation_end = models.ForeignKey(to="TagText", to_field="id", on_delete=models.CASCADE,related_name="relation_end")
    rawtext_id = models.ForeignKey(to="RawText", to_field="id", on_delete=models.CASCADE,default=1)
    relation_begin_x = models.IntegerField(default=0)  # 开始
    relation_end_x = models.IntegerField(default=0)  # 结束
    relation_begin_line = models.IntegerField(default=0)  # 开始的行数
    relation_end_line = models.IntegerField(default=0) # 结束的行数
    relation_begin_pos = models.IntegerField(default=0) # 开始位置
    relation_end_pos = models.IntegerField(default=0) # 结束位置


class Event(models.Model):
    event_name = models.CharField(max_length=255) # 关系名称
    dataset = models.ForeignKey(to="DataSet", to_field="id", on_delete=models.CASCADE)  # 数据集


class EventTag(models.Model):
    event = models.ForeignKey(to="Event", to_field="id", on_delete=models.CASCADE)
    tag = models.ForeignKey(to="Tag", to_field="id", on_delete=models.CASCADE)


# class EventText(models.Model):
#     event = models.ForeignKey(to="Event", to_field="id", on_delete=models.CASCADE)
#     event_tag = models.ForeignKey(to="TagText", to_field="id", on_delete=models.CASCADE)
#     rawtext_id = models.ForeignKey(to="RawText", to_field="id", on_delete=models.CASCADE, default=1)


class EventText(models.Model):
    event_id = models.ForeignKey(to="Event", to_field="id", on_delete=models.CASCADE)
    event_begin = models.ForeignKey(to="TagText", to_field="id", on_delete=models.CASCADE,related_name="event_begin")
    event_end = models.ForeignKey(to="TagText", to_field="id", on_delete=models.CASCADE, related_name="event_end")
    rawtext_id = models.ForeignKey(to="RawText", to_field="id", on_delete=models.CASCADE, default=1)
    event_begin_x = models.IntegerField(default=0)  # 开始
    event_end_x = models.IntegerField(default=0)  # 结束
    event_begin_line = models.IntegerField(default=0)  # 开始的行数
    event_end_line = models.IntegerField(default=0)  # 结束的行数
    event_begin_pos = models.IntegerField(default=0)  # 开始位置
    event_end_pos = models.IntegerField(default=0)  # 结束位置


class RawImage(models.Model):
    path = models.TextField()  # 图像路径
    name = models.CharField(max_length=255) # 图像名称
    raw_status = models.IntegerField(default=0)  # 是否删除，1是0否
    tesing_user = models.CharField(max_length=32, default=None)  # 质检的用户
    imgage_width = models.IntegerField(default=1)  # 图像宽度
    imgage_height = models.IntegerField(default=1)  # 图像高度
    data_id = models.ForeignKey(to="DataSet", to_field="id", on_delete=models.CASCADE)


class TagImage(models.Model):
    image = models.ForeignKey(to="RawImage", to_field="id", on_delete=models.CASCADE)
    tag = models.ForeignKey(to="Tag", to_field="id", on_delete=models.CASCADE)
    param = models.TextField(default="")  # 参数
    area = models.FloatField(default=0.0)  # 图像面积
    segmentation = models.TextField(default=None) # segmentation的值
    bbox = models.CharField(max_length=255,default="") #bbox的值


class TagImageType(models.Model):
    image = models.ForeignKey(to="RawImage", to_field="id", on_delete=models.CASCADE)
    tag = models.ForeignKey(to="Tag", to_field="id", on_delete=models.CASCADE)


class TagTextType(models.Model):
    text = models.ForeignKey(to="RawText", to_field="id", on_delete=models.CASCADE)
    tag = models.ForeignKey(to="Tag", to_field="id", on_delete=models.CASCADE)


class RawFilesImage(models.Model):
    path = models.TextField()  # 图像路径
    name = models.CharField(max_length=255) # 图像名称
    raw_status = models.IntegerField(default=0)  # 是否删除，1是0否
    testing_user = models.CharField(max_length=32, default="root")  # 质检的用户
    image_width = models.IntegerField(default=1)  # 图像宽度
    image_height = models.IntegerField(default=1)  # 图像高度
    image_id = models.IntegerField(default=1) # 外接图像id
    aidp_id = models.CharField(max_length=255,default="")
    data_id = models.ForeignKey(to="DataSet", to_field="id", on_delete=models.CASCADE)


class FileBox(models.Model):
    raw_file = models.ForeignKey(to="RawFilesImage", to_field="id", on_delete=models.CASCADE)
    text = models.CharField(max_length=255)
    bbox = models.CharField(max_length=255)
    bbox_id = models.IntegerField(default=0)


class TextBox(models.Model):
    bbox = models.ForeignKey(to="FileBox",to_field="id", on_delete=models.CASCADE)
    bbox_begin = models.IntegerField(default=0)
    bbox_end = models.IntegerField(default=0)


class MarkBox(models.Model):
    tag = models.ForeignKey(to="Tag",to_field="id", on_delete=models.CASCADE)
    textbox = models.ForeignKey(to="TextBox",to_field="id", on_delete=models.CASCADE)
    argument = models.TextField(default="{}")


class RelationImage(models.Model):
    relation = models.ForeignKey(to="Relation", to_field="id", on_delete=models.CASCADE)
    begin = models.ForeignKey(to="MarkBox", to_field="id", on_delete=models.CASCADE, related_name="relation_image_begin")
    end = models.ForeignKey(to="MarkBox", to_field="id", on_delete=models.CASCADE, related_name="relation_image_end")
    rawimage = models.ForeignKey(to="RawFilesImage", to_field="id", on_delete=models.CASCADE, default=1)
