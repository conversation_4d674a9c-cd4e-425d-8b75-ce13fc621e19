from transformers import PretrainedConfig


class ErnieLayoutConfig(PretrainedConfig):

    model_type = 'ernie-layout'

    def __init__(self,
                 attention_probs_dropout_prob=0.1,
                 bos_token_id=0,
                 coordinate_size=128,
                 eos_token_id=2,
                 gradient_checkpointing=False,
                 has_relative_attention_bias=True,
                 has_spatial_attention_bias=True,
                 has_visual_segment_embedding=False,
                 hidden_act='gelu',
                 hidden_dropout_prob=0.1,
                 hidden_size=768,
                 image_feature_pool_shape=[7, 7, 256],
                 initializer_range=0.02,
                 intermediate_size=3072,
                 layer_norm_eps=1e-12,
                 max_2d_position_embeddings=1024,
                 max_position_embeddings=514,
                 max_rel_2d_pos=256,
                 max_rel_pos=128,
                 num_attention_heads=12,
                 num_hidden_layers=12,
                 output_past=True,
                 pad_token_id=1,
                 shape_size=128,
                 rel_2d_pos_bins=64,
                 rel_pos_bins=32,
                 type_vocab_size=100,
                 vocab_size=250002,
                 **kwargs
                 ):
        super(ErnieLayoutConfig, self).__init__(**kwargs)
        self.attention_probs_dropout_prob = attention_probs_dropout_prob
        self.bos_token_id = bos_token_id
        self.coordinate_size = coordinate_size
        self.eos_token_id = eos_token_id
        self.gradient_checkpointing = gradient_checkpointing
        self.has_relative_attention_bias = has_relative_attention_bias
        self.has_spatial_attention_bias = has_spatial_attention_bias
        self.has_visual_segment_embedding = has_visual_segment_embedding
        self.hidden_act = hidden_act
        self.hidden_dropout_prob = hidden_dropout_prob
        self.hidden_size = hidden_size
        self.image_feature_pool_shape = image_feature_pool_shape
        self.initializer_range = initializer_range
        self.intermediate_size = intermediate_size
        self.layer_norm_eps = layer_norm_eps
        self.max_2d_position_embeddings = max_2d_position_embeddings
        self.max_position_embeddings = max_position_embeddings
        self.max_rel_2d_pos = max_rel_2d_pos
        self.max_rel_pos = max_rel_pos
        self.num_attention_heads = num_attention_heads
        self.num_hidden_layers = num_hidden_layers
        self.output_past = output_past
        self.pad_token_id = pad_token_id
        self.shape_size = shape_size
        self.rel_2d_pos_bins = rel_2d_pos_bins
        self.rel_pos_bins = rel_pos_bins
        self.type_vocab_size = type_vocab_size
        self.vocab_size = vocab_size

