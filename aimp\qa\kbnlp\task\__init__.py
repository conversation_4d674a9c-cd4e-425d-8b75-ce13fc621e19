# coding:utf-8
# Copyright (c) 2021  PaddlePaddle Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License"
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
import inspect
import os
import abc
import math
from abc import abstractmethod
from multiprocessing import cpu_count
from typing import Union, Optional, List

from .utils import cut_chinese_sent
from qa.kbnlp import KBNLP_HOME


class Task(metaclass=abc.ABCMeta):
    """
    The meta classs of task in Taskflow. The meta class has the five abstract function,
        the subclass need to inherit from the meta class.
    Args:
        task(string): The name of task.
        model(string): The model name in the task.
        kwargs (dict, optional): Additional keyword arguments passed along to the specific task.
        home_path (string):指定kbnlp存放tasks相关文件的家目录。
        task_path (string): 指定任务模型加载的路径。
    """

    def __init__(self, model: str, device='cpu', **kwargs):
        try:
            # TODO: 添加batch_size,split_sentence,max_seq_len属性，以供后续对实例直接修改。
            self.model = model  # model name.
            self.task = os.path.splitext(os.path.basename(inspect.getfile(self.__class__)))[0]
            self._usage = ""
            self._model = None  # The dygraph model instantce
            self._device = device
            self._max_seq_len = kwargs[
                'max_seq_len'] if 'max_seq_len' in kwargs else 512
            self._batch_size = kwargs[
                'batch_size'] if 'batch_size' in kwargs else 64
            self._split_sentence = kwargs[
                'split_sentence'] if 'split_sentence' in kwargs else False
            # self._infer_precision = kwargs[
            #     'precision'] if 'precision' in kwargs else 'fp32'
            # The root directory for storing tasks related files, default to ~/.kbnlp.
            self._home_path = kwargs[
                'home_path'] if 'home_path' in kwargs else KBNLP_HOME
            if 'task_path' in kwargs and kwargs['task_path']:
                self._task_path = kwargs['task_path']
            else:
                self._task_path = os.path.join(self._home_path, "tasks",
                                               self.task, self.model)
            self._check_task_files()
            self._construct_tokenizer()
            self._check_predictor_type()
            self._get_inference_model()
        except Exception as e:
            import traceback
            print(traceback.format_exc())

    @abstractmethod
    def _construct_model(self):
        """
        Construct the inference model for the predictor.
        """

    @abstractmethod
    def _construct_tokenizer(self):
        """
        Construct the tokenizer for the predictor.
        """

    @abstractmethod
    def _preprocess(self, inputs: Union[str, List[str]], padding=True, add_special_tokens=True, **kwargs):
        """
        Transform the raw text to the model inputs, two steps involved:
           1) Transform the raw text to token ids.
           2) Generate the other model inputs from the raw text and token ids.
        inputs:__call__方法的输入
        """

    @abstractmethod
    def _run_model(self, inputs):
        """
        inputs: _preprocess方法的输出
        Run the task model from the outputs of the `_tokenize` function.
        """

    @abstractmethod
    def _postprocess(self, inputs):
        """
        The model output is the logits and pros, this function will convert the model output to raw text.
        inputs:_run_model方法的输出
        """

    def _check_task_files(self):
        """
        Check files required by the task.
        """
        pass

    def _check_predictor_type(self):
        pass
        #  检测device， fp16


        # if paddle.get_device() == 'cpu' and self._infer_precision == 'fp16':
        #     logger.info(
        #         "The inference precision is change to 'fp32', 'fp16' inference only takes effect on gpu."
        #     )
        # else:
        #     if self._infer_precision == 'fp16':
        #         try:
        #             import onnx
        #             import onnxruntime as ort
        #             import paddle2onnx
        #             from onnxconverter_common import float16
        #             self._predictor_type = 'onnxruntime'
        #         except:
        #             logger.info(
        #                 "The inference precision is change to 'fp32', please install the dependencies that required for 'fp16' inference, pip install onnxruntime-gpu onnx onnxconverter-common paddle2onnx"
        #             )

    def _prepare_onnx_mode(self):
        pass

    def _get_inference_model(self):
        try:
            self._construct_model()
            # if hasattr(self._model, 'script') and callable(getattr(self._model, 'script', None)):
            #     print("self._model。。。qqqq")
            #     print("self._model.script()。。。qqqq")
            #     self._model = self._model.script()
            self.to(self.device)
        except Exception as e:
            import traceback
            print(traceback.format_exc())

    @property
    def device(self):
        return self._device

    def to(self, device):
        self._device = device
        self._model.to(self.device)

    def _check_input_text(self, inputs):
        if isinstance(inputs, str):
            if len(inputs.strip()) == 0:
                raise ValueError(
                    "Invalid inputs, input text should not be empty text, please check your input.".
                        format(type(inputs)))
            inputs = [inputs]
        elif isinstance(inputs, list):
            if not (isinstance(inputs[0], str) and len(inputs[0].strip()) > 0):
                raise TypeError(
                    "Invalid inputs, input text should be list of str, and first element of list should not be empty text.".
                        format(type(inputs[0])))
        else:
            raise TypeError(
                "Invalid inputs, input text should be str or list of str, but type of {} found!".format(type(inputs)))
        return inputs

    def _auto_splitter(self, input_texts, max_text_len, split_sentence=False):
        """
        Split the raw texts automatically for model inference.
        Args:
            input_texts (List[str]): input raw texts.
            max_text_len (int): cutting length.
            split_sentence (bool): If True, sentence-level split will be performed.
        return:
            short_input_texts (List[str]): the short input texts for model inference.
            input_mapping (dict): mapping between raw text and short input texts.
        """
        input_mapping = {}
        short_input_texts = []
        cnt_org = 0
        cnt_short = 0
        for text in input_texts:
            if not split_sentence:
                sens = [text]
            else:
                sens = cut_chinese_sent(text)
            for sen in sens:
                lens = len(sen)
                if lens <= max_text_len:
                    short_input_texts.append(sen)
                    if cnt_org not in input_mapping.keys():
                        input_mapping[cnt_org] = [cnt_short]
                    else:
                        input_mapping[cnt_org].append(cnt_short)
                    cnt_short += 1
                else:
                    temp_text_list = [
                        sen[i:i + max_text_len]
                        for i in range(0, lens, max_text_len)
                    ]
                    short_input_texts.extend(temp_text_list)
                    short_idx = cnt_short
                    cnt_short += math.ceil(lens / max_text_len)
                    temp_text_id = [
                        short_idx + i for i in range(cnt_short - short_idx)
                    ]
                    if cnt_org not in input_mapping.keys():
                        input_mapping[cnt_org] = temp_text_id
                    else:
                        input_mapping[cnt_org].extend(temp_text_id)
            cnt_org += 1
        return short_input_texts, input_mapping

    def _auto_joiner(self, short_results, input_mapping, is_dict=False):
        """
        Join the short results automatically and generate the final results to match with the user inputs.
        Args:
            short_results (List[dict] / List[List[str]] / List[str]): input raw texts.
            input_mapping (dict): cutting length.
            is_dict (bool): whether the element type is dict, default to False.
        return:
            short_input_texts (List[str]): the short input texts for model inference.
        """
        concat_results = []
        elem_type = {} if is_dict else []
        for k, vs in input_mapping.items():
            single_results = elem_type
            for v in vs:
                if len(single_results) == 0:
                    single_results = short_results[v]
                elif isinstance(elem_type, list):
                    single_results.extend(short_results[v])
                elif isinstance(elem_type, dict):
                    for sk in single_results.keys():
                        if isinstance(single_results[sk], str):
                            single_results[sk] += short_results[v][sk]
                        else:
                            single_results[sk].extend(short_results[v][sk])
                else:
                    raise ValueError(
                        "Invalid element type, the type of results "
                        "for each element should be list of dict, "
                        "but {} received.".format(type(single_results)))
            concat_results.append(single_results)
        return concat_results

    # def help(self):
    #     """
    #     Return the usage message of the current task.
    #     """
    #     print("Examples:\n{}".format(self._usage))

    def __call__(self, inputs: Union[str, List[str]], **kwargs):
        inputs = self._preprocess(inputs, **kwargs)
        inputs = self._run_model(inputs)
        results = self._postprocess(inputs)
        return results



