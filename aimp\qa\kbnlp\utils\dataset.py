import torch
import numpy as np
from torch.utils.data import Dataset
from torch.nn.utils.rnn import pad_sequence


class StepLevelDependencyDataSet(Dataset):
    def __init__(self, inputs, outputs):

        self.word_inputs = torch.cat(inputs[0])
        self.tag_inputs = torch.cat(inputs[1])
        self.label_inputs = torch.cat(inputs[2])
        self.outputs = torch.cat(outputs)
        print('转移step总数为：%d。' % len(self))

    def __len__(self):  # 返回语料库所有的时间步总和
        return len(self.word_inputs)

    def __getitem__(self, index):
        return (self.word_inputs[index], self.tag_inputs[index], self.label_inputs[index]), self.outputs[index]

    def shuffle(self):
        indices = list(range(len(self)))
        np.random.shuffle(indices)
        self.word_inputs = [self.word_inputs[i] for i in indices]
        self.tag_inputs = [self.tag_inputs[i] for i in indices]
        self.label_inputs = [self.label_inputs[i] for i in indices]
        self.outputs = [self.outputs[i] for i in indices]

    @staticmethod
    def collate_batch(batch):
        inputs, targets = (i for i in zip(*batch))
        x_w, x_t, x_l = (torch.stack(x) for x in [i for i in zip(*inputs)])
        targets = torch.tensor(targets)
        return (x_w, x_t, x_l), targets


class SentenceLevelDependencyDataSet(Dataset):
    def __init__(self, inputs, outputs):
        self.word_inputs = inputs[0]
        self.tag_inputs = inputs[1]
        self.label_inputs = inputs[2]
        self.outputs = outputs
        assert len(self.word_inputs) == len(self.tag_inputs) == len(self.label_inputs) == len(self.outputs)

    def __len__(self):  # 返回句子数
        return len(self.word_inputs)

    def __getitem__(self, sentence_index):
        return (self.word_inputs[sentence_index], self.tag_inputs[sentence_index], self.label_inputs[sentence_index]), \
               self.outputs[sentence_index]

    def shuffle(self):
        indices = list(range(len(self)))
        np.random.shuffle(indices)
        self.word_inputs = [self.word_inputs[i] for i in indices]
        self.tag_inputs = [self.tag_inputs[i] for i in indices]
        self.label_inputs = [self.label_inputs[i] for i in indices]
        self.outputs = [self.outputs[i] for i in indices]

    @staticmethod
    def collate_batch(batch):
        inputs, targets = (i for i in zip(*batch))
        x_w, x_t, x_l = (torch.cat(x) for x in [i for i in zip(*inputs)])
        targets = torch.cat(targets)
        return (x_w, x_t, x_l), targets


class SequenceDependencyDataSet(Dataset):
    def __init__(self, inputs, outputs, outputs_pos_tag, padding_value_w, padding_value_t):
        self.inputs = inputs
        self.outputs = outputs
        self.outputs_pos_tag = outputs_pos_tag
        self.padding_value_w = padding_value_w
        self.padding_value_t = padding_value_t
        assert len(self.inputs) == len(self.outputs) == len(self.outputs_pos_tag)

    def __len__(self):  # 返回句子数
        return len(self.inputs)

    def __getitem__(self, sentence_index):
        return self.inputs[sentence_index], self.outputs[sentence_index], self.outputs_pos_tag[sentence_index]

    def shuffle(self):
        indices = list(range(len(self)))
        np.random.shuffle(indices)
        self.inputs = [self.inputs[i] for i in indices]
        self.outputs = [self.outputs[i] for i in indices]
        self.outputs_pos_tag = [self.outputs_pos_tag[i] for i in indices]

    @staticmethod
    def sortByLength(batch):
        sorted_indices, batch = tuple(
            i for i in zip(*sorted(enumerate(batch), key=lambda x: len(x[1][0][0]), reverse=True)))
        return batch, torch.tensor(sorted_indices)

    def collate_batch(self, batch):
        batch, _ = SequenceDependencyDataSet.sortByLength(batch)

        inputs, outputs, outputs_pos_tag = tuple(zip(*batch))
        words_batch, tags_batch, transition_f_batch = tuple(zip(*inputs))

        len_words = torch.tensor([len(w) for w in words_batch]).view(-1, 1)
        words_batch = pad_sequence(words_batch, batch_first=True, padding_value=self.padding_value_w)
        tags_batch = pad_sequence(tags_batch, batch_first=True, padding_value=self.padding_value_t)
        words_step = torch.arange(words_batch.size()[1])
        words_step_mask = words_step >= len_words

        transition_step_length_batch = torch.tensor([len(i) for i in transition_f_batch]).view(-1, 1)
        transition_f_batch = pad_sequence(transition_f_batch, batch_first=True, padding_value=0)  # 这里随意padding一个值，后续用mask处理
        transition_step = torch.arange(transition_f_batch.size()[1])
        transition_step_mask = transition_step < transition_step_length_batch
        outputs = torch.cat(outputs, dim=-1)
        outputs_pos_tag = torch.cat(outputs_pos_tag, dim=-1)
        return (words_batch, tags_batch, transition_f_batch), outputs, outputs_pos_tag, len_words.view(
            -1), words_step_mask, transition_step_mask
