# Copyright (c) 2022 PaddlePaddle Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import os
import re
import math
import json
import random
from tqdm import tqdm
import jsonlines
import numpy as np
from kbnlp.scripts.information_extraction.log import logger
from kbnlp.taskflow.models.information_extraction.information_extraction_model import SUBJECT_PREDICATE_CONJUNCT_WORD
from typing import Dict, List, Union
from collections import OrderedDict
from copy import copy, deepcopy
from PIL import ImageDraw, Image, ImageFont
"""
空格全角'\u3000'，空格半角' '。
非空格全部全角字符如下：
['＂', '＃', '＄', '％', '＆', '＇', '（', '）', '＊', '＋', '，', '－', '．', '／', '０', '１', '２', '３', '４', '５', '６', '７', '８', '９', '：', '；', '＜', '＝', '＞', '？', '＠', 'Ａ', 'Ｂ', 'Ｃ', 'Ｄ', 'Ｅ', 'Ｆ', 'Ｇ', 'Ｈ', 'Ｉ', 'Ｊ', 'Ｋ', 'Ｌ', 'Ｍ', 'Ｎ', 'Ｏ', 'Ｐ', 'Ｑ', 'Ｒ', 'Ｓ', 'Ｔ', 'Ｕ', 'Ｖ', 'Ｗ', 'Ｘ', 'Ｙ', 'Ｚ', '［', '＼', '］', '＾', '＿', '｀', 'ａ', 'ｂ', 'ｃ', 'ｄ', 'ｅ', 'ｆ', 'ｇ', 'ｈ', 'ｉ', 'ｊ', 'ｋ', 'ｌ', 'ｍ', 'ｎ', 'ｏ', 'ｐ', 'ｑ', 'ｒ', 'ｓ', 'ｔ', 'ｕ', 'ｖ', 'ｗ', 'ｘ', 'ｙ', 'ｚ', '｛', '｜', '｝']
非空格全部半角字符如下：
['"', '#', '$', '%', '&', "'", '(', ')', '*', '+', ',', '-', '.', '/', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', ':', ';', '<', '=', '>', '?', '@', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', '[', '\\', ']', '^', '_', '`', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z', '{', '|', '}']
"""


def dbc2sbc(s):
    """
    全角转半角。
    dbc2sbc('１９９６年存栏各类牲畜１６５７．２万头（只），')  # '1996年存栏各类牲畜1657.2万头(只),'
    dbc2sbc('4²')  # '4²'
    如果使用unicodedata.normalize('4²')就会变成‘42’，这可能不是想要的。
    """
    rs = ""
    for char in s:
        code = ord(char)
        if code == 0x3000:
            code = 0x0020
        else:
            code -= 0xfee0
        if not (0x0021 <= code <= 0x7e):
            rs += char
            continue
        rs += chr(code)
    return rs


def sbc2dbc(ustring):
    """
    半角转全角
    """
    rstring = ""
    for uchar in ustring:
        inside_code = ord(uchar)
        if inside_code == 0x0020:
            inside_code = 0x3000
        else:
            if not (0x0021 <= inside_code  <= 0x7e):
                rstring += uchar
                continue
            inside_code += 0xfee0
        rstring += chr(inside_code)
    return rstring


def trim_backslash(examples):
    """
        doccano在导出时在字符'/'前自动添加一个'\\'字符，但是start, end的位置信息对应于原始文本。所以需要手动去除'\\'字符。
    :return: 处理后的examples.
    """
    res = []
    for exam in examples:
        exam['text'] = exam['text'].replace('\\', '')
        res.append(exam)
    return res


# 标注工具要求：两个有后续关系的实体不能有交叉，两个实体之间的后续关系要么是正向，要么是反向，不能同时存在。
def deal_next_relation2(examples: List[Dict]):
    class Node:
        def __init__(self, node_id: int, length: int, visited: bool = False):
            self.node_id = node_id
            self.length = length
            self.visited = visited
            self.children = []

        def add_child(self, node_id: int):
            self.children.append(node_id)

    ans = []
    for ii, exam in enumerate(examples):
        # if ii == 29:
        #     print()
        exam = deepcopy(exam)
        entities = OrderedDict()
        relations = OrderedDict()
        # 去重实体，只保留一个。并将关系中涉及到重复的实体修正。
        for entity in exam['entities']:
            copy_entity = copy(entity)
            copy_entity.pop('id')
            copy_entity = tuple((k, v) for k, v in copy_entity.items())
            if copy_entity not in entities:
                entities[copy_entity] = entity
            else:
                exist_entity = entities[copy_entity]
                for rel in exam['relations']:
                    if rel['from_id'] == entity['id']:
                        rel['from_id'] = exist_entity['id']
                    if rel['to_id'] == entity['id']:
                        rel['to_id'] = exist_entity['id']
        # 去重关系
        for rel in exam['relations']:
            copy_rel = copy(rel)
            copy_rel.pop('id')
            copy_rel = tuple((k, v) for k, v in copy_rel.items())
            if copy_rel not in relations:
                relations[copy_rel] = rel
            else:
                print()
        entities = list(entities.values())
        entities.sort(key=lambda item: int(item['id']))
        relations = list(relations.values())
        relations.sort(key=lambda item: int(item['id']))

        entity_id2entity = {}
        entity_id2node = {}
        for entity in entities:
            entity_id2entity[entity['id']] = entity
            entity_id2node[entity['id']] = Node(node_id=entity['id'],
                                                length=entity['end_offset'] - entity['start_offset'])
        roots = set(entity_id2node.values())
        final_relations = []
        for rel in relations:
            f_id = rel['from_id']
            t_id = rel['to_id']
            f_e = entity_id2entity[f_id]
            t_e = entity_id2entity[t_id]
            # f_e与t_e在二者关系是后续，且label相同，且在文本中相邻的情况下仍然不足以确定将它们合并，同时还要保证f_e与t_e不能有其他任何一种关系，否则就不合并。
            if rel['type'] == '后续' and \
                    f_e['label'] == t_e['label'] and \
                    f_e['end_offset'] == t_e['start_offset'] and \
                    not any([(rel_['from_id'] == rel['to_id'] and rel_['to_id'] == rel['from_id']) or
                             (rel_['from_id'] == rel['from_id'] and rel_['to_id'] == rel['to_id'])
                             for rel_ in relations if rel != rel_]):
                entity_id2node[f_id].add_child(entity_id2node[t_id])
                roots.remove(entity_id2node[t_id])
            else:
                final_relations.append(rel)

        def traverse(node: Node):
            max_length = node.length
            offspring = [n.node_id for n in node.children]
            for child in node.children:
                child.visited = True
                ids, length = traverse(child)
                child.visited = False
                max_length = max(node.length + length, max_length)
                offspring.extend(ids)
            return offspring, max_length
        final_entities = []
        offspring2root = {}
        for root in roots:
            offspring, max_length = traverse(root)
            entity = entity_id2entity[root.node_id]
            entity['end_offset'] += max_length - (entity['end_offset'] - entity['start_offset'])
            final_entities.append(entity)
            for son in offspring:
                offspring2root[son] = root.node_id

        for rel in relations:
            if rel['from_id'] in offspring2root:
                rel['from_id'] = offspring2root[rel['from_id']]
            if rel['to_id'] in offspring2root:
                rel['to_id'] = offspring2root[rel['to_id']]
            final_relations.append(rel)
        final_relations.sort(key=lambda item: int(item['id']))
        final_entities.sort(key=lambda item: int(item['id']))
        final_relations = [rel for rel in final_relations if rel['from_id'] != ['to_id']]
        entity_ids = {e['id'] for e in final_entities}
        for rel in final_relations:
            assert rel['from_id'] in entity_ids and rel['to_id'] in entity_ids
        exam['entities'] = final_entities
        exam['relations'] = final_relations
        ans.append(exam)
    return ans


def deal_next_relation2_check(datas1, datas2):
    """
    Args:
        datas1:原始数据集。
        datas2:修改‘后续’后数据集。

    Returns:

    """
    for i, (d1, d2) in enumerate(zip(datas1, datas2)):

        text = d1['text']
        es1 = d1['entities']
        es2 = d2['entities']
        mentions = []
        removed_e_id = []
        for e1 in es1:
            s_e1, e_e1 = e1['start_offset'], e1['end_offset']
            for e2 in es2:
                if e1['id'] == e2['id']:
                    s_e2, e_e2 = e2['start_offset'], e2['end_offset']
                    if not (e1['label'], (s_e1, e_e1, text[s_e1: e_e1])) == (e2['label'], (s_e2, e_e2, text[s_e2: e_e2])):
                        mentions.append((e1['id'], e1['label'], (s_e1, e_e1, text[s_e1: e_e1]), e2['label'], (s_e2, e_e2, text[s_e2: e_e2])))
                    break
            else:
                mentions.append((e1['id'], e1['label'], (s_e1, e_e1, text[s_e1: e_e1])))
                removed_e_id.append(e1['id'])
        rel1 = d1['relations']
        rel2 = d2['relations']
        spo = []
        for r1 in rel1:
            from_r1 = r1['from_id']
            to_r1 = r1['to_id']
            for r2 in rel2:
                if r1['id'] == r2['id']:
                    from_r2 = r2['from_id']
                    to_r2 = r2['to_id']
                    if from_r1 in removed_e_id or to_r1 in removed_e_id:
                        spo.append(((r1['type'], from_r1, to_r1), (r2['type'], from_r2, to_r2)))
                    break
            else:
                spo.append((r1['type'], from_r1, to_r1))
        if mentions or spo:
            print('data index:', i)
        for mention in mentions:
            print(mention)
        for _ in spo:
            print(_)
        if mentions or spo:
            print('*' * 80)


def deal_next_relation(examples: Dict):
    """
        doccano在给实体打标签时，有时遇到一个实体文本处在上一行的末尾和下一行的开头的情况不能直接将他们标注在一块，
    个人使用'后续'连接两个被迫分开的文本。本函数将分开标注的本文重新连接起来。同时由于ocr在识别时有时将同一行文本识别成两个文本块，或者一个单元格内有两行文字时，
    ocr会将它们分开，此时本人在标注时也使用‘后续’关系将它们连接到了一块，但对于这种情况，不需要将他们重新连接，因为它们有不同的bbox，不影响使用layoutlm模型。
    :param examples:
    :return: 处理后的examples.
    """
    res = []
    for exam in examples:
        relations = exam['relations']
        id2entity = dict()
        for entity in exam['entities']:
            id2entity[entity['id']] = entity
        roots = set(id2entity.keys())
        start2end = {}
        t_id2real_relations = {}
        f_id2real_relations = {}
        for rel in relations:
            f_id = rel['from_id']
            t_id = rel['to_id']
            if rel['type'] == '后续':
                f_e = id2entity[f_id]
                t_e = id2entity[t_id]
                if f_e['end_offset'] == t_e['start_offset']:
                    # 这种情况下的'后续'关系被认为是一种伪的，为了应付doccano不能标注占据两行实体的问题而创建的。
                    start2end[f_id] = t_id
                    roots.remove(t_id)
                else:
                    t_id2real_relations.setdefault(t_id, []).append(rel)
                    f_id2real_relations.setdefault(f_id, []).append(rel)
            else:
                t_id2real_relations.setdefault(t_id, []).append(rel)
                f_id2real_relations.setdefault(f_id, []).append(rel)

        for e_id in roots:
            cur = e_id
            e = id2entity[e_id]
            while cur in start2end:
                cur = start2end[cur]
                e_cur = id2entity[cur]
                e['end_offset'] = e_cur['end_offset']
                if cur in t_id2real_relations:
                    for rel in t_id2real_relations[cur]:
                        rel['to_id'] = e_id
                if cur in f_id2real_relations:
                    for rel in f_id2real_relations:
                        rel['from_id'] = e_id
        entities = sorted(list([id2entity[idx] for idx in roots]), key=lambda ent: ent['id'])
        relations = sorted(sum(t_id2real_relations.values(), []), key=lambda r: r['id'])
        exam['entities'] = entities
        exam['relations'] = relations
        res.append(exam)
    return res


# def convert_example(example, tokenizer, max_seq_len):
#     """
#     example: {
#         title
#         prompt
#         text
#         result_list
#     }
#     """
#     encoded_inputs = tokenizer(text=[example["prompt"]],
#                                text_pair=[example["text"]],
#                                truncation=True,
#                                max_seq_len=max_seq_len,
#                                pad_to_max_seq_len=True,
#                                return_attention_mask=True,
#                                return_position_ids=True,
#                                return_dict=False,
#                                return_offsets_mapping=True)
#     encoded_inputs = encoded_inputs[0]
#     offset_mapping = [list(x) for x in encoded_inputs["offset_mapping"]]
#     bias = 0
#     for index in range(1, len(offset_mapping)):
#         mapping = offset_mapping[index]
#         if mapping[0] == 0 and mapping[1] == 0 and bias == 0:
#             bias = offset_mapping[index - 1][1] + 1  # Includes [SEP] token
#         if mapping[0] == 0 and mapping[1] == 0:
#             continue
#         offset_mapping[index][0] += bias
#         offset_mapping[index][1] += bias
#     start_ids = [0 for x in range(max_seq_len)]
#     end_ids = [0 for x in range(max_seq_len)]
#     for item in example["result_list"]:
#         start = map_offset(item["start"] + bias, offset_mapping)
#         end = map_offset(item["end"] - 1 + bias, offset_mapping)
#         start_ids[start] = 1.0
#         end_ids[end] = 1.0
#
#     tokenized_output = [
#         encoded_inputs["input_ids"], encoded_inputs["token_type_ids"],
#         encoded_inputs["position_ids"], encoded_inputs["attention_mask"],
#         start_ids, end_ids
#     ]
#     tokenized_output = [np.array(x, dtype="int64") for x in tokenized_output]
#     return tuple(tokenized_output)


def map_offset(ori_offset, offset_mapping):
    """
    map ori offset to token offset
    """
    for index, span in enumerate(offset_mapping):
        if span[0] <= ori_offset < span[1]:
            return index
    return -1


def reader(data_path, auto_prompt_len=0, max_seq_len=512):
    """
    read json，要求result list内span按照start升序排列。否则在超过max_seq_len的情况下，将text分割可能会使得end溢出。
    # TODO：现在的算法在处理长度超过max_seq_len时，可能会将主实体和客实体分开。还要注意主客实体的距离大于max_seq_len的特例。
    """
    # 最终模型的输入形如：[CLS] (auto prompt) prompt [SEP] text [SEP]
    special_token_len = 3  # 三个special token
    with open(data_path, 'r', encoding='utf-8') as f:
        for line in f:
            json_line = json.loads(line)
            text = json_line['text'].strip()
            prompt = json_line['prompt']
            # Model Input is as like: [CLS] prompt [SEP] text [SEP]
            # It include three summary tokens.
            if max_seq_len <= len(prompt) + auto_prompt_len + special_token_len:
                raise ValueError(
                    "The value of max_seq_len is too small, please set a larger value"
                )
            max_text_len = max_seq_len - len(prompt) - auto_prompt_len - special_token_len
            if len(text) <= max_text_len:
                yield json_line
            else:
                result_list = json_line['result_list']
                json_lines = []
                accumulate = 0
                while True:
                    cur_result_list = []

                    for result in result_list:
                        if result['start'] + 1 <= max_text_len < result[
                                'end']:
                            max_text_len = result['start']
                            break

                    cur_text = text[:max_text_len]
                    res_text = text[max_text_len:]

                    while True:
                        if len(result_list) == 0:
                            break
                        elif result_list[0]['end'] <= max_text_len:
                            if result_list[0]['end'] > 0:
                                cur_result = result_list.pop(0)
                                cur_result_list.append(cur_result)
                            else:
                                cur_result_list = [
                                    result for result in result_list
                                ]
                                break
                        else:
                            break

                    json_line = {
                        'text': cur_text,
                        'result_list': cur_result_list,
                        'prompt': prompt
                    }
                    json_lines.append(json_line)

                    for result in result_list:
                        if result['end'] <= 0:
                            break
                        result['start'] -= max_text_len
                        result['end'] -= max_text_len
                    accumulate += max_text_len
                    max_text_len = max_seq_len - len(prompt) - auto_prompt_len - special_token_len
                    if len(res_text) == 0:
                        break
                    elif len(res_text) < max_text_len:
                        json_line = {
                            'text': res_text,
                            'result_list': result_list,
                            'prompt': prompt
                        }
                        json_lines.append(json_line)
                        break
                    else:
                        text = res_text

                for json_line in json_lines:
                    yield json_line


def unify_prompt_name(prompt):
    # The classification labels are shuffled during finetuning, so they need
    # to be unified during evaluation.
    if re.search(r'\[.*?\]$', prompt):
        prompt_prefix = prompt[:prompt.find("[", 1)]
        cls_options = re.search(r'\[.*?\]$', prompt).group()[1:-1].split(",")
        cls_options = sorted(list(set(cls_options)))
        cls_options = ",".join(cls_options)
        prompt = prompt_prefix + "[" + cls_options + "]"
        return prompt
    return prompt


def add_negative_example(examples, raw_examples, prompts, label_set, negative_ratio, matrix_prob=None, binomial_prob=None):
    negative_examples = []
    positive_examples = []
    texts = [exam['text'] for exam in raw_examples]
    label_list = sorted(label_set)
    l2id = dict(zip(label_list, range(len(label_list))))

    with tqdm(total=len(prompts), mininterval=10) as pbar:
        for i, prompt in enumerate(prompts):
            prompt = sorted(prompt)
            positive_examples.extend(examples[i])
            redundants_list = list(label_set ^ set(prompt))
            if redundants_list:  # 如果有候选负样本label
                redundants_list.sort()

                num_positive = len(examples[i])  # 第i个句子中实体的种类数

                if num_positive == 0:
                    num_positive = 1

                if matrix_prob is None:
                    actual_ratio = math.ceil(len(redundants_list) / num_positive)
                    if actual_ratio <= negative_ratio or negative_ratio == -1:
                        idxs = [k for k in range(len(redundants_list))]
                    else:
                        idxs = random.sample(range(0, len(redundants_list)),
                                             negative_ratio * num_positive)

                    for idx in idxs:
                        negative_result = {
                            "text": texts[i],
                            "result_list": [],
                            "prompt": redundants_list[idx],
                        }
                        negative_examples.append(negative_result)
                        for k, v in raw_examples[i].items():
                            if k != 'predicate' and k != 'subject_label' and k != 'entities' and k != 'relations' and k not in negative_result:
                                negative_result[k] = v
                # if num_positive != 0:  # 百度实现，导致空样本会采集所有候选负样本。尽管后续会重新采样，但可能会影响效率。
                #     actual_ratio = math.ceil(len(redundants_list) / num_positive)
                # else:
                #     # Set num_positive to 1 for text without positive example
                #     num_positive, actual_ratio = 1, 0
                else:
                    if negative_ratio > 0:
                        probs = matrix_prob[[l2id[l] for l in set(prompt)]].copy()
                        neg_prompts = []
                        for prob in probs:
                            if not np.all(prob < 1e-6):
                                neg_prompts.extend(np.random.choice(label_list, (negative_ratio,), replace=True, p=prob).tolist())
                        neg_prompts.extend([label_list[i] for i, coin in
                                            enumerate(np.random.binomial(1, p=binomial_prob)) if coin == 1])

                    elif negative_ratio == -1:
                        neg_prompts = redundants_list
                    else:
                        neg_prompts = []
                    for neg_prompt in [n_p for n_p in set(neg_prompts) if n_p not in prompt]:
                        negative_result = {
                            "text": texts[i],
                            "result_list": [],
                            "prompt": neg_prompt,
                        }
                        negative_examples.append(negative_result)
                        for k, v in raw_examples[i].items():
                            if k != 'predicate' and k != 'subject_label' and k != 'entities' and k != 'relations' and k not in negative_result:
                                negative_result[k] = v

            pbar.update(1)
    return positive_examples, negative_examples


def add_full_negative_example(examples, texts, relation_prompts, predicate_set,
                              subject_goldens, conjunct=SUBJECT_PREDICATE_CONJUNCT_WORD):
    with tqdm(total=len(relation_prompts), mininterval=10) as pbar:
        for i, relation_prompt in enumerate(relation_prompts):
            negative_sample = []
            for subject in subject_goldens[i]:
                for predicate in predicate_set:
                    # The relation prompted is constructed as follows:
                    # (subject_name, predicate)
                    prompt = conjunct.join((subject, predicate))
                    if prompt not in relation_prompt:
                        negative_result = {
                            "text": texts[i],
                            "result_list": [],
                            "prompt": prompt,
                            "subject_label": None
                        }
                        negative_sample.append(negative_result)
            examples[i].extend(negative_sample)
            pbar.update(1)
    return examples


def construct_prompt_relation(entity_name_set, predicate_set, conjunct=SUBJECT_PREDICATE_CONJUNCT_WORD):
    """
    Args:
        entity_name_set:
        predicate_set:
        conjunct:

    Returns:
        形如(prompt,predicate)的二元组组成的列表。
    """
    relation_prompts = []
    for entity_name in entity_name_set:
        for predicate in predicate_set:
            # The relation prompt is constructed as follows:
            # subject_name + conjunct + predicate
            prompt = conjunct.join((entity_name, predicate))
            relation_prompts.append((prompt, predicate))
    return relation_prompts


def generate_cls_example(text, labels, prompt_prefix, options):
    # random.shuffle(options)  # 暂时先取消
    cls_options = sorted(list(set(options)))
    prompt = prompt_prefix + "[" + ','.join(cls_options) + "]"

    result_list = []
    example = {"text": text, "result_list": result_list, "prompt": prompt}
    for label in labels:
        start = prompt.rfind(label) - len(prompt) - 1
        end = start + len(label)
        result = {"text": label, "start": start, "end": end}
        example["result_list"].append(result)
    return example


def convert_cls_examples(raw_examples,
                         prompt_prefix="情感倾向",
                         options=["正向", "负向"]):
    """
    Convert labeled data export from doccano for classification taskflow.
    """
    examples = []
    logger.info(f"Converting doccano data...")
    with tqdm(total=len(raw_examples), mininterval=10) as pbar:
        for items in raw_examples:
            # Compatible with doccano >= 1.6.2
            if 'cls_label' in items:
                if "data" in items.keys():
                    text, labels = items["data"], items["cls_label"]
                else:
                    text, labels = items["text"], items["cls_label"]
                example = generate_cls_example(text, labels, prompt_prefix, options)
                examples.append(example)
    return examples


def convert_ext_examples(raw_examples,
                         negative_ratio,
                         prompt_prefix="情感倾向",
                         options=["正向", "负向"],
                         separator="##",
                         is_train=True,
                         conjunct=SUBJECT_PREDICATE_CONJUNCT_WORD,
                         matrix=None):
    """
    Convert labeled data export from doccano for extraction and aspect-level classification taskflow.
    """

    def _sep_cls_label(label, separator):
        label_list = label.split(separator)
        if len(label_list) == 1:
            return label_list[0], None
        return label_list[0], label_list[1:]

    def _concat_examples(positive_examples, negative_examples, negative_ratio):
        examples = []
        if math.ceil(len(negative_examples) /
                     len(positive_examples)) <= negative_ratio:
            examples = positive_examples + negative_examples
        else:
            # Random sampling the negative examples to ensure overall negative ratio unchanged.
            # idxs = random.sample(range(0, len(negative_examples)),
            #                      negative_ratio * len(positive_examples))
            idxs = range(len(negative_examples))
            negative_examples_sampled = []
            for idx in idxs:
                negative_examples_sampled.append(negative_examples[idx])
            examples = positive_examples + negative_examples_sampled
        return examples

    texts = []
    entity_examples = []
    relation_examples = []
    entity_cls_examples = []
    entity_prompts = []
    relation_prompts = []
    entity_label_set = set()
    entity_name_set = set()
    predicate_set = []
    subject_goldens = []

    logger.info(f"Converting doccano data...")
    with tqdm(total=len(raw_examples), mininterval=10) as pbar:
        for items in raw_examples:
            entity_id = 0
            if "data" in items.keys():  # doccano < 1.7.0,不维护，可能有错误。
                pass
            else:
                # Export file in JSONL format which doccano >= 1.7.0
                # e.g. {"text": "", "label": [ [0, 2, "ORG"], ... ]}
                if "label" in items.keys():  # 处理纯实体的数据
                    text = items["text"]
                    entities = []
                    for item in items["label"]:
                        entity = {
                            "id": entity_id,
                            "start_offset": item[0],
                            "end_offset": item[1],
                            "label": item[2]
                        }
                        entities.append(entity)
                        entity_id += 1
                    relations = []
                else:
                    # Export file in JSONL (relation) format
                    # e.g. {"text": "", "relations": [ {"id": 0, "start_offset": 0, "end_offset": 6, "label": "ORG"}, ... ], "entities": [ {"id": 0, "from_id": 0, "to_id": 1, "type": "foundedAt"}, ... ]}
                    try:
                        text, relations, entities = items["text"], items[
                            "relations"], items["entities"]
                    except KeyError as e:
                        raise e
            texts.append(text)

            entity_example = []
            entity_prompt = []

            entity_example_map = {}
            entity_map = {}  # id to entity name
            for entity in entities:
                entity_name = text[entity["start_offset"]:entity["end_offset"]]
                entity_map[entity["id"]] = {
                    "name": entity_name,
                    "start": entity["start_offset"],
                    "end": entity["end_offset"],
                    "label": entity['label']
                }

                entity_label, entity_cls_label = _sep_cls_label(
                    entity["label"], separator)

                # Define the prompt prefix for entity-level classification
                entity_cls_prompt_prefix = entity_name + conjunct + prompt_prefix
                if entity_cls_label is not None:
                    entity_cls_example = generate_cls_example(
                        text, entity_cls_label, entity_cls_prompt_prefix,
                        options)

                    entity_cls_examples.append(entity_cls_example)

                result = {
                    "text": entity_name,
                    "start": entity["start_offset"],
                    "end": entity["end_offset"]
                }
                if entity_label not in entity_example_map.keys():
                    entity_example_map[entity_label] = {
                        "text": text,
                        "result_list": [result],
                        "prompt": entity_label
                    }

                    # 保留数据中其它字段
                    for k_, v_ in items.items():
                        if k_ != 'entities' and k_ != 'relations' and k_ not in entity_example_map[entity_label]:
                            entity_example_map[entity_label][k_] = v_
                else:
                    entity_example_map[entity_label]["result_list"].append(
                        result)

                entity_label_set.add(entity_label)
                entity_name_set.add(entity_name)
                entity_prompt.append(entity_label)

            for v in entity_example_map.values():
                entity_example.append(v)

            entity_examples.append(entity_example)
            entity_prompts.append(entity_prompt)

            subject_golden = []  # Golden entity inputs
            relation_example = []
            relation_prompt = []
            relation_example_map = {}

            for relation in relations:
                predicate = relation["type"]
                subject_id = relation["from_id"]
                object_id = relation["to_id"]
                # The relation prompt is constructed as follows:
                # (subject_name, predicate)
                prompt = conjunct.join((entity_map[subject_id]["name"], predicate))
                if entity_map[subject_id]["name"] not in subject_golden:
                    subject_golden.append(entity_map[subject_id]["name"])
                result = {
                    "text": entity_map[object_id]["name"],
                    "start": entity_map[object_id]["start"],
                    "end": entity_map[object_id]["end"]
                }
                if prompt not in relation_example_map.keys():
                    relation_example_map[prompt] = {
                        "text": text,
                        "result_list": [result],
                        "prompt": prompt,
                        "subject_label": entity_map[subject_id]['label'],
                        "predicate": predicate,
                    }
                    # 保留数据中其它字段
                    for k_, v_ in items.items():
                        if k_ != 'entities' and k_ != 'relations' and k_ not in relation_example_map[prompt]:
                            relation_example_map[prompt][k_] = v_
                else:
                    relation_example_map[prompt]["result_list"].append(result)

                if predicate not in predicate_set:
                    predicate_set.append(predicate)
                relation_prompt.append(prompt)

            for v in relation_example_map.values():
                relation_example.append(v)

            relation_examples.append(relation_example)
            relation_prompts.append(relation_prompt)
            subject_goldens.append(subject_golden)
            pbar.update(1)

    logger.info(f"Adding negative samples for first stage prompt...")

    if matrix is not None and len(raw_examples) and entity_label_set:
        entity_label_list = sorted(entity_label_set)
        print(entity_label_list)
        DUMMY_LABEL = -1
        counts = np.array([matrix[l][DUMMY_LABEL] for l in entity_label_list])
        diagonal = np.array([matrix[l][l] for l in entity_label_list], dtype=np.float)
        binomial_prob = counts / (counts + diagonal)
        binomial_prob[(binomial_prob != binomial_prob) | (np.abs(binomial_prob) == np.inf) | (binomial_prob <= 0)] = 1e-6
        matrix_T = np.zeros((len(entity_label_list), len(entity_label_list)))
        for i, l1 in enumerate(entity_label_list):
            for j, l2 in enumerate(entity_label_list):
                if l2 != l1 and matrix[l2][l1] != 0:
                    matrix_T[i][j] = matrix[l2][l1]
                else:
                    matrix_T[i][j] = 1e-12

        def score(x):
            x_row_sum = x.sum(axis=-1, keepdims=True)
            res = x / x_row_sum
            return res

        # def softmax(x):
        #     x_row_max = x.max(axis=-1, keepdims=True)
        #     x = x - x_row_max
        #     x_exp = np.exp(x)
        #     x_exp_row_sum = x_exp.sum(axis=-1, keepdims=True)
        #     score = x_exp / x_exp_row_sum
        #     return score
        print('matrix_T:', matrix_T)
        # matrix_prob = softmax(matrix_T)
        matrix_prob = score(matrix_T)
        for i, arr in enumerate(matrix_T):
            if np.all(arr == 1e-12):
                matrix_prob[i] = 0
    else:
        matrix_prob = None
        binomial_prob = None
    print('binomial_prob:', binomial_prob)
    positive_examples, negative_examples = add_negative_example(
        entity_examples, raw_examples, entity_prompts, entity_label_set,
        negative_ratio, matrix_prob, binomial_prob)
    print('****************', 'positive_examples:', len(positive_examples), 'negative_examples:', len(negative_examples), '*************')
    if len(positive_examples) == 0:
        all_entity_examples = []
    elif is_train:
        all_entity_examples = _concat_examples(positive_examples,
                                               negative_examples,
                                               negative_ratio)
    else:
        all_entity_examples = positive_examples + negative_examples

    all_relation_examples = []
    if len(predicate_set) != 0:
        if is_train:
            logger.info(f"Adding negative samples for second stage prompt...")
            relation_prompt_set = construct_prompt_relation(
                entity_name_set, set(predicate_set))
            positive_examples, negative_examples = add_negative_example(
                relation_examples, raw_examples, relation_prompts, set(next(zip(*relation_prompt_set))),
                negative_ratio)
            all_relation_examples = _concat_examples(positive_examples,
                                                     negative_examples,
                                                     negative_ratio)
        else:
            logger.info(f"Adding negative samples for second stage prompt...")
            relation_examples = add_full_negative_example(
                relation_examples, texts, relation_prompts, predicate_set,
                subject_goldens, conjunct)
            all_relation_examples = [
                r for relation_example in relation_examples
                for r in relation_example
            ]
    return all_entity_examples, all_relation_examples, entity_cls_examples


def get_k_fewshot_data(examples, save_dir=None, shuffle=False, k=10):
    """
    Args:
        examples: 从doccano17中导出的json文件中读出的数据。
        save_dir: 存放路径。默认为None，不保存。
        shuffle:
        k: 保证每个label出现的次数不低于k（大致在k附件）。

    Returns: 返回k-few-shot的数据。

    """
    if k <= 0:
        return examples
    res = []
    labels = set()
    for exam in examples:
        for e in exam['entities']:
            labels.add(e['label'])
        for rel in exam['relations']:
            labels.add(rel['type'])

    label_count = dict((l, 0) for l in labels)
    if shuffle:
        idx = np.random.permutation(len(examples))
        examples = [examples[i] for i in idx]
    for d in examples:
        temp = {}
        all_overflow = True
        for entity_d in d['entities']:
            label = entity_d['label']
            if label in temp:
                temp[label] = temp[label] + 1
            else:
                temp[label] = 1
                if label_count[label] < k:
                    all_overflow = False
        for relation_d in d['relations']:
            label = relation_d['type']
            if label in temp:
                temp[label] = temp[label] + 1
            else:
                temp[label] = 1
                if label_count[label] < k:
                    all_overflow = False
        if not all_overflow:
            for l, c in temp.items():
                label_count[l] = label_count[l] + temp[l]
            res.append(d)
    if shuffle:
        idx = np.random.permutation(len(res))
        res = [res[i] for i in idx]
    if save_dir:
        with jsonlines.open(os.path.join(save_dir, 'fewshot_%d.jsonl' % k), mode='w') as jw:
            for d in res:
                jw.write(d)
    return res


def stack_images(*images, horizontal=True, margin=10, base_color='white'):
    """
        将多张图片按行或者按列摆放。
    :param images:  PIL.Image对象。
    :param horizontal: 默认为True,水平摆放。否则垂直摆放。
    :param margin: 相邻图片之间的间隔。
    :param base_color: 底片的颜色，默认为白色。
    :return: 返回摆放好的新图片。
    """
    cum_dim = 0
    if not horizontal:
        cum_dim = 1
    cum_size_dim1 = 0
    max_size_dim2 = 0
    for image in images:
        size = image.size
        cum_size_dim1 += size[cum_dim]
        max_size_dim2 = max(max_size_dim2, size[1-cum_dim])
    cum_size_dim1 += (len(images) - 1) * margin
    if not horizontal:
        cum_size_dim1, max_size_dim2 = max_size_dim2, cum_size_dim1
    blank = Image.new("RGB", [cum_size_dim1, max_size_dim2], 'white')
    x, y = 0, 0
    for image in images:
        blank.paste(image, (x, y))
        if horizontal:
            x += image.size[0] + margin
        else:
            y += image.size[1] + margin
    return blank


def draw_bbox_label(image: Image, texts: List[str], bboxes: List[List[Union[int, float]]],
                    color: Union[List[str], str] = None, offset: int = 10, inplace=True, **kwargs):
    """
        画出box, 并将text打印在box内。
    :param image: Image对象。
    :param texts: 打印到box中的文字的列表。长度应该和bboxes相同。
    :param bboxes: 所有box的列表。任何一个box是一个长为4的序列，[x0,y0,x1,y1]。其中0,1代表box的左上角和右下角。
    :param color: 默认为空，所有字体box和文字都用green显示。可以为str，或者list[str]，前者让所有的box和文字都是用相同颜色，后者为每个（text,box）单独设置颜色。
    :param offset: 控制打印文字相对box左上角的offset像素值。默认值为10, 将文字左上角坐标x,y设置为：box[0] + 10, box[1] - 10。
    :param kwargs: 可选参数有font，用来设置字体。默认情况下使用ImageFont的默认字体。
    :param inplace : 是否在原图上修改，默认为True,否则创建一个副本，在副本上修改。
    :return: 返回修改后的image。
    """
    assert len(texts) == len(bboxes) and (not color or isinstance(color, str) or len(texts) == len(color)), \
        'texts长度应该和bboxes相同, color的长度应该于texts相同或者没有。'

    if not color:
        color = ['green'] * len(texts)
    elif isinstance(color, str):
        color = [color] * len(texts)
    if not inplace:
        image = image.copy()
    draw = ImageDraw.Draw(image)
    font = kwargs.pop('font', ImageFont.load_default())
    for text, box, co in zip(texts, bboxes, color):
        try:
            draw.rectangle(box, outline=co)
        except Exception as e:
            print(box)
            raise e
        draw.text((box[0], box[1] - offset), text=text, fill=co, font=font)
    return image


def unnormalize_box(bbox, width, height):
    """
        对于一张图片，在使用LayoutLM前会将bbox归一化到(1000, 1000)的矩形上。使用本函数可以bbox反规范化。
    :param bbox:
    :param width:  原始图片的width。
    :param height:  原始图片的height。
    :return:
    """
    return [
        width * (bbox[0] / 1000),
        height * (bbox[1] / 1000),
        width * (bbox[2] / 1000),
        height * (bbox[3] / 1000),
    ]


def rectify_box(box):
    if isinstance(box, list) and len(box) == 4 and isinstance(box[0], (int, float)):
        x0 = box[0]
        x2 = box[2]
    else:
        x0 = box[0][0]
        x2 = box[2][0]
    if x2 < x0:
        box = box[1:3] + box[0]
    return box


def v_overlap(box1, box2):
    """
    Args:
        box1:
        box2:

    Returns:

    """
    box1, box2 = rectify_box(box1), rectify_box(box2)
    if isinstance(box1, list) and len(box1) == 4 and isinstance(box1[0], (int, float)):
        if box1[0] > box2[0]:
            box1, box2 = box2, box1
        b1_y0 = box1[1]
        b1_y3 = box1[3]  # y2代替
        b2_y0 = box2[1]
        b2_y3 = box2[3]  # y2代替
        return min(b1_y3, b2_y3) - max(b1_y0, b2_y0)
    else:
        if box1[0][0] > box2[0][0]:
            box1, box2 = box2, box1
        b1_y0 = box1[0][1]
        b1_y3 = box1[3][1]
        b2_y0 = box2[0][1]
        b2_y3 = box2[3][1]
        return min(b1_y3, b2_y3) - max(b1_y0, b2_y0)


def sort_boxes2(bboxes: List[List[Union[int, float]]], margin_patient: Union[int, float] = 18.):
    bboxes = [rectify_box(box) for box in bboxes]
    arr = sorted([(i, box, (box[0] + box[2]) / 2, (box[1] + box[3]) / 2) for i, box in enumerate(bboxes)],
                 key=lambda x: (x[1][1] + x[1][3]) / 2)
    bucket_id = -1
    last = -100
    arr_ = []
    for ind, box, x, y in arr:
        if y - last > margin_patient:
            bucket_id += 1
        arr_.append((ind, box, x, y, bucket_id))
        last = y
    arr = arr_
    arr = sorted(arr, key=lambda tup: (tup[4], tup[2]))
    last_bucket = -1
    start_ids_per_line = []
    # ind代表在原始bbox列表中索引
    for i, (ind, box, x, y, bucket_id) in enumerate(arr):
        if bucket_id != last_bucket:
            start_ids_per_line.append(i)
            last_bucket = bucket_id
    indices = [arr_i[0]for arr_i in arr]
    bboxes = [arr_i[1]for arr_i in arr]
    return indices, bboxes, start_ids_per_line


def sort_boxes(bboxes: List[List[Union[int, float]]], margin_patient: Union[int, float] = 18.):
    # arr = sorted([(i, box, box[0], box[1]) for i, box in enumerate(bboxes)], key=lambda x: x[1][1])
    arr = sorted([(i, box, (box[0] + box[2]) / 2, (box[1] + box[3]) / 2) for i, box in enumerate(bboxes)],
                 key=lambda x: (x[1][1] + x[1][3]) / 2)
    bucket_id = -1
    last = -100
    arr_ = []
    for ind, box, x, y in arr:
        if y - last > margin_patient:
            bucket_id += 1
        arr_.append((ind, box, x, y, bucket_id))
        last = y
    arr = arr_
    arr = sorted(arr, key=lambda tup: (tup[4], tup[2]))
    last_bucket = -1
    start_ids_per_line = []
    # ind代表在原始bbox列表中索引
    for i, (ind, box, x, y, bucket_id) in enumerate(arr):
        if bucket_id != last_bucket:
            start_ids_per_line.append(i)
            last_bucket = bucket_id
    indices = [arr_i[0]for arr_i in arr]
    bboxes = [arr_i[1]for arr_i in arr]
    return indices, bboxes, start_ids_per_line


def ocr_sort_draw(image: str, ocr_system=None,
                  use_customized_pp_ocr=False,
                  margin_patient=18.,
                  other_word=None,
                  other_bbox=None,
                  show_word=True,
                  show_other_word=True,
                  offset: int = 20,
                  inplace=False,
                  font=None,
                  line_length=26,
                  line_width=3,
                  shrink_width=0,
                  shrink_height=0,
                  ):
    """
    1，对一张图片进行ocr；2，将返回的结果重新排序；3，将排序后的结果画到图片上，包括阿box，text，被判定为同一行的文字、文本框用相同的颜色。
    Args:
        image: 图片路径
        ocr_system: ocr system可以是pp ocr或者zhu ocr。当该值不为None时，use_customized_pp_ocr无效。该参数必须有一个方法为'ocr'。
        use_customized_pp_ocr: 默认值为False，使用zhu ocr;否则使用pp ocr。
        margin_patient: 文本框y轴相差小于或等于该值的文本框，被判定为同一行文本。
        other_word: 其他文本框对应的文字。
        other_bbox: 其他任意任意指定的文本框。如果该字段为None,则other_word、show_other_word都失效。
        show_word: 该字段表示是否展示图片的ocr结果到每个文本框上。默认为True，该文本框所在行号和该行的文本框序号以及该文本框的ocr结果；
            否则只展示该文本框所在行号和该行的文本框序号。
        show_other_word: 该字段表示是否展示other_word到other_bbox之上。默认为True,将word展示到指定的other box之上，否则不展示。不影响other box的显示。
        offset: 控制打印文字相对box左上角的offset像素值。该字段需要和设定font字体size相同。
        inplace: 是否在原图上修改，如果为False,将创建一个副本，在副本上修改。
        font: 用来设置字体。
        line_length: 画字坐标的线长
        line_width: 画字坐标的线宽
        shrink_width: 缩小ocr检测出的文本框宽度。固定质心，在左右均缩小该值。
        shrink_height: 缩小ocr检测出的文本框高度。固定质心，在上下均缩小该值。
        **kwargs:

    Returns: 返回画出box和text的图片。

    """
    if font is None:
        font = ImageFont.truetype(r'D:\workspace\pycharm\layoutLMv3\simfang.ttf', size=20)
    if ocr_system is None:
        if not use_customized_pp_ocr:
            from kbnlp.scripts.information_extraction.zhu_ocr import OCRSystem
            ocr_system = OCRSystem(det_model_path=os.path.join(os.path.dirname(os.path.abspath(__file__)),
                                                               'dtm/001.pt'),
                                   cls_model_path=os.path.join(os.path.dirname(os.path.abspath(__file__)),
                                                               'dtm/003.pt'),
                                   cls_vertical_model_path=os.path.join(os.path.dirname(os.path.abspath(__file__)),
                                                                        'dtm/006.pt'),
                                   rec_model_path=os.path.join(os.path.dirname(os.path.abspath(__file__)),
                                                               'dtm/tp1t7.pt'),
                                   rec_char_dict_path=os.path.join(os.path.dirname(os.path.abspath(__file__)),
                                                                   'dtm/ppocr_keys_v1.txt'))  # , det_max_side_len=1152)
        else:
            from kbnlp.scripts.information_extraction.pp_ocr_customized import PaddleOCRCustomized
            import paddle
            ocr_system = PaddleOCRCustomized(show_log=False, lang='ch', use_gpu=True if paddle.get_device().startswith('gpu') else False)
    ocr_res = ocr_system.ocr(image)[0]

    word = [x[1][0] for x in ocr_res]
    char_positions = None
    if len(ocr_res) > 0 and len(ocr_res[0][1]) >= 3:
        char_positions = [x[1][2] for x in ocr_res]
    bbox = [[min(x[0][0][0], x[0][3][0]), min(x[0][0][1], x[0][1][1]), max(x[0][2][0], x[0][1][0]),
             max(x[0][2][1], x[0][3][1])] for x in ocr_res]
    bbox = [[box[0] + shrink_width, box[1] + shrink_height, box[2] - shrink_width, box[3] - shrink_height] for box in bbox]
    indices, bbox, start_ids = sort_boxes(bbox, margin_patient=margin_patient)
    word = [word[ind] for ind in indices]
    color = ['red'] * len(word)
    ids = start_ids + [len(word)]
    for i, (s, e) in enumerate(zip(ids[:-1], ids[1:])):
        for ii in range(s, e):
            if i % 2 == 0:
                color[ii] = 'blue'
            word[ii] = str(i + 1) + '-' + str(ii - s + 1) + ('：' + word[ii] if show_word else '')

    img = draw_bbox_label(Image.open(image).convert("RGB"), word, bbox, color=color,
                          font=font, offset=offset, inplace=inplace)
    if other_bbox is not None:
        if show_other_word and other_word is not None:
            assert len(other_word) == len(other_bbox)
        else:
            other_word = [''] * len(other_bbox)
        img = draw_bbox_label(img, other_word, other_bbox, color='black', font=font, offset=offset, inplace=inplace)
    if char_positions is not None:
        draw = ImageDraw.Draw(img)
        for positions in char_positions:
            for x, y in positions:
                draw.line([(x, y - line_length/2), (x, y + line_length/2)], fill=(0, 255, 0), width=line_width)
    return img


# def draw_ocr_result(tx_path, im_path=None, font=ImageFont.truetype(r'D:\workspace\pycharm\layoutLMv3\simfang.ttf')):
#     """
#
#     Args:
#         tx_path: ocr解析结果文件存放的位置。是一个jsonl文件。
#         im_path: 图片位置,默认为None，此时tx_path指定的文件中需要指定'image_path'，否则报错。如果im_path不是None,则从im_path出加载图片。
#         font: 字体。
#
#     Returns:
#
#     """
#     with jsonlines.open(tx_path, mode='r') as jr:
#         for exam in jr:
#             bboxes = exam['bboxes']
#             # for box in bboxes:
#             #     box[0] -= 8
#             #     box[1] -= 8
#             #     box[2] += 8
#             #     box[3] += 5
#             words = exam['words']
#             image = Image.open(im_path if im_path else exam.pop('image_path')).convert("RGB")
#             break
#
#     image_ = draw_bbox_label(image, words, bboxes, inplace=False, offset=font.size, font=font)
#     return image_


def box_coverage_area(box1, box2):
    """calc intersection over box1 area"""
    x1 = max(box1[0], box2[0])
    y1 = max(box1[1], box2[1])
    x2 = min(box1[2], box2[2])
    y2 = min(box1[3], box2[3])
    if x2 <= x1 or y2 <= y1:
        return 0.0
    box1_area = (box1[2] - box1[0]) * (box1[3] - box1[1])
    return (x2 - x1) * (y2 - y1) * 1.0 / box1_area


def isInside(x1, y1, x2, y2, x3, y3, x4, y4, x, y):
    """判断(x,y)是否在矩形内部, 其中1、2、3、4代表矩形按照顺时针顺序的顶点序列。"""
    def getCross(x1_, y1_, x2_, y2_, x_, y_):
        """计算(x1_,y1_)(x2_,y2_)、(x1_,y1_)(x_,y_)向量的叉乘"""
        a = (x2_ - x1_, y2_ - y1_)
        b = (x_ - x1_, y_ - y1_)
        return a[0] * b[1] - a[1] * b[0]
    return (getCross(x1, y1, x2, y2, x, y) * getCross(x3, y3, x4, y4, x, y) >= 0 and
            getCross(x2, y2, x3, y3, x, y) * getCross(x4, y4, x1, y1, x, y) >= 0)


def show_label_studio_entity_box():
    """
    临时使用方法。
    Returns:

    """
    import os
    from PIL import Image, ImageFont
    import jsonlines
    with jsonlines.open(r'D:\workspace\pycharm\nlp_project\kbnlp\raw_data\银行年报\train.jsonl') as jr:
        datas = list(jr)
    i = 1
    img = os.path.join(r'D:\workspace\pycharm\nlp_project\kbnlp\raw_data\银行年报\images'), \
          os.path.basename(datas[i]['data']['image']).split('-', maxsplit=1)[-1]
    print(img)
    img = Image.open(os.path.join(r'D:\workspace\pycharm\nlp_project\kbnlp\raw_data\银行年报\images',
                                  os.path.basename(datas[i]['data']['image']).split('-', maxsplit=1)[-1])).convert(
        "RGB")
    img_w = img.width
    img_h = img.height
    bboxes = [[int(e['value']['x'] * 0.01 * img_w),
               int(e['value']['y'] * 0.01 * img_h),
               int((e['value']['x'] + e['value']['width']) * 0.01 * img_w),
               int((e['value']['y'] + e['value']['height']) * 0.01 * img_h)]
              for e in datas[i]['annotations'][0]['result'] if e['type'] == 'rectanglelabels']
    texts = [e['value']['rectanglelabels'][0] for e in datas[i]['annotations'][0]['result'] if
             e['type'] == 'rectanglelabels']
    draw_img = draw_bbox_label(img, texts=texts, bboxes=bboxes,
                               font=ImageFont.truetype(r'D:\workspace\pycharm\layoutLMv3\simfang.ttf', size=24))
    draw_img.show()


if __name__ == '__main__':
    # dbc = ['＂', '＃', '＄', '％', '＆', '＇', '（', '）', '＊', '＋', '，', '－', '．', '／', '０', '１', '２', '３', '４', '５', '６', '７', '８', '９', '：', '；', '＜', '＝', '＞', '？', '＠', 'Ａ', 'Ｂ', 'Ｃ', 'Ｄ', 'Ｅ', 'Ｆ', 'Ｇ', 'Ｈ', 'Ｉ', 'Ｊ', 'Ｋ', 'Ｌ', 'Ｍ', 'Ｎ', 'Ｏ', 'Ｐ', 'Ｑ', 'Ｒ', 'Ｓ', 'Ｔ', 'Ｕ', 'Ｖ', 'Ｗ', 'Ｘ', 'Ｙ', 'Ｚ', '［', '＼', '］', '＾', '＿', '｀', 'ａ', 'ｂ', 'ｃ', 'ｄ', 'ｅ', 'ｆ', 'ｇ', 'ｈ', 'ｉ', 'ｊ', 'ｋ', 'ｌ', 'ｍ', 'ｎ', 'ｏ', 'ｐ', 'ｑ', 'ｒ', 'ｓ', 'ｔ', 'ｕ', 'ｖ', 'ｗ', 'ｘ', 'ｙ', 'ｚ', '｛', '｜', '｝']
    # sbc = ['"', '#', '$', '%', '&', "'", '(', ')', '*', '+', ',', '-', '.', '/', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', ':', ';', '<', '=', '>', '?', '@', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', '[', '\\', ']', '^', '_', '`', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z', '{', '|', '}']
    # print(list(zip(dbc, [dbc2sbc(c) for c in dbc])))
    # print(list(zip(sbc, [sbc2dbc(c) for c in sbc])))
    with jsonlines.open(r'D:\workspace\pycharm\nlp_project\kbnlp\raw_data\real_estate\real_estate_labeled_trim_.jsonl') as jr:
        datas = list(jr)
    examples = deal_next_relation2(datas)

    deal_next_relation2_check(datas, examples)
    print()
