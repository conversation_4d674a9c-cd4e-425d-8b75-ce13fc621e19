<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="a9c0e45b-72e8-4757-ac13-546a32f2a332" name="Default" comment="">
      <change type="DELETED" beforePath="$PROJECT_DIR$/aimp-f2e/package-lock.json" afterPath="" />
      <change type="MODIFICATION" beforePath="$PROJECT_DIR$/aimp/.env" afterPath="$PROJECT_DIR$/aimp/.env" />
      <change type="MODIFICATION" beforePath="$PROJECT_DIR$/aimp/aimp.spec" afterPath="$PROJECT_DIR$/aimp/aimp.spec" />
      <change type="MODIFICATION" beforePath="$PROJECT_DIR$/aimp/common/auth.py" afterPath="$PROJECT_DIR$/aimp/common/auth.py" />
      <change type="MODIFICATION" beforePath="$PROJECT_DIR$/aimp/em.py" afterPath="$PROJECT_DIR$/aimp/em.py" />
      <change type="MODIFICATION" beforePath="$PROJECT_DIR$/aimp/infra.py" afterPath="$PROJECT_DIR$/aimp/infra.py" />
      <change type="MODIFICATION" beforePath="$PROJECT_DIR$/aimp/kbnlp/scripts/information_extraction/zhu_ocr.py" afterPath="$PROJECT_DIR$/aimp/kbnlp/scripts/information_extraction/zhu_ocr.py" />
      <change type="MODIFICATION" beforePath="$PROJECT_DIR$/aimp/main.py" afterPath="$PROJECT_DIR$/aimp/main.py" />
      <change type="MODIFICATION" beforePath="$PROJECT_DIR$/aimp/sfx-aidq.conf" afterPath="$PROJECT_DIR$/aimp/sfx-aidq.conf" />
      <change type="MODIFICATION" beforePath="$PROJECT_DIR$/aimp/shutdown-aidq.bat" afterPath="$PROJECT_DIR$/aimp/shutdown-aidq.bat" />
      <change type="MODIFICATION" beforePath="$PROJECT_DIR$/aimp/start-aidq.bat" afterPath="$PROJECT_DIR$/aimp/start-aidq.bat" />
      <change type="MODIFICATION" beforePath="$PROJECT_DIR$/aimp/startup.bat" afterPath="$PROJECT_DIR$/aimp/startup.bat" />
    </list>
    <option name="EXCLUDED_CONVERTED_TO_IGNORED" value="true" />
    <option name="TRACKING_ENABLED" value="true" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ExecutionTargetManager" SELECTED_TARGET="default_target" />
  <component name="FileEditorManager">
    <leaf SIDE_TABS_SIZE_LIMIT_KEY="450">
      <file leaf-file-name="em.py" pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/aimp/em.py">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="31274">
              <caret line="1680" column="10" lean-forward="false" selection-start-line="1680" selection-start-column="10" selection-end-line="1680" selection-end-column="10" />
              <folding>
                <element signature="e#0#11#0" expanded="true" />
              </folding>
            </state>
          </provider>
        </entry>
      </file>
      <file leaf-file-name="infra.py" pinned="false" current-in-tab="true">
        <entry file="file://$PROJECT_DIR$/aimp/infra.py">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="133">
              <caret line="16" column="15" lean-forward="false" selection-start-line="16" selection-start-column="15" selection-end-line="16" selection-end-column="15" />
              <folding />
            </state>
          </provider>
        </entry>
      </file>
      <file leaf-file-name="database.py" pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/aimp/common/database.py">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="76">
              <caret line="7" column="49" lean-forward="false" selection-start-line="7" selection-start-column="49" selection-end-line="7" selection-end-column="49" />
              <folding />
            </state>
          </provider>
        </entry>
      </file>
      <file leaf-file-name="start.bat" pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/aimp/start.bat">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="209">
              <caret line="11" column="1" lean-forward="false" selection-start-line="11" selection-start-column="1" selection-end-line="11" selection-end-column="1" />
              <folding />
            </state>
          </provider>
        </entry>
      </file>
      <file leaf-file-name=".env" pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/aimp/.env">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="95">
              <caret line="5" column="19" lean-forward="false" selection-start-line="5" selection-start-column="19" selection-end-line="5" selection-end-column="19" />
              <folding />
            </state>
          </provider>
        </entry>
      </file>
      <file leaf-file-name="dj_database_url.py" pinned="false" current-in-tab="false">
        <entry file="file://C:/ProgramData/Anaconda3/envs/py3.9_torch/Lib/site-packages/dj_database_url.py">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="817">
              <caret line="44" column="44" lean-forward="false" selection-start-line="44" selection-start-column="44" selection-end-line="44" selection-end-column="44" />
              <folding />
            </state>
          </provider>
        </entry>
      </file>
    </leaf>
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="FindInProjectRecents">
    <findStrings>
      <find>lock_dll_path</find>
      <find>E</find>
      <find>Error</find>
      <find>Error in</find>
      <find>Error in re</find>
      <find>Error in rea</find>
      <find>Error in read</find>
      <find>Error in read do</find>
      <find>Error in read dog</find>
      <find>Error in read dog in</find>
      <find>Error in read dog inf</find>
      <find>h_login</find>
      <find>slm_get_info</find>
      <find>Error in read dog info</find>
      <find>.</find>
      <find>.env</find>
      <find>DATABASE_URL</find>
      <find>assert</find>
      <find>print</find>
      <find>param_contrast</find>
      <find>------</find>
      <find>read_image</find>
      <find>MAX_IMAGE_PIXELS</find>
      <find>Unsupported Media Type</find>
      <find>return img</find>
      <find>continue</find>
      <find>get_dominant_color</find>
      <find>inspect</find>
      <find>is_license_exists</find>
      <find>code</find>
    </findStrings>
    <replaceStrings>
      <replace>log</replace>
    </replaceStrings>
    <dirStrings>
      <dir>G:\Jenkins_Workdir\workspace\kubao-aimp-bak\aimp</dir>
    </dirStrings>
  </component>
  <component name="Git.Settings">
    <option name="ROOT_SYNC" value="DONT_SYNC" />
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/aimp" />
  </component>
  <component name="IdeDocumentHistory">
    <option name="CHANGED_PATHS">
      <list>
        <option value="$PROJECT_DIR$/infra.py" />
        <option value="$PROJECT_DIR$/aimp/aimp-linux.spec" />
        <option value="$PROJECT_DIR$/aimp/aimp-wf.spec" />
        <option value="$PROJECT_DIR$/aimp/common/auth1.py" />
        <option value="$PROJECT_DIR$/aimp/startup.bat" />
        <option value="$PROJECT_DIR$/build-jenkins-aimp.bat.bak" />
        <option value="$PROJECT_DIR$/aimp/sfx.conf" />
        <option value="$PROJECT_DIR$/aimp/main.py" />
        <option value="$PROJECT_DIR$/aimp/em.exe.ssp" />
        <option value="$PROJECT_DIR$/aimp/sfx-aidp.conf" />
        <option value="$PROJECT_DIR$/aimp/start.bat" />
        <option value="$PROJECT_DIR$/aimp/static/config/log.json" />
        <option value="$PROJECT_DIR$/aimp/aimp.spec" />
        <option value="$PROJECT_DIR$/aimp/common/auth.py" />
        <option value="$PROJECT_DIR$/build-jenkins-aidp.bat" />
        <option value="$USER_HOME$/.kubao/nocode/logs/ai-api.2024-07-31.log" />
        <option value="$USER_HOME$/.kubao/nocode/logs/kubao.2024-08-01.log" />
        <option value="$USER_HOME$/.kubao/nocode/logs/ai-api.2024-08-01.log" />
        <option value="$USER_HOME$/.kubao/nocode/logs/kubao.2024-08-03.log" />
        <option value="$USER_HOME$/.kubao/nocode/logs/ai-api.2024-08-03.log" />
        <option value="$PROJECT_DIR$/aimp/.env" />
        <option value="C:/ProgramData/Anaconda3/envs/py3.9_torch/Lib/site-packages/dj_database_url.py" />
        <option value="C:/ProgramData/Anaconda3/envs/py3.9_torch/Lib/site-packages/django/db/backends/kingbase/creation.py" />
        <option value="C:/ProgramData/Anaconda3/envs/py3.9_torch/Lib/site-packages/django/db/backends/kingbase/operations.py" />
        <option value="C:/ProgramData/Anaconda3/envs/py3.9_torch/Lib/site-packages/django/db/backends/kingbase/schema.py" />
        <option value="C:/ProgramData/Anaconda3/envs/py3.9_torch/Lib/site-packages/django/db/backends/kingbase/base.py" />
        <option value="$PROJECT_DIR$/aimp/em.py" />
        <option value="$PROJECT_DIR$/aimp/infra.py" />
      </list>
    </option>
  </component>
  <component name="ProjectFrameBounds">
    <option name="x" value="-8" />
    <option name="y" value="-8" />
    <option name="width" value="1936" />
    <option name="height" value="1056" />
  </component>
  <component name="ProjectInspectionProfilesVisibleTreeState">
    <entry key="Project Default">
      <profile-state>
        <expanded-state>
          <State>
            <id />
          </State>
          <State>
            <id>Python</id>
          </State>
        </expanded-state>
        <selected-state>
          <State>
            <id>PyCompatibilityInspection</id>
          </State>
        </selected-state>
      </profile-state>
    </entry>
  </component>
  <component name="ProjectLevelVcsManager" settingsEditedManually="true" />
  <component name="ProjectView">
    <navigator currentView="ProjectPane" proportions="" version="1">
      <flattenPackages />
      <showMembers />
      <showModules />
      <showLibraryContents />
      <hideEmptyPackages />
      <abbreviatePackageNames />
      <autoscrollToSource />
      <autoscrollFromSource />
      <sortByType />
      <manualOrder />
      <foldersAlwaysOnTop value="true" />
    </navigator>
    <panes>
      <pane id="Scratches" />
      <pane id="Scope" />
      <pane id="ProjectPane">
        <subPane>
          <PATH>
            <PATH_ELEMENT>
              <option name="myItemId" value="kubao-aimp-bak" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.ProjectViewProjectNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="kubao-aimp-bak" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.PsiDirectoryNode" />
            </PATH_ELEMENT>
          </PATH>
        </subPane>
      </pane>
    </panes>
  </component>
  <component name="PropertiesComponent">
    <property name="settings.editor.selected.configurable" value="project.propVCSSupport.Mappings" />
    <property name="last_opened_file_path" value="$PROJECT_DIR$/aimp" />
    <property name="SearchEverywhereHistoryKey" value="config.py&#9;FILE&#9;file://G:/Jenkins_Workdir/workspace/kubao-aimp-bak/aimp/aimp/config.py&#10;main&#9;FILE&#9;file://G:/Jenkins_Workdir/workspace/kubao-aimp-bak/aimp/main.py&#10;a&#9;FILE&#9;file://G:/Jenkins_Workdir/workspace/kubao-aimp-bak/aimp/main.py&#10;inf&#9;FILE&#9;file://G:/Jenkins_Workdir/workspace/kubao-aimp-bak/aimp/infra.py" />
  </component>
  <component name="PyConsoleOptionsProvider">
    <option name="myPythonConsoleState">
      <console-settings module-name="kubao-aimp-bak" is-module-sdk="true" working-directory="$PROJECT_DIR$/aimp">
        <envs>
          <env key="Path" value="C:\ProgramData\Anaconda3\envs\py3.9_torch\Scripts" />
        </envs>
        <option name="myUseModuleSdk" value="true" />
        <option name="myModuleName" value="kubao-aimp-bak" />
        <option name="myEnvs">
          <map>
            <entry key="Path" value="C:\ProgramData\Anaconda3\envs\py3.9_torch\Scripts" />
          </map>
        </option>
        <option name="myWorkingDirectory" value="$PROJECT_DIR$/aimp" />
        <option name="myAddSourceRoots" value="true" />
      </console-settings>
    </option>
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="G:\Jenkins_Workdir\workspace\kubao-aimp-bak\aimp\common" />
      <recent name="G:\Jenkins_Workdir\workspace\kubao-aimp-bak\aimp" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="G:\Jenkins_Workdir\workspace\kubao-aimp-bak\aimp" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="ruleStates">
      <list>
        <RuleState>
          <option name="name" value="ConfigurationTypeDashboardGroupingRule" />
        </RuleState>
        <RuleState>
          <option name="name" value="StatusDashboardGroupingRule" />
        </RuleState>
      </list>
    </option>
  </component>
  <component name="RunManager">
    <configuration default="true" type="PythonConfigurationType" factoryName="Python">
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="C:\ProgramData\Anaconda3\envs\py3.9_torch\python.exe" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/aimp" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <module name="kubao-aimp-bak" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/aimp/main.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <method />
    </configuration>
    <configuration default="true" type="Tox" factoryName="Tox">
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs />
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <module name="kubao-aimp-bak" />
      <method />
    </configuration>
    <configuration default="true" type="tests" factoryName="Doctests">
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs />
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <module name="kubao-aimp-bak" />
      <option name="SCRIPT_NAME" value="" />
      <option name="CLASS_NAME" value="" />
      <option name="METHOD_NAME" value="" />
      <option name="FOLDER_NAME" value="" />
      <option name="TEST_TYPE" value="TEST_SCRIPT" />
      <option name="PATTERN" value="" />
      <option name="USE_PATTERN" value="false" />
      <method />
    </configuration>
    <configuration default="true" type="tests" factoryName="Unittests">
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs />
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <module name="kubao-aimp-bak" />
      <option name="_new_additionalArguments" value="&quot;&quot;" />
      <option name="_new_target" value="&quot;.&quot;" />
      <option name="_new_targetType" value="&quot;PATH&quot;" />
      <method />
    </configuration>
  </component>
  <component name="ShelveChangesManager" show_recycled="false">
    <option name="remove_strategy" value="false" />
  </component>
  <component name="SvnConfiguration">
    <configuration>C:\Users\<USER>\AppData\Roaming\Subversion</configuration>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="a9c0e45b-72e8-4757-ac13-546a32f2a332" name="Default" comment="" />
      <created>1720168809487</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1720168809487</updated>
    </task>
    <servers />
  </component>
  <component name="ToolWindowManager">
    <frame x="-8" y="-8" width="1936" height="1056" extended-state="6" />
    <editor active="true" />
    <layout>
      <window_info id="TODO" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="6" side_tool="false" content_ui="tabs" />
      <window_info id="DB Browser" active="false" anchor="left" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="3" side_tool="false" content_ui="tabs" />
      <window_info id="DB Execution Console" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="11" side_tool="false" content_ui="tabs" />
      <window_info id="Event Log" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.4134997" sideWeight="0.50214595" order="7" side_tool="true" content_ui="tabs" />
      <window_info id="Run" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.31339714" sideWeight="0.5" order="2" side_tool="false" content_ui="tabs" />
      <window_info id="Version Control" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.16985646" sideWeight="0.49791667" order="8" side_tool="false" content_ui="tabs" />
      <window_info id="Python Console" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.32894737" sideWeight="0.5" order="9" side_tool="false" content_ui="tabs" />
      <window_info id="Terminal" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.24043062" sideWeight="0.49785408" order="10" side_tool="false" content_ui="tabs" />
      <window_info id="Project" active="false" anchor="left" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="true" show_stripe_button="true" weight="0.1825054" sideWeight="0.5" order="0" side_tool="false" content_ui="combo" />
      <window_info id="Structure" active="false" anchor="left" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.14887339" sideWeight="0.5" order="1" side_tool="false" content_ui="tabs" />
      <window_info id="Debug" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.7236842" sideWeight="0.5" order="3" side_tool="false" content_ui="tabs" />
      <window_info id="Favorites" active="false" anchor="left" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="2" side_tool="true" content_ui="tabs" />
      <window_info id="Data View" active="false" anchor="right" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.32991362" sideWeight="0.5" order="3" side_tool="false" content_ui="tabs" />
      <window_info id="Cvs" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.25" sideWeight="0.5" order="4" side_tool="false" content_ui="tabs" />
      <window_info id="Message" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="0" side_tool="false" content_ui="tabs" />
      <window_info id="Commander" active="false" anchor="right" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.4" sideWeight="0.5" order="0" side_tool="false" content_ui="tabs" />
      <window_info id="Inspection" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.4" sideWeight="0.5" order="5" side_tool="false" content_ui="tabs" />
      <window_info id="Hierarchy" active="false" anchor="right" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.25" sideWeight="0.5" order="2" side_tool="false" content_ui="combo" />
      <window_info id="Find" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="true" show_stripe_button="true" weight="0.2505721" sideWeight="0.5" order="1" side_tool="false" content_ui="tabs" />
      <window_info id="Ant Build" active="false" anchor="right" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.25" sideWeight="0.5" order="1" side_tool="false" content_ui="tabs" />
    </layout>
  </component>
  <component name="VcsContentAnnotationSettings">
    <option name="myLimit" value="2678400000" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/aimp/em.py</url>
          <line>1432</line>
          <option name="timeStamp" value="3" />
        </line-breakpoint>
      </breakpoints>
      <option name="time" value="5" />
    </breakpoint-manager>
    <watches-manager />
  </component>
  <component name="editorHistoryManager">
    <entry file="file://$PROJECT_DIR$/aimp/dataset/__init__.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/aimp/dataset/migrations/0007_dataset_dataset_type.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="285">
          <caret line="15" column="13" lean-forward="false" selection-start-line="15" selection-start-column="13" selection-end-line="15" selection-end-column="13" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/aimp/aimp/config.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="684">
          <caret line="18" column="11" lean-forward="true" selection-start-line="18" selection-start-column="11" selection-end-line="18" selection-end-column="11" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/aimp/dataset/models.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/aimp/dataset/admin.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/aimp/aimp/settings.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://C:/ProgramData/Anaconda3/envs/py3.9_torch/Lib/site-packages/django/core/management/__init__.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="5075">
          <caret line="442" column="4" lean-forward="false" selection-start-line="442" selection-start-column="4" selection-end-line="442" selection-end-column="4" />
        </state>
      </provider>
    </entry>
    <entry file="file://C:/ProgramData/Anaconda3/envs/py3.9_torch/Lib/site-packages/dotenv/main.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="509">
          <caret line="80" column="8" lean-forward="false" selection-start-line="80" selection-start-column="8" selection-end-line="80" selection-end-column="8" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/aimp/utils/logger.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/aimp/model/models1.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="874">
          <caret line="23" column="11" lean-forward="false" selection-start-line="23" selection-start-column="11" selection-end-line="23" selection-end-column="11" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/aimp/manage.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="57">
          <caret line="5" column="8" lean-forward="false" selection-start-line="5" selection-start-column="8" selection-end-line="5" selection-end-column="8" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/aimp/model/router.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="9044">
          <caret line="500" column="12" lean-forward="false" selection-start-line="500" selection-start-column="12" selection-end-line="500" selection-end-column="12" />
        </state>
      </provider>
    </entry>
    <entry file="file://$USER_HOME$/.kubao/nocode/logs/kubao.2024-08-01.log" />
    <entry file="file://$USER_HOME$/.kubao/nocode/logs/ai-api.2024-08-01.log" />
    <entry file="file://$USER_HOME$/.kubao/nocode/logs/ai-api.2024-07-31.log" />
    <entry file="file://$PROJECT_DIR$/aimp/main.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="266">
          <caret line="16" column="47" lean-forward="false" selection-start-line="16" selection-start-column="47" selection-end-line="16" selection-end-column="47" />
        </state>
      </provider>
    </entry>
    <entry file="file://$USER_HOME$/.kubao/nocode/logs/kubao.2024-08-03.log" />
    <entry file="file://C:/ProgramData/Anaconda3/envs/py3.9_torch/Lib/platform.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="17461">
          <caret line="925" column="0" lean-forward="false" selection-start-line="925" selection-start-column="0" selection-end-line="925" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://C:/ProgramData/Anaconda3/envs/py3.9_torch/Lib/site-packages/torch/__init__.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="3496">
          <caret line="190" column="53" lean-forward="false" selection-start-line="190" selection-start-column="53" selection-end-line="190" selection-end-column="53" />
        </state>
      </provider>
    </entry>
    <entry file="file://C:/ProgramData/Anaconda3/envs/py3.9_torch/Lib/site-packages/anyio/_backends/_asyncio.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="172">
          <caret line="866" column="0" lean-forward="false" selection-start-line="866" selection-start-column="0" selection-end-line="866" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://C:/ProgramData/Anaconda3/envs/py3.9_torch/Lib/subprocess.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="27056">
          <caret line="1435" column="0" lean-forward="false" selection-start-line="1435" selection-start-column="0" selection-end-line="1435" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://C:/ProgramData/Anaconda3/envs/py3.9_torch/Lib/site-packages/starlette/routing.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="9956">
          <caret line="543" column="0" lean-forward="false" selection-start-line="543" selection-start-column="0" selection-end-line="543" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://$USER_HOME$/.kubao/nocode/logs/ai-api.2024-08-03.log" />
    <entry file="file://$USER_HOME$/.PyCharmCE2017.1/system/python_stubs/-*********/cv2/cv2/__init__.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="81586">
          <caret line="4308" column="4" lean-forward="false" selection-start-line="4308" selection-start-column="4" selection-end-line="4308" selection-end-column="4" />
        </state>
      </provider>
    </entry>
    <entry file="file://C:/ProgramData/Anaconda3/envs/py3.9_torch/Lib/site-packages/fastapi/param_functions.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="5206">
          <caret line="277" column="4" lean-forward="false" selection-start-line="277" selection-start-column="4" selection-end-line="277" selection-end-column="4" />
        </state>
      </provider>
    </entry>
    <entry file="file://C:/ProgramData/Anaconda3/envs/py3.9_torch/Lib/site-packages/PIL/Image.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="582">
          <caret line="1193" column="8" lean-forward="false" selection-start-line="1193" selection-start-column="8" selection-end-line="1193" selection-end-column="8" />
        </state>
      </provider>
    </entry>
    <entry file="file://C:/ProgramData/Anaconda3/envs/py3.9_torch/Lib/site-packages/PIL/ImageOps.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="10792">
          <caret line="572" column="4" lean-forward="false" selection-start-line="572" selection-start-column="4" selection-end-line="572" selection-end-column="4" />
        </state>
      </provider>
    </entry>
    <entry file="file://$USER_HOME$/.kubao/nocode/logs/ai-api.2024-08-06.log" />
    <entry file="file://$PROJECT_DIR$/aimp/requirements.txt">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/build-jenkins-aidp.bat">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="855">
          <caret line="45" column="35" lean-forward="false" selection-start-line="45" selection-start-column="35" selection-end-line="45" selection-end-column="35" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/aimp/common/auth1.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="265">
          <caret line="274" column="67" lean-forward="false" selection-start-line="274" selection-start-column="67" selection-end-line="274" selection-end-column="67" />
        </state>
      </provider>
    </entry>
    <entry file="file://C:/ProgramData/Anaconda3/envs/py3.9_torch/Lib/importlib/__init__.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="110">
          <caret line="126" column="0" lean-forward="false" selection-start-line="126" selection-start-column="0" selection-end-line="126" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://C:/ProgramData/Anaconda3/envs/py3.9_torch/Lib/site-packages/django/db/backends/kingbase/creation.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="19">
          <caret line="1" column="0" lean-forward="false" selection-start-line="1" selection-start-column="0" selection-end-line="1" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://C:/ProgramData/Anaconda3/envs/py3.9_torch/Lib/site-packages/django/db/backends/kingbase/operations.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="0" column="6" lean-forward="false" selection-start-line="0" selection-start-column="6" selection-end-line="0" selection-end-column="6" />
        </state>
      </provider>
    </entry>
    <entry file="file://C:/ProgramData/Anaconda3/envs/py3.9_torch/Lib/site-packages/django/db/backends/kingbase/schema.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="0" column="8" lean-forward="false" selection-start-line="0" selection-start-column="8" selection-end-line="0" selection-end-column="8" />
        </state>
      </provider>
    </entry>
    <entry file="file://C:/ProgramData/Anaconda3/envs/py3.9_torch/Lib/site-packages/psycopg2/__init__.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://C:/ProgramData/Anaconda3/envs/py3.9_torch/Lib/site-packages/django/db/backends/kingbase/base.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="171">
          <caret line="19" column="15" lean-forward="false" selection-start-line="19" selection-start-column="15" selection-end-line="19" selection-end-column="15" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/aimp/common/database.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="76">
          <caret line="7" column="49" lean-forward="false" selection-start-line="7" selection-start-column="49" selection-end-line="7" selection-end-column="49" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://C:/ProgramData/Anaconda3/envs/py3.9_torch/Lib/site-packages/dj_database_url.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="817">
          <caret line="44" column="44" lean-forward="false" selection-start-line="44" selection-start-column="44" selection-end-line="44" selection-end-column="44" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/aimp/.env">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="95">
          <caret line="5" column="19" lean-forward="false" selection-start-line="5" selection-start-column="19" selection-end-line="5" selection-end-column="19" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://C:/ProgramData/Anaconda3/envs/py3.9_torch/Lib/enum.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="267">
          <caret line="792" column="8" lean-forward="false" selection-start-line="792" selection-start-column="8" selection-end-line="792" selection-end-column="8" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/aimp/.idea/aimp.iml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/aimp/.idea/dbnavigator.xml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="-7920">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/aimp/.idea/workspace.xml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="-1596">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/aimp/.idea/vcs.xml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/aimp/.idea/modules.xml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/aimp/.idea/misc.xml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/aimp/.idea/encodings.xml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/aimp/start.bat">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="209">
          <caret line="11" column="1" lean-forward="false" selection-start-line="11" selection-start-column="1" selection-end-line="11" selection-end-column="1" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/aimp/em.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="31274">
          <caret line="1680" column="10" lean-forward="false" selection-start-line="1680" selection-start-column="10" selection-end-line="1680" selection-end-column="10" />
          <folding>
            <element signature="e#0#11#0" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/aimp/infra.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="133">
          <caret line="16" column="15" lean-forward="false" selection-start-line="16" selection-start-column="15" selection-end-line="16" selection-end-column="15" />
          <folding />
        </state>
      </provider>
    </entry>
  </component>
</project>