# Copyright (c) 2022 PaddlePaddle Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import os
import sys
from functools import partial
from typing import Optional
from dataclasses import dataclass, field
from kbnlp.taskflow.models.information_extraction.information_extraction_model import UIEX
from kbnlp.pretrained.ernie_layout.tokenization_ernie_layout_fast import ErnieLayoutTokenizerFast
import numpy as np
import random
import torch
# from uiex_p.datasets import load_dataset
from datasets import load_dataset, Dataset
from transformers import TrainerCallback, TrainerState, TrainerControl
from transformers import HfArgumentPars<PERSON>, TrainingArguments, Trainer, set_seed
from transformers.trainer_utils import get_last_checkpoint
from transformers import XLMRobertaTokenizerFast
from log import logger
from temp_ie_utils import compute_metrics
from temp_utils import reader, convert_example


def _callback(*args, **kwargs):
    return


def _setup_post_callback():
    if os.environ.get('SLURM_PROCID', '0') != '0':
        return
    job_id = int(os.environ.get('SLURM_JOB_ID', '-1'))
    import requests
    url = os.environ.get('CALLBACK_URL', '')
    global _callback
    if not url:
        return

    def callback(*args, **kwargs):
        res = requests.post(url, json={'msg': args, 'detail': kwargs, 'JobId': job_id}, timeout=10)
        if res.ok:
            j = res.json()  # ; print(j)
        else:
            print('NOTE callback code={} error={}'.format(res.status_code, res.text))
        return

    _callback = callback


def seed_everything(seed):
    torch.manual_seed(seed)       # Current CPU
    torch.cuda.manual_seed(seed)  # Current GPU
    np.random.seed(seed)          # Numpy module
    random.seed(seed)             # Python random module
    # torch.backends.cudnn.benchmark = False    # Close optimization
    # torch.backends.cudnn.deterministic = True  # Close optimization
    torch.cuda.manual_seed_all(seed)  # All GPU (Optional)


# yapf: disable
@dataclass
class DataArguments:
    """
    Arguments pertaining to what data we are going to input our model for training and eval.
    Using `PdArgumentParser` we can turn this class into argparse arguments to be able to
    specify them on the command line.
    """
    train_path: str = field(
        default=None,
        metadata={
            "help": "The name of the dataset to use (via the datasets library)."
        })

    dev_path: str = field(
        default=None,
        metadata={
            "help": "The name of the dataset to use (via the datasets library)."
        })

    max_seq_len: Optional[int] = field(
        default=512,
        metadata={
            "help":
            "The maximum total input sequence length after tokenization. Sequences longer "
            "than this will be truncated, sequences shorter will be padded."
        },
    )


@dataclass
class ModelArguments:
    """
    Arguments pertaining to which model/config/tokenizer we are going to fine-tune from.
    """
    model_name_or_path: Optional[str] = field(
        default="uie-x-base",
        metadata={
            "help":
            "Path to pretrained model"
        })
    # export_model_dir: Optional[str] = field(
    #     default=None,
    #     metadata={
    #         "help": "Path to directory to store the exported inference model."
    #     },
    # )


class MyCallBack(TrainerCallback):

    def on_train_begin(self, args: TrainingArguments, state: TrainerState, control: TrainerControl, **kwargs):
        if state.is_world_process_zero:
            _callback(step='start training', start_epoch=state.epoch, epochs=state.num_train_epochs)

    # def on_step_begin(self, args: TrainingArguments, state: TrainerState, control: TrainerControl, **kwargs):
    #     _callback(step='begin step', epoch=state.global_step)

    def on_epoch_begin(self, args: TrainingArguments, state: TrainerState, control: TrainerControl, **kwargs):
        if state.is_world_process_zero:
            _callback(step='begin epoch', epoch=state.epoch)

    def on_epoch_end(self, args: TrainingArguments, state: TrainerState, control: TrainerControl, **kwargs):
        if state.is_world_process_zero:
            _val = _callback(step='end epoch', epoch=state.epoch)
            if _val:
                print('Issued exit by callback reply={}'.format(_val))
                control.should_training_stop = True


def main():
    _setup_post_callback()
    parser = HfArgumentParser(
        (ModelArguments, DataArguments, TrainingArguments))
    if len(sys.argv) == 2 and sys.argv[1].endswith(".json"):
        # If we pass only one argument to the script and it's the path to a json file,
        # let's parse it to get our arguments.
        model_args, data_args, training_args = parser.parse_json_file(json_file=os.path.abspath(sys.argv[1]))
    else:
        model_args, data_args, training_args = parser.parse_args_into_dataclasses()

    seed_everything(training_args.seed)

    # Log model and data config
    logger.info(f"ModelArguments parameters {model_args}")
    logger.info(f"DataTrainingArguments parameters {data_args}")
    logger.info(f"Training/evaluation parameters {training_args}")

    # paddle.set_device(training_args.device)

    # Log on each process the small summary:
    logger.warning(
        f"Process rank: {training_args.local_rank}, device: {training_args.device}, world_size: {training_args.world_size}, "
        +
        f"distributed training: {bool(training_args.local_rank != -1)}, 16-bits training: {training_args.fp16}"
    )

    # Detecting last checkpoint.
    last_checkpoint = None
    if os.path.isdir(
            training_args.output_dir
    ) and training_args.do_train and not training_args.overwrite_output_dir:
        last_checkpoint = get_last_checkpoint(training_args.output_dir)
        if last_checkpoint is None and len(os.listdir(
                training_args.output_dir)) > 0:
            raise ValueError(
                f"Output directory ({training_args.output_dir}) already exists and is not empty. "
                "Use --overwrite_output_dir to overcome.")
        elif last_checkpoint is not None and training_args.resume_from_checkpoint is None:
            logger.info(
                f"Checkpoint detected, resuming training at {last_checkpoint}. To avoid this behavior, change "
                "the `--output_dir` or add `--overwrite_output_dir` to train from scratch."
            )

    # Define model and tokenizer
    model = UIEX.from_pretrained(model_args.model_name_or_path)
    tokenizer = XLMRobertaTokenizerFast.from_pretrained(model_args.model_name_or_path)

    train_ds = list(reader(data_args.train_path, max_seq_len=data_args.max_seq_len))
    dev_ds = list(reader(data_args.dev_path, max_seq_len=data_args.max_seq_len))

    def list_dict2dict_list(datas):
        keys = datas[0].keys()
        res = {k: [] for k in keys}
        for d in datas:
            for k in keys:
                if k in d:
                    res[k].append(d[k])
                else:
                    raise ValueError('请保持每个数据中的keys保持一致。')
        return res

    train_ds = Dataset.from_dict(list_dict2dict_list(train_ds))
    dev_ds = Dataset.from_dict(list_dict2dict_list(dev_ds))
    trans_fn = partial(convert_example,
                       tokenizer=tokenizer,
                       max_seq_len=data_args.max_seq_len)
    train_ds = train_ds.map(trans_fn)
    dev_ds = dev_ds.map(trans_fn)

    # Load and preprocess dataset
    # train_ds = load_dataset(reader,
    #                         data_path=data_args.train_path,
    #                         max_seq_len=data_args.max_seq_len,
    #                         lazy=False)
    # dev_ds = load_dataset(reader,
    #                       data_path=data_args.dev_path,
    #                       max_seq_len=data_args.max_seq_len,
    #                       lazy=False)
    # trans_fn = partial(convert_example,
    #                    tokenizer=tokenizer,
    #                    max_seq_len=data_args.max_seq_len)
    # train_ds = train_ds.map(trans_fn)
    # dev_ds = dev_ds.map(trans_fn)

    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=train_ds if training_args.do_train else None,
        eval_dataset=dev_ds if training_args.do_eval else None,
        tokenizer=tokenizer,
        compute_metrics=compute_metrics,
        callbacks=[MyCallBack()]
    )

    trainer.optimizer = torch.optim.AdamW(
        params=model.parameters(),
        lr=training_args.learning_rate)
    checkpoint = None
    if training_args.resume_from_checkpoint is not None:
        checkpoint = training_args.resume_from_checkpoint
    elif last_checkpoint is not None:
        checkpoint = last_checkpoint

    # Training
    if training_args.do_train:
        checkpoint = last_checkpoint if last_checkpoint else None
        train_result = trainer.train(resume_from_checkpoint=checkpoint)
        metrics = train_result.metrics
        trainer.save_model()
        trainer.log_metrics("train", metrics)
        trainer.save_metrics("train", metrics)
        trainer.save_state()

    # Evaluate and tests model
    if training_args.do_eval:
        eval_metrics = trainer.evaluate()
        trainer.log_metrics("eval", eval_metrics)

    # export inference model
    # if training_args.do_export:
    #     # You can also load from certain checkpoint
    #     # trainer.load_state_dict_from_checkpoint("/path/to/checkpoint/")
    #     input_spec = [
    #         paddle.static.InputSpec(shape=[None, None],
    #                                 dtype="int64",
    #                                 name='input_ids'),
    #         paddle.static.InputSpec(shape=[None, None],
    #                                 dtype="int64",
    #                                 name='token_type_ids'),
    #         paddle.static.InputSpec(shape=[None, None],
    #                                 dtype="int64",
    #                                 name='position_ids'),
    #         paddle.static.InputSpec(shape=[None, None],
    #                                 dtype="int64",
    #                                 name='attention_mask'),
    #         paddle.static.InputSpec(shape=[None, None, 4],
    #                                 dtype="int64",
    #                                 name='bbox'),
    #         paddle.static.InputSpec(shape=[None, 3, 224, 224],
    #                                 dtype="int64",
    #                                 name='image')
    #     ]
        # if model_args.export_model_dir is None:
        #     model_args.export_model_dir = os.path.join(training_args.output_dir,
        #                                                "export")
        # export_model(model=trainer.model,
        #              input_spec=input_spec,
        #              path=model_args.export_model_dir)


if __name__ == "__main__":
    """python -m torch.distributed.launch --nproc_per_node 4 uiex_finetune.py --logging_steps 50 --save_steps 100 --eval_steps 100 --seed 42 --model_name_or_path ../../taskflow/models/information_extraction/uiex-base --output_dir ../../checkpoint/baidu_tax_test/ --train_path /home/<USER>/workspace/nlp_project/kbnlp/raw_data/tax/split/train.txt --dev_path /home/<USER>/workspace/nlp_project/kbnlp/raw_data/tax/split/dev.txt --max_seq_len 512 --per_device_train_batch_size 4 --gradient_accumulation_steps 2 --per_device_eval_batch_size 4 --num_train_epochs 10 --learning_rate 1e-5 --label_names start_positions end_positions --do_train --do_eval --evaluation_strategy steps --overwrite_output_dir --disable_tqdm True --metric_for_best_model eval_f1 --load_best_model_at_end False --save_total_limit 1"""
    main()

    # def train(model_name_or_path,
    #           output_dir,
    #           train_path,
    #           dev_path,
    #           max_seq_len=512,
    #           doc_stride=128,
    #           num_train_epochs=30,
    #           learning_rate=1e-5,
    #           **kwargs
    #           ):
    #     """
    #
    #     Args:
    #         model_name_or_path: 基模型的存放路径或标识符,str.
    #         output_dir: 模型训练后的保存路径.str
    #         max_seq_len: 句子的最大长度，默认512。int
    #         doc_stride: 未处理长文档设置的回退滑窗大小，默认128,必须小于max_seq_len。int。
    #         num_train_epochs: 最大的训练轮数,默认30。int
    #         learning_rate: 学习率，默认1e-5。float
    #         **kwargs: 其他参数字典，普通用户不需要设置。
    #
    #     Returns: None
    #
    #     """
    #     pass
    #
    # def taskflow(task_name='information_extraction',
    #              model='uiex_p',
    #              task_path=None,
    #              batch_size=1,
    #              **kwargs
    #              ):
    #     """
    #
    #     Args:
    #         task_name: 任务名，对于文档信息抽取赋值为‘information_extraction’即可。字符串
    #         model: 模型名，对于文档信息抽取赋值为‘uiex_p’即可。字符串
    #         task_path: 模型存放的路径，字符串。
    #         batch_size: 预测时的批量大小，默认为1。整数。
    #         **kwargs: 其他非普通用户参数字典。
    #
    #     Returns: uiex模型，torch.nn.Module。
    #
    #     """
    #     pass
    #
    # def predict(document,
    #             uiex_p,
    #             max_seq_len=512,
    #             doc_stride=128,
    #             batch_size=1,
    #             **kwargs
    #             ):
    #     """
    #
    #     Args:
    #         document: ocr处理后的图片数据，List[Dict]。列表大小对应图片张数。字典的key包括‘text’,代表ocr处理后得到所有文本框;'bbox'代表
    #             对应文本框的坐标,共四个坐标值，[left_top_x, left_top_y, right_bottom_x, right_bottom_y]。
    #             [{'text': ['成绩单', '姓名', '张三'...],
    #               'bbox': [[551, 97, 636, 137], [551, 97, 636, 137], [551, 97, 636, 137]]},
    #              {'text': ['成绩单', '姓名', '李四'...],
    #               'bbox': [[688, 113, 874, 156], [688, 113, 874, 156], [688, 113, 874, 156]]},
    #              ...
    #             ]
    #         uiex_p: 文档信息抽取模型，torch.nn.Module。
    #         max_seq_len: 句子的最大长度，默认512,不能超过512。int。
    #         doc_stride: 未处理长文档设置的回退滑窗大小，默认128,必须小于max_seq_len。int。
    #         batch_size: 预测是批量大小，默认为1，int。
    #         **kwargs: 其他参数字典
    #
    #     Returns:  Dict,代表抽取到的信息。key代表著录项，value代表对应的抽取结果。
    #
    #     """
    #     pass
    #
    #
    # def get_all_models():
    #     """
    #
    #     Returns:list[str]，返回所有模型路径。
    #
    #     """
    #     pass

    """
        # 文档标注导出数据格式
        数据导出成jsonlines格式，每行一个样本，图片放在jsonl文件的同级目录下的images/file_name文件夹下。在jsonl文件中，每个样本是一个字典，包含字段：
        'fileBookName':  # 如，同北京设计一致，需要确保同一案卷同一案件内的所有图片的该字段值相同。
        'word': List[str], ocr得到的文字列表。
        'bbox': List[List[int, int, int, int]], ocr得到的所有文本框的bbox列表。
        'image': str, 记录jsonl文件的同级images目录到该图片文件的路径信息）,如，某张图片名为'pig1.jgp',其放在images/pig目录下，该本字段记录为'images/pig/pig1.jpg'。
        'entities': 如下示例，id代表该图片中标注的实体id,从0开始，依次递增，label代表实体标签（著录项），fragment记录用户对该著录项的标注结果，
                    该示例中fragment列表中的第一个著录项'住址著录项'对应的标注步骤如下：
                        1，用户首先在box_id为0的文本框中选中‘上海市’，’上海市‘在该文本框中的start、end值为(2, 5);
                        2，然后在该文本框中选中'SOHO东海广场',其在文本框中的start、end值为(8, 16)；
                        3，最后在box_id为3的文本框中选中了'2208室'，其在3号文本框中的start、end值为(7, 12)。
                    '公司名称著录项'著录像类似。
                    
                    例子：
                    [{'fragment': [{'box_id': 0, 'mention': ['上海市', 'SOHO东海广场'], 'offset_map': [(2, 5), (8, 16)]},
                                   {'box_id': 3, 'mention': ['2208室'], 'offset_map': [(7, 12)]}],
                      'id': 0,
                      'label': '住址著录项'},
                     {'fragment': [{'box_id': 5, 'mention': ['北京酷豹（上海数豹）科技'], 'offset_map': [(3, 15)]},
                                   {'box_id': 6, 'mention': ['有限公司'], 'offset_map': [(0, 4)]}],
                      'id': 1,
                      'label': '公司名称著录项'},
                     {'fragment': [{'box_id': 21, 'mention': ['上海市南京西路'], 'offset_map': [(11, 18)]}],
                      'id': 8,
                      'label': '住址著录项'}]
                    注意：如果一个实体类型（著录项）对应不不止一个实体实例（著录结果），如'住址著录项'包括’上海市SOHO东海广场‘、’上海市南京西路‘，
                    则他们需要要在entities中分别占据一项记录。
        relations: 一个空列表（不是None）。目前不做关系抽取。

    """

    """
        # AIDP3.1拉框版文档标注导出数据格式
        数据导出成jsonlines格式，每行一个样本，记录一张图片的信息。对应的真实图片放在jsonl文件的同级目录下的images/file_name文件夹下。在jsonl文件中，每个样本是一个字典，包含字段：
            'fileBookName':  # 如，同北京设计一致，需要确保同一案卷同一案件内的所有图片的该字段值相同。
            'volume_id': # 案卷id，用以区分整批数据中的不同案卷。
            'file_id': # 案件id，用以区分同一案卷中的不同案件。
            'page_id' # 图片页面id，用以区分不同的图片。
            # 'word': List[str], ocr得到的文字列表。
            # 'bbox': List[List[int, int, int, int]], ocr得到的所有文本框的bbox列表。
            'image': str, 记录jsonl文件的同级images目录到该图片文件的路径信息）,如，本条样本对应的图片名为'pig1.jgp',其放在images/pig目录下，则本字段记录为'images/pig/pig1.jpg'。
            'entities': 如下示例，
                        id代表该图片中标注的实体的id,从0开始，依次递增。
                        record_type:用来记录该实体是案卷、案件、页面中的实体，可取值有：volume, file, page
                        label代表实体标签（著录项）。
                        fragment字段记录了该著录项的值，该值是一个列表，列表中每个元素都是一个字典，每个fragment都代表一个实体，当标注不方便时可用多个box代替，但如果最终在处理发现他们不连续，无法拼接成一个实体时，则会被自动分成多个实体。
                            字典中bbox表示用户拉框的坐标（长为4的整数列表，分别代表左上角x坐标、左上角y坐标、右下角x坐标、右下角y坐标）；
                            mention表示拉框ocr的结果。列表中元素的顺序和用户拉框顺序一致。
                        例子：
                         [{'fragment': [{'bbox': [1200, 23, 2450, 67], 'mention': '上海市SOHO东'},
                                       {'bbox': [90, 80, 358, 159], 'mention': '海广场'}], 
                           'id': 0,
                           'record_type': 'volume',  
                           'label': '住址著录项'},
                          {'fragment': [{'bbox': [600, 63, 1040, 120], 'mention': '北京酷豹（上海数豹）科技有限公司'}],
                           'id': 1,
                           'record_type': 'file',
                           'label': '公司名称著录项'}]
            relations: 没有的话，是一个空列表。
                       id代表该图片中标注的关系id,从0开始，依次递增。
                       [{'id': 0, 'from_id': 1, 'to_id': 0, 'type': '办公地点'}]
    """
