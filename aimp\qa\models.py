from datetime import datetime
from sqlalchemy import <PERSON><PERSON><PERSON>, Column, Integer, String, TEXT, DateTime
from common.database import Base


# 定义 User 类
class QA(Base):
    __tablename__ = 'qa'  # 定义表名
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    name = Column(String(128), unique=True)
    knowledge_graph = Column(Integer)
    intentionNums = Column(Integer, default=0)
    config = Column(TEXT)
    is_active = Column(Boolean, default=False)
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now)

