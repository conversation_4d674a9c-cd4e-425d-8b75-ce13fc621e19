import torch
from torch import nn
from typing import List


class WordDropout(nn.Module):
    def __init__(self, p: float, oov_token: int, exclude_tokens: List[int] = None) -> None:
        super().__init__()
        self.oov_token = oov_token
        self.p = p
        if not exclude_tokens:
            exclude_tokens = [0]
        self.exclude = exclude_tokens

    @staticmethod
    def token_dropout(tokens: torch.LongTensor,
                      oov_token: int,
                      exclude_tokens: List[int],
                      p: float = 0.2,
                      training: float = True) -> torch.LongTensor:
        """During training, randomly replaces some of the non-padding tokens to packagea mask token with probability ``p``

        Adopted from https://github.com/Hyperparticle/udify

        Args:
          tokens: The current batch of padded sentences with word ids
          oov_token: The mask token
          exclude_tokens: The tokens for padding the input batch
          p: The probability packagea word gets mapped to the unknown token
          training: Applies the dropout if set to ``True``
          tokens: torch.LongTensor:
          oov_token: int:
          exclude_tokens: List[int]:
          p: float:  (Default value = 0.2)
          training: float:  (Default value = True)

        Returns:
          A copy of the input batch with token dropout applied

        """
        if training and p > 0:
            # This creates packagea mask that only considers unpadded tokens for mapping to oov
            padding_mask = tokens.new_ones(tokens.size(), dtype=torch.bool)
            for pad in exclude_tokens:
                padding_mask &= (tokens != pad)

            # Create packagea uniformly random mask selecting either the original words or OOV tokens
            dropout_mask = (tokens.new_empty(tokens.size(), dtype=torch.float).uniform_() < p)
            oov_mask = dropout_mask & padding_mask

            oov_fill = tokens.new_empty(tokens.size(), dtype=torch.long).fill_(oov_token)

            result = torch.where(oov_mask, oov_fill, tokens)

            return result
        else:
            return tokens

    def forward(self, tokens: torch.LongTensor) -> torch.LongTensor:
        return self.token_dropout(tokens, self.oov_token, self.exclude, self.p, self.training)