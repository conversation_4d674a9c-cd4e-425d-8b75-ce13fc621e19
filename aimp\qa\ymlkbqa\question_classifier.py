import ahocorasick
from build_graph import get_statistics
from openpyxl import Workbook, load_workbook
schema = ['姓名', '性别', '出生日期', '哪里人', '入伍日期', '部队', '牺牲日期', '战争战役', '职务', '别名', '政治面貌', {'战功': ['次数']}, '次数', '入党日期']


class QuestionClassifier:
    def __init__(self):
        self.property_question_word = ['性别', '出生日期', '哪里人', '入伍日期', '部队', '牺牲日期', '战争战役', '职务', '别名', '政治面貌', '战功', '入党日期']
        self.pro_map = {'性别': 'gender', '出生日期': 'birthday', '哪里人': 'birthplace', '入伍日期': 'join_army_day',
                        '牺牲日期': 'sacrifice_day', '职务': 'duty', '别名': 'alias', '政治面貌': 'politics_status', '战功': 'feat',
                        '入党日期': 'join_party_day', '部队': 'troop', '战争战役': 'battle'}

        self.gender_query = ['性别', '是男是女', '是男', '是女']

        self.date_query = ['时间', '时辰', '日期', '日子', '时候', '何时', '那一天', '那天', '哪天', '哪一天']

        self.birthday_verb = ['出生']
        self.birthday_noun = ['生日', '生辰', '诞辰']
        self.join_army_day_verb = ['参军', '从军', '入伍', '当兵', '成为军人', '变成军人', '成为志愿军', '变成志愿军']
        self.join_army_day_noun = []
        self.join_party_day_verb = ['入党', '参党', '从党', '成为党员', '变成党员']
        self.join_party_day_noun = []
        self.sacrifice_day_verb = ['牺牲', '去世', '去逝', '死亡', '死', '亡', '殒命', '逝世', '丧生', '卒']
        self.sacrifice_day_noun = ['忌日', '祭日', '忌辰', '祭辰', '卒日']  # 错别字也添加进去

        self.battle_query = ['战争战役', '战争', '战斗', '战役', '斗争']
        self.duty_query = ['职务', '工作', '职位', '官职', '岗位', '职责']
        self.alias_query = ['别名', '曾用名', '其它名']
        self.politics_status_query = ['政治面貌', '政治状态']
        self.troop_query = ['部队', '军队', '集团', '团体', '部门', '军团', '军', '师', '团', '营', '连', '排']
        self.feat_query = ['战功', '荣誉', '功劳', '军功', '战工', '工劳', '军工']

        self.where_query = ['哪里', '那里', '地方', '地']
        self.birthplace_verb = ['出生于', '出生自', '来自', '来于', '居住', '住', '是哪', '在哪']  # 这里暂时不适用'出生',之后修改query_keywords_dict的value为list。
        self.birthplace_noun = ['哪里人', '那里人', '籍贯', '老家', '居住地', '家乡', '出生地']

        # self.number_query = ['多少', '哪些', '哪个', '哪一', '哪几', '哪些', '那个', '那一', '那几', '那些', '几多', '几个']

        self.query_keywords_dict = dict([(w, 'gender_query') for w in self.gender_query] +
                                        [(w, 'date_query') for w in self.date_query] +
                                        [(w, 'birthday_verb') for w in self.birthday_verb] +
                                        [(w, 'birthday_noun') for w in self.birthday_noun] +
                                        [(w, 'join_army_day_verb') for w in self.join_army_day_verb] +
                                        [(w, 'join_army_day_noun') for w in self.join_army_day_noun] +
                                        [(w, 'join_party_day_verb') for w in self.join_party_day_verb] +
                                        [(w, 'join_party_day_noun') for w in self.join_party_day_noun] +
                                        [(w, 'sacrifice_day_verb') for w in self.sacrifice_day_verb] +
                                        [(w, 'sacrifice_day_noun') for w in self.sacrifice_day_noun] +
                                        [(w, 'battle_query') for w in self.battle_query] +
                                        [(w, 'duty_query') for w in self.duty_query] +
                                        [(w, 'alias_query') for w in self.alias_query] +
                                        [(w, 'politics_status_query') for w in self.politics_status_query] +
                                        [(w, 'troop_query') for w in self.troop_query] +
                                        [(w, 'feat_query') for w in self.feat_query] +
                                        [(w, 'where_query') for w in self.where_query] +
                                        [(w, 'birthplace_verb') for w in self.birthplace_verb] +
                                        [(w, 'birthplace_noun') for w in self.birthplace_noun]
                                        # + [(w, 'number_query') for w in self.number_query]
                                        )

        self.query_keywords_tree = self.build_actree(self.gender_query
                                                     + self.date_query
                                                     + self.birthday_verb +
                                                     self.birthday_noun + self.join_army_day_verb +
                                                     self.join_army_day_noun + self.join_party_day_verb +
                                                     self.join_party_day_noun + self.sacrifice_day_verb +
                                                     self.sacrifice_day_noun + self.battle_query + self.duty_query +
                                                     self.alias_query + self.politics_status_query + self.troop_query +
                                                     self.feat_query
                                                     + self.where_query
                                                     + self.birthplace_verb +
                                                     self.birthplace_noun
                                                     # + self.number_query
                                                     )

        # self.where_qw = ['住哪', '是哪', '在哪', '老家', '籍贯', '家乡']
        #
        # self.date_qw = ['哪一天', '哪天', '日期', '时候']
        # self.sacrifice_qw = ['牺牲', '死亡', '去世']
        # self.join_party = ['入党', '加入', '入伍日期']
        #
        # self.how_many_question_word = ['多少', '有多少', '有哪些', '哪一些', '哪几个']

        # (_, _, _, _, _, person_name, person_alias, person_gender, person_birthplace, person_duty,
        #  person_politics_status, person_feat, troop_name, battle_name) = get_statistics('yml6structure.jsonl')
        #
        # self.region_words = list(person_name) + list(person_alias) + list(person_gender) + \
        #                     list(person_birthplace) + list(person_duty) + list(person_politics_status) + \
        #                     list(person_feat) + list(troop_name) + list(battle_name)
        # self.region_tree = self.build_actree(list(self.region_words))
        # self.wdtype_dict = dict()
        # self.wdtype_dict.update({(p_name, '姓名') for p_name in person_name})
        # self.wdtype_dict.update({(p_alias, '别名') for p_alias in person_alias})
        # self.wdtype_dict.update({(p_gender, '性别') for p_gender in person_gender})
        # self.wdtype_dict.update({(p_birthplace, '哪里人') for p_birthplace in person_birthplace})
        # self.wdtype_dict.update({(p_duty, '职务') for p_duty in person_duty})
        # self.wdtype_dict.update({(p_politics_status, '政治面貌') for p_politics_status in person_politics_status})
        # self.wdtype_dict.update({(p_feat, '战功') for p_feat in person_feat})
        # self.wdtype_dict.update({(t_name, '部队') for t_name in troop_name})
        # self.wdtype_dict.update({(b_name, '战争战役') for b_name in battle_name})

        def read_columns(path):
            wb = load_workbook(path)
            ws = wb.worksheets[0]
            columns = ws.columns
            label = ws.title
            label2instance_set = dict()
            for col in columns:
                column_label = col[0].value
                for row in col[1:]:
                    if row.value:
                        if column_label == 'ID':
                            pass
                        elif column_label in ['name', 'gender', 'duty']:
                            label2instance_set.setdefault(column_label, set()).add(row.value)
                        elif column_label == 'battle':
                            # 暂行方案，之后引入实体识别模型，通过模糊匹配或者相似度查找。
                            label2instance_set.setdefault(column_label, set()).add(row.value)
                        elif column_label in ['birthplace', 'troop']:
                            # 暂时先简单的添加到actree，之后在切分。
                            label2instance_set.setdefault(column_label, set()).add(row.value)
                        elif column_label == 'feat':
                            for fe in row.value.split('，'):
                                label2instance_set.setdefault(column_label, set()).add(fe)
                        elif column_label in ['join_army_day', 'sacrifice_day', 'birthday']:
                            # 暂行方案，之后日期不放入到actree中，而是通过正则匹配。
                            label2instance_set.setdefault(column_label, set()).add(row.value)
                        elif column_label == 'politics_status':
                            label2instance_set.setdefault(column_label, set()).add(row.value)
            label2instance_set['politics_status'].update(['党员', '团员', '青年团', '共产党'])
            return label2instance_set
        label2instance_set = read_columns(path='二等功及以上英雄_图库文件.xlsx')
        self.region_tree = self.build_actree(set(sum([list(v) for v in label2instance_set.values()], [])))
        self.wdtype_dict = dict()
        for k, v in label2instance_set.items():
            for val in v:
                self.wdtype_dict.setdefault(val, []).append(k)

    '''构造actree，加速过滤'''
    def build_actree(self, wordlist):
        actree = ahocorasick.Automaton()
        for index, word in enumerate(wordlist):
            actree.add_word(word, (index, word))
        actree.make_automaton()
        return actree

    def check_query_keywords(self, question):
        query_keywords = []
        for i in self.query_keywords_tree.iter_long(question):
            wd = i[1][1]
            query_keywords.append(wd)
        stop_wds = []
        for wd1 in query_keywords:
            for wd2 in query_keywords:
                if wd1 in wd2 and wd1 != wd2:
                    stop_wds.append(wd1)
        final_wds = [i for i in query_keywords if i not in stop_wds]
        final_dict = {i: self.query_keywords_dict.get(i) for i in final_wds}
        return final_dict

    def check_entity(self, question):
        region_wds = []
        for i in self.region_tree.iter(question):
            wd = i[1][1]
            region_wds.append(wd)
        stop_wds = []
        for wd1 in region_wds:
            for wd2 in region_wds:
                if wd1 in wd2 and wd1 != wd2:
                    stop_wds.append(wd1)
        final_wds = [i for i in region_wds if i not in stop_wds]
        final_dict = {i: self.wdtype_dict.get(i) for i in final_wds}
        return final_dict

    def classify2(self, question):
        # data = {}
        word2entity_type = self.check_entity(question)
        if not word2entity_type:
            return {}
        # data['args'] = word2entity_type
        #收集问句当中所涉及到的实体类型
        # words = []
        # types = []
        # for word_, type_ in info_dict.items():
        #     words.append(word_)
        #     types.append(type_)

        entity_type2word = {}
        for words_, types_ in word2entity_type.items():
            for type_ in types_:
                if type_ in entity_type2word:
                    entity_type2word[type_].append(words_)
                else:
                    entity_type2word[type_] = [words_]

        word2qk_type = self.check_query_keywords(question)  # (str: str)
        qk_type_set = set(list(word2qk_type.values()))

        # {'性别': 'gender', '出生日期': 'birthday', '哪里人': 'birthplace', '入伍日期': 'join_army_day',
        #  '牺牲日期': 'sacrifice_day', '职务': 'duty', '别名': 'alias', '政治面貌': 'politics_status', '战功': 'feat',
        #  '入党日期': 'join_party_day'}
        sqls_question_class = []
        # property_question
        if 'name' in entity_type2word and qk_type_set:
            xms = entity_type2word['name']
            q_or_k2entity_type = {'gender_query': 'gender', 'battle_query': 'battle', 'duty_query': 'duty',
                                  'alias_query': 'alias', 'politics_status_query': 'politics_status',
                                  'troop_query': 'troop', 'feat_query': 'feat', 'birthday_noun': 'birthday',
                                  'join_army_day_noun': 'join_army_day', 'sacrifice_day_noun': 'sacrifice_day',
                                  'birthplace_noun': 'birthplace'
                                  }
            # attr types
            entity_types = [q_or_k2entity_type[qk] for qk in qk_type_set if qk in q_or_k2entity_type]

            # if 'date_query' in qk_type_set:
            if 'birthday_verb' in qk_type_set:
                entity_types.append('birthday')
            if 'join_army_day_verb' in qk_type_set:
                entity_types.append('join_army_day')
            if 'join_party_day_verb' in qk_type_set:
                entity_types.append('join_party_day')
            if 'sacrifice_day_verb' in qk_type_set:
                entity_types.append('sacrifice_day')

            # if 'where_query' in qk_type_set:
            if 'birthplace_verb' in qk_type_set:
                entity_types.append('birthplace')
            entity_types = list(set(entity_types))

            if xms and entity_types:
                entity_types.append('ID')
                for xm in xms:
                    sql = "MATCH (m:人物) where m.name = '%s' return m" % xm
                    # sql += ' '.join(["'%s', m.%s" % (e_type, e_type) for e_type in entity_types])
                    sqls_question_class.append({'cypher': sql,
                                                'intent': 1,
                                                'input_fields': {'entity_name': xm, 'entity_type': '人物'},
                                                'output_keys': entity_types[:]})
        elif entity_type2word:  # entity_question
            #  暂时只处理vals中第一个。
            sql = "MATCH(m: 人物) where %s return m" % ' and '.join(["m.%s = '%s'" % (type_, vals[0]) for type_, vals in entity_type2word.items()])

            sqls_question_class.append({'cypher': sql,
                                        'intent': 2,
                                        'input_fields': [{'attr': type_, 'attr_value': vals[0]} for type_, vals in entity_type2word.items()],
                                        'output_keys': ['人物/name', 'ID']})

        # elif 'number_query' in qk_type_set and entity_type2word:
        #     sqls_question_class.append(("MATCH (m:人物) where " + " and ".join(["m.{0} = '{1}'".
        #         format(self.pro_map[e_type], word) for e_type, words in entity_type2word.items() for word in words])
        #                                 + " return m.name", ('number_question', list(entity_type2word.keys()))))

        """
        property_ = []
        for pro in self.property_question_word:
            if pro in question:
                property_.append(pro)
        sqls = []
        if property_ and ('姓名' in types):
            # question_type = 'person_property'
            # question_types.append(question_type)
            xm = words[types.index('姓名')]
            sqls += ["MATCH (m:人物) where m.name = '{0}' return m.name, m.{1}".format(xm, self.pro_map[i]) for i in property_]

        # if self.check_words(self.where_qw, question) and ('姓名' in types):
        #     question_type = 'where_from'
        #     question_types.append(question_type)
        #
        # if self.check_words(self.date_qw, question) and self.check_words(self.join_party, question) and ('姓名' in types):
        #     question_type = 'data_join_party'
        #     question_types.append(question_type)
        #
        # if self.check_words(self.date_qw, question) and self.check_words(self.sacrifice_qw, question) and ('姓名' in types):
        #     question_type = 'data_sacrifice'
        #     question_types.append(question_type)
        #
        if self.check_words(self.how_many_question_word, question) and set(self.property_question_word).intersection(types):
            # question_type = 'how_many'
            # question_types.append(question_type)
            e_types = set(self.property_question_word).intersection(types)
            e_val = [words[types.index(e_t)] for e_t in e_types]

            sqls += ["MATCH (m:人物) where m.{0} = '{1}' return m.name".format(self.pro_map[t], v) for t, v in zip(e_types, e_val)]
        #
        # data['question_types'] = question_types
        """
        return sqls_question_class


    def classify(self, question):
        data = {}
        info_dict = self.check_entity(question)
        if not info_dict:
            return {}
        data['args'] = info_dict
        #收集问句当中所涉及到的实体类型
        words = []
        types = []
        for word_, type_ in info_dict.items():
            words.append(word_)
            types.append(type_)

        property_ = []
        for pro in self.property_question_word:
            if pro in question:
                property_.append(pro)
        sqls = []
        if property_ and ('姓名' in types):
            # question_type = 'person_property'
            # question_types.append(question_type)
            xm = words[types.index('姓名')]
            sqls += ["MATCH (m:人物) where m.name = '{0}' return m.name, m.{1}".format(xm, self.pro_map[i]) for i in property_]

        # if self.check_words(self.where_qw, question) and ('姓名' in types):
        #     question_type = 'where_from'
        #     question_types.append(question_type)
        #
        # if self.check_words(self.date_qw, question) and self.check_words(self.join_party, question) and ('姓名' in types):
        #     question_type = 'data_join_party'
        #     question_types.append(question_type)
        #
        # if self.check_words(self.date_qw, question) and self.check_words(self.sacrifice_qw, question) and ('姓名' in types):
        #     question_type = 'data_sacrifice'
        #     question_types.append(question_type)
        #
        if self.check_words(self.how_many_question_word, question) and set(self.property_question_word).intersection(types):
            # question_type = 'how_many'
            # question_types.append(question_type)
            e_types = set(self.property_question_word).intersection(types)
            e_val = [words[types.index(e_t)] for e_t in e_types]

            sqls += ["MATCH (m:人物) where m.{0} = '{1}' return m.name".format(self.pro_map[t], v) for t, v in zip(e_types, e_val)]
        #
        # data['question_types'] = question_types
        return sqls

    def check_words(self, wds, sent):
        for wd in wds:
            if wd in sent:
                return True
        return False


if __name__ == '__main__':
    classifier = QuestionClassifier()
    print(classifier.classify2('有几个人被追记特等功'))
    print('finish.')
