if __name__ == '__main__':
    from kbnlp.taskflow import Taskflow
    from pprint import pprint
    intent = Taskflow('intent_detection', devcie='cuda:0',
                      task_path=r'D:\workspace\pycharm\nlp_project\kbnlp\data\prompted\intent_detection_uie_nano\checkpoint2\model_best',
                      schema={'音乐播放': ['播放模式', '语言', '唱片年份', '乐器', '唱片专辑', '歌手', '歌曲名称'],
                              '旅行查询': ['日期', '出发地', '具体时间', '目的地']})
    res = intent(['我要去洛阳涧西区看看怎么走', '现在想听80年代的英文歌', '你找一个微微一笑很倾城的主题曲单曲循环吧'])
    pprint(res)
