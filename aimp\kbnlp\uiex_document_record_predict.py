# -*- coding: utf-8 -*-
from kbnlp.taskflow import Taskflow
import argparse
from pprint import pprint

parse = argparse.ArgumentParser(description='') 
parse.add_argument('--image', type=str, help='') 
parse.add_argument('--device', type=str, default='cuda', help='')
parse.add_argument('--pp_ocr', action='store_true')
parse.add_argument('--task', type=str, default='document_record')
# task=information_extraction表示跳过分类。
parse.add_argument('--cls_max_seq_len', type=int, default=512, help='')
parse.add_argument('--position_prob', default=0.5, type=float, help='') 
parse.add_argument('--schema', default=['DJYY', 'SLSJ', 'SLR', 'YWBH', 'YWRXM', 'BDCZLDZ', 'QLRXM', 'DJLX'], type=str, nargs='+')
parse.add_argument('--task_path', default='/home/<USER>/workspace/nlp_project/kbnlp/data/finetuned/uiex-real-estate-record/checkpoints/checkpoint-1500', type=str, help='')

args = parse.parse_args()
print(args)
#uiex = Taskflow('document_record', task_path='/home/<USER>/workspace/nlp_project/kbnlp/data/finetuned/uiex-real-estate-record/checkpoints/checkpoint-1500',
uiex = Taskflow(args.task, task_path=args.task_path, pp_ocr=args.pp_ocr, schema=args.schema, device=args.device, cls_max_seq_len=args.cls_max_seq_len, position_prob=args.position_prob)
# pprint(uiex({'image': args.image}))

pprint(uiex([{'image_name': '01.jpg',
              'image_id': '0',
              'image_path': 'C:\\Users\\<USER>\\Desktop\\data\\scan\\分机著录测试\\admin\\01087\\01.jpg',
              'fileBookName': '01087',
              'file_id': '01087-0001',
              'volume_id': '01087'}]))