import os
import re
from functools import wraps
from urllib import parse

import yaml
from pathlib import Path


if 'PREFIX' in os.environ and os.environ['PREFIX']:
    config_path = os.path.join(os.environ['PREFIX'], "share/aimp/static/config/base_config.yaml")
else:
    config_path = Path(__file__).parent.parent / "static/config/base_config.yaml"


class ConfigMap(dict):
    __setattr__ = dict.__setitem__
    __getattr__ = dict.__getitem__


def configMap():
    with open(str(config_path), mode='r', encoding='utf-8') as fd:
        data = yaml.load(fd, Loader=yaml.FullLoader)
        _config = data.get(data.get("run_config"))
        _config["LAST_METADATA_PATH"] = os.path.expanduser(_config["LAST_METADATA_PATH"])
    inst = ConfigMap()
    for k, v in _config.items():
        inst[k] = v

    return inst


def parse_uri(database_uri):
    search_ret = re.search("://.*?:(.*)@", database_uri)
    password = search_ret.group(1)
    new_password = parse.quote_plus(password)
    new_database_uri = database_uri.replace(password, new_password)

    return new_database_uri


# def exception_decoration(func):
#     """
#     异常捕获
#     :param func:
#     :return:
#     """
#     @wraps(func)
#     def inner(request, *args, **kwargs):
#         try:
#             return func(request, *args, **kwargs)
#         except Exception as e:
#             log.error(traceback.format_exc())
#             if not hasattr(e, "data"):
#                 if hasattr(e, "orig"):
#                     msg = e.orig.args[-1]
#                 else:
#                     msg = "please check the error in the log"
#             else:
#                 msg = e.data
#             return {"code": 400, "success": False, "data": "", "msg": msg}
#     return inner
