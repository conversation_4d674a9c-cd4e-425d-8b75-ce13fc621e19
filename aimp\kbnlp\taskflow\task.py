# coding:utf-8
# Copyright (c) 2021  PaddlePaddle Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License"
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import abc
import math
import os
from abc import abstractmethod
import torch


from ..scripts.information_extraction.log import logger
from .utils import cut_chinese_sent
from kbnlp import KBNLP_HOME


class Task(metaclass=abc.ABCMeta):
    """
    The meta classs of taskflow in Taskflow. The meta class has the five abstract function,
        the subclass need to inherit from the meta class.
    Args:
        task(string): The name of taskflow.
        model(string): The model name in the taskflow.
        kwargs (dict, optional): Additional keyword arguments passed along to the specific taskflow.
            home_path (string):指定kbnlp存放tasks相关文件的家目录。
            task_path (string): 指定任务模型加载的路径。
    """

    def __init__(self, task, model, device='cpu', **kwargs):
        self.task = task
        self.model = model
        self.kwargs = kwargs
        self._usage = ""
        # The torch module instance
        self._model = None
        self._device = device
        if not (torch.cuda.is_available() and isinstance(self._device, str) and self._device.startswith('cuda')):
            self._device = 'cpu'
        self._to_script = kwargs['to_script'] if 'to_script' in kwargs else False
        self._max_seq_len = kwargs['max_seq_len'] if 'max_seq_len' in kwargs else 512
        self._batch_size = kwargs['batch_size'] if 'batch_size' in kwargs else 8
        self._split_sentence = kwargs['split_sentence'] if 'split_sentence' in kwargs else False
        self._home_path = self.kwargs["home_path"] if "home_path" in self.kwargs else KBNLP_HOME
        self._custom_model = False
        if "task_path" in self.kwargs:
            self._task_path = self.kwargs["task_path"]
            self._custom_model = True
        else:
            self._task_path = os.path.join(self._home_path, "taskflow", self.task, self.model)
        self._init_class = None
        self._model_prepared = False

    def prepare_model(self):
        """
        在__call__中调用.
        Returns:

        """
        if not self._model_prepared:
            self._check_task_files()
            self._construct_tokenizer()
            self._check_predictor_type()
            self._get_inference_model()
            self._model_prepared = True

    @abstractmethod
    def _construct_model(self):
        """
        Construct the inference model for the predictor.
        """

    @abstractmethod
    def _construct_tokenizer(self):
        """
        Construct the tokenizer for the predictor.
        """

    @abstractmethod
    def _preprocess(self, inputs, padding=True, add_special_tokens=True):
        """
        Transform the raw text to the model inputs, two steps involved:
           1) Transform the raw text to token ids.
           2) Generate the other model inputs from the raw text and token ids.
        """

    @abstractmethod
    def _run_model(self, inputs):
        """
        Run the taskflow model from the outputs of the `_tokenize` function.
        """

    @abstractmethod
    def _postprocess(self, inputs):
        """
        The model output is the logits and pros, this function will convert the model output to raw text.
        """

    def _check_task_files(self):
        """
        Check files required by the taskflow.
        """
        pass

    def _check_predictor_type(self):
        pass

    def _construct_ocr_engine(self, lang="ch", use_angle_cls=True):
        """
        Construct the OCR engine
        """
        try:
            import paddle
            from paddleocr import PaddleOCR
        except ImportError:
            raise ImportError("Please install the dependencies first, pip install paddleocr")
        use_gpu = False if paddle.get_device() == "cpu" else True
        self._ocr = PaddleOCR(use_angle_cls=use_angle_cls, show_log=False, use_gpu=use_gpu, lang=lang)

    # def _construce_layout_analysis_engine(self):
    #     """
    #     Construct the layout analysis engine
    #     """
    #     try:
    #         from paddleocr import PPStructure
    #     except ImportError:
    #         raise ImportError("Please install the dependencies first, pip install paddleocr")
    #     self._layout_analysis_engine = PPStructure(table=False, ocr=True, show_log=False)


    # def _prepare_onnx_mode(self):
    #     pass

    def _get_inference_model(self):
        """
        Return the inference program, inputs and outputs in static mode.
        """
        self._construct_model()
        self.to(self.device)
        if self._to_script and hasattr(self._model, 'script') and callable(getattr(self._model, 'script', None)):
            self._model = self._model.script()

    @property
    def device(self):
        return self._device

    def to(self, device):
        self._device = device
        self._model.to(self.device)

    def _convert_dygraph_to_static(self):
        """
        Convert the dygraph model to static model.
        """
        pass

    def _check_input_text(self, inputs):
        """
        Check whether the input text meet the requirement.
        """
        # inputs = inputs[0]
        if isinstance(inputs, str):
            if len(inputs.strip()) == 0:
                raise ValueError("Invalid inputs, input text should not be empty text, please check your input.")
            inputs = [inputs]
        elif isinstance(inputs, list):
            if not (isinstance(inputs[0], str) and len(inputs[0].strip()) > 0):
                raise TypeError(
                    "Invalid inputs, input text should be list of str, and first element of list should not be empty text."
                )
        else:
            raise TypeError(
                "Invalid inputs, input text should be str or list of str, but type of {} found!".format(type(inputs))
            )
        return inputs

    def _auto_splitter(self, input_texts, max_text_len, bbox_list=None, split_sentence=False):
        """
        Split the raw texts automatically for model inference.
        Args:
            input_texts (List[str]): input raw texts.
            max_text_len (int): cutting length.
            bbox_list (List[float, float,float, float]): bbox for document input.
            split_sentence (bool): If True, sentence-level split will be performed.
                `split_sentence` will be set to False if bbox_list is not None since sentence-level split is not support for document.
        return:
            short_input_texts (List[str]): the short input texts for model inference.
            input_mapping (dict): mapping between raw text and short input texts.
        """
        input_mapping = {}
        short_input_texts = []
        cnt_org = 0
        cnt_short = 0
        with_bbox = False
        if bbox_list:
            with_bbox = True
            short_bbox_list = []
            if split_sentence:
                logger.warning(
                    "`split_sentence` will be set to False if bbox_list is not None since sentence-level split is not support for document."
                )
                split_sentence = False

        for idx in range(len(input_texts)):
            if not split_sentence:
                sens = [input_texts[idx]]
            else:
                sens = cut_chinese_sent(input_texts[idx])
            for sen in sens:
                lens = len(sen)
                if lens <= max_text_len:
                    short_input_texts.append(sen)
                    if with_bbox:
                        short_bbox_list.append(bbox_list[idx])
                    input_mapping.setdefault(cnt_org, []).append(cnt_short)
                    cnt_short += 1
                else:
                    temp_text_list = [sen[i : i + max_text_len] for i in range(0, lens, max_text_len)]
                    short_input_texts.extend(temp_text_list)
                    if with_bbox:
                        temp_bbox_list = [bbox_list[idx][i : i + max_text_len] for i in range(0, lens, max_text_len)]
                        short_bbox_list.extend(temp_bbox_list)
                    short_idx = cnt_short
                    cnt_short += math.ceil(lens / max_text_len)
                    temp_text_id = [short_idx + i for i in range(cnt_short - short_idx)]
                    input_mapping.setdefault(cnt_org, []).extend(temp_text_id)
            cnt_org += 1
        if with_bbox:
            return short_input_texts, short_bbox_list, input_mapping
        else:
            return short_input_texts, input_mapping

    def _auto_joiner(self, short_results, input_mapping, is_dict=False):
        """
        Join the short results automatically and generate the final results to match with the user inputs.
        Args:
            short_results (List[dict] / List[List[str]] / List[str]): input raw texts.
            input_mapping (dict): cutting length.
            is_dict (bool): whether the element type is dict, default to False.
        return:
            short_input_texts (List[str]): the short input texts for model inference.
        """
        concat_results = []
        elem_type = {} if is_dict else []
        for k, vs in input_mapping.items():
            single_results = elem_type
            for v in vs:
                if len(single_results) == 0:
                    single_results = short_results[v]
                elif isinstance(elem_type, list):
                    single_results.extend(short_results[v])
                elif isinstance(elem_type, dict):
                    for sk in single_results.keys():
                        if isinstance(single_results[sk], str):
                            single_results[sk] += short_results[v][sk]
                        else:
                            single_results[sk].extend(short_results[v][sk])
                else:
                    raise ValueError(
                        "Invalid element type, the type of results "
                        "for each element should be list of dict, "
                        "but {} received.".format(type(single_results))
                    )
            concat_results.append(single_results)
        return concat_results

    def help(self):
        """
        Return the usage message of the current taskflow.
        """
        print("Examples:\n{}".format(self._usage))

    def __call__(self, inputs, **kwargs):
        self.prepare_model()
        inputs = self._preprocess(inputs, **kwargs)
        outputs = self._run_model(inputs)
        results = self._postprocess(outputs)
        return results
