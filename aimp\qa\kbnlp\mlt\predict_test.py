if __name__ == '__main__':
    from qa.kbnlp import MltParser, model_path
    import torch
    from time import time

    # model_path = 'data/pretrained/temp.jsonl/electra_small_tok_dep_standard/save_load_test_epoch_20'

    mlt = MltParser(save_dir=model_path, device='cuda:0' if torch.cuda.is_available() else 'cpu')

    # from kbnlp.mlt.task.segment import Segment, read_cws_tsv_data
    # # _CTB8_HOME = r'/home/<USER>/.hanlp'  # C:/Users/<USER>/AppData/Roaming/hanlp
    # _CTB8_HOME = r'C:/Users/<USER>/AppData/Roaming/hanlp'
    # CTB8_CWS_DEV = _CTB8_HOME + '/thirdparty/wakespace.lib.wfu.edu/bitstream/handle/10339/39379/LDC2013T21/data/tasks/cws/dev.txt'
    # data = [''.join(d) for d in read_cws_tsv_data(CTB8_CWS_DEV)[:]]
    # a = map(len, data)
    # print(sum(map(len, data)) / len(data))
    # # data = read_cws_tsv_data(CTB8_CWS_DEV)[:]
    # print(len(data))
    # s = time()
    # res = mlt(data, batch_size=2, tasks=['tok', 'rte'])
    # e = time()
    # print(e - s)


    # data = [
    #     '丝角蝗科，Oedipodidae，昆虫纲直翅目蝗总科的一个科。',
    #     '融安县属中亚热带季风气候区。',
    #     '马红宝 男 汉族，1949年8月生，浙江省长兴县人，1978年8月加入中国共产党，1967年9月参加工作，大普文化。',
    #     '欢迎关注哈工大讯飞联合实验室官方微信公众号。',
    #     '华纳音乐旗下的新垣结衣在12月21日于日本武道馆举办歌手出道活动。',
    #     '2021年HanLPv2.1为生产环境带来次世代最先进的多语种NLP技术。',
    # ]
    data = [
        '2021年，天山堡村被评为重庆市市级文明村。',
        '冉慧是重庆市酉阳县桃花源街道天山堡村党支部书记、村委会主任。',
        '马红宝 男 汉族，1949年8月生，浙江省长兴县人，1978年8月加入中国共产党，1967年9月参加工作，大普文化。',
        '当选全国人大代表后，冉慧多次在全国两会期间提出建议，建议加大对西部贫困山区的交通基础设施投入。',
        '据央视新闻，当地时间3月4日，俄罗斯国防部表示，乌克兰扎波罗热核电站已被俄军控制。 此前，乌克兰国家原子能监管监察局通过社交平台发布消息说，扎波罗热核电站已被俄军控制，电站人员工作正常，能够保证反应堆的正常运行，电厂内核辐射检测数值正常。该站系欧洲最大核电站，供应乌克兰五分之一电力。'
    ]
    res = mlt(data, batch_size=3, skip_tasks=['dep'])
    for task_name, res in res.items():
        print(task_name, ': ', res)
    print()
    '''
    data = [
            ['丝角蝗科', '，', 'Oedipodidae', '，', '昆虫纲', '直翅目', '蝗总科', '的', '一个', '科', '。'],
            ['融安县', '属', '中亚热带季风', '气候区', '。'],
            ['马红宝', '男', '汉族', '，', '1949年8月', '生', '，', '浙江省长兴县', '人', '，', '1978年8月', '加入', '中国', '共产党',
             '，', '1967年9月', '参加', '工作', '，', '大普', '文化', '。'],
            ['欢迎', '关注', '哈工大讯飞联合实验室', '官方', '微信公众号', '。'],
            ['华纳', '音乐', '旗下', '的', '新垣结衣', '在', '12月21日', '于', '日本武道馆', '举办', '歌手', '出道', '活动', '。'],
            ['2021年', 'HanLPv2.1', '为', '生产', '环境', '带来', '次世代', '最', '先进', '的', '多', '语种', 'NLP', '技术', '。'],
    ]
    res = mlt(data, batch_size=3, is_split_into_words=True, skip_tasks=['rte'])
    for task_name, res in res.items():
        print(task_name, ': ', res)
    print()
    res = mlt(data, batch_size=3, is_split_into_words=False, return_group_info=True, skip_tasks=['rte'])
    for task_name, res in res.items():
        print(task_name, ': ', res)
    '''
