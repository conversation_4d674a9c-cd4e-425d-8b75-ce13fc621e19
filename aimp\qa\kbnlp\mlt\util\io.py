import json
import os
import pickle
import sys
from collections import defaultdict, deque


def save_pickle(item, path):
    with open(path, 'wb') as f:
        pickle.dump(item, f)


def load_pickle(path):
    with open(path, 'rb') as f:
        return pickle.load(f)


def save_json(item: dict, path: str, ensure_ascii=False, cls=None, default=lambda o: repr(o), indent=2):
    dirname = os.path.dirname(path)
    if dirname:
        os.makedirs(dirname, exist_ok=True)
    with open(path, 'w', encoding='utf-8') as out:
        json.dump(item, out, ensure_ascii=ensure_ascii, indent=indent, cls=cls, default=default)


def load_json(path):
    with open(path, encoding='utf-8') as src:
        return json.load(src)


def filename_is_json(filename):
    filename, file_extension = os.path.splitext(filename)
    return file_extension in ['.json', '.jsonl']


def eprint(*args, **kwargs):
    print(*args, file=sys.stderr, **kwargs)


# words, heads 都是数组。索引0上的值不考虑，句子第1个单词的索引记为1，列表不包含虚根ROOT。虚根的索引看做原始句子的长度加1.
# 如原始句子['猫','吃','鱼']。words=['-1','猫','吃','鱼']，heads=[None, 2, 4, 2]。数组第0为无所谓是什么词。
def isCyclic(words, heads, verbose=False):
    head_dependants = defaultdict(list)
    for i in range(1, len(heads)):
        if heads[i] is None or not isinstance(heads[i], int) or heads[i] < 1 or heads[i] > len(words):
            print('不是一棵完整依存树,或者中心词超出句子范围。', heads[1:])
            return True
        head_dependants[heads[i]].append(i)
    deq = deque()
    root_ind = len(words)
    deq.append(root_ind)
    while len(deq) != 0:
        head = deq.popleft()
        if head in head_dependants:
            sons = head_dependants.pop(head)
            for son in sons:
                deq.append(son)
    if len(head_dependants) != 0:
        if verbose:
            print(words)
            print('依存树有回环。')
    return len(head_dependants) != 0


#  依存分析要求依存树符合投射性
def isProjective(words, heads, verbose=False):  # words, heads 都是数组。索引0上的值不考虑，句子第个单词的索引记为1，列表不包含虚根ROOT
    if isCyclic(words, heads, verbose):
        return False
    for dependant in range(1, len(words)):
        head = heads[dependant]
        left, right = min(dependant, head), max(dependant, head)
        for i in range(left + 1, right):
            if heads[i] < left or heads[i] > right:
                if verbose:
                    print(words)
                    print('交叉弧：', left, '<-', right, i, '<-', heads[i])
                return False
    return True


def CoNLLReader_generator(path):
    """
    :param path:
    :param root_mode: 表示句子中心词的head表示方式。last表示用句子长度加1表示；first表示用0表示；self表示用该中心词在句子中的位置表示。
    :return:
    """
    with open(path, 'r', encoding='utf-8') as fr:
        lines = fr.readlines()
        words = []
        heads = []
        tags = []
        labels = []
        length = 0
        for line in lines:
            item = line.strip().split()
            if item:
                length += 1
                words.append(item[1])
                head = int(item[6])
                if head == 0:
                    core = length
                heads.append(head)
                tags.append(item[3])
                labels.append(item[7])
            else:
                if words:
                    heads[core - 1] = length + 1
                    yield words, heads, tags, labels
                words = []
                heads = []
                tags = []
                labels = []
                length = 0
        if words:
            heads[core - 1] = length + 1
            yield words, heads, tags, labels


def CoNLLReader(path, check_projective=True, verbose=False):
    """
    读取conll格式的依存数据。
    :param path:
    :param root_mode: 表示句子中心词的head表示方式。last表示用句子长度加1表示；first表示用0表示。self表示用该中心词在句子中的位置表示。
    :param check_projective:
    :param verbose:
    :return:
    """
    return [(words, heads, tags, labels) for words, heads, tags, labels in CoNLLReader_generator(path)
            if not check_projective or isProjective([-1] + words, [-1] + heads)]
