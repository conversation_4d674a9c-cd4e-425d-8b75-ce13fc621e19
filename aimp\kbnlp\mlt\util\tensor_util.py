import torch
from torch.nn.utils.rnn import pad_sequence, pack_sequence
from typing import Union, Iterable
from functools import partial


def no_grad(func):
    def wrapper(*args, **kwargs):
        with torch.no_grad():
            return func(*args, **kwargs)

    return wrapper


def lengths_to_mask(seq_len, max_len=None):
    r"""
    .. code-block::

        >>> seq_len = torch.arange(2, 16)
        >>> mask = lengths_to_mask(seq_len)
        >>> print(mask.size())
        torch.Size([14, 15])
        >>> seq_len = torch.arange(2, 16)
        >>> mask = lengths_to_mask(seq_len, max_len=100)
        >>>print(mask.size())
        torch.Size([14, 100])

    :param torch.LongTensor seq_len: (B,)
    :param int max_len: max sequence length。
    :return:  torch.Tensor  (B, max_len)
    """
    assert seq_len.dim() == 1, f"seq_len can only have one dimension, got {seq_len.dim() == 1}."
    batch_size = seq_len.size(0)
    max_len = int(max_len) if max_len else seq_len.max().long()
    # broad_cast_seq_len = torch.arange(max_len).expand(batch_size, -1).to(seq_len)
    broad_cast_seq_len = torch.arange(max_len).unsqueeze(0).to(seq_len)
    mask = broad_cast_seq_len.lt(seq_len.unsqueeze(1))

    return mask


def pick_tensor_for_each_token2(h: torch.Tensor, token_tail: torch.Tensor, average_subwords: bool):
    if token_tail is None:
        return h
    if average_subwords:
        b = h.size(0)
        cumsum = torch.cumsum(h, dim=1)  # b * l * hidden
        first_word_tail = token_tail[:, :1]  # b * 1
        other_word_tail = token_tail[:, 1:]  # b * l-1
        first_word_size = first_word_tail + 1  # b * 1
        other_word_size = other_word_tail - token_tail[:, :-1]  # b * l-1
        other_word_size[other_word_tail.eq(0)] = 1
        b_ind = torch.arange(b).unsqueeze(-1)
        first_word = cumsum[b_ind, first_word_tail] / first_word_size.unsqueeze(-1)
        other_word = cumsum[b_ind, other_word_tail] / other_word_size.unsqueeze(-1)
        return torch.cat([first_word, other_word], dim=1)
    else:
        return h.gather(1, token_tail.unsqueeze(-1).expand(-1, -1, h.size(-1)))


def pick_tensor_for_each_token(h: torch.Tensor, token_tail: torch.Tensor, average_subwords: bool):
    if token_tail is None:
        return h
    if average_subwords:
        b = h.size(0)
        cumsum = torch.cumsum(h, dim=1)  # b * l * hidden
        first_word_tail = token_tail[:, :1]  # b * 1
        other_word_tail = token_tail[:, 1:]  # b * l-1
        first_word_size = first_word_tail + 1  # b * 1
        other_word_size = other_word_tail - token_tail[:, :-1]  # b * l-1
        other_word_size[other_word_tail.eq(0)] = 1
        b_ind = torch.arange(b).unsqueeze(-1)
        first_word = cumsum[b_ind, first_word_tail] / first_word_size.unsqueeze(-1)
        other_word = (cumsum[b_ind, other_word_tail] - cumsum[b_ind, token_tail[:, :-1]]) / other_word_size.unsqueeze(-1)
        return torch.cat([first_word, other_word], dim=1)
    else:
        return h.gather(1, token_tail.unsqueeze(-1).expand(-1, -1, h.size(-1)))

# def pick_tensor_for_each_token(h, token_span, average_subwords):
#     if token_span is None:
#         return h
#     if average_subwords and token_span.size(-1) > 1:
#         batch_size, length, max_subtoken_size = token_span.size()
#         embed = h[torch.arange(batch_size).unsqueeze(-1), token_span.view(batch_size, -1)].view(batch_size, length,
#                                                                                                 max_subtoken_size, -1)
#         n_sub_tokens = token_span.ne(0)
#         n_sub_tokens[:, 0, 0] = True
#         h_span = (embed * n_sub_tokens.unsqueeze(-1)).sum(2)
#         n_sub_tokens = n_sub_tokens.sum(-1).unsqueeze(-1)
#         zero_mask = n_sub_tokens == 0
#         if torch.any(zero_mask):
#             n_sub_tokens[zero_mask] = 1  # avoid dividing by zero
#         embed = h_span / n_sub_tokens
#
#     else:
#         embed = h.gather(1, token_span[:, :, 0].unsqueeze(-1).expand(-1, -1, h.size(-1)))
#     return embed


def pad_data(data: Union[torch.Tensor, Iterable], pad, dtype, device=None):
    """Perform the actual padding for package a given data.

    Args:
        data: Data to be padded.
        pad: Padding value.
        dtype: Data type.
        device: Device to be moved onto.

    Returns:
        torch.Tensor: 1~3D
    """
    if isinstance(data[0], torch.Tensor):
        data = pad_sequence(data, True, pad)
    elif isinstance(data[0], Iterable):
        if isinstance(data[0][0], Iterable):
            lengths = list(map(len, data))
            data = list(map(torch.tensor, sum(data, [])))
            data = pad_sequence(torch.split(pad_sequence(data).T, lengths)).transpose(0, 1).contiguous().to(device=device)
            # max_seq_len = len(max(data, key=len))
            # max_word_len = len(max([chars for words in data for chars in words], key=len))
            # ids = torch.zeros(len(data), max_seq_len, max_word_len, dtype=dtype, device=device)
            # for i, words in enumerate(data):
            #     for j, chars in enumerate(words):
            #         ids[i][j][:len(chars)] = torch.tensor(chars, dtype=dtype, device=device)
            # data = ids
        else:
            try:
                _data = data
                data = pad_sequence([torch.tensor(x, dtype=dtype) for x in data], True, pad).to(device)
            except TypeError as e:
                print(_data)
                raise e
    elif isinstance(data, list):
        data = torch.tensor(data, dtype=dtype, device=device)
    return data

# def pad_data(data: Union[torch.Tensor, Iterable], pad, dtype, device=None):
#     """Perform the actual padding for package a given data.
#
#     Args:
#         data: Data to be padded.
#         pad: Padding value.
#         dtype: Data type.
#         device: Device to be moved onto.
#
#     Returns:
#         torch.Tensor: 1~3D
#     """
#     if isinstance(data[0], torch.Tensor):
#         data = pad_sequence(data, True, pad)
#     elif isinstance(data[0], Iterable):
#         if isinstance(data[0][0], Iterable):
#             max_seq_len = len(max(data, key=len))
#             max_word_len = len(max([chars for words in data for chars in words], key=len))
#             ids = torch.zeros(len(data), max_seq_len, max_word_len, dtype=dtype, device=device)
#             for i, words in enumerate(data):
#                 for j, chars in enumerate(words):
#                     ids[i][j][:len(chars)] = torch.tensor(chars, dtype=dtype, device=device)
#             data = ids
#         else:
#             try:
#                 _data = data
#                 data = pad_sequence([torch.tensor(x, dtype=dtype, device=device) for x in data], True, pad)
#             except TypeError as e:
#                 print(_data)
#                 raise e
#     elif isinstance(data, list):
#         data = torch.tensor(data, dtype=dtype, device=device)
#     return data