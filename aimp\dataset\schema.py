# -*- coding: utf-8 -*-
# __author__:SH
# 2022/9/29 10:41
from datetime import datetime
from enum import Enum
from typing import Union

from fastapi import Body
from pydantic import BaseModel, Field


class IdIn(BaseModel):
    # id单个id传入
    id: int


class AdjacentID(BaseModel):
    id:int
    dataset_id:int
    dataset_type:int
    position:int # 0 是上一个，1是下一个
    ope_type:int # 0 是标注，1是质检

class UpdataIntention(BaseModel):
    id:int
    intention_name:str

class IdInList(BaseModel):
    # id列表的形式传入
    id_list: list[int] = []

class GetTag(BaseModel):
    image_id:int
    dataset_id:int


class RawStatus(BaseModel):
    raw_id:int
    dataset_type:int



class RawStatusDelete(BaseModel):
    raw_id:int
    dataset_type:int
    status:int


class GetTagText(BaseModel):
    text_id:int
    dataset_id:str

class GetTagImage(BaseModel):
    image_id:int
    dataset_id:str

class ResponseMsg(BaseModel):
    # 只返回内容提示信息
    msg: str

class ResponseInt(BaseModel):
    # 只返回内容提示信息
    msg: int

class Code(BaseModel):
    code:int


class GetRaw(BaseModel):
    path:str
    tag_id:int
    width:int
    image_width:int
    height:int


class GetRawText(BaseModel):
    text:str


class TextMarkIn(BaseModel):
    name: str


class TextRelation(BaseModel):
    id:int
    begin_id:int
    end_id:int
    raw_text_id:int



class Paging(BaseModel):
    order_key: str = Field(default="id")  # 排序条件
    order: int = Field(default=1)  # 顺序还是逆序
    page: int = Field(default=1)  # 页数
    size: int = Field(default=10)  # 大小


class DataSetIn(BaseModel):
    # 创建dataset时，传入的参数
    dataset_name: str = Field(max_length=255)
    create_user: str = Field(max_length=255)
    dataset_type: int


class DataSetUpdata(BaseModel):
    # 创建dataset时，传入的参数
    dataset_name: str = Field(max_length=255)
    dataset_id: str
    dataset_type: str


class DataSetOut(BaseModel):
    # 查询dataset的单条返回形式
    id: int
    dataset_name: str = Field(max_length=255)  # 数据集名称
    create_user: str = Field(max_length=32)  # 创建人
    create_time: datetime  # 创建时间
    amount: int  # 数量
    mark_count: int
    mark_precent: str = Field(max_length=32, regex='%$')
    dataset_type: int = Field(ge=0, lt=4)  # 数据集类型

    class Config:
        orm_mode = True


class CommonDbParams(BaseModel):
    offset: int = Field(default=0, ge=0)
    limit: int = Field(default=10, ge=1)


class DatasetOutOrder(str, Enum):
    id = 'id'
    create_time = 'create_time'


class DataSetOutWithCount(BaseModel):
    count: int
    datasets: list[DataSetOut]


class DataSetProperty(BaseModel):
    dataset_id: int
    dataset_type: int
    dataset_name: str = Field(max_length=255)
    create_user: str = Field(max_length=255)
    create_time: str
    dataset_amount: int
    amount: int
    inspection_count: int
    inspection_precent: str
    update_time: str
    rate: str


class DataSetOutObj(BaseModel):
    key: int
    id: int
    dataset_name: str  # 数据集名称
    dataset_type: int  # 数据集类型
    create_user: str  # 创建人
    create_time: str  # 创建时间
    amount: int  # 数量
    mark_precent: str  # 标准进度
    update_time: str
    rate: str


class DataSetInList(BaseModel):
    search_data: str = Body()
    data_type_list: list[int] = []


class DataSetOutList(BaseModel):
    count: int
    datasets: list[DataSetOutObj]


class TagIn(BaseModel):
    tag_name: str = Field(min_length=1, max_length=255)
    shoutcut_key: str = Field(max_length=255)
    color: str = Field(max_length=255)
    dataset_id: int


class TagOut(BaseModel):
    id: int
    tag: str
    tag_id: int
    shoutcut_key: str
    color: str
    fill_color: str


class TagList(BaseModel):
    tag_list: list[TagOut]


class PostTagTextIn(BaseModel):
    tag_id: int
    raw_id: int
    tag_begin_x: int
    tag_end_x: int
    tag_begin_text: str
    tag_end_text: str


class RawTextOut(BaseModel):
    key: int
    id: int
    text: str
    status: int


class TagText(BaseModel):
    rawid: int
    tag_id: int
    begin: int
    end: int
    line: int


class RawTextList(BaseModel):
    count: int
    rawtexts: list[RawTextOut]


class RawIn(BaseModel):
    search_data: str
    dataset_id: int


class RawInList(BaseModel):
    search_data: str
    dataset_id: list[int]


class RawStatusIn(BaseModel):
    id: int
    status: int


class RelaionOut(BaseModel):
    id:int
    text: str
    d: str
    rx: str
    ry: str
    rc: str
    rw: str
    rh: str
    tx: str
    ty: str
    tc: str


class TagPosOut(BaseModel):
    id:int
    text: str
    color: str
    fill_color: str
    shoutcut_key: str
    x1: str
    x2: str
    y1: str
    y2: str
    cx: str
    cy: str
    tx: str
    ty: str
    line_pos: str
    text_color:str


class RawType(BaseModel):
    type:int


class TextOut(BaseModel):
    text: str
    color: str
    x: str
    y: str


class EventsOut(BaseModel):
    x1: str
    x2: str
    y1: str
    y2: str


class Mark(BaseModel):
    relations: Union[list[RelaionOut],list] = []
    text: TextOut
    tags: list[TagPosOut]
    events: Union[list[EventsOut],list] = []
    # relations: list[int]
    # tags:list[int]
    # events: list[int]


class ReturnList(BaseModel):
    return_list: list[Mark]



class TagIntentionOut(BaseModel):
    id:int
    text: str
    x1: str
    x2: str
    y1: str
    y2: str
    cx: str
    cy: str
    tx: str
    ty: str
    line_pos: str


class MarkIntention(BaseModel):
    text: TextOut
    tags: list[TagIntentionOut]


class IntentionList(BaseModel):
    return_list: list[MarkIntention]

class EventEnity(BaseModel):
    tag_id:int
    tag_name:str


class EventOut(BaseModel):
    event_id: int
    event_name: str
    enity: list[EventEnity]


class EventList(BaseModel):
    return_list: list[EventOut]


class RealtionExchange(BaseModel):
    begin: int
    end: int


class RelationOut(BaseModel):
    relation_id: int
    relation_name: str
    relation_list: list[int]


class RelationList(BaseModel):
    return_list: list[RelationOut]


class RelationName(BaseModel):
    id:int
    relation_name:str

class EventName(BaseModel):
    id:int
    event_name:str


class RawImageOut(BaseModel):
    id: int
    path: str
    name: str
    status: int


class RawImageList(BaseModel):
    count: int
    raw_images: list[RawImageOut]


class ImageMarkParam(BaseModel):
    tag_mark_id: int
    path: str
    circle_list: list[list[int]] = []


class ImageMark(BaseModel):
    tag_name: str
    tag_id: str
    color: str
    fill_color: str
    # path:list[str] = []
    # circle_list:list[list[list[int]]]=[]
    svg_param: list[ImageMarkParam]


class ImageMarkOutWithPath(BaseModel):
    return_list: list[ImageMark]
    path: str
    width: int
    height: float
    image_width: int


class ImageMarkOut(BaseModel):
    return_list: list[ImageMark]


class MarkForm(BaseModel):
    mark_id: int
    tag_name: str
    path: str


class MarkList(BaseModel):
    return_list: list[MarkForm]


class AddCircleIn(BaseModel):
    id: int
    pos: int
    param: list[int] = []


class DeleteCircleIn(BaseModel):
    id: int
    pos: int


class UpdateImageIn(BaseModel):
    id: int
    param: list[int] = []


class CreateImageMark(BaseModel):
    tag_id: int
    image_id: int
    param: list[list[int]] = []


class UpdateStatus(BaseModel):
    image_id: int
    status: int


class Status(BaseModel):
    status: int


class IsPre(BaseModel):
    pre: int


class NewId(BaseModel):
    raw_id: int
    dataset_id: int
    is_pre: int


class DataDrop(BaseModel):
    dataset_id: int
    name: int


class DatasetListOut(BaseModel):
    return_list: list[DataDrop]


class ImageOut(BaseModel):
    mark_id: int
    tag_id: int


class ImageOutList(BaseModel):
    return_list: list[ImageOut]


class DatasetDict(BaseModel):
    dataset_id: int
    dataset_name: str
    dataset_type: int


class ResopnseDict(BaseModel):
    dataset: list[DatasetDict]


class Slot(BaseModel):
    slot_name:str
    key:int


class Intention(BaseModel):
    intention_name:str
    intention_id:str
    slots:list[Slot]


class IntentionOut(BaseModel):
    intentions:list[Intention]


class IntentionTagOut(BaseModel):
    id: int
    tag: str
    tag_id: int


class IntentionTagList(BaseModel):
    tag_list: list[IntentionTagOut]


class IntentionIn(BaseModel):
    id: int
    raw_id: int


class SlotOut(BaseModel):
    name:str
    id:int


class SlotListOut(BaseModel):
    slots:list[SlotOut]


class UpdateSlotIn(BaseModel):
    slot_name:str
    id:int


class CreateRelation(BaseModel):
    id:int
    option:int
    tag_id:int


class CreateEvent(BaseModel):
    id:int
    tag_id:int