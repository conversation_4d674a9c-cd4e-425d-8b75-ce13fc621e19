#!/usr/bin/env python
# coding=utf-8
import logging
import os
import sys
from dataclasses import dataclass, field
from typing import Optional
import random
from functools import partial
from PIL import Image
import numpy as np

import torch
from torch.utils.data.dataset import Dataset as TorchDataset
import transformers
from transformers import (
    AutoConfig,
    AutoModelForTokenClassification,
    AutoTokenizer,
    XLMRobertaTokenizerFast,
    PreTrainedTokenizerFast,
    Trainer,
    HfArgumentParser,
    TrainingArguments,
)
from transformers.trainer_utils import get_last_checkpoint, is_main_process
from datasets import load_dataset, load_metric, Dataset
from datasets.packaged_modules import _PACKAGED_DATASETS_MODULES, _hash_python_lines
import inspect

from kbnlp.scripts.information_extraction.temp_ie_utils import map_offset
from kbnlp.scripts.information_extraction.temp_doc_parser import DocParser
from kbnlp.pretrained.ernie_layout.modeling_ernie_layout import ErnieLayoutForTokenClassification
from kbnlp.pretrained.ernie_layout.configuration_ernie_layout import Ernie<PERSON>ayoutConfig

from kbnlp.scripts.information_extraction import label_studio_document_close_domain_builder, doccano_document_close_domain_builder
from kbnlp.scripts.information_extraction.my_convert import LabelStudioConvertorForCloseDomain, DoccanoConvertorForCloseDomain

script_identifier = label_studio_document_close_domain_builder.__name__.split('.')[-1]
_PACKAGED_DATASETS_MODULES[script_identifier] = (label_studio_document_close_domain_builder.__name__,
                                                 _hash_python_lines(
                                                     inspect.getsource(label_studio_document_close_domain_builder).splitlines()))
reader_module = LabelStudioConvertorForCloseDomain
# script_identifier = doccano_document_close_domain_builder.__name__.split('.')[-1]
# _PACKAGED_DATASETS_MODULES[script_identifier] = (doccano_document_close_domain_builder.__name__,
#                                                  _hash_python_lines(
#                                                      inspect.getsource(doccano_document_close_domain_builder).splitlines()))
# reader_module = DoccanoConvertorForCloseDomain


def seed_everything(seed):
    torch.manual_seed(seed)       # Current CPU
    torch.cuda.manual_seed(seed)  # Current GPU
    np.random.seed(seed)          # Numpy module
    random.seed(seed)             # Python random module
    # torch.backends.cudnn.benchmark = False    # Close optimization
    # torch.backends.cudnn.deterministic = True  # Close optimization
    torch.cuda.manual_seed_all(seed)  # All GPU (Optional)


logger = logging.getLogger(__name__)


@dataclass
class ModelArguments:
    """
    Arguments pertaining to which model/config/tokenizer we are going to fine-tune from.
    """

    model_name_or_path: str = field(
        metadata={"help": "Path to pretrained model or model identifier from huggingface.co/models"}
    )
    config_name: Optional[str] = field(
        default=None,
        metadata={"help": "Pretrained config name or path if not the same as model_name"}
    )
    tokenizer_name: Optional[str] = field(
        default=None,
        metadata={"help": "Pretrained config name or path if not the same as model_name"}
    )


@dataclass
class DataTrainingArguments:
    """
    Arguments pertaining to what data we are going to input our model for training and eval.
    """
    train_path: str = field(
        metadata={"help": "The input training data file (a JSON file)."})
    dev_path: str = field(
        metadata={"help": "An optional input evaluation data file to evaluate on (a JSON file)."})
    max_seq_len: int = field(
        default=512,
        metadata={"help": "最长序列长度，默认值为512，不超过默认值。"}
    )
    doc_stride: int = field(
        default=128,
        metadata={"help": "Tokenizer中的stride参数，代表回退子词数。"}
    )
    max_train_samples: Optional[int] = field(
        default=None,
        metadata={"help": "For debugging purposes or quicker training,"
                          " truncate the number of training examples to this value if set."},)
    max_dev_samples: Optional[int] = field(
        default=None,
        metadata={"help": "For debugging purposes or quicker training, "
                          "truncate the number of validation examples to this value if set."},)
    # task_type: str = field(
    #     default='ext',
    #     metadata={"help": "任务类型，'ext'代表信息抽取，'cls'代表文本分类."}  # 本脚本不支持文本分类。
    # )
    layout_analysis: bool = field(
        default=False,
        metadata={"help": "是否使用布局分析优化图片的ocr结果。"}
    )
    anno_type: str = field(
        default="image",
        metadata={"help": "表示提供训练数据的标注类型，image表示图片文档标注，text表示纯文本标注。"}

    )
    train_image_base_path: str = field(
        default=None,
        metadata={"help": "当anno_type是image时，该参数有效，表示存放train图片的基目录，如果该参数没有提供，则直接用train_path/dev_path的父目录代替。"
                          "本脚本要求提供的数据中包含'image'这一项，且image_base_path + 该项代表图片的路径。"}
    )
    dev_image_base_path: str = field(
        default=None,
        metadata={"help": "当anno_type是image时，该参数有效，表示存放dev图片的基目录，如果该参数没有提供，则直接用train_path/dev_path的父目录代替。"
                          "本脚本要求提供的数据中包含'image'这一项，且image_base_path + 该项代表图片的路径。"}
    )
    return_entity_level_metrics: bool = field(
        default=False,
        metadata={"help": "Whether to return all the entity levels during evaluation or just the overall ones."},)
    use_cache: bool = field(
        default=False,
        metadata={"help": "是否使用datasets的缓存机制,普通用户不使用该参数。"
                          "如果值为True，则会将使用load_dataset方法加载；否则使用Datasets.from_dict方法加载，不使用缓存机制。"}
    )
    force_reload: bool = field(
        default=False,
        metadata={"help": "当use_cache为True时，本参数生效。默认值为False。当值为True时，会重新加载数据，不管有没有缓存；否则优先从缓存中加载。"}
    )


class DocumentDataset(TorchDataset):

    def __init__(self, examples: Dataset, image_base_path, image_size=224, interp=1,
                 mean=(123.675, 116.280, 103.530), std=(58.395, 57.120, 57.375),
                 bbox_scale=1000):
        super(DocumentDataset, self).__init__()
        self.image_base_path = image_base_path
        assert isinstance(examples, Dataset), "仅支持HF Dataset。"
        self.examples = examples
        self.file_name2image = {}  # 图片名到图片（resize、取均值、换通道后的图片）
        self.file_name2image_size = {}  # 图片名到图片的size，(height, width)
        self.image_size = image_size
        self.interp = interp
        self.mean = mean
        self.std = std
        self.bbox_scale = bbox_scale

    def __len__(self):
        return len(self.examples)

    def __getitem__(self, item):
        # HF Dataset获取第item项样本example时相当于做了deepcopy，所以对example做的改变不影响HF Dataset本身。
        example = self.examples[item]
        if example['image'] not in self.file_name2image:
            image = DocParser.read_image(os.path.join(self.image_base_path, example['image']))
            # resize image
            image = Image.fromarray(image)
            image_width = image.width
            image_height = image.height
            self.file_name2image_size[example['image']] = (image_width, image_height)
            image = np.array(image.resize((self.image_size, self.image_size), self.interp))
            # norm image, channel last.
            image = image.astype(np.float32, copy=False)
            mean = np.array(self.mean)[np.newaxis, np.newaxis, :]
            std = np.array(self.std)[np.newaxis, np.newaxis, :]
            image -= mean
            image /= std
            # change to channel fist
            image = np.swapaxes(image, 1, 2)
            image = np.swapaxes(image, 1, 0)
            self.file_name2image[example['image']] = image
        else:
            image = self.file_name2image[example['image']]
            image_width, image_height = self.file_name2image_size[example['image']]
        bbox = []
        for box in example['bbox']:
            bbox.append(DocParser.normalize_box(box, old_size=(image_width, image_height),
                                                new_size=(self.bbox_scale, self.bbox_scale)))
        example['bbox'] = bbox
        example['image'] = image
        example['position_ids'] = list(range(len(example['input_ids'])))
        return example

    # def get_segment_ids(self, bboxs):  # ernielayout没有segment_ids，只有token_type_ids，且和bert是一致的。
    #     segment_ids = []
    #     for i in range(len(bboxs)):
    #         if i == 0:
    #             segment_ids.append(0)
    #         else:
    #             if bboxs[i - 1] == bboxs[i]:
    #                 segment_ids.append(segment_ids[-1])
    #             else:
    #                 segment_ids.append(segment_ids[-1] + 1)
    #     return segment_ids

    # def get_position_ids(self, segment_ids):  # ernielayout没有segment_ids直接从0到length-1
    #     position_ids = []
    #     for i in range(len(segment_ids)):
    #         if i == 0:
    #             position_ids.append(2)
    #         else:
    #             if segment_ids[i] == segment_ids[i - 1]:
    #                 position_ids.append(position_ids[-1] + 1)
    #             else:
    #                 position_ids.append(2)
    #     return position_ids


def main():
    parser = HfArgumentParser((ModelArguments, DataTrainingArguments, TrainingArguments))
    if len(sys.argv) == 2 and sys.argv[1].endswith(".json"):
        # If we pass only one argument to the script and it's the path to a json file,
        # let's parse it to get our arguments.
        model_args, data_args, training_args = parser.parse_json_file(json_file=os.path.abspath(sys.argv[1]))
    else:
        model_args, data_args, training_args = parser.parse_args_into_dataclasses()

    # Detecting last checkpoint.
    last_checkpoint = None
    if os.path.isdir(
            training_args.output_dir) and training_args.do_train and not training_args.overwrite_output_dir:
        last_checkpoint = get_last_checkpoint(training_args.output_dir)
        if last_checkpoint is None and len(os.listdir(training_args.output_dir)) > 0:
            raise ValueError(
                f"Output directory ({training_args.output_dir}) already exists and is not empty. "
                "Use --overwrite_output_dir to overcome."
            )
        elif last_checkpoint is not None:
            logger.info(
                f"Checkpoint detected, resuming training at {last_checkpoint}. To avoid this behavior, change "
                "the `--output_dir` or add `--overwrite_output_dir` to train from scratch."
            )

    logging.basicConfig(
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        datefmt="%m/%d/%Y %H:%M:%S",
        handlers=[logging.StreamHandler(sys.stdout)],
    )
    logger.setLevel(logging.INFO if is_main_process(training_args.local_rank) else logging.WARN)

    # Log on each process the small summary:
    logger.warning(
        f"Process rank: {training_args.local_rank}, device: {training_args.device}, n_gpu: {training_args.n_gpu}"
        + f"distributed training: {bool(training_args.local_rank != -1)}, 16-bits training: {training_args.fp16}"
    )
    # Set the verbosity to info of the Transformers logger (on main process only):
    if is_main_process(training_args.local_rank):
        transformers.utils.logging.set_verbosity_info()
        transformers.utils.logging.enable_default_handler()
        transformers.utils.logging.enable_explicit_format()
    logger.info(f"Training/evaluation parameters {training_args}")

    # Set seed before initializing model.
    seed_everything(training_args.seed)
    if data_args.use_cache:
        # 以下是脚本中datasets.BuilderConfig子类的参数。
        kwargs = {'is_train': True,
                  'task_type': 'ext',  # 本脚本不支持文本分类
                  'layout_analysis': data_args.layout_analysis,
                  'anno_type': data_args.anno_type, }
        if script_identifier == doccano_document_close_domain_builder.__name__.split('.')[-1]:
            kwargs.pop('layout_analysis')
        train_ds = load_dataset(script_identifier,
                                data_files=data_args.train_path,
                                download_mode='force_redownload' if data_args.force_reload else None,
                                **kwargs
                                )['train']
        kwargs['is_train'] = False
        dev_ds = load_dataset(script_identifier,
                              data_files=data_args.dev_path,
                              download_mode='force_redownload' if data_args.force_reload else None,
                              # 以下是脚本中datasets.BuilderConfig子类的参数。
                              **kwargs
                              )['train']
    else:
        #  InMemoryTable,不会缓存到cache目录下。
        train_ds, _, _ = reader_module(data_args.train_path,
                                       layout_analysis=data_args.layout_analysis,
                                       splits=[1., 0, 0],
                                       task_type='ext',
                                       is_shuffle=False,
                                       seed=None,
                                       anno_type=data_args.anno_type,
                                       verbose=False,
                                       ).convert()
        _, dev_ds, _ = reader_module(data_args.train_path,
                                     layout_analysis=data_args.layout_analysis,
                                     splits=[0, 1., 0],
                                     task_type='ext',
                                     is_shuffle=False,
                                     seed=None,
                                     anno_type=data_args.anno_type,
                                     verbose=False,
                                     ).convert()

        train_ds = {k: [d[k] for d in train_ds] for k in train_ds[0].keys()}
        dev_ds = {k: [d[k] for d in dev_ds] for k in dev_ds[0].keys()}
        train_ds = Dataset.from_dict(train_ds)
        dev_ds = Dataset.from_dict(dev_ds)
    if data_args.max_train_samples is not None and data_args.max_train_samples > 0:
        train_ds = train_ds.select(range(data_args.max_train_samples))
    if data_args.max_dev_samples is not None and data_args.max_dev_samples > 0:
        dev_ds = dev_ds.select(range(data_args.max_dev_samples))

    # tokenizer = AutoTokenizer.from_pretrained(
    #     model_args.tokenizer_name if model_args.tokenizer_name else model_args.model_name_or_path,
    #     tokenizer_file=None,  # avoid loading from a cached file of the pre-trained model in another machine
    #     use_fast=True,
    # )
    tokenizer = XLMRobertaTokenizerFast.from_pretrained(model_args.tokenizer_name if model_args.tokenizer_name
                                                         else model_args.model_name_or_path)
    entity_label2id_ = {'O': 0}
    for entities_sample in train_ds['entities']:
        for entity in entities_sample:
            if ('B-' + entity['label']) not in entity_label2id_ \
                    and ('I-' + entity['label']) not in entity_label2id_:
                entity_label2id_['B-' + entity['label']] = len(entity_label2id_)
                entity_label2id_['I-' + entity['label']] = len(entity_label2id_)

    # TODO:放到torch.utils.data.Dataset中
    def prepare_features(examples, entity_label2id):
        """
            Tokenize our examples with truncation and padding, but keep the overflows using a stride. This results
        in one example possible giving several features when a context is long, each of those features having a
        context that overlaps a bit the context of the previous feature.
        Args:
            examples:
            entity_label2id:
        Returns:

        """
        tokenized_examples = tokenizer(
            examples["word"],
            is_split_into_words=True,
            truncation=True,
            max_length=data_args.max_seq_len,
            stride=data_args.doc_stride,
            return_overflowing_tokens=True,
            return_offsets_mapping=True,
            return_token_type_ids=True,
            padding="longest",
        )

        # Since one example might give us several features if it has a long context, we need a map from a feature to
        # its corresponding example. This key gives us just that.
        sample_mapping = tokenized_examples.pop("overflow_to_sample_mapping")
        # The offset mappings will give us a map from token to character position in the original context. This will
        # help us compute the start_positions and end_positions.
        offset_mapping = tokenized_examples.pop("offset_mapping")
        labels = []
        bbox_all = []
        for exam_id, offsets in enumerate(offset_mapping):
            # One example can give several spans, this is the index of the example containing this span of text.
            sample_index = sample_mapping[exam_id]
            word_ids = tokenized_examples.word_ids(exam_id)
            input_ids = tokenized_examples['input_ids'][exam_id]
            sep_index = input_ids.index(tokenizer.sep_token_id)
            #  处理bbox（未缩放）
            new_bbox = [[0, 0, 0, 0]] * len(offsets)
            new_bbox[sep_index] = [1000, 1000, 1000, 1000]   # 这里不对，因为没有缩放
            bbox = examples['bbox'][sample_index]
            for j, word_id in enumerate(word_ids):
                if word_id is not None:
                    new_bbox[j] = bbox[word_id]
            bbox_all.append(new_bbox)
            #  处理labels
            offset_bias = 0
            pre_word_id = word_ids[1]  # 该值也有可能为None，如果传入的是空字符串，则为word_ids为[None, None]
            new_offsets = []
            entities = examples["entities"][sample_index]
            for offset, word_id in zip(offsets, word_ids):
                new_offset = (0, 0)
                if word_id is not None:
                    if word_id != pre_word_id:
                        offset_bias = new_offsets[-1][-1]
                        pre_word_id = word_id
                    new_offset = (offset[0] + offset_bias, offset[1] + offset_bias)
                new_offsets.append(new_offset)

            exam_label_ids = [entity_label2id['O']] * len(offsets)
            # CLS设置为-100.
            exam_label_ids[0] = -100
            # 将SEP和PAD均设置为-100.
            for i_ in range(sep_index, len(offsets)):
                exam_label_ids[i_] = -100

            #  仅处理不包含有重叠的实体
            for entity in entities:
                start = map_offset(entity['start_offset'], new_offsets)
                end = map_offset(entity["end_offset"] - 1, new_offsets)
                label = entity['label']
                for idx in range(start, end + 1):
                    if idx == start:
                        exam_label_ids[idx] = entity_label2id['B-' + label] \
                            if ('B-' + label) in entity_label2id else entity_label2id['O']
                    else:
                        exam_label_ids[idx] = entity_label2id['I-' + label] \
                            if ('I-' + label) in entity_label2id else entity_label2id['O']
            labels.append(exam_label_ids)
            assert len(tokenized_examples['input_ids'][exam_id]) == len(tokenized_examples['attention_mask'][exam_id]) \
                   == len(exam_label_ids) == len(new_bbox), (len(tokenized_examples['input_ids'][exam_id]),
                                                             len(tokenized_examples['attention_mask'][exam_id]),
                                                             len(exam_label_ids),
                                                             len(new_bbox))
        tokenized_examples = dict(tokenized_examples)
        tokenized_examples['labels'] = labels
        tokenized_examples['bbox'] = bbox_all
        tokenized_examples['image'] = [examples['image'][sample_index] for sample_index in sample_mapping]
        # 不要求数据中记录image_width、image_height，后期读图片时直接获取。
        # tokenized_examples['image_width'] = [examples['image_width'][sample_index] for sample_index in sample_mapping]
        # tokenized_examples['image_height'] = [examples['image_height'][sample_index] for sample_index in sample_mapping]
        return tokenized_examples

    train_ds = train_ds.map(partial(prepare_features, entity_label2id=entity_label2id_),
                            batched=True, remove_columns=train_ds.column_names)
    dev_ds = dev_ds.map(partial(prepare_features, entity_label2id=entity_label2id_),
                        batched=True, remove_columns=dev_ds.column_names)
    train_ds = DocumentDataset(train_ds,
                               image_base_path=data_args.train_image_base_path if data_args.train_image_base_path
                               else os.path.dirname(data_args.train_path))
    dev_ds = DocumentDataset(dev_ds,
                             image_base_path=data_args.dev_image_base_path if data_args.dev_image_base_path
                             else os.path.dirname(data_args.dev_path))
    # x = train_ds[0]
    config = ErnieLayoutConfig.from_pretrained(
        model_args.config_name if model_args.config_name else model_args.model_name_or_path,
        num_labels=len(entity_label2id_),
    )
    model = ErnieLayoutForTokenClassification.from_pretrained(
        model_args.model_name_or_path,
        config=config,
    )

    if not isinstance(tokenizer, PreTrainedTokenizerFast):
        raise ValueError(
            "This script only works for models that have a fast tokenizer."
        )

    def get_label_list():
        label_list = [[key, val] for key, val in entity_label2id_.items()]
        label_list = sorted(label_list, key=lambda x: x[1], reverse=False)
        label_list = [label for label, _ in label_list]
        return label_list

    label_list = get_label_list()

    # Metrics
    metric = load_metric("seqeval")

    def compute_metrics(p):
        predictions, labels = p
        predictions = np.argmax(predictions, axis=2)

        # Remove ignored index (special tokens)
        true_predictions = [
            [label_list[p] for (p, l) in zip(prediction, label) if l != -100]
            for prediction, label in zip(predictions, labels)
        ]
        true_labels = [
            [label_list[l] for (p, l) in zip(prediction, label) if l != -100]
            for prediction, label in zip(predictions, labels)
        ]

        results = metric.compute(predictions=true_predictions, references=true_labels)
        if data_args.return_entity_level_metrics:
            # Unpack nested dictionaries
            final_results = {}
            for key, value in results.items():
                if isinstance(value, dict):
                    for n, v in value.items():
                        final_results[f"{key}_{n}"] = v
                else:
                    final_results[key] = value
            return final_results
        else:
            return {
                "precision": results["overall_precision"],
                "recall": results["overall_recall"],
                "f1": results["overall_f1"],
                "accuracy": results["overall_accuracy"],
            }

    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=train_ds if training_args.do_train else None,
        eval_dataset=dev_ds if training_args.do_eval else None,
        tokenizer=tokenizer,
        compute_metrics=compute_metrics,
    )

    # Training
    if training_args.do_train:
        checkpoint = last_checkpoint if last_checkpoint else None
        train_result = trainer.train(resume_from_checkpoint=checkpoint)
        metrics = train_result.metrics
        trainer.save_model()  # Saves the tokenizer too for easy upload
        trainer.log_metrics("train", metrics)
        trainer.save_metrics("train", metrics)
        trainer.save_state()

    # Evaluation
    if training_args.do_eval:
        metrics = trainer.evaluate()
        trainer.log_metrics("eval", metrics)
        trainer.save_metrics("eval", metrics)


if __name__ == '__main__':
    # '--logging_steps 5 --save_steps 15 --eval_steps 15 --seed 42 --model_name_or_path C:/Users/<USER>/Desktop/uie-x-base-v1 --output_dir ../../data/finetuned/doc_ie_close_domain_test/checkpoints --train_path ../../raw_data/real_estate/split_trim_underline_fake_relation/train.txt --dev_path ../../raw_data/real_estate/split_trim_underline_fake_relation/dev.txt --max_seq_len 512 --per_device_train_batch_size 2 --gradient_accumulation_steps 1 --per_device_eval_batch_size 2 --num_train_epochs 80 --learning_rate 1e-5 --label_names labels --do_train --do_eval --evaluation_strategy steps --overwrite_output_dir --disable_tqdm True --metric_for_best_model eval_f1 --load_best_model_at_end False --save_total_limit 1 --no_cuda --use_cache 1 --force_reload 0 --train_image_base_path C:/Users/<USER>/Desktop/不动产登记申请审批表/images --dev_image_base_path C:/Users/<USER>/Desktop/不动产登记申请审批表/images'
    main()



