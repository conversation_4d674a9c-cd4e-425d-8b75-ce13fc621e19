if __name__ == '__main__':
    from qa.kbnlp import MltParser, cut_sentence, model_path
    import torch
    document = '''
    The Third World Academy  of Sciences。
    1986年毕业于国防科技大学计算机应用专业，获学时学位。请座的小朋友把手举起来。
    丝角蝗科，Oedipodidae，昆虫纲直翅目蝗总科的一个科。
    融安县属中亚热带季风气候区。
    马红宝 男 汉族，1949年8月生，浙江省长兴县人，1978年8月加入中国共产党，1967年9月参加工作，大普文化。
    欢迎关注哈工大讯飞联合实验室官方微信公众号。
    华纳音乐旗下的新垣结衣在12月21日于日本武道馆举办歌手出道活动。
    2021年HanLPv2.1为生产环境带来次世代最先进的多语种NLP技术。'''
    model_path = r'mlt/data/models/electra_small_tok_pos_ner_dep_rte_dist/'
    mlt = MltParser(save_dir=model_path, device='cuda:0' if torch.cuda.is_available() else 'cpu')
    data = cut_sentence(document)
    res = mlt(data=['空军航空兵第8师24团1大队1中队'], batch_size=4, skip_tasks=[], is_split_into_words=False)
    for task_name, res in res.items():
        print(task_name, ': ', res)

