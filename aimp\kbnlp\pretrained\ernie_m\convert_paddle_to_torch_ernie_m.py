from pprint import pprint
import paddle
import torch
import math
from collections import OrderedDict
import numpy as np
from kbnlp.pretrained.ernie_m.configuration_ernie_m import ErnieMConfig
from kbnlp.pretrained.ernie_m.modeling_ernie_m import ErnieMModel
from kbnlp.taskflow.models.information_extraction.information_extraction_model import UIEM
from paddlenlp.transformers import ErnieMModel as ErnieMModel2
from paddlenlp.transformers import ErnieMConfig as ErnieMConfig2
from paddlenlp.transformers import UIEM as UIEM2


paddle.set_device('cpu')


def to_numpy(data):
    if isinstance(data, torch.Tensor) or isinstance(data, paddle.Tensor):
        return data.numpy()
    else:
        return data


def convert(torch_model_class, paddle_model_class, paddle_model_name_or_path, model_inputs, num_labels=0, acc=4):
    # model_p = paddle_model_class(ErnieMConfig2()).to('cpu')
    if num_labels > 0:
        model_p = paddle_model_class.from_pretrained(paddle_model_name_or_path, num_classes=num_labels).to('cpu')
    else:
        model_p = paddle_model_class.from_pretrained(paddle_model_name_or_path).to('cpu')
    torch_config_dict = dict(model_p.config.to_dict())
    # if 'model_type' in torch_config_dict:
    #     torch_config_dict.pop('model_type')
    if num_labels > 0:
        torch_config_dict['num_labels'] = num_labels
    if torch_config_dict['num_hidden_layers'] == 24:  # paddel官方提供的uie-m-large的congfig文件中intermediate_size有误。
        torch_config_dict['intermediate_size'] = 4096
    config_t = ErnieMConfig(**torch_config_dict)
    model_t = torch_model_class(config_t)
    model_t.eval()
    model_p.eval()
    # 修改key
    # state_t = OrderedDict([(k, v) for k, v in model_t.state_dict().items() if 'tracked' not in k])
    # state_p = dict([(k.replace('._mean', '.running_mean').replace('._variance', '.running_var'), v)
    #                 if '._mean' in k or '._variance' in k else (k, v)
    #                 for k, v in model_p.state_dict().items()])
    state_t = OrderedDict(**model_t.state_dict())
    state_p = OrderedDict(**{k.replace('self_attn', 'self_attn.self_attn') if 'out_proj' not in k else k: v
                             for k, v in model_p.state_dict().items()})
    assert len(state_t) == len(state_p)

    name_mismatch = [(k1, k2) for k1, k2 in zip(state_t, state_p) if k1 != k2]
    print('name_mismatch:')
    pprint(name_mismatch)

    # 修改value
    for k, v in state_p.items():
        k_ = k
        v_arr = v.numpy()
        v_ = torch.tensor(v_arr.T if '.weight' in k_ and 'embeddings.' not in k_ and 'conv' not in k_ and len(v_arr.shape) == 2 else v_arr)
        state_t[k_] = v_

    shape_mismatch = []
    for (k1, v1), (k2, v2) in zip(state_t.items(),
                                  [(k, v) for k, v in model_t.state_dict().items() if 'tracked' not in k]):
        assert k1 == k2
        if list(v1.size()) != list(v2.size()):
            shape_mismatch.append((k1, list(v1.size()), list(v2.size())))
    print('shape_mismatch:')
    pprint(shape_mismatch)

    # 向torch模型中加载参数
    print(model_t.load_state_dict(state_t, strict=False))

    # 验证模型精度
    model_inputs = OrderedDict([(k, to_numpy(v)) for k, v in model_inputs.items()])
    inputs_p = dict([(k, paddle.to_tensor(v, place='cpu')) for k, v in model_inputs.items()])
    # if 'labels' in inputs_p:
    #     inputs_p['labels'] = paddle.to_tensor([0] * inputs_p['labels'].shape[0])
    # inputs_p.pop('labels')
    inputs_t = dict([(k, torch.tensor(v)) for k, v in model_inputs.items()])
    with torch.no_grad():
        output_t = model_t(**inputs_t)
        if not isinstance(output_t, tuple):
            output_t = tuple(output_t)
    with paddle.no_grad():
        output_p = model_p(**inputs_p)
        if not isinstance(output_p, tuple):
            output_p = tuple(output_p)

    acc_ = math.pow(10, -acc)
    all_guaranteed = True
    assert len(output_t) == len(output_p)
    for i in range(len(output_t)):
        acc_guaranteed = np.all(np.abs(output_t[i].numpy() - output_p[i].numpy()) < acc_)
        all_guaranteed &= acc_guaranteed
        print('结果元祖中第%d个值的shape：' % (i + 1), list(output_t[i].numpy().shape))
        print('结果元祖中第%d个值保证小数点后%d位精度：' % (i, acc), acc_guaranteed)
    print('在所有的值中，保证小数点后%d位精度：' % acc, all_guaranteed)
    return model_t


if __name__ == '__main__':

    def convert_ErnieLayoutModel(paddle_model_name_or_path=None):
        from kbnlp.pretrained.ernie_m.tokenization_ernie_m import ErnieMTokenizer
        from paddlenlp.transformers import ErnieMTokenizer as ErnieMTokenizer2
        p_tokenizer = ErnieMTokenizer2.from_pretrained('ernie-m-base')
        p_tokenizer.save_pretrained('./paddle_tokenizer')
        t_tokenizer = ErnieMTokenizer.from_pretrained('./paddle_tokenizer')
        model_inputs_ = paddle.load(r'D:\workspace\pycharm\nlp_project\kbnlp\experiment\convert_pytorch\uiex_inputs.params')
        model_inputs_.pop('bbox')  # ErnieMModel没有该输入参数
        model_inputs_.pop('image')  # ErnieMModel没有该输入参数
        model_inputs_.pop('token_type_ids')  # ErnieMModel没有该输入参数

        model_inputs_.pop('labels')
        model = convert(UIEM, UIEM2,
                        paddle_model_name_or_path=paddle_model_name_or_path,
                        model_inputs=model_inputs_)
        model.save_pretrained('./uie-m-large')
        t_tokenizer.save_pretrained('./uie-m-large')

    convert_ErnieLayoutModel(paddle_model_name_or_path=r'C:\Users\<USER>\.paddlenlp\taskflow\information_extraction\uie-m-large')
