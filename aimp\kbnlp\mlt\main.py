from kbnlp.mlt.util.io import load_json
from kbnlp.mlt.data_transform import NormalizeCharacter
from kbnlp.mlt.layers.encoder.transformer_encoder import ContextualWordEmbedding
from kbnlp.mlt.task.dep_graph import DepBiaffine
from kbnlp.mlt.task.dep_graph import read_CoNLL_data as read_CoNLL_data2
from kbnlp.mlt.task.segment import Segment, read_cws_tsv_data
from kbnlp.mlt.task.postag import PosTag, read_pos_data
from kbnlp.mlt.task.ner import TeNer, read_ner_tsv_data, get_k_fewshot_data
from kbnlp.mlt.task.dep_transition import DepStandard, read_CoNLL_data
from kbnlp.mlt.task.grte import read_Baidu_rte_pseudo_data, load_Baidu_rte_schemas, Grte
from kbnlp.mlt.multi_task_learning import MultiTaskLearning

from collections import OrderedDict
from transformers import AutoModel, AutoTokenizer


def main(args):  # dep_full是hanlp提供的ctb8，数据量比较大5万左右的句子。
    seed = args.seed
    data_home = args.data_home  # linux '/home/<USER>/.hanlp' windows 'C:/Users/<USER>/AppData/Roaming/hanlp'
    _CTB8_HOME = data_home + '/thirdparty/wakespace.lib.wfu.edu/bitstream/handle/10339/39379/LDC2013T21/data/'
    transformer = args.transformer
    if not args.tokenizer:
        tokenizer = transformer  # 'hfl/chinese-electra-180g-base-discriminator'
    else:
        tokenizer = args.tokenizer
    tok = args.tok
    pos = args.pos
    ner = args.ner
    dep = args.dep
    dep_full = args.dep_full
    graph = args.graph
    use_grte_decoder = args.use_grte_decoder
    grte_num_label = args.num_label
    rounds = args.rounds
    dep_extra_layer_normal = args.dep_extra_layer_normal
    method = args.method  # 依存图算法计算loss的方式，之后会取消该参数。
    ignore_proj = args.ignore_proj
    
    rte = args.rte
    rte_rounds = args.rte_rounds
    rte_extra_layer_normal=args.rte_extra_layer_normal
    epochs = args.epochs
    batch_size = args.batch_size
    dep_hidden_size = args.dep_hidden_size
    lr = args.lr
    encoder_lr = args.encoder_lr
    grad_norm = args.grad_norm
    warm_up = args.warm_up
    word_dropout = args.word_dropout
    max_sequence_length = args.max_sequence_length
    shuffle = args.shuffle
    device = args.device
    save_dir = args.save_dir

    train_portion, eval_portion = None, None
    if args.demo:
        train_portion = 1000
        eval_portion = 200
        epochs = 30
        # shuffle = False


    char_table = data_home + '/thirdparty/file.hankcs.com/corpus/char_table.json'
    tasks = OrderedDict()
    if tok:
        CTB8_CWS_DEMO = _CTB8_HOME + 'tasks/cws/demo.txt'
        CTB8_CWS_TRAIN = _CTB8_HOME + 'tasks/cws/train.txt'
        CTB8_CWS_DEV = _CTB8_HOME + 'tasks/cws/dev.txt'
        CTB8_CWS_TEST = _CTB8_HOME + 'tasks/cws/test.txt'
        seg_trn = read_cws_tsv_data(CTB8_CWS_TRAIN)[:train_portion]
        seg_dev = read_cws_tsv_data(CTB8_CWS_DEV)[:eval_portion]
        print('Segment: train size, %d.' % len(seg_trn), 'dev size, %d.' % len(seg_dev))
        tasks[tok] = Segment(trn=seg_trn, dev=seg_dev, batch_size=batch_size)
    if pos:
        CTB8_POS_TRAIN = _CTB8_HOME + 'tasks/pos/train.tsv'
        CTB8_POS_DEV = _CTB8_HOME + 'tasks/pos/dev.tsv'
        CTB8_POS_TEST = _CTB8_HOME + 'tasks/pos/test.tsv'
        pos_trn = read_pos_data(path=CTB8_POS_TRAIN)[:train_portion]
        pos_dev = read_pos_data(path=CTB8_POS_DEV)[:eval_portion]
        print('Postag: train size, %d.' % len(pos_trn), 'dev size,%d.' % len(pos_dev))
        tasks[pos] = PosTag(trn=pos_trn, dev=pos_dev, batch_size=batch_size)
    if ner:
        MSRA_NER_DEMO = data_home + '/thirdparty/file.hankcs.com/corpus/msra_ner_token_level/word_level.demo.short.tsv'
        MSRA_NER_TRAIN = data_home + '/thirdparty/file.hankcs.com/corpus/msra_ner_token_level/word_level.train.short.tsv'
        MSRA_NER_DEV = data_home + '/thirdparty/file.hankcs.com/corpus/msra_ner_token_level/word_level.dev.short.tsv'
        MSRA_NER_TEST = data_home + '/thirdparty/file.hankcs.com/corpus/msra_ner_token_level/word_level.test.short.tsv'
        # ner_trn = read_ner_tsv_data(MSRA_NER_TRAIN)[:train_portion]

        ner_trn = read_ner_tsv_data(MSRA_NER_TRAIN)[:]
        ner_trn = get_k_fewshot_data(ner_trn, shuffle=False, k=10)

        ner_dev = read_ner_tsv_data(MSRA_NER_DEV)[:eval_portion]
        print('Ner: train size, %d.' % len(ner_trn), 'dev size,%d.' % len(ner_dev))
        tasks[ner] = TeNer(trn=ner_trn, dev=ner_dev, num_layer=2, hidden_size=512, num_heads=4, drop_transformer=0.2,
                           batch_size=batch_size)
    if dep:
        CTB8_DEP_DEMO = '../raw_data/demo.conll'
        CTB8_DEP_TRAIN = '../raw_data/train.conll'
        CTB8_DEP_DEV = '../raw_data/dev.conll'
        CTB8_DEP_TEST = '../raw_data/test.conll'
        if graph:
            dep_trn = read_CoNLL_data2(CTB8_DEP_TRAIN, check_projective=not ignore_proj)[:train_portion]
            dep_dev = read_CoNLL_data2(CTB8_DEP_DEV, check_projective=not ignore_proj)[:eval_portion]
        else:
            dep_trn = read_CoNLL_data(CTB8_DEP_TRAIN)[:train_portion]
            dep_dev = read_CoNLL_data(CTB8_DEP_DEV)[:eval_portion]
        print('Dep: train size, %d.' % len(dep_trn), 'dev size,%d.' % len(dep_dev))
        if graph:
            tasks[dep] = DepBiaffine(trn=dep_trn, dev=dep_dev, batch_size=batch_size, method=method,
                                     use_grte_decoder=use_grte_decoder, num_label=grte_num_label, rounds=rounds, extra_layer_normal=dep_extra_layer_normal)
        else:  # transition
            tasks[dep] = DepStandard(trn=dep_trn, dev=dep_dev, hidden_size=dep_hidden_size, batch_size=batch_size)
    if dep_full:
        CTB8_DEP_TRAIN_HanLP = _CTB8_HOME + 'tasks/dep/train.conllx'
        CTB8_DEP_DEV_HanLP = _CTB8_HOME + 'tasks/dep/dev.conllx'
        CTB8_DEP_TEST_HanLP = _CTB8_HOME + 'tasks/dep/test.conllx'
        if graph:
            dep_trn_HanLP = read_CoNLL_data2(CTB8_DEP_TRAIN_HanLP, check_projective=not ignore_proj)[:train_portion]
            dep_dev_HanLP = read_CoNLL_data2(CTB8_DEP_DEV_HanLP, check_projective=not ignore_proj)[:eval_portion]
        else:
            dep_trn_HanLP = read_CoNLL_data(CTB8_DEP_TRAIN_HanLP)[:train_portion]
            dep_dev_HanLP = read_CoNLL_data(CTB8_DEP_DEV_HanLP)[:eval_portion]
        print('Dep_HanLP: train size, %d.' % len(dep_trn_HanLP), 'dev size,%d.' % len(dep_dev_HanLP))
        if graph:
            tasks[dep_full] = DepBiaffine(trn=dep_trn_HanLP, dev=dep_dev_HanLP, batch_size=batch_size, method=method,
                                          use_grte_decoder=use_grte_decoder, num_label=grte_num_label, rounds=rounds, extra_layer_normal=dep_extra_layer_normal)
        else:  # transition
            tasks[dep_full] = DepStandard(trn=dep_trn_HanLP, dev=dep_dev_HanLP, hidden_size=dep_hidden_size,
                                    batch_size=batch_size)
    if rte:
        rte_trn_baidu_pseudo = read_Baidu_rte_pseudo_data('../raw_data/baidu_rte/train_data_pseudo.json')[:train_portion]
        rte_dev_baidu_pseudo = read_Baidu_rte_pseudo_data('../raw_data/baidu_rte/dev_data_pseudo.json')[:eval_portion]
        print('rte_baidu_pseudo: train size, %d.' % len(rte_trn_baidu_pseudo), 'dev size,%d.' % len(rte_dev_baidu_pseudo))
        tasks[rte] = Grte(trn=rte_trn_baidu_pseudo,
                          dev=rte_dev_baidu_pseudo,
                          batch_size=batch_size,
                          schemas=load_Baidu_rte_schemas('../raw_data/baidu_rte/all_50_schemas'), rounds=rte_rounds, extra_layer_normal=rte_extra_layer_normal)
    mlt = MultiTaskLearning()
    if not tasks:
        return mlt
    mlt.fit(_transformer=AutoModel.from_pretrained(transformer),
            _tokenizer=AutoTokenizer.from_pretrained(tokenizer, use_fast=True,
                                                     do_basic_tokenize=True),
            encoder=ContextualWordEmbedding(average_subwords=True,
                                            word_dropout=word_dropout,
                                            max_sequence_length=max_sequence_length,
                                            input_key='original_input',
                                            ),
            tasks=tasks,
            save_dir=save_dir,
            epochs=epochs,
            lr=lr,
            grad_norm=grad_norm,
            encoder_lr=encoder_lr,
            warmup_steps=warm_up,
            shuffle=shuffle,
            device=device,
            input_key='original_input',
            extra_transform=NormalizeCharacter(mapper=load_json(char_table),
                                              src='original_input'),
            seed=seed,
            )
    # predict_test(mlt, tok, device)
    # mlt.save('./data/pretrained/save_load_test')
    # mlt = MultiTaskLearning()
    # mlt.load('./data/pretrained/save_load_test', device=device)
    # predict_test(mlt, tok, device)
    print('finish.')
    return mlt


def predict_test(mlt, tok, device):
    data1 = ['欢迎关注哈工大讯飞联合实验室官方微信公众号。',
             '2021年HanLPv2.1为生产环境带来 次 世 代最先 进的多 语种NLP技术。',
             '华纳音乐旗下的新垣结衣在12月21日于日本武道馆举办歌手出道活动.']
    data2 = [['华纳', '音乐', '旗下', '的', '新垣结衣', '在', '12月', '21日', '于', '日本', '武道馆', '举办', '歌手', '出道', '活动', '.'],
             ['欢迎', '关注', '哈工大', '讯飞', '联合', '实验室', '官方', '微信', '公众号', '。'],
             ]
    if tok:
        results = mlt.predict(data1, batch_size=6, device=device)
        for task_name, res in results.items():
            print(task_name, ': ', res)
        print()
    results = mlt.predict(data2, batch_size=6, device=device)
    for task_name, res in results.items():
        print(task_name, ': ', res)
    print()


# print(mlt.eval(OrderedDict([('dep', dep_dev[:])]), device=device))
