# encoding=utf-8
from catalog_compile.text_classify.index import Index
import logging
'''
附件识别
'''

log = logging.getLogger(__name__)


# attach_score=1.5
def cross_validate(labe2datas, id2label, w2v, more, less, res_attach_ini, conf_attach_score=1.5):
    f = None
    try:
        attach_set = set()
        train = []
        for train_label in less:
            train += labe2datas[train_label]
        index = Index(w2v, '', 2, conf_attach_score)
        index.build_annoy_index_attach(train)
        test = []
        for test_label in more:
            test += labe2datas[test_label]
        for test_data in test:
            intents = index.search_attach(test_data.text, test_data.is_biaoti)
            most_ids = dict()
            for intent in intents:
                count = 1
                if intent in most_ids:
                    count = most_ids[intent] + 1
                most_ids[intent] = count
            most_id = sorted(most_ids.items(), key=lambda x: x[1], reverse=True)[0]
            if most_id[1] >= 3:
                test_data.pred_id = int(most_id[0])
                test_data.pred_label = id2label[int(most_id[0])]
            else:
                test_data.pred_id = 0
                test_data.pred_label = '内页'
            if test_data.pred_id != 0 and test_data.id != test_data.pred_id:
                attach_set.add(test_data.label + '=-' + test_data.pred_label)
        f = open(res_attach_ini, 'a+', encoding='utf-8')
        for attach in attach_set:
            f.writelines(attach + '\n')
    except:
        log.exception('cross validate error')
    finally:
        if f:
            f.close()
