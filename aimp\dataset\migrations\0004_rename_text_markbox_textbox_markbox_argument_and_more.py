# Generated by Django 4.1.1 on 2023-03-31 08:27

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("dataset", "0003_rawfilesimage_aidp_id"),
    ]

    operations = [
        migrations.RenameField(
            model_name="markbox", old_name="text", new_name="textbox",
        ),
        migrations.AddField(
            model_name="markbox", name="argument", field=models.TextField(default="{}"),
        ),
        migrations.AlterField(
            model_name="dataset",
            name="dataset_type",
            field=models.IntegerField(default=8),
        ),
        migrations.CreateModel(
            name="RelationImage",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "begin",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="relation_image_begin",
                        to="dataset.markbox",
                    ),
                ),
                (
                    "end",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="relation_image_end",
                        to="dataset.markbox",
                    ),
                ),
                (
                    "rawimage",
                    models.ForeignKey(
                        default=1,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="dataset.rawfilesimage",
                    ),
                ),
                (
                    "relation",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="dataset.relation",
                    ),
                ),
            ],
        ),
    ]
