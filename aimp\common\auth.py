import platform
import ctypes
import os
import sys
import json
import time, datetime
import threading

import logging
log = logging.getLogger(__name__)

MODULES = {
    "catalog": 1,
    "image_process": 2,
    "inspection": 3,
    "rpa": 4,
    "high_res": 5,
    "username_nums": 6,
    "info_extraction": 7,  # 通用信息抽取小模型
    "open_inspect": 8,
    "secret_detect": 9,
    "knowledge_graph": 10,
    "question_answer": 11,
    "semantic_search": 12,
    "ocr": 13,
    "user_num": 14,
    "handwriting_ocr": 15,
    "doc_ie": 16, # 文档信息抽取大模型
    "aimp": 17,
    "aimp-cv": 18,
    "aimp-nlp": 19,
    "aimp-chat": 20,
}

ADMIN_MODULES = {
    "license_id": 4,
    "license_admin": 1,
    "compile_admin": 2,
    "release_admin": 3
}

archi = platform.architecture()
#('------------------arch:' + archi[0] + 'sys:' + platform.system())
if (platform.system() == "Windows"):
    if (archi[0] == '32bit'):
        lock_dll_path = r'common\x86\slm_runtime.dll'
    elif (archi[0] == '64bit'):
        lock_dll_path = r'common\x64\slm_runtime.dll'
elif (platform.system() == "Linux"):
    if (archi[0] == '32bit'):
        lock_dll_path = r'common/x86/libslm_runtime.so'
    elif (archi[0] == '64bit'):
        lock_dll_path = r'common/x64/libslm_runtime.so'

if getattr(sys, 'frozen', False):
    lock_dll_path = os.path.join(sys._MEIPASS, lock_dll_path)
    is_frozen = True
    log.info('In frozen mode')
else:
    abs_path = os.path.abspath(__file__)
    if abs_path.find(".zip/") >= 0:
        _folder = os.path.dirname(os.path.dirname(os.path.dirname(abs_path)))
    else:
        _folder = os.path.dirname(os.path.dirname(abs_path))
    lock_dll_path = os.path.join(_folder, lock_dll_path)
    #('-------------{}', lock_dll_path)

    #if sys.platform == 'linux':
    #    lock_dll_path = 'libslm_runtime.so'
    #    log.info('System:linux')
    #else:
    #    lock_dll_path = os.path.join(res_folder, "slm_runtime.dll")
    #    log.info('System: Other[not Linux]')
    #plugins_path = os.path.join(_folder, 'rpa/plugins')
    is_frozen = __file__.find('.zip/') >= 0

SYSTEM_TYPE = platform.system()
PRODUCT_MODE_TRAIN = "train"
PRODUCT_MODE_ONLINE = "product"

GLOBAL_MODEL = None
# For dog
ROM = 0
RAW = 1
PUB = 2
MODULE_NUM = 4


def is_module_exist(module_no, license_id):
    if is_frozen:
        return check_module_exist(license_id, module_no)
    else:
        return True


class InitParams(ctypes.Structure):
    _pack_ = 1
    _fields_ = [("version", ctypes.c_uint32),
                ("flag", ctypes.c_uint32),
                ("pfn", ctypes.c_void_p),
                ("timeout", ctypes.c_uint32),
                ("password", ctypes.c_byte * 16)]


# 登陆结构体
class LoginParams(ctypes.Structure):
    _pack_ = 1
    _fields_ = [("size", ctypes.c_uint32),
                ("license_id", ctypes.c_uint32),
                ("timeout", ctypes.c_uint32),
                ("login_mode", ctypes.c_uint32),
                ("login_flag", ctypes.c_uint32),
                ("sn", ctypes.c_ubyte * 16),
                ("server", ctypes.c_byte * 32),
                ("access_token", ctypes.c_byte * 64),
                ("cloud_server", ctypes.c_byte * 100),
                ("snippet_seed", ctypes.c_ubyte * 32),
                ("user_guid", ctypes.c_byte * 128)]


# 登陆状态
h_login = ctypes.c_uint32(0)
rom_size = ctypes.c_uint32(0)
## 加载 DLL，初始化接口函数
# 引用 DLL（x86或x64） 与当前运行环境中的 python.exe 解释器（32位、64位）保持相同，否则会加载失败。
try:
    slm_runtime = ctypes.cdll.LoadLibrary(lock_dll_path)
    if (platform.system() == "Windows"):
        # windows中的函数需要手动定位在动态库中的位置
        slm_runtime.slm_init = slm_runtime[1]
        slm_runtime.slm_find_license = slm_runtime[2]
        slm_runtime.slm_login = slm_runtime[3]
        slm_runtime.slm_get_cloud_token = slm_runtime[4]
        slm_runtime.slm_logout = slm_runtime[5]
        slm_runtime.slm_keep_alive = slm_runtime[6]
        slm_runtime.slm_check_module = slm_runtime[7]
        slm_runtime.slm_encrypt = slm_runtime[8]
        slm_runtime.slm_decrypt = slm_runtime[9]
        slm_runtime.slm_user_data_getsize = slm_runtime[10]
        slm_runtime.slm_user_data_read = slm_runtime[11]
        slm_runtime.slm_user_data_write = slm_runtime[12]
        slm_runtime.slm_get_info = slm_runtime[13]
        slm_runtime.slm_execute_static = slm_runtime[14]
        slm_runtime.slm_execute_dynamic = slm_runtime[15]
        slm_runtime.slm_execute = slm_runtime[16]
        slm_runtime.slm_mem_alloc = slm_runtime[17]
        slm_runtime.slm_mem_free = slm_runtime[18]
        slm_runtime.slm_mem_read = slm_runtime[19]
        slm_runtime.slm_mem_write = slm_runtime[20]
        slm_runtime.slm_is_debug = slm_runtime[21]
        slm_runtime.slm_get_device_cert = slm_runtime[22]
        slm_runtime.slm_sign_by_device = slm_runtime[23]
        slm_runtime.slm_adjust_time_request = slm_runtime[24]
        slm_runtime.slm_led_control = slm_runtime[25]
        slm_runtime.slm_get_version = slm_runtime[26]
        slm_runtime.slm_update = slm_runtime[27]
        slm_runtime.slm_update_ex = slm_runtime[28]
        slm_runtime.slm_enum_device = slm_runtime[29]
        slm_runtime.slm_free = slm_runtime[30]
        slm_runtime.slm_get_developer_id = slm_runtime[31]
        slm_runtime.slm_error_format = slm_runtime[32]
        slm_runtime.slm_cleanup = slm_runtime[33]
        slm_runtime.slm_pub_data_getsize = slm_runtime[36]
        slm_runtime.slm_pub_data_read = slm_runtime[37]
        slm_runtime.slm_d2c_update_inside = slm_runtime[38]
        slm_runtime.slm_enum_license_id = slm_runtime[39]
        slm_runtime.slm_get_license_info = slm_runtime[40]
        slm_runtime.slm_license_sign = slm_runtime[41]
        slm_runtime.slm_license_verify = slm_runtime[42]
        slm_runtime.slm_get_cert = slm_runtime[43]
        # 下面几个函数只有新版的库才能调用
        # slm_runtime.slm_enum_device_ex      = slm_runtime[44]
        # slm_runtime.slm_enum_license_id_ex  = slm_runtime[45]
        # slm_runtime.slm_get_license_info_ex = slm_runtime[46]
        # slm_runtime.slm_extensions_config   = slm_runtime[47]
        # 处理返回值
    slm_runtime.slm_init.restype = ctypes.c_uint32
    slm_runtime.slm_find_license.restype = ctypes.c_uint32
    slm_runtime.slm_login.restype = ctypes.c_uint32
    slm_runtime.slm_get_cloud_token.restype = ctypes.c_uint32
    slm_runtime.slm_logout.restype = ctypes.c_uint32
    slm_runtime.slm_keep_alive.restype = ctypes.c_uint32
    slm_runtime.slm_check_module.restype = ctypes.c_uint32
    slm_runtime.slm_encrypt.restype = ctypes.c_uint32
    slm_runtime.slm_decrypt.restype = ctypes.c_uint32
    slm_runtime.slm_user_data_getsize.restype = ctypes.c_uint32
    slm_runtime.slm_user_data_read.restype = ctypes.c_uint32
    slm_runtime.slm_user_data_write.restype = ctypes.c_uint32
    slm_runtime.slm_get_info.restype = ctypes.c_uint32
    slm_runtime.slm_execute_static.restype = ctypes.c_uint32
    slm_runtime.slm_execute_dynamic.restype = ctypes.c_uint32
    slm_runtime.slm_execute.restype = ctypes.c_uint32
    slm_runtime.slm_mem_alloc.restype = ctypes.c_uint32
    slm_runtime.slm_mem_free.restype = ctypes.c_uint32
    slm_runtime.slm_mem_read.restype = ctypes.c_uint32
    slm_runtime.slm_mem_write.restype = ctypes.c_uint32
    slm_runtime.slm_is_debug.restype = ctypes.c_uint32
    slm_runtime.slm_get_device_cert.restype = ctypes.c_uint32
    slm_runtime.slm_sign_by_device.restype = ctypes.c_uint32
    slm_runtime.slm_adjust_time_request.restype = ctypes.c_uint32
    slm_runtime.slm_led_control.restype = ctypes.c_uint32
    slm_runtime.slm_get_version.restype = ctypes.c_uint32
    slm_runtime.slm_update.restype = ctypes.c_uint32
    slm_runtime.slm_update_ex.restype = ctypes.c_uint32
    slm_runtime.slm_enum_device.restype = ctypes.c_uint32
    slm_runtime.slm_free.restype = ctypes.c_uint32
    slm_runtime.slm_get_developer_id.restype = ctypes.c_uint32
    slm_runtime.slm_error_format.restype = ctypes.c_uint32
    slm_runtime.slm_cleanup.restype = ctypes.c_uint32
    slm_runtime.slm_pub_data_getsize.restype = ctypes.c_uint32
    slm_runtime.slm_pub_data_read.restype = ctypes.c_uint32
    slm_runtime.slm_d2c_update_inside.restype = ctypes.c_uint32
    slm_runtime.slm_enum_license_id.restype = ctypes.c_uint32
    slm_runtime.slm_get_license_info.restype = ctypes.c_uint32
    slm_runtime.slm_license_sign.restype = ctypes.c_uint32
    slm_runtime.slm_license_verify.restype = ctypes.c_uint32
    slm_runtime.slm_get_cert.restype = ctypes.c_uint32
    # 下面几个函数只有新版的库才能调用
    # slm_runtime.slm_enum_device_ex      =ctypes.c_uint32
    # slm_runtime.slm_enum_license_id_ex  =ctypes.c_uint32
    # slm_runtime.slm_get_license_info_ex =ctypes.c_uint32
    # slm_runtime.slm_extensions_config   =ctypes.c_uint32
except OSError as ex:
    if is_frozen:
        raise ex
    class _FakeSlmRuntime:
        def slm_user_data_getsize(self, *args):
            pass
        def slm_user_data_write(self, *args):
            pass
        def slm_user_data_read(self, *args):
            pass
        def slm_get_info(self, *args):
            pass
        def slm_free(self, *args):
            pass
        def slm_update(self, *args):
            pass
        def slm_init(self, *args):
            pass
        def slm_login(self, *args):
            pass
        def slm_logout(self, *args):
            pass
        def slm_get_developer_id(self, *args):
            pass
        def slm_cleanup(self, *args):
            pass
    slm_runtime = _FakeSlmRuntime()


def is_license_exists(license_id):
    """
    检查是否授权：只有通过授权的模块才能启用
    :param license_id: 证书编号
    :return:  True 授权通过，False 未授权
    """
    ok = _slm_init(license_id)
    if not ok:
        log.error('Read dog done: %s, License No.5 not exists.', license_id)
        return False
    else:
        log.info('Read dog done: %s，License No.5 exists.', license_id)
        left_days = get_dog_info().get("left_days") if get_dog_info().get("left_days") is not None else 0
        # 许可登出（释放登录许可句柄，一个进程最多只能打开 255 句柄）
        r = slm_runtime.slm_logout(h_login)
        # 释放资源
        #r = slm_runtime.slm_cleanup()
        # print('slm_logout r', r)
        if int(left_days) < 0:
            log.error('Read dog done: %s, License expired %s.days ago', -int(license_id))
            return False
        else:
            return True


def check_module_exist(license_id, module_no):
    """
    检查模块是否授权：只有通过授权的模块才能启用
    :param license_id: 证书编号
    :param module_no: 1 图像识别、2 图像处理、3 合规审查、4 RPA
    :return:  True 授权通过，False 未授权
    """
    ok = _slm_init(license_id)
    if not ok:
        return False
    module_ok = slm_runtime.slm_check_module(h_login, ctypes.c_uint32(int(module_no)))

    #format_print(logging.DEBUG, "模块[{0}]是否存在：{1}".format(module_no, module_ok))
    log.info('Module [ %s ] autherised: %s', module_no, module_ok)
    if module_ok == 0:
        # 测试
        # write_data_to_dog(number_left=22)
        # number_left = read_data_from_dog()
        # format_print(logging.INFO, 'Read dog done: {}'.format(number_left))
        # rite_data_to_dog(number_left=4000000)
        number_left = read_data_from_dog(module_no)
        # 许可登出（释放登录许可句柄，一个进程最多只能打开 255 句柄）
        r = slm_runtime.slm_logout(h_login)
        # 释放资源
        #r = slm_runtime.slm_cleanup()
        # print('slm_logout r', r)
        log.info('Read dog done: %s', number_left)
        return True
    else:
        return False


def _slm_init(license_id):
    # 初始化
    slm_init_param = InitParams()
    slm_init_param.version = ctypes.c_uint32(0x02)
    slm_init_param.flag = ctypes.c_uint32(0x01)
    slm_init_param.pfn = ctypes.c_void_p(0)
    slm_init_param.timeout = ctypes.c_uint32(1000 * 10)
    # 开发者 API 密码
    # E4BCFB7EE899735C6CD7B4AC47AD8A02
    slm_init_param.password = (ctypes.c_byte * 16)(0xE4, 0xBC, 0xFB, 0x7E, 0xE8, 0x99, 0x73, 0x5C, 0x6C, 0xD7, 0xB4,
                                                   0xAC, 0x47, 0xAD, 0x8A, 0x02)
    r = slm_runtime.slm_init(ctypes.byref(slm_init_param))
    #print('slm_init r', r)
    if r == None:
        log.error('slm_init runtime error, please check license driver, runtime:%s', r)
        return False
    elif r != 0:
        log.error('slm_init errno:0x%x', r)
        if r == 0x13000038:
            print('错误的API密码，请更换后重试。')
        return False  # 初始化失败，直接退出，后续功能无执行必要。

    # 许可登录
    slm_login_param = LoginParams()
    slm_login_param.license_id = ctypes.c_uint32(license_id)
    slm_login_param.size = ctypes.sizeof(slm_login_param)
    slm_login_param.login_mode = ctypes.c_uint32(0x0000)
    slm_login_param.timeout = ctypes.c_uint32(600)
    r = slm_runtime.slm_login(ctypes.byref(slm_login_param), ctypes.c_uint32(3), ctypes.byref(h_login),
                              ctypes.c_void_p(0))
    # print('slm_login r', r, 'handle', h_login)
    if r == 0:
        log.info("加密狗登陆正常：0x%08X" % r)
        # 许可登录成功，可以操作许可接口，最后调用 slm_logout 释放句柄资源
        # 模块许可操作
        r = slm_runtime.slm_user_data_getsize(h_login, RAW, ctypes.byref(rom_size))
        if r != 0:
            log.error('slm_user_data_getsize error : {}'.format(r))
        else:
            log.info('slm_user_data_getsize ok rom_size:{}'.format(rom_size.value))
        # 许可登出（释放登录许可句柄，一个进程最多只能打开 255 句柄）
        # r = slm_runtime.slm_logout(h_login)
        # 释放资源
        # r = slm_runtime.slm_cleanup()
        # print('slm_logout r', r)
        return True
    else:
        log.info("加密狗登陆异常：0x%08X" % r)
        return False


def get_dog_info():
    """
    获取加密狗信息
    :return: start_time:授权开始时间；end_time：授权结束时间；last_update：上一次更新时间；left_days：剩余天数
    """
    ok = _slm_init(3)
    if not ok:
        return False
    # 获取许可信息
    c_license_info = ctypes.c_char_p(0)
    # 查询许可信息type：3
    SESSION_INFO = 2
    JSON = 2
    session_info = ctypes.c_char_p()
    r = slm_runtime.slm_get_info(h_login, SESSION_INFO, JSON, ctypes.byref(session_info))
    # print('slm_get_info r', r, session_info.value)
    is_succeed = slm_runtime.slm_get_info(h_login, ctypes.c_uint32(3), ctypes.c_uint32(2), ctypes.byref(c_license_info))

    # license_info = ctypes.c_uint32(0)     # Unsupported Struct
    shell_num, lock_sn, start_time_styled, end_time_styled, last_update_styled, left_days = None, None, None, None, None, None
    if is_succeed == 0:
        license_info_str = bytes(ctypes.string_at(c_license_info)).decode()
        log.debug(license_info_str)
        license_info_json = json.loads(license_info_str)
        start_time = license_info_json.get('start_time', 0)
        end_time = license_info_json.get('end_time', 0)
        first_use_time = license_info_json.get('first_use_time', 0)
        span_time = license_info_json.get('span_time', 0)
        last_update = license_info_json.get('last_update_timestamp', 0)
        if end_time and span_time:
            end_time = min(end_time,max(first_use_time,last_update)+span_time)
        elif span_time:
            end_time = first_use_time+span_time
            start_time = first_use_time
        start_time_array = time.localtime(start_time)
        start_time_styled = time.strftime("%Y-%m-%d %H:%M:%S", start_time_array)
        end_time_array = time.localtime(end_time)
        end_time_styled = time.strftime("%Y-%m-%d %H:%M:%S", end_time_array)
        last_update_styled = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(last_update))

        date1 = time.strptime(time.strftime("%Y-%m-%d", start_time_array), "%Y-%m-%d")
        date2 = time.strptime(time.strftime("%Y-%m-%d", end_time_array), "%Y-%m-%d")
        # 根据上面需要计算日期还是日期时间，来确定需要几个数组段。下标0表示年，小标1表示月，依次类推...
        # date1=datetime.datetime(date1[0],date1[1],date1[2],date1[3],date1[4],date1[5])
        # date2=datetime.datetime(date2[0],date2[1],date2[2],date2[3],date2[4],date2[5])
        date1 = datetime.datetime(date1[0], date1[1], date1[2])
        date2 = datetime.datetime(date2[0], date2[1], date2[2])
        # 返回两个变量相差的值，就是相差天数
        left_days = (date2 - date1).days
        log.info("授权开始时间：{}, 结束时间：{}, 上次更新时间：{}，剩余天数：{}".format(start_time_styled, end_time_styled, last_update_styled,
                                                             left_days))

        # 查询硬件信息type：1
        c_lock_info = ctypes.c_char_p(0)
        is_succeed = slm_runtime.slm_get_info(h_login, ctypes.c_uint32(1), ctypes.c_uint32(2),  ctypes.byref(c_lock_info))
        if is_succeed == 0:
            lock_info_str = bytes(ctypes.string_at(c_lock_info)).decode()
            log.debug(lock_info_str)
            lock_info_str_json = json.loads(lock_info_str)
            if 'lock_sn' in lock_info_str_json.keys():
                lock_sn = lock_info_str_json["lock_sn"]

            shell_num = lock_info_str_json["shell_num"]
        # 释放接口内申请的内存，不释放会内存泄露
        slm_runtime.slm_free(c_license_info)
        slm_runtime.slm_free(c_lock_info)
    else:
        log.error('Error in read dog info:%s', is_succeed)
    return {"shell_num": shell_num, "lock_sn": lock_sn, 'start_time': start_time_styled, 'end_time': end_time_styled, 'last_update': last_update_styled,
            'left_days': left_days}


def read_data_from_dog(module_no=-1, data_type=RAW):
    """
    获得模块对应的授权数据
    :param module_no: 模块序号，默认为1编目，2 图像处理，3 合规，4 RPA，-1时表示读取所有模块数据
    :param data_type: 数据块的类型
    ROM = 0
    RAW = 1
    PUB = 2(套餐数量)
    :return: 模块授权数据/多个数据元组
    """
    if is_frozen:
        try:
            readbuf = (ctypes.c_int*1)()

            # r = slm_runtime.slm_user_data_read(h_login, data_type, ctypes.byref(readbuf), 0, 4)
            # if r != 0:
            #     format_print(logging.FATAL, '加密狗数据读取异常，请联系技术人员，错误代码：{}'.format(r))
            values = []
            if module_no == -1:
                # 查询所有授权数据
                for name, value in MODULES.items():
                    # 历史原因，模块号为2对应数据块为0，模块号为1对应数据块为1
                    data_pos = 1 if value == MODULES['catalog'] else \
                        0 if value == MODULES['image_process'] else value - 1
                    # 每隔16个字节重新开始1个新的加密狗数据
                    r = slm_runtime.slm_user_data_read(h_login, data_type, ctypes.byref(readbuf), data_pos*16, 4)
                    num = 0
                    if r == 0:
                        num = int(readbuf[0])
                    else:
                        log.info('加密狗数据读取异常，请联系技术人员，错误代码：{}'.format(r))
                    values.append((value, num))
                left_num_result = values
            else:
                # 历史原因，模块号为2对应数据块为0，模块号为1对应数据块为1
                data_pos = 1 if module_no == MODULES['catalog'] else \
                    0 if module_no == MODULES['image_process'] else module_no - 1

                # 只查询模块号对应的授权数据
                r = slm_runtime.slm_user_data_read(h_login, data_type, ctypes.byref(readbuf), data_pos*16, 4)
                if r != 0:
                    log.error('加密狗数据读取异常，请联系技术人员，错误代码：{}'.format(r))
                    return 0
                else:
                    left_num_result = int(readbuf[0])
            return left_num_result
        except RuntimeError:
            return 0
    else:
        if module_no == -1:
            return [(i, 400*10**4) for i in range(1, 9)]
        return 4000000


def _write_data_to_dog(module_no=1, number_left=0):
    """
    更新加密狗数据
    :param module_no: 需要更新模块的模块号
    :param number_left: 需要更新模块的授权数量
    :return:
    """
    if is_frozen:
        # 历史原因，模块号为2对应数据块为0，模块号为1对应数据块为1
        data_pos = 1 if module_no == 1 else 0 if module_no == 2 else module_no - 1
        writebuf = (ctypes.c_int*1)(number_left)
        size = 4
        offset = 16
        # 从第0个数据开始写入
        if data_pos < 0 or rom_size.value < data_pos * offset + offset:
            return False
        r = slm_runtime.slm_user_data_write(h_login, ctypes.byref(writebuf), data_pos*offset, size)
        if r != 0:
            log.error('加密狗写入错误slm_user_data_write error:0x%08X' % r)
            return False
        else:
            log.info('加密狗写入成功slm_user_data_write [{}] done!'.format(writebuf[0]))
            return True
    return True


HD_LOCKER = threading.Lock()


def write_data_to_dog(module_no=1, number=0, number_left=0):
    """
    :param module_no:
    :param number: used number
    :param number_left: ignore this param
    :return:
    """
    if int(number) <= 0:
        log.warning('invalid number of usage: {%s}', number)
        return False
    with HD_LOCKER:
        left_num = read_data_from_dog(module_no)
        left_num -= number
        left_num = left_num if left_num > 0 else 0
        return _write_data_to_dog(module_no, left_num)


class Reader:
    def __init__(self):
        self.authed_modules = self.get_modules_in_lock()

    @staticmethod
    def get_modules_in_lock():
        """
        获取授权的模块no
        :return:
        """
        module_ids = []
        for _, no in MODULES.items():
            try:
                if is_module_exist(no, 3):
                    module_ids.append(no)
            except:  # noqa
                pass
        return module_ids

    @staticmethod
    def transfer_usage(num):
        """
        图像处理A套餐（3个月100万张）
        图像处理B套餐（6个月200万张）
        图像处理C套餐（12个月400万张）
        图像处理试用套餐（2万张）
        :param num:
        :return:
        """
        base_num = 10 ** 4
        num_map = {
            2 * base_num: '图像处理试用套餐（2万张）',
            100 * base_num: '图像处理A套餐',
            200 * base_num: '图像处理B套餐',
            400 * base_num: '图像处理C套餐'
        }
        return num_map.get(num, '未设置')

    def get_full_dog_info(self):
        """
        :return: dict
        """
        data = []
        try:
            #is_module_exist(1)
            dog_data = get_dog_info()

            total_nums_data = {}
            total_nums = read_data_from_dog(data_type=0, module_no=-1)
            for _module_num, _num in total_nums:
                total_nums_data[_module_num] = _num
            for module_no, num in read_data_from_dog(module_no=-1):
                if module_no not in self.authed_modules:
                    continue
                detail = dict()
                # total = read_data_from_dog(data_type=0, module_no=module_no)
                # 总量
                detail['total'] = total_nums_data[module_no]
                detail['left_num'] = num
                detail['module_id'] = module_no
                module_name = ''
                for name, value in MODULES.items():
                    if value==module_no:
                        module_name = name
                detail['module_name'] = module_name
                # 套餐类型
                detail['service_type'] = Reader.transfer_usage(total_nums_data[module_no])
                detail.update(dog_data)
                detail = {k: '' if v is None else v for k, v in detail.items()}
                data.append(detail)
                log.info(f'Get left num from dog id:{module_no}, total:{total_nums_data[module_no]}, left: {num}')
        except RuntimeError as e:
            log.error(f'Get full dog info failed: {e}')
        return data


reader = Reader()