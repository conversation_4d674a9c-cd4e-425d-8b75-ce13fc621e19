from datareader import CoNLLReader
from vocabulary import build_vocabs_from_CoNLL_iterator
from collections import OrderedDict


'''
# 从较大预训练词表中抽取出所有和训练重合的词向量。
embed_size: 200
训练集中共32525个单词。
D:\Tencent_AILab_ChineseEmbedding\Tencent_AILab_ChineseEmbedding.txt文件中覆盖了训练集中28814个单词。
embed_size: 200
训练集中共32525个单词。
D:\BaiduNetdiskDownload\500000-small.txt文件中覆盖了训练集中24127个单词。
'''


def extract_vector(vec_path, to_path):
    train_path = '../raw_data/train.conll'
    word_voc, _, _ = build_vocabs_from_CoNLL_iterator(CoNLLReader(train_path))

    word_voc_set = set(word_voc.get_itos())

    vectors = OrderedDict()
    with open(vec_path, 'r', encoding='utf-8') as fr:
        embed_size = None
        for line in fr:
            if embed_size is None:
                temp = line.strip().split()
                if len(temp) == 2:
                    embed_size = int(temp[1])
                else:
                    embed_size = len(temp) - 1
                print('embed_size:', embed_size)
            else:
                pair = line.strip().split(maxsplit=1)
                word, embed = pair[0], pair[1]
                if word in word_voc_set:
                    vectors[word] = embed.strip()
    # 按顺序输出
    with open(to_path, 'w', encoding='utf-8') as fw:
        pairs = []
        for word, embed in vectors.items():
            ss = embed.split()
            if len(ss) == embed_size:
                pairs.append(word + ' ' + embed)
            else:
                print(len(ss), word, embed)
        print('训练集中共%d个单词。' % len(word_voc))
        print('%s文件中覆盖了训练集中%d个单词。' % (vec_path, len(pairs)))
        fw.write('\n'.join(pairs))


if __name__ == '__main__':

    vec_path = r'D:\Tencent_AILab_ChineseEmbedding\Tencent_AILab_ChineseEmbedding.txt'
    to_path = '../data/tencent_whole_200.txt'
    extract_vector(vec_path, to_path)
    #
    # to_path = '../data/tencent_200.txt'
    # vec_path = r'D:\BaiduNetdiskDownload\500000-small.txt'
    # extract_vector(vec_path, to_path)

    train_path = '../raw_data/train.conll'
    word_voc, tag_voc, label_voc = build_vocabs_from_CoNLL_iterator(CoNLLReader(train_path), min_freqs=[2, 1, 1])
    with open('../data/tencent_whole_200.txt', 'r', encoding='utf-8') as fr:
        pretrain_words = set()
        for line in fr:
            pretrain_words.add(line.strip().split(maxsplit=1)[0])

    print(len(word_voc.get_itos()), len(pretrain_words))
    cover = 0
    for w in word_voc.get_itos():
        if w in pretrain_words:
            cover += 1
    print(cover, cover / len(word_voc.get_itos()))


