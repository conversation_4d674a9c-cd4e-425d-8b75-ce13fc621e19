# 样例文件 说明服务编码习惯
import os
from fastapi import (
    FastAPI, Depends, Query, UploadFile, HTTPException
    )
from pydantic import BaseModel
from aiodataloader import DataLoader # 将请求批次化的aiodataloader包
# 本样例所指DataLoader都是指来自aiodataloader的工具类
from functools import lru_cache # 精确控制可使用cachetools包

app = FastAPI()

# 在启动逻辑中执行配置逻辑，挂载接口，
@app.on_event('startup')
async def startup():
    # 导入AI推理服务引擎
    import torch, torchvision
    # 引擎全局配置，该配置禁止运行时根据用户输入数据优化模型执行，节省显存和时间。
    torch._C._jit_set_profiling_mode(False)

    # 部署环境变量
    # 1) SLURM变量，如：
    #    SLURM_JOB_ID SLURM_JOB_NAME SLURM_STEP_ID SLURM_STEP_NODELIST SLURM_TASK_PID SLURM_SUBMIT_HOST
    # 2) CUDA变量  
    #    CUDA_VISIBLE_DEVICES CUDA_MPS_ACTIVE_THREAD_PERCENTAGE
    # 3) MASTER变量
    #    MASTER_CONF 分布式工作状态配置字符串，可自行定义含义使用
    # 4) env_file文件初始化

    ## 配置类型声明，这里示例一个，以下均为该组配置的，如多组可分别处理。
    class ServiceOut(BaseModel):
        class Config:
            frozen = True
        id:     str = 'ex1'
        module: str = 'infer.models'
        attr  : str = 'BatchImageClassifier'
        model_pt_path: str = '/myjfs/share/dog/runs/train_cls-1260/model.pt'
        use_gpu: bool = True
        # gpu_id: int = 0 # 部署中GPU由节点环境变量指定为1个，单进程单GPU模式

    ## 从数据层获取部署必要的配置信息，这里用假数据代替。 
    svc_list = [
        ServiceOut(),
        ServiceOut(id = 'ex2', attr = 'BatchObjectDetector',
            model_pt_path='/myjfs/share/aimp_model/63/runs/train_det-1149/model.pt')
    ]
    ### 信息获取结束
    ## 导入一些加载配置挂在服务需要的工具包 
    from importlib import import_module
    
    for svc in svc_list:
        ## 利用python函数参数默认值将svc的当前值传给函数，注意这个循环会改变svc的值。

        # 定义创建dataloader的函数，利用cache修饰实现一次加载
        @lru_cache(1)
        def _dataloader(svc=svc):
            # 下面这句等价于 from infer.models import BatchImageClassifier
            clazz = getattr(import_module(svc.module), svc.attr)
            # 初始化有关模型，详见infer.models样例说明，该类同时实现了推理模型和DataLoader
            m = clazz()
            m = m.get_model(svc.model_pt_path, svc.use_gpu)
            return m
    
        # 防止用于Depends时出现多线程加载（cache等对象线程不安全）（async def FastAPI下约定）
        async def anew_dataloader(svc = Query(default=svc, include_in_schema=False)):
            return _dataloader(svc)

        # 建立一个推理接口，利用DataLoader接口实现。
        @app.post(f'/inference/{svc.id}/execute')
        async def inference_model(image_file: UploadFile, a: DataLoader = Depends(anew_dataloader)):
            try:
                r = await a.load(image_file.file)
            except Exception as e:
                raise HTTPException(status_code=400, detail=str(e))
            return { 'result': r }
        ## 可建立多个

        # 运行时配置，实验演示性质，应可切换模型文件，同样利用async def顺序化操作
        # 在服务架构下，不方便在线配置总是可以通过重新部署解决，使用重新部署可简化代码逻辑
        # 生产系统包含该接口需要考虑鉴权、副本同步、可用性等多种问题，单机单副本该接口可提供便利
        @app.post(f'/inference/{svc.id}/reload')
        async def inference_reload(a: DataLoader = Depends(anew_dataloader),
            model_pt_path: str = '/myjfs/share/dog/runs/train_cls-1260/model.pt',
            svc = Query(default=svc, include_in_schema=False)
            ):
            a.initialize(model_pt_path, svc.use_gpu)
            return { 'result': 'success' }
    
        # 接口状态查询，通过发送一个样例数据实现。这里直接传入一张1x1位图(0,0,0)像素.
        # 也可考虑把样例数据写在配置中.
        from io import BytesIO
        @app.get(f'/inference/{svc.id}/status')
        async def inference_status(a: DataLoader = Depends(anew_dataloader)):
            r = await a.load(BytesIO(b'P6\n1 1\n255\n\0\0\0'))
            return { 'result': r }

        ### 完成一个服务配置

    del svc, svc_list

    ## 完成全部配置
