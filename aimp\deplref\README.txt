基于FastAPI ASGI应用的部署包样例文件
含图像分类和检测的可用推理代码

请以fm2.py及其内含文档注释为教程


infer目录
  /cls.py   ImageClassifier
  /det.py   ObjectDetector     分割同样兼容，由于分割结果很大，生产不会用JSON文本字符串做部署。
  /mixin.py BatchInferFnMixin  提供批次化处理代码
  /model.py 利用上述类实做 BatchImageClassifier BatchObjectDetector，接入aiodataloader的DataLoader。

集群API参阅
http://ubt-b450m-02.lan:9000/docs#/default/workload_job_create_workload_job_post
输入类型：JobAIAIn 详见在线文档
得到返回值JobId后请 GET /workload/job/{jobid} 确认部署实例在运行中
外部删除部署实例 DELETE /workload/job/{jobid}
因复用此前的接口，使用了工作负载和任务字样

--- 以下为站点信息
其他使用接口需要了解的信息（查询接口暂未实现）

计算节点计算单元数，当前设置：
ubt-B450M-02 : NVIDIA GTX     1660 = 200
ubt-B450M-03 : NVIDIA Quadro A4000 = 700
用于设置compute_count时参考，默认100，和JobAIDPIn离线任务相同。
建议调高到300获得较为不错的性能。
该值设定最早在离线任务中的目的是允许1660显卡同时运行版面分析和OCR两个GPU任务。
A4000=700为近日经速度评测后修改，现值速度较为合理，不同型号很难在不同任务下完全对应性能比。
图像训练任务采用占卡模式，占一卡。
（现有）文本训练任务计算单元设定150。

调度说明
CPU：一核。
内存：集群管理软件一般不做管理，因生产环境一般都是超提交（同时运行的程序总内存需求和大于总物理内存，
由于操作系统的机智，程序总是运行良好，实际使用量也小于物理内存数）。

Python环境说明
--- the basic performant Uvicorn.
Installing collected packages: h11, uvicorn, uvloop, httptools
Successfully installed h11-0.13.0 httptools-0.4.0 uvicorn-0.18.3 uvloop-0.16.0

--- python-dotenv
Installing collected packages: python-dotenv
Successfully installed python-dotenv-0.21.0

--- FastAPI
Installing collected packages: sniffio, anyio, starlette, pydantic, fastapi
Successfully installed anyio-3.6.1 fastapi-0.85.0 pydantic-1.10.2 sniffio-1.3.0 starlette-0.20.4

--- for file uploading
$ pip install python-multipart
Successfully installed python-multipart-0.0.5

--- Django
Successfully installed Django-4.1.1 sqlparse-0.4.3

--- aiodataloader the generic batching and caching layer for accessing datastores
$ pip install aiodataloader==0.3

--- cachetools a module for some caching algorithms 
Successfully installed cachetools-5.2.0

--- PyTorch, the basic training and inference plat. in AIMP (torch 1.13 torchaudio 0.13 torchvision 0.14)
Successfully installed nvidia-cublas-cu11-********** nvidia-cuda-nvrtc-cu11-11.7.99 nvidia-cuda-runtime-cu11-11.7.99 nvidia-cudnn-cu11-******** torch-1.13.0 torchaudio-0.13.0 torchvision-0.14.0

--- transformers (The huggingface transformers)
Successfully installed attrs-22.1.0 filelock-3.8.0 huggingface-hub-0.11.0 jsonlines-3.1.0 pyyaml-6.0 regex-2022.10.31 tokenizers-0.12.1 transformers-4.21.0

--- pycocotools For AIMP Vision Training
Successfully built pycocotools
Successfully installed contourpy-1.0.5 cycler-0.11.0 fonttools-4.37.4 kiwisolver-1.4.4 matplotlib-3.6.1 pycocotools-2.0.5

--- colordog in UIE training code, should be removed. 
Successfully installed colorlog-6.7.0
