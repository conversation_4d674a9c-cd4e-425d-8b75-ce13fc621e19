import numpy as np
import torch
from kbnlp.taskflow.models.information_extraction.information_extraction_model import UIE
from kbnlp.pretrained.ernie.configuration_ernie import ErnieConfig


def convert_and_exam(saved_path=None,
                     vocab_path=None,
                     paddle_model_path=r'C:\Users\<USER>\.uiex_p\taskflow\information_extraction\uie-tiny',
                     acc=4,
                     hidden_size=768,
                     num_hidden_layers=6,
                     intermediate_size=3078,
                     max_position_embeddings=2048,
                     task_type_vocab_size=16,
                     use_task_id=True):
    """
    将paddle中的ernie模型转换位transformers中的torch模型。
    Args:
        saved_path: 转换后模型的保存路径。
        paddle_model_path: paddle的加载路径。
        acc: 验证保证小数点后的精度位数。
        num_hidden_layers:
        max_position_embeddings:
        task_type_vocab_size:
        use_task_id:

    Returns:

    """
    inputs = np.random.randint(100, 200, (2, 10))
    inputs[1, 3] = 0
    # inputs = np.arange(12).reshape(2, 6)  # 检测有padding=0值。

    torch_uie_config = ErnieConfig(hidden_size=hidden_size, num_hidden_layers=num_hidden_layers, max_position_embeddings=max_position_embeddings,
                                   task_type_vocab_size=task_type_vocab_size, intermediate_size=intermediate_size, use_task_id=use_task_id)
    torch_uie = UIE(torch_uie_config)

    import paddle
    from paddlenlp.transformers import ErniePretrainedModel
    paddle.set_device('cpu')

    class Paddle_UIE(ErniePretrainedModel):
        def __init__(self, encoding_model):
            super(Paddle_UIE, self).__init__()
            self.encoder = encoding_model
            hidden_size = self.encoder.config["hidden_size"]
            self.linear_start = paddle.nn.Linear(hidden_size, 1)
            self.linear_end = paddle.nn.Linear(hidden_size, 1)
            self.sigmoid = paddle.nn.Sigmoid()

        def forward(self, input_ids, token_type_ids=None, pos_ids=None, att_mask=None):
            sequence_output, pooled_output = self.encoder(
                input_ids=input_ids,
                token_type_ids=token_type_ids,
                position_ids=pos_ids,
                attention_mask=att_mask)
            start_logits = self.linear_start(sequence_output)
            start_logits = paddle.squeeze(start_logits, -1)
            start_prob = self.sigmoid(start_logits)
            end_logits = self.linear_end(sequence_output)
            end_logits = paddle.squeeze(end_logits, -1)
            end_prob = self.sigmoid(end_logits)
            return start_logits, end_logits, start_prob, end_prob
    print('paddle模型加载路径为：%s' % paddle_model_path)
    paddle_uie = Paddle_UIE.from_pretrained(paddle_model_path)
    paddle_uie.eval()
    start_logit_paddle, end_logit_paddle = paddle_uie(paddle.to_tensor(inputs))[0:2]
    start_logit_paddle, end_logit_paddle = start_logit_paddle.numpy(), end_logit_paddle.numpy()
    print('start_logit_paddle:', start_logit_paddle, 'end_logit_paddle:', end_logit_paddle, sep='\n')

    from collections import OrderedDict
    # paddle linear与torch linear的weight互为转置，embeddings的weight是一致的。
    paddle_state_dict = OrderedDict(
        ((k, torch.tensor(v.T.numpy())) if (v.ndim == 2 and 'embeddings' not in k) else (k, torch.tensor(v.numpy()))
         for k, v in paddle_uie.state_dict().items()))
    torch_state_dict = OrderedDict((('ernie.embeddings.position_ids', torch.arange(0, 2048).unsqueeze(0)),
                                    ('ernie.embeddings.word_embeddings.weight',
                                     paddle_state_dict['encoder.embeddings.word_embeddings.weight']),
                                    ('ernie.embeddings.position_embeddings.weight',
                                     paddle_state_dict['encoder.embeddings.position_embeddings.weight']),
                                    ('ernie.embeddings.token_type_embeddings.weight',
                                     paddle_state_dict['encoder.embeddings.token_type_embeddings.weight']),
                                    ('ernie.embeddings.task_type_embeddings.weight',
                                     paddle_state_dict['encoder.embeddings.task_type_embeddings.weight']),
                                    ('ernie.embeddings.LayerNorm.weight',
                                     paddle_state_dict['encoder.embeddings.layer_norm.weight']),
                                    ('ernie.embeddings.LayerNorm.bias',
                                     paddle_state_dict['encoder.embeddings.layer_norm.bias']),
                                    ))
    num_hidden_layers = len(paddle_uie.encoder.encoder.layers)
    for layer in range(num_hidden_layers):
        prefix = ('ernie.encoder.layer.' + str(layer) + '.')
        torch_state_dict.update(((prefix + 'attention.self.query.weight',
                                  paddle_state_dict[
                                      'encoder.encoder.layers.' + str(layer) + '.self_attn.q_proj.weight']),
                                 (prefix + 'attention.self.query.bias',
                                  paddle_state_dict['encoder.encoder.layers.' + str(layer) + '.self_attn.q_proj.bias']),
                                 (prefix + 'attention.self.key.weight',
                                  paddle_state_dict[
                                      'encoder.encoder.layers.' + str(layer) + '.self_attn.k_proj.weight']),
                                 (prefix + 'attention.self.key.bias',
                                  paddle_state_dict['encoder.encoder.layers.' + str(layer) + '.self_attn.k_proj.bias']),
                                 (prefix + 'attention.self.value.weight',
                                  paddle_state_dict[
                                      'encoder.encoder.layers.' + str(layer) + '.self_attn.v_proj.weight']),
                                 (prefix + 'attention.self.value.bias',
                                  paddle_state_dict['encoder.encoder.layers.' + str(layer) + '.self_attn.v_proj.bias']),
                                 (prefix + 'attention.output.dense.weight',
                                  paddle_state_dict[
                                      'encoder.encoder.layers.' + str(layer) + '.self_attn.out_proj.weight']),
                                 (prefix + 'attention.output.dense.bias',
                                  paddle_state_dict[
                                      'encoder.encoder.layers.' + str(layer) + '.self_attn.out_proj.bias']),
                                 (prefix + 'attention.output.LayerNorm.weight',
                                  paddle_state_dict['encoder.encoder.layers.' + str(layer) + '.norm1.weight']),
                                 (prefix + 'attention.output.LayerNorm.bias',
                                  paddle_state_dict['encoder.encoder.layers.' + str(layer) + '.norm1.bias']),
                                 (prefix + 'intermediate.dense.weight',
                                  paddle_state_dict['encoder.encoder.layers.' + str(layer) + '.linear1.weight']),
                                 (prefix + 'intermediate.dense.bias',
                                  paddle_state_dict['encoder.encoder.layers.' + str(layer) + '.linear1.bias']),
                                 (prefix + 'output.dense.weight',
                                  paddle_state_dict['encoder.encoder.layers.' + str(layer) + '.linear2.weight']),
                                 (prefix + 'output.dense.bias',
                                  paddle_state_dict['encoder.encoder.layers.' + str(layer) + '.linear2.bias']),
                                 (prefix + 'output.LayerNorm.weight',
                                  paddle_state_dict['encoder.encoder.layers.' + str(layer) + '.norm2.weight']),
                                 (prefix + 'output.LayerNorm.bias',
                                  paddle_state_dict['encoder.encoder.layers.' + str(layer) + '.norm2.bias']),
                                 ))
    torch_state_dict.update((('linear_start.weight', paddle_state_dict['linear_start.weight']),
                             ('linear_start.bias', paddle_state_dict['linear_start.bias']),
                             ('linear_end.weight', paddle_state_dict['linear_end.weight']),
                             ('linear_end.bias', paddle_state_dict['linear_end.bias'])))
    torch_uie.load_state_dict(torch_state_dict, strict=True)
    # paddle的word_embeddings的padding_idx对应的词向量没有设置为0，而transformers中的word_embeddings的padding_idx对应的词向量是要求必须为0的。
    word_embeddings = torch_uie.ernie.embeddings.word_embeddings
    word_embeddings.weight.data[word_embeddings.padding_idx].zero_()
    torch_uie.eval()
    with torch.no_grad():
        start_logit_torch, end_logit_torch = torch_uie(torch.tensor(inputs), output_start_end_logits=True)[2:]
    start_logit_torch, end_logit_torch = start_logit_torch.numpy(), end_logit_torch.numpy()
    print('start_logit_torch:', start_logit_torch, 'end_logit_torch:', end_logit_torch, sep='\n')
    import math
    acc_ = math.pow(10, -acc)
    acc_guaranteed = np.all(np.abs(start_logit_torch -
                                   start_logit_paddle) < acc_) and np.all(np.abs(end_logit_torch -
                                                                                end_logit_paddle) < acc_)
    print('保证小数点后%d位精度：' % acc, acc_guaranteed)
    if acc_guaranteed:
        torch_uie.save_pretrained(saved_path)
        print('torch model saved in:%s.' % saved_path)
        import os
        if vocab_path and os.path.exists(vocab_path):
            from transformers import BertTokenizerFast
            print('load vocab form: %s' % vocab_path)
            tokenizer = BertTokenizerFast(vocab_path)
            tokenizer.save_pretrained(saved_path)
            print('Transformers Tokenizer config files saved in: %s' % saved_path)
        else:
            print('Without vocab.txt file, Tokenzier config files then need to be pasted in model path manually.')
    else:
        print('未能保证精度，模型未保存。')
    print('finish.')


if __name__ == '__main__':
    vocab_path = r'/kbnlp/pretrained/ernie/vocab.txt'

    # uie_tiny_path = r'C:\Users\<USER>\.uiex_p\taskflow\information_extraction\uie-tiny'
    # saved_path = './uie-tiny'
    # convert_and_exam(saved_path=saved_path, vocab_path=vocab_path, paddle_model_path=uie_tiny_path)

    # uie_base_path = r'C:\Users\<USER>\.uiex_p\taskflow\information_extraction\uie-base'
    # saved_path = './uie-base'
    # convert_and_exam(saved_path=saved_path, vocab_path=vocab_path, paddle_model_path=uie_base_path, num_hidden_layers=12,
    #                  task_type_vocab_size=3,)

    # uie_tiny_msra_path = r'C:\Users\<USER>\Desktop\uie-tiny-msra-paddle\model_best'
    # saved_path = r'D:\workspace\pycharm\nlp_project\kbnlp\data\finetuned\uie-tiny-msra'
    # convert_and_exam(saved_path=saved_path, vocab_path=vocab_path, paddle_model_path=uie_tiny_msra_path)

    # uie_nano_path = r'C:\Users\<USER>\.uiex_p\taskflow\information_extraction\uie-nano'
    uie_nano_path = r'D:\workspace\pycharm\nlp_project\kbnlp\temp\model_1750'
    # saved_path = r'C:\Users\<USER>\.kbnlp\tasks\information_extraction\uie-nano'
    saved_path = r'D:\workspace\pycharm\nlp_project\kbnlp\temp\uie_nana_step_1750'
    convert_and_exam(saved_path=saved_path, vocab_path=vocab_path, paddle_model_path=uie_nano_path, hidden_size=312,
                     num_hidden_layers=4, intermediate_size=1248)
