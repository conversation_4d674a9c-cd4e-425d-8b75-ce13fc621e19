from openpyxl import Workbook, load_workbook
import re
from ymlkbqa.question_classifier2 import deal_address
feats_sorted = {'特等功': 0, '五大功': 0, '一等功': 1, '三大功': 1, '四大功': 1, '集体一等功': 2, '二等功': 3, '一大功': 3,
                '二大功': 3, '三小功': 3, '双大功': 3, '大功': 3, '集体二等功': 4, '三等功': 5, '一小功': 5, '二小功': 5,
                '小功': 5, '四等功': 6}


def get_feat_node_data():
    wb = Workbook()
    ws = wb.active
    ws.title = '战功'
    column_names = ['ID', 'name']
    column_index = [chr(ord('A') + i) + '1' for i in range(len(column_names))]  # A1~M1
    for idx, name in zip(column_index, column_names):
        ws[idx] = name

    wb_hero = load_workbook(r'D:\workspace\pycharm\nlp_project\ymlkbqa\二等功及以上英雄_图库文件.xlsx')
    ws_hero = wb_hero.active
    cols = ws_hero.columns
    unique_feat = set()
    for i, col in enumerate(cols):
        if col[0].value == 'feat':
            unique_feat.update(sum([(row.value).split('，') for row in col[1:] if row.value], []))
    unique_feat = [f for f in unique_feat if f in feats_sorted]
    for i, feat in enumerate(sorted(unique_feat, key=lambda f: feats_sorted[f])):
        ws.append([i+1, feat])
    wb.save(r'D:\workspace\pycharm\nlp_project\ymlkbqa\战功_图库文件.xlsx')


def get_hero_feat_relation():
    wb_feat = load_workbook(r'D:\workspace\pycharm\nlp_project\ymlkbqa\战功_图库文件.xlsx')
    ws_feat = wb_feat.active
    feat2id = dict()
    for i, row in enumerate(ws_feat.rows):
        if i == 0:
            pass
        else:
            feat2id[row[1].value] = row[0].value

    wb_hero = load_workbook(r'D:\workspace\pycharm\nlp_project\ymlkbqa\二等功及以上英雄_图库文件.xlsx')
    ws_hero = wb_hero.active
    column_names = []
    hero_feat_rel = []
    for i, row in enumerate(ws_hero.rows):
        if i == 0:
            column_names.extend([col.value for col in row if col.value])
        else:
            hero_id = row[column_names.index('ID')].value
            if row[column_names.index('feat')].value:
                feats = row[column_names.index('feat')].value.split('，')
                for feat in feats:
                    if feat in feat2id:
                        hero_feat_rel.append([hero_id, feat2id[feat]])
                    else:
                        try:
                            raise ValueError('没有记录的战功：%s' % feat)
                        except ValueError as e:
                            print(e)
    wb_hero_feat_rel = Workbook()
    ws_hero_feat_rel = wb_hero_feat_rel.active
    ws_hero_feat_rel.title = '获得战功'
    ws_hero_feat_rel.append(['LABEL=获得战功', 'SRC_LABEL=人物', 'DST_LABEL=战功'])
    ws_hero_feat_rel.append(['ID:STRING:SRC_ID', 'ID:STRING:DST_ID'])
    for l in hero_feat_rel:
        ws_hero_feat_rel.append(l)
    wb_hero_feat_rel.save(r'D:\workspace\pycharm\nlp_project\ymlkbqa\人物_战功_图库关系文件.xlsx')




p_battle = re.compile('抗美援朝战争|第一次战役|第二次战役|第三次战役|第四次战役|第五次战役')


def deal_battle(battle):
    return [b for b in re.split(p_battle, battle) if b]


def get_battle_node_data():
    wb = Workbook()
    ws = wb.active
    ws.title = '战争战役'
    column_names = ['ID', 'name']
    column_index = [chr(ord('A') + i) + '1' for i in range(len(column_names))]  # A1~M1
    for idx, name in zip(column_index, column_names):
        ws[idx] = name
    wb_hero = load_workbook(r'D:\workspace\pycharm\nlp_project\ymlkbqa\二等功及以上英雄_图库文件.xlsx')
    ws_hero = wb_hero.active

    unique_battle = dict()
    for b in ['抗美援朝战争', '第一次战役', '第二次战役', '第三次战役', '第四次战役', '第五次战役']:
        unique_battle[b] = len(unique_battle) + 1

    column_names = []
    for i, row in enumerate(ws_hero.rows):
        if i == 0:
            column_names.extend([col.value for col in row if col.value])
        else:
            battle = row[column_names.index('battle')].value
            if battle:
                battles = deal_battle(battle)
                if battles:
                    if battles[-1] not in unique_battle:
                        unique_battle[battles[-1]] = len(unique_battle) + 1

    for ID, battle in sorted([(v, k) for k, v in unique_battle.items()], key=lambda it: it[0]):
        ws.append([ID, battle])
    wb.save(r'D:\workspace\pycharm\nlp_project\ymlkbqa\战争战役_图库文件.xlsx')


def get_hero_battle_relation():
    wb_battle = load_workbook(r'D:\workspace\pycharm\nlp_project\ymlkbqa\战争战役_图库文件.xlsx')
    ws_battle = wb_battle.active
    battle2id = dict()
    for i, row in enumerate(ws_battle.rows):
        if i == 0:
            pass
        else:
            battle2id[row[1].value] = row[0].value

    wb_hero = load_workbook(r'D:\workspace\pycharm\nlp_project\ymlkbqa\二等功及以上英雄_图库文件.xlsx')
    ws_hero = wb_hero.active
    column_names = []
    hero_battle_rel = []
    for i, row in enumerate(ws_hero.rows):
        if i == 0:
            column_names.extend([col.value for col in row if col.value])
        else:
            hero_id = row[column_names.index('ID')].value
            if row[column_names.index('battle')].value:
                battles = deal_battle(row[column_names.index('battle')].value)
                if battles:
                    battle = battles[-1]
                else:
                    battle = re.findall(p_battle, row[column_names.index('battle')].value)[-1]
                if battle in battle2id:
                    hero_battle_rel.append([hero_id, battle2id[battle]])
                else:
                    try:
                        raise ValueError('没有记录的战役：%s' % battle)
                    except ValueError as e:
                        print(e)
    wb_hero_battle_rel = Workbook()
    ws_hero_battle_rel = wb_hero_battle_rel.active
    ws_hero_battle_rel.title = '参加战役'
    ws_hero_battle_rel.append(['LABEL=参加战役', 'SRC_LABEL=人物', 'DST_LABEL=战争战役'])
    ws_hero_battle_rel.append(['ID:STRING:SRC_ID', 'ID:STRING:DST_ID'])
    for l in hero_battle_rel:
        ws_hero_battle_rel.append(l)
    wb_hero_battle_rel.save(r'D:\workspace\pycharm\nlp_project\ymlkbqa\人物_战争战役_图库关系文件.xlsx')


p_troop = re.compile(r'\w+军')


def deal_troop(troop):
    res = []
    if troop:
        m = re.match(p_troop, troop)
        if m:
            res.append(troop[: m.span()[1]])
            res.append(troop[m.span()[1]:])
        else:
            res.append(troop)
    return res


def get_troop_node_data():
    wb = Workbook()
    ws = wb.active
    ws.title = '军团'
    column_names = ['ID', 'name']
    column_index = [chr(ord('A') + i) + '1' for i in range(len(column_names))]  # A1~M1
    for idx, name in zip(column_index, column_names):
        ws[idx] = name
    wb_hero = load_workbook(r'D:\workspace\pycharm\nlp_project\ymlkbqa\二等功及以上英雄_图库文件.xlsx')
    ws_hero = wb_hero.active
    column_names = []
    unique_troop = dict()
    for i, row in enumerate(ws_hero.rows):
        if i == 0:
            column_names.extend([col.value for col in row if col.value])
        else:
            troop = row[column_names.index('troop')].value
            if troop:
                troops = deal_troop(troop)
                if troops and troops[0] not in unique_troop:
                    unique_troop[troops[0]] = len(unique_troop) + 1
    for ID, troop in sorted([(v, k) for k, v in unique_troop.items()], key=lambda it: it[0]):
        ws.append([ID, troop])
    wb.save(r'D:\workspace\pycharm\nlp_project\ymlkbqa\军团_图库文件.xlsx')


def get_hero_troop_relation():
    wb_troop = load_workbook(r'D:\workspace\pycharm\nlp_project\ymlkbqa\军团_图库文件.xlsx')
    ws_troop = wb_troop.active
    troop2id = dict()
    for i, row in enumerate(ws_troop.rows):
        if i == 0:
            pass
        else:
            troop2id[row[1].value] = row[0].value

    wb_hero = load_workbook(r'D:\workspace\pycharm\nlp_project\ymlkbqa\二等功及以上英雄_图库文件.xlsx')
    ws_hero = wb_hero.active
    column_names = []
    hero_troop_rel = []
    for i, row in enumerate(ws_hero.rows):
        if i == 0:
            column_names.extend([col.value for col in row if col.value])
        else:
            hero_id = row[column_names.index('ID')].value
            # 仅保留‘军’
            if row[column_names.index('troop')].value:
                troop = deal_troop(row[column_names.index('troop')].value)[0]
                if troop in troop2id:
                    hero_troop_rel.append([hero_id, troop2id[troop]])
                else:
                    try:
                        raise ValueError('没有记录的军团：%s' % troop)
                    except ValueError as e:
                        print(e)
    wb_hero_troop_rel = Workbook()
    ws_hero_troop_rel = wb_hero_troop_rel.active
    ws_hero_troop_rel.title = '所在军团'
    ws_hero_troop_rel.append(['LABEL=所在军团', 'SRC_LABEL=人物', 'DST_LABEL=军团'])
    ws_hero_troop_rel.append(['ID:STRING:SRC_ID', 'ID:STRING:DST_ID'])
    for l in hero_troop_rel:
        ws_hero_troop_rel.append(l)
    wb_hero_troop_rel.save(r'D:\workspace\pycharm\nlp_project\ymlkbqa\人物_军团_图库关系文件.xlsx')


def get_position_node_data():
    wb = Workbook()
    ws = wb.active
    ws.title = '籍贯'
    column_names = ['ID', 'name']
    column_index = [chr(ord('A') + i) + '1' for i in range(len(column_names))]  # A1~M1
    for idx, name in zip(column_index, column_names):
        ws[idx] = name
    wb_hero = load_workbook(r'D:\workspace\pycharm\nlp_project\ymlkbqa\二等功及以上英雄_图库文件.xlsx')
    ws_hero = wb_hero.active
    column_names = []
    unique_position = dict()
    for i, row in enumerate(ws_hero.rows):
        if i == 0:
            column_names.extend([col.value for col in row if col.value])
        else:
            position = row[column_names.index('birthplace')].value
            if position:
                positions = deal_address(position)
                if positions and positions[0] not in unique_position:
                    unique_position[positions[0]] = len(unique_position) + 1
    for ID, position in sorted([(v, k) for k, v in unique_position.items()], key=lambda it: it[0]):
        ws.append([ID, position])
    wb.save(r'D:\workspace\pycharm\nlp_project\ymlkbqa\籍贯_图库文件.xlsx')


def get_hero_position_relation():
    wb_position = load_workbook(r'D:\workspace\pycharm\nlp_project\ymlkbqa\籍贯_图库文件.xlsx')
    ws_position = wb_position.active
    position2id = dict()
    for i, row in enumerate(ws_position.rows):
        if i == 0:
            pass
        else:
            position2id[row[1].value] = row[0].value

    wb_hero = load_workbook(r'D:\workspace\pycharm\nlp_project\ymlkbqa\二等功及以上英雄_图库文件.xlsx')
    ws_hero = wb_hero.active
    column_names = []
    hero_position_rel = []
    for i, row in enumerate(ws_hero.rows):
        if i == 0:
            column_names.extend([col.value for col in row if col.value])
        else:
            hero_id = row[column_names.index('ID')].value
            if row[column_names.index('birthplace')].value:
                position = deal_address(row[column_names.index('birthplace')].value)[0]
                if position in position2id:
                    hero_position_rel.append([hero_id, position2id[position]])
                else:
                    try:
                        raise ValueError('没有记录的军团：%s' % position)
                    except ValueError as e:
                        print(e)
    wb_hero_position_rel = Workbook()
    ws_hero_position_rel = wb_hero_position_rel.active
    ws_hero_position_rel.title = '籍贯关系'
    ws_hero_position_rel.append(['LABEL=籍贯关系', 'SRC_LABEL=人物', 'DST_LABEL=籍贯'])
    ws_hero_position_rel.append(['ID:STRING:SRC_ID', 'ID:STRING:DST_ID'])
    for l in hero_position_rel:
        ws_hero_position_rel.append(l)
    wb_hero_position_rel.save(r'D:\workspace\pycharm\nlp_project\ymlkbqa\人物_籍贯_图库关系文件.xlsx')


if __name__ == '__main__':
    get_feat_node_data()
    get_battle_node_data()
    get_troop_node_data()
    get_position_node_data()
    get_hero_troop_relation()
    get_hero_battle_relation()
    get_hero_feat_relation()
    get_hero_position_relation()