import openpyxl
from typing import Dict
import requests
import json
from openpyxl import Workbook, load_workbook
# wb = load_workbook('二等功及以上英雄_图库文件.xlsx')
# ws = wb.active


if __name__ == '__main__':

    login_url = "http://lgraph:7090/login"
    graph_name = 'archive'
    data = requests.post(login_url, json={'user': "admin", 'password': "73@TuGraph"})
    # print(data)
    jwt = data.json()['jwt']
    # print(jwt)
    _auth = {'Authorization': f'Bearer {jwt}'}
    headers = {'Accept': 'application/json; charset=UTF-8',
               'Content-Type': 'application/json; charset=UTF-8'}
    headers.update(_auth)
    _cypher_url = f'http://lgraph:7090/cypher'

    def run(cypher):
        # try:
        query_data = {
                "graph": graph_name,
                "script": cypher,
            }
        query_ret = requests.post(f'http://lgraph:7090/cypher', json=query_data, headers=headers)
        return query_ret

    def insert_edge(subject_type, predicate, object_type, SRC_ID, DST_ID):
        insert_flag = False
        check_cypher = "MATCH (a:%s)-[rel:%s]-> (b:%s) WHERE a.ID = '%s' AND b.ID = '%s' return  rel" % (subject_type, predicate, object_type, SRC_ID, DST_ID)
        insert_cypher = "MATCH (a:%s {ID:'%s'}), (b:%s {ID:'%s'})  CREATE (a)-[r:%s]->(b)" % (subject_type, SRC_ID, object_type, DST_ID, predicate)
        check_res = run(check_cypher)
        if check_res.status_code != 200:
            print(check_cypher)
            print(check_res)
        else:
            if len(check_res.json()['result']) == 0:
                insert_res = run(insert_cypher)
                if insert_res.status_code != 200:
                    print(insert_cypher)
                    print(insert_res)
                else:
                    insert_flag = True
        return insert_flag


    def insert_edges(relations_path):
        wb_person_troop = load_workbook(relations_path)
        ws_person_troop = wb_person_troop.active
        rel_label = ''
        subject_type = ''
        object_type = ''
        for i, row in enumerate(ws_person_troop.rows):
            if i == 0:
                labels = [col.value.split('=')[-1] for col in row if col.value]
                rel_label = labels[0]
                subject_type = labels[1]
                object_type = labels[2]
            elif i == 1:
                pass
            else:
                ids = [str(col.value) for col in row if col.value]
                src_id = ids[0]
                dst_id = ids[1]
                insert_res = insert_edge(subject_type, rel_label, object_type, src_id, dst_id)
                # if not insert_res:
                #     print(i, (subject_type, rel_label, object_type, src_id, dst_id))


    # insert_edges(r'D:\workspace\pycharm\nlp_project\ymlkbqa\人物_军团_图库关系文件.xlsx')
    # insert_edges(r'D:\workspace\pycharm\nlp_project\ymlkbqa\人物_战争战役_图库关系文件.xlsx')
    # insert_edges(r'D:\workspace\pycharm\nlp_project\ymlkbqa\人物_战功_图库关系文件.xlsx')
    # insert_edges(r'D:\workspace\pycharm\nlp_project\ymlkbqa\人物_籍贯_图库关系文件.xlsx')
