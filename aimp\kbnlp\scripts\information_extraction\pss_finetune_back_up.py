#!/usr/bin/env python
# coding=utf-8
import math
from torch import nn
import torch.nn.functional as F
from transformers.modeling_outputs import SequenceClassifierOutput
from kbnlp.pretrained.ernie_layout.modeling_ernie_layout import ErnieLayoutModel, ErnieLayoutPretrainedModel

import logging
import os
import shutil
import sys
import re
import warnings
from dataclasses import dataclass, field
from collections.abc import Sequence
from typing import Optional
import random
from functools import partial
from PIL import Image
import numpy as np
from copy import copy, deepcopy
from itertools import accumulate
import torch
from torch.utils.data.dataset import Dataset as TorchDataset, T_co
from torch.utils.data.dataset import IterableDataset
import transformers
from transformers import (

    XLMRobertaTokenizerFast,
    PreTrainedTokenizerFast,
    TrainerCallback,
    TrainerState,
    TrainerControl,
    Trainer,
    HfArgumentParser,
    TrainingArguments,
)
from transformers.trainer_utils import get_last_checkpoint, is_main_process
from datasets import load_dataset, load_metric, Dataset
from datasets.packaged_modules import _PACKAGED_DATASETS_MODULES, _hash_python_lines
import inspect

from kbnlp.scripts.information_extraction.temp_ie_utils import map_offset
from kbnlp.scripts.information_extraction.temp_doc_parser import DocParser
from kbnlp.taskflow.models.information_extraction.information_extraction_model import UIEX
from kbnlp.pretrained.ernie_layout.configuration_ernie_layout import ErnieLayoutConfig
# from temp_ie_utils import compute_metrics
from kbnlp.scripts.information_extraction import label_studio_document_uiex_builder, doccano_document_uiex_builder, aidp_document_uiex_builder, aidp_draw_box_document_uiex_builder
from kbnlp.scripts.information_extraction.my_convert import LabelStudioConvertorForUIEX, ConvertorForUIEX, AIDPConvertorForUIEX, AIDPDrawBoxConvertorForUIEX, AIDPDrawBoxConvertorForPSS

torch._C._jit_set_profiling_mode(False)  # 需要在最开始处执行，解决torch1.9~1.12script模型可能执行不正常的bug

# data_source2builder = {"aidp3.0": aidp_document_uiex_builder,
#                        "aidp3.1": aidp_draw_box_document_uiex_builder}
# data_source2reader_module = {"aidp3.0": AIDPConvertorForUIEX,
#                              "aidp3.1": AIDPDrawBoxConvertorForUIEX}
data_source2reader_module = {"aidp3.1": AIDPDrawBoxConvertorForPSS}
p = re.compile('\[[.+,]*.+\]')


class PSSModel(ErnieLayoutPretrainedModel):
    def __init__(self, config):
        # input_size, hidden_size, num_layers, batch_first, dropout, bidirectional
        super(PSSModel, self).__init__(config)
        self.ernie_layout = ErnieLayoutModel(config)
        self.dropout1 = nn.Dropout(config.hidden_dropout_prob)
        self.layer_norm1 = nn.LayerNorm(config.hidden_size * 3,
                                        eps=config.layer_norm_eps)
        self.linear = nn.Linear(config.hidden_size * 3, config.hidden_size // 2)
        self.dropout2 = nn.Dropout(config.hidden_dropout_prob)
        self.lstm = nn.LSTM(config.hidden_size // 2, config.hidden_size // 2, num_layers=1, batch_first=True,
                            dropout=config.hidden_dropout_prob, bidirectional=True)
        self.dropout3 = nn.Dropout(config.hidden_dropout_prob)
        self.classifier = nn.Linear(config.hidden_size, 2)

    def forward(self,
                input_ids=None,
                bbox=None,
                image=None,
                token_type_ids=None,
                position_ids=None,
                attention_mask=None,
                head_mask=None,
                labels=None,
                output_hidden_states=False,
                output_attentions=False,
                return_dict=False
                ):
        input_shape = input_ids.size()
        visual_shape = list(input_shape)
        visual_shape[1] = self.ernie_layout.config.image_feature_pool_shape[
                              0] * self.ernie_layout.config.image_feature_pool_shape[1]
        visual_bbox = self.ernie_layout._calc_visual_bbox(
            self.ernie_layout.config.image_feature_pool_shape, bbox,
            visual_shape)

        visual_position_ids = torch.arange(0, visual_shape[1], device=input_ids.device).expand(
            [input_shape[0], visual_shape[1]])

        initial_image_embeddings = self.ernie_layout._calc_img_embeddings(
            image=image,
            bbox=visual_bbox,
            position_ids=visual_position_ids,
        )

        outputs = self.ernie_layout(
            input_ids=input_ids,
            bbox=bbox,
            image=image,
            attention_mask=attention_mask,
            token_type_ids=token_type_ids,
            position_ids=position_ids,
            head_mask=head_mask,
            output_hidden_states=output_hidden_states,
            output_attentions=output_attentions,
            return_dict=return_dict
        )
        seq_length = input_ids.shape[1]
        sequence_output, final_image_embeddings = outputs[
            0][:, :seq_length], outputs[0][:, seq_length:]

        cls_final_output = sequence_output[:, 0, :]

        # average-pool the visual embeddings
        pooled_initial_image_embeddings = initial_image_embeddings.mean(axis=1)
        pooled_final_image_embeddings = final_image_embeddings.mean(axis=1)
        # concatenate with cls_final_output
        sequence_output = torch.cat([
            cls_final_output, pooled_initial_image_embeddings,
            pooled_final_image_embeddings
        ],
            dim=1)
        sequence_output = torch.unsqueeze(sequence_output, 0)
        hidden_states = self.lstm(self.dropout2(F.gelu(self.linear(self.dropout1(self.layer_norm1(sequence_output))))))
        hidden_states = hidden_states[0].view(input_ids.size(0), -1)
        logits = self.classifier(self.dropout3(hidden_states))

        loss = None
        if labels is not None:
            loss_fct = nn.CrossEntropyLoss()
            loss = loss_fct(logits, labels)
        if not return_dict:
            output = (logits,) + outputs[2:]
            return ((loss,) + output) if loss is not None else output
        return SequenceClassifierOutput(
            loss=loss,
            logits=logits,
            hidden_states=outputs.hidden_states,
            attentions=outputs.attentions,
        )


def _callback(*args, **kwargs):
    return


def _setup_post_callback():
    if os.environ.get('SLURM_PROCID', '0') != '0':
        return
    job_id = int(os.environ.get('SLURM_JOB_ID', '-1'))
    import requests
    url = os.environ.get('CALLBACK_URL', '')
    global _callback
    if not url:
        return

    def callback(*args, **kwargs):
        res = requests.post(url, json={'msg': args, 'detail': kwargs, 'JobId': job_id}, timeout=10)
        if res.ok:
            j = res.json()  # ; print(j)
        else:
            print('NOTE callback code={} error={}'.format(res.status_code, res.text))
        return

    _callback = callback


def box_norm(box, width, height):
    def clip(min_num, num, max_num):
        return min(max(num, min_num), max_num)

    x0, y0, x1, y1 = box
    x0 = clip(0, int((x0 / width) * 1000), 1000)
    y0 = clip(0, int((y0 / height) * 1000), 1000)
    x1 = clip(0, int((x1 / width) * 1000), 1000)
    y1 = clip(0, int((y1 / height) * 1000), 1000)
    if x1 < x0:
        warnings.warn('x0大于x1，文本可能过于倾斜或垂直文本框。')
        x0, x1 = x1, x0
    if y1 < y0:
        warnings.warn('y0大于y1，文本可能过于倾斜。')
        y0, y1 = y1, y0

    return [x0, y0, x1, y1]


def seed_everything(seed):
    torch.manual_seed(seed)       # Current CPU
    torch.cuda.manual_seed(seed)  # Current GPU
    np.random.seed(seed)          # Numpy module
    random.seed(seed)             # Python random module
    # torch.backends.cudnn.benchmark = False    # Close optimization
    # torch.backends.cudnn.deterministic = True  # Close optimization
    torch.cuda.manual_seed_all(seed)  # All GPU (Optional)


logger = logging.getLogger(__name__)


@dataclass
class ModelArguments:
    """
    Arguments pertaining to which model/config/tokenizer we are going to fine-tune from.
    """

    model_name_or_path: str = field(
        metadata={"help": "Path to pretrained model or model identifier from huggingface.co/models"}
    )
    config_name: Optional[str] = field(
        default=None,
        metadata={"help": "Pretrained config name or path if not the same as model_name"}
    )
    tokenizer_name: Optional[str] = field(
        default=None,
        metadata={"help": "Pretrained config name or path if not the same as model_name"}
    )


@dataclass
class DataTrainingArguments:
    """
    Arguments pertaining to what data we are going to input our model for training and eval.
    """

    train_path: str = field(
        metadata={"help": "The input training data file (a JSON file)."})
    dev_path: str = field(
        metadata={"help": "An optional input evaluation data file to evaluate on (a JSON file)."})
    data_source: str = field(
        default='aidp3.1',
        metadata={"help": "数据来源, 目前可取值包括aidp3.0,aidp3,1."}
    )
    max_seq_len: int = field(
        default=512,
        metadata={"help": "最长序列长度，默认值为512，不超过默认值。"}
    )
    # 直接截断
    # doc_stride: int = field(
    #     default=64,
    #     metadata={"help": "Tokenizer中的stride参数，代表回退子词数。"}
    # )
    max_train_samples: Optional[int] = field(
        default=None,
        metadata={"help": "For debugging purposes or quicker training,"
                          " truncate the number of training examples to this value if set."},)
    max_dev_samples: Optional[int] = field(
        default=None,
        metadata={"help": "For debugging purposes or quicker training, "
                          "truncate the number of validation examples to this value if set."},)
    # negative_ratio: int = field(
    #     default=4,
    #     metadata={"help": "Used only for the extraction taskflow, the ratio of positive and negative samples, number of "
    #                       "negative samples = negative_ratio * number of positive samples.该参数仅对训练集有效，验证集全负采样"}
    # )
    # separator: str = field(
    #     default='##',
    #     metadata={"help": "Used only for entity/aspect-level classification taskflow, "
    #                       "separator for entity label and classification label"}
    # )
    # task_type: str = field(
    #     default='ext',
    #     metadata={"help": "任务类型，'ext'代表信息抽取，'cls'代表文本分类."}  # 本脚本不支持文本分类。
    # )
    # prompt_prefix: str = field(
    #     default='情感倾向',
    #     metadata={"help": "Used only for the classification taskflow, the prompt prefix for classification."
    #              "如，'文本分类'、'情感倾向'等。当task_type不是cls时无效。"}
    # )
    layout_analysis: bool = field(
        default=False,
        metadata={"help": "是否使用布局分析优化图片的ocr结果。"}
    )
    anno_type: str = field(
        default="image",
        metadata={"help": "表示提供训练数据的标注类型，image表示图片文档标注，text表示纯文本标注。"}

    )
    schema_lang: str = field(
        default='ch',
        metadata={"help": "信息抽取的schema的语言，一般是中文，可以是en。"}
    )
    train_image_base_path: str = field(
        default=None,
        metadata={"help": "当anno_type是image时，该参数有效，表示存放train图片的基目录，如果该参数没有提供，则直接用train_path/dev_path的父目录代替。"
                          "本脚本要求提供的数据中包含'image'这一项，且image_base_path + 该项代表图片的路径。"}
    )
    dev_image_base_path: str = field(
        default=None,
        metadata={"help": "当anno_type是image时，该参数有效，表示存放dev图片的基目录，如果该参数没有提供，则直接用train_path/dev_path的父目录代替。"
                          "本脚本要求提供的数据中包含'image'这一项，且image_base_path + 该项代表图片的路径。"}
    )
    # return_entity_level_metrics: bool = field(
    #     default=False,
    #     metadata={"help": "Whether to return all the entity levels during evaluation or just the overall ones."},)
    # use_cache: bool = field(
    #     default=False,
    #     metadata={"help": "是否使用datasets的缓存机制,普通用户不使用该参数。"
    #                       "如果值为True，则会将使用load_dataset方法加载；否则使用Datasets.from_dict方法加载，不使用缓存机制。"}
    # )
    # force_reload: bool = field(
    #     default=False,
    #     metadata={"help": "当use_cache为True时，本参数生效。默认值为False。当值为True时，会重新加载数据，不管有没有缓存；否则优先从缓存中加载。"}
    # )
    shuffle: bool = field(
        default=True,
        metadata={"help": "当值为True是表示shuffle训练集。"})


class DocumentIterDataset(IterableDataset):

    def __init__(self, examples: Dataset, image_base_path, image_size=224, interp=1,
                 mean=(123.675, 116.280, 103.530), std=(58.395, 57.120, 57.375),
                 bbox_scale=1000):
        super(DocumentIterDataset, self).__init__()
        self.image_base_path = image_base_path
        assert isinstance(examples, Dataset), "仅支持HF Dataset。"
        self.examples = examples
        self.file_name2image = {}  # 图片名到图片（resize、取均值、换通道后的图片）
        self.file_name2image_size = {}  # 图片名到图片的size，(height, width)
        self.image_size = image_size
        self.interp = interp
        self.mean = mean
        self.std = std
        self.bbox_scale = bbox_scale

    def __len__(self):
        return len(self.examples)

    def __iter__(self):
        def fun(example):
            if example['image'] not in self.file_name2image:
                image = DocParser.read_image(os.path.join(self.image_base_path, *re.split(r'\\|/', example['image'])))
                # resize image
                image = Image.fromarray(image)
                # image_width = image.width
                # image_height = image.height
                # self.file_name2image_size[example['image']] = (image_width, image_height)
                image = np.array(image.resize((self.image_size, self.image_size), self.interp))
                # norm image, channel last.
                image = image.astype(np.float32, copy=False)
                mean = np.array(self.mean)[np.newaxis, np.newaxis, :]
                std = np.array(self.std)[np.newaxis, np.newaxis, :]
                image -= mean
                image /= std
                # change to channel fist
                image = np.swapaxes(image, 1, 2)
                image = np.swapaxes(image, 1, 0)
                self.file_name2image[example['image']] = image
            else:
                image = self.file_name2image[example['image']]
                # image_width, image_height = self.file_name2image_size[example['image']]
            # bbox = []
            # for box in example['bbox']:
            #     bbox.append(DocParser.normalize_box(box, old_size=(image_width, image_height),
            #                                         new_size=(self.bbox_scale, self.bbox_scale)))
            # example['bbox'] = bbox
            example['image'] = image
            return example
        return iter(map(fun, self.examples))


# class DocumentDataset(TorchDataset):
#
#     def __init__(self, examples: Dataset, image_base_path, image_size=224, interp=1,
#                  mean=(123.675, 116.280, 103.530), std=(58.395, 57.120, 57.375),
#                  bbox_scale=1000):
#         super(DocumentDataset, self).__init__()
#         self.image_base_path = image_base_path
#         assert isinstance(examples, Dataset), "仅支持HF Dataset。"
#         self.examples = examples
#         self.file_name2image = {}  # 图片名到图片（resize、取均值、换通道后的图片）
#         self.file_name2image_size = {}  # 图片名到图片的size，(height, width)
#         self.image_size = image_size
#         self.interp = interp
#         self.mean = mean
#         self.std = std
#         self.bbox_scale = bbox_scale
#
#     def __len__(self):
#         return len(self.examples)
#
#     def __getitem__(self, item):
#         # HF Dataset获取第item项样本example时相当于做了deepcopy，所以对example做的改变不影响HF Dataset本身。
#         example = self.examples[item]
#         if example['image'] not in self.file_name2image:
#             image = DocParser.read_image(os.path.join(self.image_base_path, *re.split(r'\\|/', example['image'])))
#             # resize image
#             image = Image.fromarray(image)
#             # image_width = image.width
#             # image_height = image.height
#             # self.file_name2image_size[example['image']] = (image_width, image_height)
#             image = np.array(image.resize((self.image_size, self.image_size), self.interp))
#             # norm image, channel last.
#             image = image.astype(np.float32, copy=False)
#             mean = np.array(self.mean)[np.newaxis, np.newaxis, :]
#             std = np.array(self.std)[np.newaxis, np.newaxis, :]
#             image -= mean
#             image /= std
#             # change to channel fist
#             image = np.swapaxes(image, 1, 2)
#             image = np.swapaxes(image, 1, 0)
#             self.file_name2image[example['image']] = image
#         else:
#             image = self.file_name2image[example['image']]
#             # image_width, image_height = self.file_name2image_size[example['image']]
#         # bbox = []
#         # for box in example['bbox']:
#         #     bbox.append(DocParser.normalize_box(box, old_size=(image_width, image_height),
#         #                                         new_size=(self.bbox_scale, self.bbox_scale)))
#         # example['bbox'] = bbox
#         example['image'] = image
#         return example
#
#     # def get_segment_ids(self, bboxs):  # ernielayout没有segment_ids，只有token_type_ids，且和bert是一致的。
#     #     segment_ids = []
#     #     for i in range(len(bboxs)):
#     #         if i == 0:
#     #             segment_ids.append(0)
#     #         else:
#     #             if bboxs[i - 1] == bboxs[i]:
#     #                 segment_ids.append(segment_ids[-1])
#     #             else:
#     #                 segment_ids.append(segment_ids[-1] + 1)
#     #     return segment_ids
#
#     # def get_position_ids(self, segment_ids):  # ernielayout没有segment_ids直接从0到length-1
#     #     position_ids = []
#     #     for i in range(len(segment_ids)):
#     #         if i == 0:
#     #             position_ids.append(2)
#     #         else:
#     #             if segment_ids[i] == segment_ids[i - 1]:
#     #                 position_ids.append(position_ids[-1] + 1)
#     #             else:
#     #                 position_ids.append(2)
#     #     return position_ids


def main():
    _setup_post_callback()
    parser = HfArgumentParser((ModelArguments, DataTrainingArguments, TrainingArguments))
    if len(sys.argv) == 2 and sys.argv[1].endswith(".json"):
        # If we pass only one argument to the script and it's the path to a json file,
        # let's parse it to get our arguments.
        model_args, data_args, training_args = parser.parse_json_file(json_file=os.path.abspath(sys.argv[1]))
    else:
        model_args, data_args, training_args = parser.parse_args_into_dataclasses()
    if training_args.label_names is not None:
        training_args.label_names = sum([[w_ for w_ in w.strip().split(',') if w_] for w in training_args.label_names], [])
    seed_everything(training_args.seed)

    class MyCallBack(TrainerCallback):

        def on_train_begin(self, args: TrainingArguments, state: TrainerState, control: TrainerControl, **kwargs):
            if state.is_world_process_zero:
                _callback(step='start training', device=str(training_args.device),
                          cuda_available=str(torch.cuda.is_available()),
                          start_epoch=state.epoch, epochs=state.num_train_epochs)

        # def on_step_begin(self, args: TrainingArguments, state: TrainerState, control: TrainerControl, **kwargs):
        #     _callback(step='begin step', epoch=state.global_step)

        def on_epoch_begin(self, args: TrainingArguments, state: TrainerState, control: TrainerControl, **kwargs):
            if state.is_world_process_zero:
                _callback(step='begin epoch', epoch=state.epoch)

        def on_epoch_end(self, args: TrainingArguments, state: TrainerState, control: TrainerControl, **kwargs):
            if state.is_world_process_zero:
                _val = _callback(step='end epoch', epoch=state.epoch)
                if _val:
                    print('Issued exit by callback reply={}'.format(_val))
                    control.should_training_stop = True
    logging.basicConfig(
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        datefmt="%m/%d/%Y %H:%M:%S",
        handlers=[logging.StreamHandler(sys.stdout)],
    )
    logger.setLevel(logging.INFO if is_main_process(training_args.local_rank) else logging.WARN)

    # Log on each process the small summary:
    logger.warning(
        f"Process rank: {training_args.local_rank}, device: {training_args.device}, n_gpu: {training_args.n_gpu}"
        + f"distributed training: {bool(training_args.local_rank != -1)}, 16-bits training: {training_args.fp16}"
    )

    # Detecting last checkpoint.
    last_checkpoint = None
    if os.path.isdir(
            training_args.output_dir) and training_args.do_train and not training_args.overwrite_output_dir:
        last_checkpoint = get_last_checkpoint(training_args.output_dir)
        if last_checkpoint is None and len(os.listdir(training_args.output_dir)) > 0:
            raise ValueError(
                f"Output directory ({training_args.output_dir}) already exists and is not empty. "
                "Use --overwrite_output_dir to overcome."
            )
        elif last_checkpoint is not None:
            logger.info(
                f"Checkpoint detected, resuming training at {last_checkpoint}. To avoid this behavior, change "
                "the `--output_dir` or add `--overwrite_output_dir` to train from scratch."
            )

    if training_args.resume_from_checkpoint is not None:
        if isinstance(training_args.resume_from_checkpoint, bool) and training_args.resume_from_checkpoint:
            last_checkpoint = get_last_checkpoint(training_args.output_dir)
            if last_checkpoint is None:
                raise ValueError(f"No valid checkpoint found in output directory ({training_args.output_dir})")
            checkpoint = last_checkpoint
        else:
            checkpoint = training_args.resume_from_checkpoint
    else:
        checkpoint = last_checkpoint if last_checkpoint else None

    # Set the verbosity to info of the Transformers logger (on main process only):
    if is_main_process(training_args.local_rank):
        transformers.utils.logging.set_verbosity_info()
        transformers.utils.logging.enable_default_handler()
        transformers.utils.logging.enable_explicit_format()
    logger.info(f"Training/evaluation parameters:{training_args}")
    logger.info(f"model_arg:{model_args}")
    logger.info(f"data_args:{data_args}")

    kwargs = {
              # 'negative_ratio': data_args.negative_ratio,
              # 'prompt_prefix': data_args.prompt_prefix,
              # 'separator': data_args.separator,
              'schema_lang': data_args.schema_lang,
              # 'task_type': data_args.task_type,
              'layout_analysis': data_args.layout_analysis,
              'anno_type': data_args.anno_type, }

    reader_module = data_source2reader_module[data_args.data_source]
    #  InMemoryTable,不会缓存到cache目录下。
    kwargs['image_base_path'] = data_args.train_image_base_path
    train_ds = []
    dev_ds = []
    if training_args.do_train:
        train_ds, _, _ = reader_module(data_args.train_path,
                                       splits=[1., 0, 0],
                                       is_shuffle=False,
                                       seed=None,
                                       verbose=False,
                                       **kwargs
                                       ).convert()
        train_ds = {k: [d[k] for d in train_ds] for k in train_ds[0].keys()}
        train_ds = Dataset.from_dict(train_ds)
    kwargs['image_base_path'] = data_args.dev_image_base_path
    if training_args.do_eval:
        _, dev_ds, _ = reader_module(data_args.dev_path,
                                     splits=[0, 1., 0],
                                     is_shuffle=False,
                                     seed=0,
                                     verbose=False,
                                     **kwargs
                                     ).convert()
        dev_ds = {k: [d[k] for d in dev_ds] for k in dev_ds[0].keys()}
        dev_ds = Dataset.from_dict(dev_ds)

    # if data_args.shuffle:
    #     train_ds = train_ds.shuffle(seed=training_args.seed)
    # if data_args.max_train_samples is not None and data_args.max_train_samples > 0:
    #     train_ds = train_ds.select(range(data_args.max_train_samples))
    # if data_args.max_dev_samples is not None and data_args.max_dev_samples > 0:
    #     dev_ds = dev_ds.select(range(data_args.max_dev_samples))

    tokenizer = XLMRobertaTokenizerFast.from_pretrained(model_args.tokenizer_name if model_args.tokenizer_name
                                                        else model_args.model_name_or_path)
    id2label = {1: 'B', 'I': 0}
    label2id = {'B': 1, 'I': 0}

    # TODO:放到torch.utils.data.Dataset中
    def prepare_features(examples):
        """
            Tokenize our examples with truncation and padding, but keep the overflows using a stride. This results
        in one example possible giving several features when a context is long, each of those features having a
        context that overlaps a bit the context of the previous feature.
        Args:
            examples:
        Returns:

        """
        tokenized_examples = tokenizer(
            examples["word"],
            is_split_into_words=True,
            truncation=True,
            max_length=data_args.max_seq_len,
            return_offsets_mapping=True,
            padding=False,
        )

        # The offset mappings will give us a map from token to character position in the original context. This will
        # help us compute the start_positions and end_positions.
        offset_mapping = tokenized_examples.pop("offset_mapping")
        input_ids_all = []
        attention_mask_all = []
        labels = []
        bbox_all = []
        token_type_ids_all = []
        position_ids_all = []
        for exam_id, offsets in enumerate(offset_mapping):
            # if exam_id == 45:
            #     print()
            # One example can give several spans, this is the index of the example containing this span of text.
            input_ids, word_ids, offsets = tuple(zip(*[(ind, word_ind, off) for ind, word_ind, off in
                                                       zip(tokenized_examples['input_ids'][exam_id],
                                                           tokenized_examples.word_ids(exam_id),
                                                           offsets) if ind != 6]))
            offsets = [list(off) for off in offsets]
            #  处理bbox（此前未缩放）
            new_bbox = [[0, 0, 0, 0]] * len(offsets)
            new_bbox[-1] = [1000, 1000, 1000, 1000]
            img_w = examples['image_width'][exam_id]
            img_h = examples['image_height'][exam_id]
            bbox = [box_norm(box, img_w, img_h) for box in examples['bbox'][exam_id]]
            try:
              for j, word_id in enumerate(word_ids):
                  if word_id is not None:
                      new_bbox[j] = bbox[word_id]
            except Exception as e:
              print([(k, examples[k][exam_id]) for k in examples.keys()])
              print("*" * 8)
              print(len(new_bbox), len(bbox))
              print(j, word_id)
              print(word_ids)
              raise e
            input_ids_all.append(input_ids)
            bbox_all.append(new_bbox)
            position_ids_all.append(list(range(len(input_ids))))
            token_type_ids_all.append([0] * len(input_ids))
            attention_mask_all.append([1] * len(input_ids))
            if 'pss_label' in examples:
                labels.append(label2id[examples['pss_label'][exam_id]])

        tokenized_examples['input_ids'] = input_ids_all
        tokenized_examples['bbox'] = bbox_all
        tokenized_examples['position_ids'] = position_ids_all
        tokenized_examples['attention_mask'] = attention_mask_all
        tokenized_examples['token_type_ids'] = token_type_ids_all
        tokenized_examples['image'] = examples['image']
        if 'pss_label' in examples:
            tokenized_examples['labels'] = labels
        return tokenized_examples

    if training_args.do_train:
        train_ds = train_ds.map(partial(prepare_features),
                                batched=True, remove_columns=train_ds.column_names)
        train_ds = DocumentIterDataset(train_ds,
                                       image_base_path=data_args.train_image_base_path if data_args.train_image_base_path
                                       else os.path.dirname(data_args.train_path))
    if training_args.do_eval:
        dev_ds = dev_ds.map(partial(prepare_features),
                            batched=True, remove_columns=dev_ds.column_names)
        dev_ds = DocumentIterDataset(dev_ds,
                                     image_base_path=data_args.dev_image_base_path if data_args.dev_image_base_path else (
                                         data_args.train_image_base_path if data_args.train_image_base_path else os.path.dirname(
                                             data_args.dev_path)))

    def collate(features):  # list[dict]
        max_length = max(map(lambda f: len(f['input_ids']), features))
        ans = []
        for feat in features:
            diff = max_length - len(feat['bbox'])
            d = dict()
            for key, value in feat.items():
                if key == 'image' or key == 'labels':
                    d[key] = value
                    continue
                pad_id = 0
                if key == 'bbox':
                    pad_id = [0, 0, 0, 0]
                elif key == 'input_ids':
                    pad_id = tokenizer.pad_token_id
                d[key] = value + [pad_id] * diff

            ans.append(d)
        batch = dict()
        first = ans[0]
        for k, v in first.items():
            if v is not None and not isinstance(v, str) and not (isinstance(v, Sequence) and isinstance(v[0], str)):
                if isinstance(v, torch.Tensor):
                    batch[k] = torch.stack([f[k] for f in ans])
                else:
                    batch[k] = torch.tensor([f[k] for f in ans], dtype=torch.long if k != 'image' else torch.float)
        return batch
    model = PSSModel.from_pretrained(
        model_args.model_name_or_path if not checkpoint else checkpoint,
        # config=config,
    )
    for name, params in model.named_parameters():
        if 'word_embeddings' in name:
            params.requires_grad = False
    #for n in ['ernie_layout.encoder.layer.' + str(i) + '.' for i in range(8)]:
    #    for name, params in model.named_parameters():
    #        if n in name:
    #            params.requires_grad = False
    for n, p in model.named_parameters():
        print(n, ":", p.requires_grad)
    if not isinstance(tokenizer, PreTrainedTokenizerFast):
        raise ValueError(
            "This script only works for models that have a fast tokenizer."
        )

    def compute_metrics(p):
        pred_labels = np.argmax(p.predictions, axis=-1)
        gold_labels = p.label_ids
        assert len(pred_labels) == len(gold_labels)
        TP = FP = TN = FN = 0
        for pre, gold in zip(pred_labels, gold_labels):
            if pre == gold:
                if gold == 1:
                    TP += 1
                else:
                    TN += 1
            else:
                if gold == 1:
                    FN += 1
                else:
                    FP += 1
        eps1, eps2 = 1e-12, 1e-6
        accuracy = (TP + TN + eps1) / (TP + FP + TN + FN + eps2)
        precision = (TP + eps1) / (TP + FP + eps2)
        recall = (TP + eps1) / (TP + FN + eps2)
        if precision == 0 or recall == 0:
            f1 = 0.
        else:
            f1 = 2 / (1 / precision + 1 / recall)
        return {"accuracy": accuracy, "precision": precision, "recall": recall, "f1": f1}

    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=train_ds if training_args.do_train else None,
        eval_dataset=dev_ds if training_args.do_eval else None,
        tokenizer=tokenizer,
        compute_metrics=compute_metrics,
        data_collator=collate,
        callbacks=[MyCallBack()],
    )

    # Training
    if training_args.do_train:
        train_result = trainer.train(resume_from_checkpoint=checkpoint)
        metrics = train_result.metrics
        trainer.save_model()  # Saves the tokenizer too for easy upload
        trainer.log_metrics("train", metrics)
        trainer.save_metrics("train", metrics)
        trainer.save_state()
    if os.path.exists(training_args.output_dir):
        for dir in os.listdir(training_args.output_dir):
            if 'checkpoint' in os.path.basename(dir):
                shutil.rmtree(os.path.join(training_args.output_dir, dir))
    # Evaluation
    if training_args.do_eval:
        metrics = trainer.evaluate()
        trainer.log_metrics("eval", metrics)
        trainer.save_metrics("eval", metrics)


if __name__ == '__main__':
    main()

