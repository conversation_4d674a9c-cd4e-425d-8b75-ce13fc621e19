import math
from typing import Dict
from functools import partial
import torch
from torch import nn
import torch.nn.functional as F
from torch.utils.data.dataloader import DataLoader, Dataset
from torch.nn.utils.rnn import pad_sequence

from qa.kbnlp.mlt.util import merge_locals_kwargs, merge_dict
from qa.kbnlp.mlt.task import Task
from qa.kbnlp.mlt.data_transform import VocabDict
from qa.kbnlp.mlt.layers.decoder.linear_decoder import LinearCRFDecoder
from qa.kbnlp.mlt.common.vocab import Vocab
from qa.kbnlp.mlt.util.io import CoNLLReader
from qa.kbnlp.mlt.data_transform import TransformList
from qa.kbnlp.mlt.task.standard import Configure, StaticOracle
from qa.kbnlp.mlt.dataset import BaseDataset, SortingSamplerBuilder
from qa.kbnlp.mlt.common.metric import DependencyMetric
from qa.kbnlp.mlt.util.tensor_util import lengths_to_mask
from qa.kbnlp.mlt.util import ids_to_tags


def read_CoNLL_data(path):
    return CoNLLReader(path)


class DepForwardLayer(nn.Module):
    def __init__(self, encoder_size, hidden_size, num_features=4, drop_input=0.1, drop_hidden=0.15):
        super(DepForwardLayer, self).__init__()
        self.num_features = num_features  # 每个Configuration提取的特征数。
        self.hidden_size = hidden_size
        self.s3s2b1 = nn.Parameter(torch.Tensor(3, encoder_size))
        self.drop_encoder = nn.Dropout(p=drop_input)
        self.hidden_layer = nn.Linear(in_features=encoder_size, out_features=hidden_size * num_features)
        self.drop_hidden = nn.Dropout(p=drop_hidden)
        self.activation = nn.ReLU()
        nn.init.normal_(self.s3s2b1, mean=0., std=1.)

    def forward(self, embed):
        batch_size = embed.size(0)
        s3s2s1 = (self.s3s2b1[[0, 1, 2] * batch_size]).view(batch_size, 3, -1)
        embed = torch.cat([s3s2s1, embed], dim=1)  # 91 256
        n = embed.size(1)
        return self.activation(self.drop_hidden(self.hidden_layer(self.drop_encoder(embed)))).view(batch_size, n,  # 91,4,256
                                                                                                   self.num_features,
                                                                                                   self.hidden_size)


class DepOutLayer(nn.Module):
    def __init__(self, hidden_size, num_labels):
        super(DepOutLayer, self).__init__()
        self.out = torch.nn.Linear(hidden_size, num_labels)
        self.num_labels = num_labels  # 带方向的依存关系，注意有的依存关系只有左方向，有的只有右方向，root关系只有左方向（因为把root放在了句子末尾）。

    def forward(self, transition_hidden_state, transition_feature):
        batch_size, transition_step, num_feature = tuple(transition_feature.size())  # num_feature表示每个transition时刻选择的特征个数，如，选取s3s2s1b1，则为4。
        i_loc = torch.arange(batch_size).view(batch_size, 1, 1)
        transition_hidden_state = transition_hidden_state[i_loc, transition_feature, torch.arange(num_feature)].sum(dim=-2)  # 174 256
        score = self.out(transition_hidden_state)
        return score


class DepModel(nn.Module):
    def __init__(self, encoder_size, hidden_size, num_features, num_labels, drop_input=0.1, drop_hidden=0.15, offset=None):
        super(DepModel, self).__init__()
        self.dep_forward_layer = DepForwardLayer(encoder_size, hidden_size, num_features, drop_input, drop_hidden)
        self.out_layer = DepOutLayer(hidden_size, num_labels)
        self.offset = nn.Parameter(offset, requires_grad=False)  # 8 * relation size(无方向). 对应S,L,R可行或者不可行的8种情况。

    def forward(self, embed, transition_feature):
        transition_hidden_state = self.dep_forward_layer(embed)
        score = self.out_layer(transition_hidden_state, transition_feature)
        return score


SLR = ['S', 'L', 'R']


def transition_transform(sample):
    gold_config = Configure(sentence_length=sample['token_len'] - 2)
    head, relation = [-1] + sample['head'], [-1] + sample['relation']
    transition_tag = []
    transition_feature = []
    relation_directed = [-1] * len(relation)
    while not gold_config.is_final_state():
        feature = extract_position_feature(gold_config)
        transition_feature.append(feature)
        try:
            _action, rel = StaticOracle.getGoldAction(head, relation, gold_config)
            transition_tag.append(SLR[_action] + rel if rel is not None else SLR[_action])  # shift没有rel
        except IndexError as e:
            print('该样本转换过程出错，请检查。')
            print(head)

            print(e)
            break
        if _action == 1:
            relation_directed[gold_config.s2] = transition_tag[-1]
        elif _action == 2:
            relation_directed[gold_config.s1] = transition_tag[-1]
        gold_config.commit(_action, rel)
    sample['relation'] = relation_directed[1:]
    sample['transition_tag'] = transition_tag
    sample['transition_feature'] = transition_feature
    return sample


def extract_position_feature(config):
    '''
    special_words = ['<s2-w>', '<s3-w>', '<b1-w>', '<b2-w>', '<b3-w>',
                     '<s1-lc1-w>', '<s1-lc2-w>', '<s1-rc1-w>', '<s1-rc2-w>', '<s2-lc1-w>', '<s2-lc2-w>', '<s2-rc1-w>', '<s2-rc2-w>',
                     '<s1-lc1-lc1-w>', '<s1-rc1-rc1-w>', '<s2-lc1-lc1-w>', '<s2-rc1-rc1-w>']

    special_tags = ['<s2-t>', '<s3-t>', '<b1-t>', '<b2-t>', '<b3-t>',
                    '<s1-lc1-t>', '<s1-lc2-t>', '<s1-rc1-t>', '<s1-rc2-t>', '<s2-lc1-t>', '<s2-lc2-t>', '<s2-rc1-t>', '<s2-rc2-t>',
                    '<s1-lc1-lc1-t>', '<s1-rc1-rc1-t>', '<s2-lc1-lc1-t>', '<s2-rc1-rc1-t>']

    special_labels = ['<s1-lc1-l>', '<s1-lc2-l>', '<s1-rc1-l>', '<s1-rc2-l>', '<s2-lc1-l>', '<s2-lc2-l>', '<s2-rc1-l>', '<s2-rc2-l>',
                    '<s1-lc1-lc1-l>', '<s1-rc1-rc1-l>', '<s2-lc1-lc1-l>', '<s2-rc1-rc1-l>']
    '''
    # transition_word_features = []
    # # 0, 1, 2代表s3，s2，b1没有情况的特殊token id。
    # transition_word_features.extend((i for i in range(3 - len(config.stack[-3:]))))
    # for ind in config.stack[-3:]:
    #     transition_word_features.append(ind+2)
    # if config.buffer_size() > 0:
    #     transition_word_features.append(config.b1 + 2)
    # else:
    #     transition_word_features.append(2)

    transition_word_features = []
    # 0, 1, 2代表s3，s2，b1没有情况的特殊token id。 3代表root，使用[CLS]的hidden来表示。
    transition_word_features.extend((i for i in range(3 - len(config.stack[-3:]))))
    for ind in config.stack[-3:]:
        transition_word_features.append(ind + 3)
    if config.buffer_size() > 0:
        transition_word_features.append(config.b1 + 3)
    else:
        transition_word_features.append(2)
    if config.buffer_size() == 0:
        transition_word_features[-2] = 0
    elif config.buffer_size() == 1:
        transition_word_features[-1] = 0
    return transition_word_features


class DepDataset(BaseDataset):
    def __init__(self, data, transform_list: TransformList = None):
        super(DepDataset, self).__init__(data, transform_list)


class DepStandard(Task):
    def __init__(self,
                 trn=None,
                 dev=None,
                 tst=None,
                 hidden_size=256,
                 num_features=4,
                 drop_input=0.1,
                 drop_hidden=0.15,
                 batch_size=None,
                 batch_max_tokens=None,
                 eval_batch_size=None,
                 eval_batch_max_tokens=None,
                 # dependencies: str = None,
                 lr=None,
                 separate_optimizer=False,
                 cls_is_bos=False,
                 sep_is_eos=False,
                 **kwargs,
                 ):
        super(DepStandard, self).__init__(
            **merge_locals_kwargs(locals(), kwargs, excludes=['self', 'kwargs', '__class__']))
        self.hidden_size = hidden_size
        self.num_features = num_features
        self.drop_input = drop_input
        self.drop_hidden = drop_hidden
        self.vocabs = VocabDict()  # 至少需要一个tag的vocab

    def build_model(self, encoder_size, task_name, **kwargs):
        num_labels = len(self.vocabs['%s_tag' % task_name])
        token_to_idx = list(self.vocabs.values())[0].token_to_idx
        shift_idx, left_idx, right_idx = [], [], []
        for tok, idx in token_to_idx.items():
            if tok.startswith('S'):
                shift_idx.append(idx)
            elif tok.startswith('L'):
                left_idx.append(idx)
            elif tok.startswith('R'):
                right_idx.append(idx)
            else:
                raise ValueError("词表中的token必须以'SLR'中的一个开头。")
        # print(shift_idx, left_idx, right_idx, '\n', len(shift_idx), len(left_idx), len(right_idx))
        offset = torch.zeros(size=(8, num_labels), dtype=torch.float)
        for can_right in range(2):
            for can_left in range(2):
                for can_shift in range(2):
                    index = can_shift + (can_left << 1) + (can_right << 2)
                    offset[index][shift_idx * (not can_shift) +
                                  left_idx * (not can_left) +
                                  right_idx * (not can_right)] = float('-inf')
        return DepModel(encoder_size, self.hidden_size, self.num_features, num_labels, drop_input=self.drop_input,
                        drop_hidden=self.drop_hidden, offset=offset)

    def build_sample(self, data):
        examples = []
        for i, d in enumerate(data):
            ws, hs, ls = None, None, None
            if len(d) == 4:
                ws, hs, _, ls = d
            else:
                ws = d
            examples.append({'original_input': ws})
            examples[-1]['_id'] = i
            if isinstance(ws, list):
                examples[-1]['token'] = ws
                if hs and ls:
                    examples[-1]['head'] = hs
                    examples[-1]['relation'] = ls
        return examples

    def build_transformList(self, mode, task_name, **kwargs):
        if mode == 'train' or mode == 'eval':
            def rename(sample, src_name, tar_name):
                sample[tar_name] = sample.pop(src_name)
                return sample
            rename_transform = partial(rename, src_name='transition_tag', tar_name='%s_tag' % task_name)

            return TransformList(transition_transform, rename_transform, self.vocabs)
        elif mode == 'predict':
            return TransformList()
        else:
            raise ValueError("mode必须在'train', 'eval', 'predict' 中取值。")

    def build_dataset(self, data, transform_list, **kwargs):
        return DepDataset(data, transform_list)

    def build_dataloader(self, dataset, batch_size=None, batch_max_tokens=None, shuffle=False, generator=None, gradient_accumulation=1,
                         use_effective_tokens=False, **kwargs):
        return DataLoader(dataset=dataset,
                          batch_sampler=SortingSamplerBuilder.build([data['token_len'] * 2 + 1 for data in dataset],
                                                                    batch_size, batch_max_tokens, use_effective_tokens,
                                                                    shuffle, generator, gradient_accumulation),
                          collate_fn=partial(DepDataset.collate_fn, padding_value=0,
                                             extra_pad_keys=['transition_feature']))

    def build_criterion(self, reduction='mean', **kwargs):
        return torch.nn.CrossEntropyLoss(reduction=reduction)

    def build_optimizer(self, decoder, **kwargs):
        raise NotImplementedError('该任务没有实现独立的优化器。')

    def compute_loss(self, task_name, batch, out, mask, criterion):
        """

        :param y:
        :param out:
        :param mask: transition_step_mask
        :param criterion:
        :return:
        """
        y = batch['%s_tag_ids' % task_name]
        loss = criterion(out[mask], y[mask])
        return loss

    def build_metric(self):
        return DependencyMetric()

    def update_metric(self, prediction, batch, metric, task_name):
        max_len = batch['token_len'].max() - 2
        token_mask = torch.arange(max_len) < (batch['token_len'] - 2).unsqueeze(-1).to('cpu')
        vocab = self.vocabs['%s_tag' % task_name]
        metric(pad_sequence([torch.tensor(heads) for heads in prediction['head']], batch_first=True),
               pad_sequence([torch.tensor([vocab(r) for r in rel]) for rel in prediction['relation']], batch_first=True),
               pad_sequence([torch.tensor(heads) for heads in batch['head']], batch_first=True),
               pad_sequence([torch.tensor([vocab(r) for r in rel]) for rel in batch['relation']], batch_first=True),
               token_mask)

    def feed_batch(self, mode, h: torch.FloatTensor, batch: Dict, output_dict: Dict, decoder: torch.nn.Module, task_name):
        output_dict[task_name] = {}
        h = h[:, :-1, :]
        if mode == 'predict':
            output_dict[task_name]['transition_hidden_state'] = decoder.dep_forward_layer(h)
        elif mode == 'eval':
            transition_hidden_state = decoder.dep_forward_layer(h)
            output_dict[task_name]['transition_hidden_state'] = transition_hidden_state
            mask = lengths_to_mask((batch['token_len'] - 2) * 2)  # transition_step_mask,transition_step_len = token_len * 2 + 1
            logits = decoder.out_layer(transition_hidden_state, batch['transition_feature'])
            output_dict[task_name]['output'] = logits
            output_dict[task_name]['mask'] = mask
        elif mode == 'train':
            mask = lengths_to_mask((batch['token_len'] - 2) * 2)  # transition_step_mask,transition_step_len = token_len * 2 + 1
            logits = decoder(h, transition_feature=batch['transition_feature'])
            output_dict[task_name]['output'] = logits
            output_dict[task_name]['mask'] = mask
        else:
            raise ValueError("mode必须在'train', 'eval', 'predict' 中取值。")

    def decode_output(self, mode, output_dict, batch, model, task_name, **kwargs):
        output_dict_task = output_dict[task_name]
        if mode == 'predict':
            self.greedy_parse(output_dict, batch, model, task_name)
        elif mode == 'eval':
            output = output_dict_task['output']
            mask = output_dict_task['mask']
            predict_tag_ids = torch.argmax(output, dim=-1)
            predict_tags = ids_to_tags(tag_ids=predict_tag_ids, lens=torch.sum(mask, dim=-1),
                                       vocab=self.vocabs['%s_tag' % task_name])
            output_dict_task['prediction'] = {'transition_tag': predict_tags}
            self.greedy_parse(output_dict, batch, model, task_name)
        elif mode == 'train':
            output = output_dict_task['output']
            mask = output_dict_task['mask']
            predict_tag_ids = torch.argmax(output, dim=-1)
            predict_tags = ids_to_tags(tag_ids=predict_tag_ids, lens=torch.sum(mask, dim=-1),
                                       vocab=self.vocabs['%s_tag' % task_name])
            output_dict_task['prediction'] = {'transition_tag': predict_tags}
        else:
            raise ValueError("mode必须在'train', 'eval', 'predict' 中取值。")

    def greedy_parse(self, output_dict, batch, model, task_name):  # 要确保无空输入。
        output_dict_task = output_dict[task_name]
        token_len = batch['token_len']
        transition_hidden_state = output_dict_task['transition_hidden_state']
        indices, sorted_token_len = zip(*sorted(enumerate(token_len), key=lambda pair: pair[1], reverse=True))
        already_sorted = True
        for i, j in zip(range(len(batch['original_input'])), indices):
            if i != j:
                already_sorted = False
                break
        if not already_sorted:
            transition_hidden_state = transition_hidden_state[list(indices)]
        configs = [Configure(sentence_length=t_len - 2) for t_len in sorted_token_len]
        config_not_final = list(configs)
        while len(config_not_final) > 0:
            transition_feature = []
            can_do = []
            for conf in config_not_final:
                transition_feature.append(extract_position_feature(conf))
                # can_do_cur_step是一个属于range(8)的整数，代表是否可以做shift,left,right操作。
                can_do_cur_step = StaticOracle.isPossibleSHIFT(conf) + (StaticOracle.isPossibleLEFT(conf) << 1) + (
                            StaticOracle.isPossibleRIGHT(conf) << 2)
                can_do.append(can_do_cur_step)
            transition_feature = torch.tensor(transition_feature).unsqueeze(dim=1)
            score = model.out_layer(transition_hidden_state=transition_hidden_state,
                                    transition_feature=transition_feature)
            score += model.offset[torch.tensor(can_do)].unsqueeze(1)
            predicts = torch.argmax(score, dim=-1)
            remain = config_not_final
            config_not_final = []
            vocab = self.vocabs['%s_tag' % task_name]
            for conf, pre in zip(remain, predicts):
                tag = vocab.get_token(pre)
                action, rel = (0, None) if tag.startswith(SLR[0]) \
                    else (1, tag) if tag.startswith(SLR[1]) \
                    else (2, tag)
                try:
                    conf.commit(action, rel)  # 注意这里为了方便评估，没有将relation前面加的表示左右弧的标记'S','R'去掉。在方法prediction_to_result中去掉。
                except (TypeError, ValueError) as e:
                    print(can_do)
                    print(transition_feature)
                    print(model.offset[torch.tensor(can_do)].unsqueeze(1))
                    print(repr(conf.stack) + ' | ' + repr(list(conf.buffer)), '\n', conf.heads, '\n', conf.arcs)
                    raise e
                if not conf.is_final_state():
                    config_not_final.append(conf)
        head, relation = [], []
        for conf in configs:
            head.append(conf.heads[1:])  # 去掉之前额外填充的值。
            relation.append(conf.arcs[1:])
        if already_sorted:
            recover_indices, _ = zip(*sorted(enumerate(indices), key=lambda pair: pair[1]))
            head, relation = zip(*[(head[idx], relation[idx]) for idx in recover_indices])
        if 'prediction' not in output_dict_task:
            output_dict_task['prediction'] = {}
        output_dict_task['prediction']['head'] = head
        output_dict_task['prediction']['relation'] = relation

    def prediction_to_result(self, pred, vocab, batch, output_dict, task_name, **kwargs):
        return [{'head': h, 'relation': rel} for h, rel in
                zip(pred['head'], [[r[1:] for r in rel] for rel in pred['relation']])]
        # return {'head': pred['head'], 'relation': [[r[1:] for r in rel] for rel in pred['relation']]}

    def post_transform(self, task_name, batch, output_dict, device, **kwargs):
        output_dict.pop(task_name)


if __name__ == '__main__':
    data_train = read_CoNLL_data(path=r'C:\Users\<USER>\AppData\Roaming\hanlp\thirdparty\wakespace.lib.wfu.edu\bitstream\handle\10339\39379\LDC2013T21\data\tasks\dep/train.conllx')
    data_dev = read_CoNLL_data(path=r'C:\Users\<USER>\AppData\Roaming\hanlp\thirdparty\wakespace.lib.wfu.edu\bitstream\handle\10339\39379\LDC2013T21\data\tasks\dep/dev.conllx')
    task_name = 'dep'
    task = DepStandard(trn=data_train, dev=data_dev)
    task.build_vocab(task_name=task_name)
    sample = task.build_sample(task.trn)
    from aimp.qa.kbnlp.mlt.layers.encoder.transformer_encoder import ContextualWordEmbedding
    from aimp.qa.kbnlp.mlt.task.segment import NormalizeCharacter
    from aimp.qa.kbnlp.mlt.util.io import load_json
    HOME = 'C:/Users/<USER>/AppData/Roaming/hanlp'
    char_table = HOME + '/thirdparty/file.hankcs.com/corpus/char_table.json'
    transformer_path = 'hfl/robert'
    tokenizer_path = HOME + '/transformers/electra_zh_small_20210706_125427'
    encoder = ContextualWordEmbedding(transformer=transformer_path,
                                      tokenizer=tokenizer_path,
                                      average_subwords=True,
                                      word_dropout=0.1,
                                      max_sequence_length=510,
                                      input_key='original_input',
                                      extra_tranform=NormalizeCharacter(mapper=load_json(char_table),
                                                                        src='original_input'))
    transform_list = encoder.build_transformList()
    transform_list.append(task.build_transformList(predict=False, task_name=task_name))
    dataset = task.build_dataset(data=sample, transform_list=transform_list)
    loader = task.build_dataloader(dataset, batch_size=6)
    for batch in loader:
        print(batch)
    print('finish.')
