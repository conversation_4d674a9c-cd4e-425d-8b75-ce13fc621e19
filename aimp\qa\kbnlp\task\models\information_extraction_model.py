import collections
from dataclasses import dataclass
from collections import namedtuple
from typing import Op<PERSON>, Tu<PERSON>, Dict, List
import torch
from torch import nn
from transformers.modeling_outputs import QuestionAnsweringModelOutput
from transformers.utils import ModelOutput
from transformers.models.bert.modeling_bert import BertModel
from qa.kbnlp.pretrained.ernie.modeling_ernie import <PERSON><PERSON>reTrainModel, ErnieConfig, ErnieModel

SUBJECT_PREDICATE_CONJUNCT_WORD = "的"
EntityPrompt = namedtuple('EntityPrompt', ['label'])
TriplePrompt = namedtuple('TriplePrompt', ['subject_label', 'predicate'])


class UIE(ErniePreTrainModel):
    _keys_to_ignore_on_load_unexpected = [r"pooler"]

    def __init__(self, config: ErnieConfig):
        super().__init__(config)

        self.ernie = ErnieModel(config, add_pooling_layer=False)
        hidden_size = config.hidden_size
        self.linear_start = nn.Linear(hidden_size, 1)
        self.linear_end = nn.Linear(hidden_size, 1)
        self.sigmoid = nn.Sigmoid()

    @property
    def conjunct(self):
        return SUBJECT_PREDICATE_CONJUNCT_WORD

    def forward(
            self,
            input_ids=None,
            attention_mask=None,
            token_type_ids=None,
            position_ids=None,
            head_mask=None,
            inputs_embeds=None,
            start_positions=None,
            end_positions=None,
            output_attentions=None,
            output_hidden_states=None,
            output_start_end_logits=False,
            return_dict=None,
            **kwargs,
    ):
        r"""
        start_positions (:obj:`torch.LongTensor` of shape :obj:`(batch_size,)`, `optional`):
            Labels for position (index) of the start of the labelled span for computing the token classification loss.
            Positions are clamped to the length of the sequence (:obj:`sequence_length`). Position outside of the
            sequence are not taken into account for computing the loss.
        end_positions (:obj:`torch.LongTensor` of shape :obj:`(batch_size,)`, `optional`):
            Labels for position (index) of the end of the labelled span for computing the token classification loss.
            Positions are clamped to the length of the sequence (:obj:`sequence_length`). Position outside of the
            sequence are not taken into account for computing the loss.
        """
        return_dict = return_dict if return_dict is not None else self.config.use_return_dict

        outputs = self.ernie(
            input_ids,
            attention_mask=attention_mask,
            token_type_ids=token_type_ids,
            position_ids=position_ids,
            head_mask=head_mask,
            inputs_embeds=inputs_embeds,
            output_attentions=output_attentions,
            output_hidden_states=output_hidden_states,
            return_dict=return_dict,
        )

        sequence_output = outputs[0]

        start_logits = self.linear_start(sequence_output)
        start_logits = torch.squeeze(start_logits, -1)
        start_prob = self.sigmoid(start_logits)
        end_logits = self.linear_end(sequence_output)
        end_logits = torch.squeeze(end_logits, -1)
        end_prob = self.sigmoid(end_logits)

        total_loss = None
        if start_positions is not None and end_positions is not None:
            # If we are on multi-GPU, split add a dimension
            if len(start_positions.size()) > 1:
                start_positions = start_positions.squeeze(-1)
            if len(end_positions.size()) > 1:
                end_positions = end_positions.squeeze(-1)
            # sometimes the start/end positions are outside our pretrained inputs, we ignore these terms
            ignored_index = start_logits.size(1)
            start_positions = start_positions.clamp(0, ignored_index)
            end_positions = end_positions.clamp(0, ignored_index)

            loss_fct = nn.CrossEntropyLoss(ignore_index=ignored_index)
            start_loss = loss_fct(start_logits, start_positions)
            end_loss = loss_fct(end_logits, end_positions)
            total_loss = (start_loss + end_loss) / 2

        if not return_dict:
            output = (start_prob, end_prob) + (start_logits, end_logits) if output_start_end_logits else tuple()
            output += outputs[2:]
            return ((total_loss,) + output) if total_loss is not None else output

        return UIEModelOutput(
            loss=total_loss,
            start_prob=start_prob,
            end_prob=end_prob,
            start_logits=start_logits if output_start_end_logits else None,
            end_logits=end_logits if output_start_end_logits else None,
            hidden_states=outputs.hidden_states,
            attentions=outputs.attentions,
        )

    def collate(self, examples, tokenizer, max_seq_len):
        tokenizer_outputs = batch_encode(examples, tokenizer, max_seq_len)
        if 'result_list' in examples[0]:
            start_ids, end_ids = [], []
            length = tokenizer_outputs['input_ids'].size(1)

            for offset_mapping, exam in zip(tokenizer_outputs['offset_mapping'], examples):
                offset_mapping = [list(x) for x in offset_mapping]
                bias = 0
                for index in range(1, len(offset_mapping)):
                    mapping = offset_mapping[index]
                    if mapping[0] == 0 and mapping[1] == 0 and bias == 0:  # 找到中间的SEP
                        bias = offset_mapping[index - 1][1] + 1
                        continue
                    if mapping[0] == 0 and mapping[1] == 0 and bias != 0:  # 找到末尾的SEP
                        break
                    offset_mapping[index][0] += bias
                    offset_mapping[index][1] += bias
                start_ids_exam = [0] * length
                end_ids_exam = [0] * length
                for item in exam["result_list"]:
                    start = map_offset(item["start"] + bias, offset_mapping)
                    end = map_offset(item["end"] - 1 + bias, offset_mapping)
                    start_ids_exam[start] = 1.0
                    end_ids_exam[end] = 1.0
                start_ids.append(start_ids_exam)
                end_ids.append(end_ids_exam)
            tokenizer_outputs['start_ids'] = torch.tensor(start_ids, dtype=torch.float)
            tokenizer_outputs['end_ids'] = torch.tensor(end_ids, dtype=torch.float)
            tokenizer_outputs.pop('offset_mapping')
        return tokenizer_outputs

    def quantize(self, inplace=False):
        return torch.quantization.quantize_dynamic(self, {
            torch.nn.Linear: torch.quantization.per_channel_dynamic_qconfig,
            torch.nn.Embedding: torch.quantization.float_qparams_weight_only_qconfig, # nochange can made
        }, mapping={
            torch.nn.Linear: torch.nn.quantized.dynamic.Linear,
            torch.nn.Embedding: torch.nn.quantized.Embedding,
        }, inplace=inplace)

    def script(self):
        self.eval()
        device = self.linear_end.weight.device
        with torch.no_grad():
            b = {'input_ids': [[1, 160, 496, 2, 89, 1056, 29, 61, 239, 38, 469, 289, 10646, 4, 154, 79, 430, 221, 393, 12043, 2]],
                 'token_type_ids': [[0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]],
                 'attention_mask': [[1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]],
                 'position_ids': [[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20]]}
            b = dict((k, v.to(device) if isinstance(v, torch.Tensor) else torch.tensor(v, dtype=torch.int64, device=device)) for k, v in b.items())
            torch._C._jit_set_profiling_mode(False)
            inputs = [b['input_ids'], b['attention_mask'], b['token_type_ids'], b['position_ids']]
            return torch.jit.trace(self, inputs, strict=False)


class AutoPromptUIEConfig(ErnieConfig):

    def __init__(
            self,
            vocab_size=40000,
            hidden_size=768,
            num_hidden_layers=12,
            num_attention_heads=12,
            intermediate_size=3072,
            hidden_act="gelu",
            hidden_dropout_prob=0.1,
            attention_probs_dropout_prob=0.1,
            max_position_embeddings=512,
            type_vocab_size=4,
            initializer_range=0.02,
            layer_norm_eps=1e-12,  # baidu ernie官方默认参数值
            pad_token_id=0,
            gradient_checkpointing=False,
            position_embedding_type="absolute",
            use_cache=True,
            task_type_vocab_size=3,
            task_id=0,
            use_task_id=False,
            type_auto_prompt_size=1000,
            auto_prompt_len=3,
            auto_prompt_vocab: Optional[List] = None,
            conjunct='的',
            **kwargs):
        """
            为用户设置的提示词搜索得到比较好的提示词（可以是连续的向量，没有真正对应的单词）。
        Args:
            type_auto_prompt_size: 最多支持不同prompt的个数。如【人名，年龄，身高，……】等的个数。
            auto_prompt_len: 为每个prompt词设置自动提示词的大小。
            auto_prompt_vocab: 支持自动提示的单词列表。
            conjunct: 在设置关系抽取的提示词时，用该参数连接主实体.name和predicate。默认为的。
            **kwargs:
        """
        super().__init__(vocab_size=vocab_size,
                         hidden_size=hidden_size,
                         num_hidden_layers=num_hidden_layers,
                         num_attention_heads=num_attention_heads,
                         intermediate_size=intermediate_size,
                         hidden_act=hidden_act,
                         hidden_dropout_prob=hidden_dropout_prob,
                         attention_probs_dropout_prob=attention_probs_dropout_prob,
                         max_position_embeddings=max_position_embeddings,
                         type_vocab_size=type_vocab_size,
                         initializer_range=initializer_range,
                         layer_norm_eps=layer_norm_eps,
                         pad_token_id=pad_token_id,
                         gradient_checkpointing=gradient_checkpointing,
                         position_embedding_type=position_embedding_type,
                         use_cache=use_cache,
                         task_type_vocab_size=task_type_vocab_size,
                         task_id=task_id,
                         use_task_id=use_task_id,
                         **kwargs)

        self.type_auto_prompt_size = type_auto_prompt_size
        self.auto_prompt_len = auto_prompt_len
        self.conjunct = conjunct
        # 索引0表示不存在的提示词。
        self._auto_prompt_vocab = dict()  # 谓词前加conjunct
        self.entity_labels = []
        self.predicate2subject_labels = dict()  # 谓词前不加conjunct
        if 'entity_labels' in kwargs or 'predicate2subject_labels' in kwargs:
            self.entity_labels = kwargs.pop('entity_labels')
            self.predicate2subject_labels = kwargs.pop('predicate2subject_labels')
            self._auto_prompt_vocab = kwargs.pop('_auto_prompt_vocab')
        else:
            if auto_prompt_vocab is not None:
                for i, w in enumerate(set(auto_prompt_vocab)):
                    assert isinstance(w, collections.Sequence)
                    if isinstance(w, str):  # 此时是实体label。
                        self._auto_prompt_vocab[w] = i + 1
                        if w not in self.entity_labels:
                            self.entity_labels.append(w)
                    else:
                        assert len(w) == 2, 'w不是字符串时，必须是一个长为2的序列。索引0对应predicate，索引1对应subject_label。'
                        predicate, subject_label = w[0], w[1]
                        self._auto_prompt_vocab[self.conjunct + predicate] = i + 1
                        if predicate in self.predicate2subject_labels:
                            if subject_label not in self.predicate2subject_labels[predicate]:
                                self.predicate2subject_labels[predicate].append(subject_label)  # json 不能序列化set。
                        else:
                            self.predicate2subject_labels[predicate] = [subject_label]

    def auto_prompt_index(self, auto_prompt_token):
        if isinstance(auto_prompt_token, tuple) and len(auto_prompt_token) == 2 and isinstance(auto_prompt_token[0], str) and isinstance(auto_prompt_token[1], str):
            auto_prompt_token = self.conjunct + auto_prompt_token[1]
        if isinstance(auto_prompt_token, str) and auto_prompt_token in self._auto_prompt_vocab:
            return self._auto_prompt_vocab[auto_prompt_token]
        else:
            return 0

    def add_auto_prompt_token(self, entity_label: str = None, predicate: str = None, subject_label: str = None):
        """
        提供entiy_label或者同时提供predicate和subject_label
        Args:
            entity_label:
            predicate:
            subject_label:

        Returns:

        """
        assert (entity_label and not predicate and not subject_label) or \
               (not entity_label and predicate and subject_label), '提示词可以时实体的label，也可以是谓词加主实体标签。'
        auto_prompt = entity_label if entity_label else self.conjunct + predicate
        if auto_prompt not in self._auto_prompt_vocab:
            idx = len(self._auto_prompt_vocab)
            if idx + 1 < self.type_auto_prompt_size:
                self._auto_prompt_vocab[auto_prompt] = idx + 1
                if entity_label:
                    if entity_label not in self.entity_labels:
                        self.entity_labels.append(entity_label)
                else:
                    if predicate in self.predicate2subject_labels:
                        if subject_label not in self.predicate2subject_labels[predicate]:
                            self.predicate2subject_labels[predicate].append(subject_label)  # json 不能序列化set。
                    else:
                        self.predicate2subject_labels[predicate] = [subject_label]
            else:
                raise ValueError('已达到支持自动提示词上限。')


class AutoPromptUIE(UIE):
    def __init__(self, config: AutoPromptUIEConfig):
        super().__init__(config)
        hidden_size = config.hidden_size
        type_auto_prompt_size = config.type_auto_prompt_size
        auto_prompt_len = config.auto_prompt_len

        # self.auto_prompt_param = nn.Parameter(
        #     data=torch.normal(mean=0., std=config.initializer_range,
        #                       size=(type_auto_prompt_size, auto_prompt_len, hidden_size)))
        self.auto_prompt_param = nn.Parameter(
            torch.normal(mean=0.0, std=config.initializer_range, size=(type_auto_prompt_size, auto_prompt_len, hidden_size),
                         requires_grad=True,
                         dtype=torch.float))
        # self.auto_prompt_param = nn.Parameter(torch.zeros(size=(type_auto_prompt_size, auto_prompt_len, hidden_size),
        #                                                   requires_grad=True,
        #                                                   dtype=torch.float))
        self.register_buffer('auto_prompt_position_ids', torch.arange(auto_prompt_len).view(1, -1))

    def load_state_dict_from_uie(self, uie_state_dict, mask_id=3):
        self.load_state_dict(uie_state_dict, strict=False)
        # self.auto_prompt_param.data[0] = self.ernie.embeddings.word_embeddings.weight.data[mask_id] \
        #                                  + torch.normal(
        #     mean=0., std=1e-4, size=self.auto_prompt_param.data[0].size(), device=self.auto_prompt_param.device)
        self.eval()

    @staticmethod
    def load_from_uie_model(uie_model_path=None, uie: UIE = None, type_auto_prompt_size=1000, auto_prompt_len=5,
                            auto_prompt_vocab=[], mask_id=3):
        assert (uie_model_path is None and uie is not None) or (uie_model_path is not None and uie is None), \
            '参数uie_model_path和uie必须提供其中一个。'
        if uie is None:
            uie = UIE.from_pretrained(uie_model_path)
        config = uie.config.__dict__
        config['type_auto_prompt_size'] = type_auto_prompt_size
        config['auto_prompt_len'] = auto_prompt_len
        config['auto_prompt_vocab'] = auto_prompt_vocab
        auie = AutoPromptUIE(AutoPromptUIEConfig(**config))
        auie.load_state_dict_from_uie(uie.state_dict(), mask_id=mask_id)
        auie.eval()
        return auie

    def forward(
            self,
            input_ids=None,
            attention_mask=None,
            token_type_ids=None,
            position_ids=None,
            auto_prompt_ids=None,
            head_mask=None,
            inputs_embeds=None,
            start_positions=None,
            end_positions=None,
            output_attentions=None,
            output_hidden_states=None,
            output_start_end_logits=False,
            return_dict=None,
            **kwargs,
    ):
        """
        Returns: 默认返回start_prob, end_prob.
        """
        batch_size = input_ids.size(0) if input_ids is not None else inputs_embeds.size(0)
        device = input_ids.device if input_ids is not None else inputs_embeds.device
        auto_prompt_len = self.config.auto_prompt_len
        if inputs_embeds is None:
            inputs_embeds = self.ernie.embeddings.word_embeddings(input_ids)
            inputs_embeds = torch.cat(
                (inputs_embeds[:, 0:1],
                 self.auto_prompt_param[auto_prompt_ids] if auto_prompt_ids is not None else self.auto_prompt_param[
                     [0] * batch_size],
                 inputs_embeds[:, 1:]), dim=1)

        if attention_mask is not None:
            attention_mask = torch.cat((torch.ones((batch_size, auto_prompt_len), dtype=torch.int64, device=device),
                                        attention_mask), dim=1)
        if token_type_ids is not None:
            token_type_ids = torch.cat((torch.zeros((batch_size, auto_prompt_len), dtype=torch.int64, device=device),
                                        token_type_ids), dim=1)
        if position_ids is not None:
            position_ids = torch.cat(
                (self.auto_prompt_position_ids.expand(position_ids.size(0), -1), position_ids + auto_prompt_len),
                dim=1)
        res = super(AutoPromptUIE, self).forward(input_ids=None,
                                                 attention_mask=attention_mask,
                                                 token_type_ids=token_type_ids,
                                                 position_ids=position_ids,
                                                 head_mask=head_mask,
                                                 inputs_embeds=inputs_embeds,
                                                 start_positions=start_positions,
                                                 end_positions=end_positions,
                                                 output_attentions=output_attentions,
                                                 output_hidden_states=output_hidden_states,
                                                 output_start_end_logits=output_start_end_logits,
                                                 return_dict=True,
                                                 **kwargs, )
        return_dict = return_dict if return_dict is not None else self.config.use_return_dict
        if not return_dict:
            return tuple(torch.cat((i[:, 0:1], i[:, 1+auto_prompt_len:]), dim=1) for i in tuple(res.values()))
        return UIEModelOutput((k, torch.cat((v[:, 0:1], v[:, 1+auto_prompt_len:]), dim=1)) for k, v in res.items())

    def auto_prompt_index(self, prompt_token):
        return self.config.auto_prompt_index(prompt_token)

    def add_auto_prompt_token(self, entity_label: str = None, predicate: str = None, subject_label: str = None):
        self.config.add_auto_prompt_token(entity_label, predicate, subject_label)

    @property
    def conjunct(self):
        return self.config.conjunct

    @property
    def auto_prompt_vocab(self):
        return list(self.config._auto_prompt_vocab.keys())

    @property
    def entity_labels(self):
        return self.config.entity_labels

    @property
    def predicate2subject_labels(self):
        return self.config.predicate2subject_labels

    def collate(self, examples, tokenizer, max_seq_len):
        """

        Args:
            examples:
            tokenizer:
            max_seq_len:

        Returns:

        """
        # auto_prompt_idx = [
        #     self.auto_prompt_index(
        #         exam['prompt'] if isinstance(exam['prompt'], str) else self.config.conjunct + exam['prompt'][1])
        #     for exam in examples]
        auto_prompt_idx = [
            self.auto_prompt_index(
                exam['prompt'] if self.config.conjunct not in exam['prompt'] else
                self.config.conjunct + exam['prompt'].rsplit(self.config.conjunct)[-1]) for exam in examples]
        for exam in examples:
            if not isinstance(exam['prompt'], str):
                exam['prompt'] = self.conjunct.join(exam['prompt'])
        tokenizer_outputs = batch_encode(examples, tokenizer, max_seq_len)
        tokenizer_outputs['auto_prompt_ids'] = torch.tensor(auto_prompt_idx)
        if 'result_list' in examples[0]:
            start_ids, end_ids = [], []
            length = tokenizer_outputs['input_ids'].size(1)

            for offset_mapping, exam in zip(tokenizer_outputs['offset_mapping'], examples):
                offset_mapping = [list(x) for x in offset_mapping]
                bias = 0
                for index in range(1, len(offset_mapping)):
                    mapping = offset_mapping[index]
                    if mapping[0] == 0 and mapping[1] == 0 and bias == 0:  # 找到中间的SEP
                        bias = offset_mapping[index - 1][1] + 1
                        continue
                    if mapping[0] == 0 and mapping[1] == 0 and bias != 0:  # 找到末尾的SEP
                        break
                    offset_mapping[index][0] += bias
                    offset_mapping[index][1] += bias
                start_ids_exam = [0] * length
                end_ids_exam = [0] * length
                for item in exam["result_list"]:
                    start = map_offset(item["start"] + bias, offset_mapping)
                    end = map_offset(item["end"] - 1 + bias, offset_mapping)
                    start_ids_exam[start] = 1.0
                    end_ids_exam[end] = 1.0
                start_ids.append(start_ids_exam)
                end_ids.append(end_ids_exam)
            tokenizer_outputs['start_ids'] = torch.tensor(start_ids, dtype=torch.float)
            tokenizer_outputs['end_ids'] = torch.tensor(end_ids, dtype=torch.float)
            tokenizer_outputs.pop('offset_mapping')
        return tokenizer_outputs

    def script(self):
        self.eval()
        device = self.linear_end.weight.device
        with torch.no_grad():
            b = {'input_ids': [[1, 160, 496, 2, 89, 1056, 29, 61, 239, 38, 469, 289, 10646, 4, 154, 79, 430, 221, 393, 12043, 2]],
                 'token_type_ids': [[0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]],
                 'attention_mask': [[1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]],
                 'position_ids': [[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20]],
                 'auto_prompt_ids': [0]}
            b = dict((k, v.to(device) if isinstance(v, torch.Tensor) else torch.tensor(v, dtype=torch.int64, device=device)) for k, v in b.items())
            torch._C._jit_set_profiling_mode(False)
            inputs = [b['input_ids'], b['attention_mask'], b['token_type_ids'], b['position_ids'], b['auto_prompt_ids']]
            return torch.jit.trace(self, inputs, strict=False)


def map_offset(ori_offset, offset_mapping):
    """
    map ori offset to token offset
    """
    for index, span in enumerate(offset_mapping):
        if span[0] <= ori_offset < span[1]:
            return index
    return -1


def batch_encode(examples, tokenizer, max_seq_len):
    """
        examples: {
            prompt
            text
            result_list
        }
        """
    prompt = [exam['prompt'] for exam in examples]
    text = [exam['text'] for exam in examples]
    tokenizer_outputs = tokenizer(text=prompt,
                                  text_pair=text,
                                  max_length=max_seq_len,
                                  truncation='only_second',
                                  padding=True,
                                  return_token_type_ids=True,
                                  return_attention_mask=True,
                                  return_tensors='pt',
                                  return_offsets_mapping=True,
                                  )
    length = tokenizer_outputs['input_ids'].size(1)
    tokenizer_outputs['position_ids'] = torch.arange(length).unsqueeze(0)
    tokenizer_outputs['offset_mapping'] = tokenizer_outputs['offset_mapping'].numpy()
    return tokenizer_outputs


@dataclass
class UIEModelOutput(ModelOutput):
    """
    Base class for outputs of question answering pretrained.

    Args:
        loss (`torch.FloatTensor` of shape `(1,)`, *optional*, returned when `labels` is provided):
            Total span extraction loss is the sum of a Cross-Entropy for the start and end positions.
        start_logits (`torch.FloatTensor` of shape `(batch_size, sequence_length)`):
            Span-start scores (before SoftMax).
        end_logits (`torch.FloatTensor` of shape `(batch_size, sequence_length)`):
            Span-end scores (before SoftMax).
        hidden_states (`tuple(torch.FloatTensor)`, *optional*, returned when `output_hidden_states=True` is passed or when `config.output_hidden_states=True`):
            Tuple of `torch.FloatTensor` (one for the output of the embeddings, if the model has an embedding layer, +
            one for the output of each layer) of shape `(batch_size, sequence_length, hidden_size)`.

            Hidden-states of the model at the output of each layer plus the optional initial embedding outputs.
        attentions (`tuple(torch.FloatTensor)`, *optional*, returned when `output_attentions=True` is passed or when `config.output_attentions=True`):
            Tuple of `torch.FloatTensor` (one for each layer) of shape `(batch_size, num_heads, sequence_length,
            sequence_length)`.

            Attentions weights after the attention softmax, used to compute the weighted average in the self-attention
            heads.
    """
    loss: Optional[torch.FloatTensor] = None
    start_prob: torch.FloatTensor = None
    end_prob: torch.FloatTensor = None
    start_logits: torch.FloatTensor = None
    end_logits: torch.FloatTensor = None
    hidden_states: Optional[Tuple[torch.FloatTensor]] = None
    attentions: Optional[Tuple[torch.FloatTensor]] = None


if __name__ == '__main__':
    auie = AutoPromptUIE.load_from_uie_model(
        uie_model_path=r'C:\Users\<USER>\.kbnlp\tasks\information_extraction\uie-nano')
    print('finish.')
