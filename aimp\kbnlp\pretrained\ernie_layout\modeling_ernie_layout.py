# Copyright (c) 2022 PaddlePaddle Authors. All Rights Reserved.
# Copyright 2021 Microsoft Research and The HuggingFace Inc. team. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
""" Modeling classes for ErnieLayout model."""

import copy
import math

import torch
from torch import nn
import torch.nn.functional as F
from torch.nn.utils.rnn import pack_sequence, pad_sequence, pack_padded_sequence, pad_packed_sequence
from transformers import PreTrainedModel
from transformers.modeling_outputs import BaseModelOutputWithPastAndCrossAttentions, BaseModelOutputWithPoolingAndCrossAttentions, SequenceClassifierOutput
from kbnlp.pretrained.ernie_layout.visual_model import ResNet
from kbnlp.pretrained.ernie_layout.configuration_ernie_layout import ErnieLayoutConfig
# __all__ = [
#     'ErnieLayoutModel', "ErnieLayoutPretrainedModel",
#     "ErnieLayoutForTokenClassification", "ErnieLayoutForSequenceClassification",
#     "ErnieLayoutForPretraining", "ErnieLayoutForQuestionAnswering"
# ]


def relative_position_bucket(relative_position,
                             bidirectional=True,
                             num_buckets=32,
                             max_distance=128):
    """
    Adapted from Mesh Tensorflow:
    https://github.com/tensorflow/mesh/blob/0cb87fe07da627bf0b7e60475d59f95ed6b5be3d/mesh_tensorflow/transformer/transformer_layers.py#L593
    Translate relative position to a bucket number for relative attention. The relative position is defined as
    memory_position - query_position, i.e. the distance in tokens from the attending position to the attended-to
    position. If bidirectional=False, then positive relative positions are invalid. We use smaller buckets for small
    absolute relative_position and larger buckets for larger absolute relative_positions. All relative positions
    >=max_distance map to the same bucket. All relative positions <=-max_distance map to the same bucket. This should
    allow for more graceful generalization to longer sequences than the model has been trained on.

    Args:
        relative_position: an int32 Tensor
        bidirectional: a boolean - whether the attention is bidirectional
        num_buckets: an integer
        max_distance: an integer

    Returns:
        a Tensor with the same shape as relative_position, containing int32 values in the range [0, num_buckets)
    """

    ret = 0
    if bidirectional:
        num_buckets //= 2
        ret += (relative_position > 0).to(torch.int64) * num_buckets
        n = torch.abs(relative_position)
    else:
        n = torch.max(-relative_position, torch.zeros_like(relative_position))
    # Now n is in the range [0, inf)
    # half of the buckets are for exact increments in positions
    max_exact = num_buckets // 2
    is_small = n < max_exact

    # The other half of the buckets are for logarithmically bigger bins in positions up to max_distance
    val_if_large = max_exact + (
        torch.log(n.to(torch.float32) / max_exact) /
        math.log(max_distance / max_exact) *
        (num_buckets - max_exact)).to(torch.int64)

    val_if_large = torch.minimum(
        val_if_large, torch.full_like(val_if_large, num_buckets - 1))

    ret += torch.where(is_small, n, val_if_large)
    return ret


class ErnieLayoutPooler(nn.Module):

    def __init__(self, hidden_size, with_pool):
        super(ErnieLayoutPooler, self).__init__()
        self.dense = nn.Linear(hidden_size, hidden_size)
        self.activation = nn.Tanh()
        self.with_pool = with_pool

    def forward(self, hidden_states):
        # We "pool" the model by simply taking the hidden state corresponding
        # to the first token.
        first_token_tensor = hidden_states[:, 0]
        pooled_output = self.dense(first_token_tensor)
        if self.with_pool == 'tanh':
            pooled_output = self.activation(pooled_output)
        return pooled_output


class ErnieLayoutEmbeddings(nn.Module):
    """
    Include embeddings from word, position and token_type embeddings
    """

    def __init__(self, config):
        super(ErnieLayoutEmbeddings, self).__init__()
        self.word_embeddings = nn.Embedding(config.vocab_size,
                                            config.hidden_size)
        self.position_embeddings = nn.Embedding(
            config.max_position_embeddings, config.hidden_size)

        self.x_position_embeddings = nn.Embedding(
            config.max_2d_position_embeddings, config.hidden_size)
        self.y_position_embeddings = nn.Embedding(
            config.max_2d_position_embeddings, config.hidden_size)
        self.h_position_embeddings = nn.Embedding(
            config.max_2d_position_embeddings, config.hidden_size)
        self.w_position_embeddings = nn.Embedding(
            config.max_2d_position_embeddings, config.hidden_size)

        self.token_type_embeddings = nn.Embedding(config.type_vocab_size,
                                                  config.hidden_size)
        self.LayerNorm = nn.LayerNorm(config.hidden_size,
                                      eps=config.layer_norm_eps)
        self.dropout = nn.Dropout(config.hidden_dropout_prob)

        self.register_buffer(
            "position_ids",
            torch.arange(config.max_position_embeddings).expand((1, -1)))

    def _cal_spatial_position_embeddings(self, bbox):
        try:
            left_position_embeddings = self.x_position_embeddings(bbox[:, :, 0])
            upper_position_embeddings = self.y_position_embeddings(bbox[:, :,
                                                                        1])
            right_position_embeddings = self.x_position_embeddings(bbox[:, :,
                                                                        2])
            lower_position_embeddings = self.y_position_embeddings(bbox[:, :,
                                                                        3])
        except IndexError as e:
            raise IndexError(
                "The :obj:`bbox`coordinate values should be within 0-1000 range."
            ) from e

        h_position_embeddings = self.h_position_embeddings(bbox[:, :, 3] -
                                                           bbox[:, :, 1])
        w_position_embeddings = self.w_position_embeddings(bbox[:, :, 2] -
                                                           bbox[:, :, 0])
        return left_position_embeddings, \
                upper_position_embeddings, \
                right_position_embeddings, \
                lower_position_embeddings, \
                h_position_embeddings, \
                w_position_embeddings

    def forward(self,
                input_ids,
                bbox=None,
                token_type_ids=None,
                position_ids=None):
        if position_ids is None:
            ones = torch.ones_like(input_ids, dtype="int64")
            seq_length = torch.cumsum(ones, dim=-1)

            position_ids = seq_length - ones
            position_ids.stop_gradient = True
        if token_type_ids is None:
            token_type_ids = torch.zeros_like(input_ids, dtype="int64")

        input_embedings = self.word_embeddings(input_ids)
        position_embeddings = self.position_embeddings(position_ids)

        x1, y1, x2, y2, h, w = self.embeddings._cal_spatial_position_embeddings(
            bbox)

        token_type_embeddings = self.token_type_embeddings(token_type_ids)

        embeddings = (input_embedings + position_embeddings + x1 + y1 + x2 +
                      y2 + h + w + token_type_embeddings)

        embeddings = self.layer_norm(embeddings)
        embeddings = self.dropout(embeddings)
        return embeddings


class ErnieLayoutPretrainedModel(PreTrainedModel):
    config_class = ErnieLayoutConfig
    base_model_prefix = "ernie_layout"

    def _init_weights(self, module):
        """Initialize the weights"""
        if isinstance(module, nn.Linear):
            # Slightly different from the TF version which uses truncated_normal for initialization
            # cf https://github.com/pytorch/pytorch/pull/5617
            module.weight.data.normal_(mean=0.0, std=self.config.initializer_range)
            if module.bias is not None:
                module.bias.data.zero_()
        elif isinstance(module, nn.Embedding):
            module.weight.data.normal_(mean=0.0, std=self.config.initializer_range)
            if module.padding_idx is not None:
                module.weight.data[module.padding_idx].zero_()
        elif isinstance(module, nn.LayerNorm):
            module.bias.data.zero_()
            module.weight.data.fill_(1.0)

    def _set_gradient_checkpointing(self, module, value=False):
        if isinstance(module, ErnieLayoutEncoder):
            module.gradient_checkpointing = value


class ErnieLayoutSelfOutput(nn.Module):

    def __init__(self, config):
        super(ErnieLayoutSelfOutput, self).__init__()
        self.dense = nn.Linear(config.hidden_size, config.hidden_size)
        self.LayerNorm = nn.LayerNorm(config.hidden_size,
                                      eps=config.layer_norm_eps)
        self.dropout = nn.Dropout(config.hidden_dropout_prob)

    def forward(self, hidden_states, input_tensor):
        hidden_states = self.dense(hidden_states)
        hidden_states = self.dropout(hidden_states)
        hidden_states = self.LayerNorm(hidden_states + input_tensor)
        return hidden_states


class ErnieLayoutSelfAttention(nn.Module):

    def __init__(self, config):
        super(ErnieLayoutSelfAttention, self).__init__()
        if config.hidden_size % config.num_attention_heads != 0 and not hasattr(
                    config, "embedding_size"):
            raise ValueError(
                "The hidden size {} is not a multiple of the number of attention "
                "heads {}".format(config.hidden_size,
                                  config.num_attention_heads))
        self.num_attention_heads = config.num_attention_heads
        self.attention_head_size = int(config.hidden_size /
                                       config.num_attention_heads)
        self.all_head_size = self.num_attention_heads * self.attention_head_size

        self.has_relative_attention_bias = config.has_relative_attention_bias
        self.has_spatial_attention_bias = config.has_spatial_attention_bias

        self.query = nn.Linear(config.hidden_size, self.all_head_size)
        self.key = nn.Linear(config.hidden_size, self.all_head_size)
        self.value = nn.Linear(config.hidden_size, self.all_head_size)

        self.dropout = nn.Dropout(config.attention_probs_dropout_prob)

    def transpose_for_scores(self, x):
        x = x.reshape([
            x.size(0),
            x.size(1), self.num_attention_heads,
            self.attention_head_size
        ])
        return x.permute([0, 2, 1, 3])

    def compute_qkv(self, hidden_states):
        q = self.query(hidden_states)
        k = self.key(hidden_states)
        v = self.value(hidden_states)
        return q, k, v

    def forward(
        self,
        hidden_states,
        attention_mask=None,
        head_mask=None,
        encoder_hidden_states=None,
        encoder_attention_mask=None,
        past_key_value=None,
        output_attentions=False,
        rel_pos=None,
        rel_2d_pos=None,
    ):
        q, k, v = self.compute_qkv(hidden_states)

        # (B, L, H*D) -> (B, H, L, D)
        query_layer = self.transpose_for_scores(q)
        key_layer = self.transpose_for_scores(k)
        value_layer = self.transpose_for_scores(v)

        query_layer = query_layer / math.sqrt(self.attention_head_size)
        # [BSZ, NAT, L, L]
        attention_scores = torch.matmul(query_layer,
                                        key_layer.transpose(-1, -2))

        if self.has_relative_attention_bias:
            attention_scores += rel_pos
        if self.has_spatial_attention_bias:
            attention_scores += rel_2d_pos
        bool_attention_mask = attention_mask.to(torch.bool)
        bool_attention_mask.stop_gradient = True
        attention_scores_shape = attention_scores.size()
        attention_scores = torch.where(
            bool_attention_mask.expand(attention_scores_shape),
            torch.ones(attention_scores_shape, device=attention_scores.device) * float("-1e10"),
            attention_scores)
        attention_probs = F.softmax(attention_scores, dim=-1)
        # This is actually dropping out entire tokens to attend to, which might
        # seem a bit unusual, but is taken from the original Transformer paper.
        attention_probs = self.dropout(attention_probs)
        context_layer = torch.matmul(attention_probs, value_layer)
        context_layer = context_layer.permute([0, 2, 1, 3])
        context_layer = context_layer.reshape([
            context_layer.size(0),
            context_layer.size(1), self.all_head_size
        ])

        if output_attentions:
            outputs = [context_layer, attention_probs]
        else:
            outputs = [context_layer]
        return outputs


class ErnieLayoutAttention(nn.Module):

    def __init__(self, config):
        super(ErnieLayoutAttention, self).__init__()
        self.self = ErnieLayoutSelfAttention(config)
        self.output = ErnieLayoutSelfOutput(config)

    def forward(
        self,
        hidden_states,
        attention_mask=None,
        head_mask=None,
        encoder_hidden_states=None,
        encoder_attention_mask=None,
        past_key_value=None,
        output_attentions=False,
        rel_pos=None,
        rel_2d_pos=None,
    ):

        self_outputs = self.self(
            hidden_states,
            attention_mask,
            head_mask,
            encoder_hidden_states,
            encoder_attention_mask,
            past_key_value,
            output_attentions,
            rel_pos=rel_pos,
            rel_2d_pos=rel_2d_pos,
        )
        attention_output = self.output(self_outputs[0], hidden_states)
        # add attentions if we output them
        if output_attentions:
            outputs = [
                attention_output,
            ] + self_outputs[1:]
        else:
            outputs = [attention_output]
        return outputs


class ErnieLayoutEncoder(nn.Module):

    def __init__(self, config):
        super(ErnieLayoutEncoder, self).__init__()
        self.config = config
        self.layer = nn.Sequential(*[
            ErnieLayoutLayer(config) for _ in range(config.num_hidden_layers)
        ])

        self.has_relative_attention_bias = config.has_relative_attention_bias
        self.has_spatial_attention_bias = config.has_spatial_attention_bias
        if self.has_relative_attention_bias:
            self.rel_pos_bins = config.rel_pos_bins
            self.max_rel_pos = config.max_rel_pos
            self.rel_pos_onehot_size = config.rel_pos_bins
            self.rel_pos_bias = nn.Parameter(torch.normal(mean=0., std=config.initializer_range,
                                                          size=(
                                                          self.rel_pos_onehot_size, config.num_attention_heads),
                                                          dtype=torch.get_default_dtype()))
            # self.rel_pos_bias = paddle.create_parameter(shape=[self.rel_pos_onehot_size, \
            #         config["num_attention_heads"]], dtype=paddle.get_default_dtype())

        if self.has_spatial_attention_bias:
            self.max_rel_2d_pos = config.max_rel_2d_pos
            self.rel_2d_pos_bins = config.rel_2d_pos_bins
            self.rel_2d_pos_onehot_size = config.rel_2d_pos_bins
            self.rel_pos_x_bias = nn.Parameter(torch.normal(mean=0., std=config.initializer_range,
                                                            size=(self.rel_2d_pos_onehot_size,
                                                                  config.num_attention_heads),
                                                            dtype=torch.get_default_dtype()))
            # self.rel_pos_x_bias = paddle.create_parameter(
            #     shape=[
            #         self.rel_2d_pos_onehot_size, config["num_attention_heads"]
            #     ],
            #     dtype=paddle.get_default_dtype())
            self.rel_pos_y_bias = nn.Parameter(torch.normal(mean=0., std=config.initializer_range,
                                                            size=(
                                                            self.rel_2d_pos_onehot_size, config.num_attention_heads),
                                                            dtype=torch.get_default_dtype()))
            # self.rel_pos_y_bias = paddle.create_parameter(
            #     shape=[
            #         self.rel_2d_pos_onehot_size, config["num_attention_heads"]
            #     ],
            #     dtype=paddle.get_default_dtype())

    def _cal_1d_pos_emb(self, hidden_states, position_ids):
        rel_pos_mat = position_ids.unsqueeze(-2) - position_ids.unsqueeze(-1)
        rel_pos = relative_position_bucket(
            rel_pos_mat,
            num_buckets=self.rel_pos_bins,
            max_distance=self.max_rel_pos,
        )
        rel_pos = F.one_hot(
            rel_pos,
            num_classes=self.rel_pos_onehot_size).to(hidden_states.dtype)
        rel_pos = torch.matmul(
            rel_pos, self.rel_pos_bias).permute([0, 3, 1, 2])
        return rel_pos

    def _cal_2d_pos_emb(self, hidden_states, bbox):
        position_coord_x = bbox[:, :, 0]
        position_coord_y = bbox[:, :, 3]
        rel_pos_x_2d_mat = position_coord_x.unsqueeze(
            -2) - position_coord_x.unsqueeze(-1)
        rel_pos_y_2d_mat = position_coord_y.unsqueeze(
            -2) - position_coord_y.unsqueeze(-1)
        rel_pos_x = relative_position_bucket(
            rel_pos_x_2d_mat,
            num_buckets=self.rel_2d_pos_bins,
            max_distance=self.max_rel_2d_pos,
        )
        rel_pos_y = relative_position_bucket(
            rel_pos_y_2d_mat,
            num_buckets=self.rel_2d_pos_bins,
            max_distance=self.max_rel_2d_pos,
        )
        rel_pos_x = F.one_hot(rel_pos_x,
                              num_classes=self.rel_2d_pos_onehot_size).to(
                                  hidden_states.dtype)
        rel_pos_y = F.one_hot(rel_pos_y,
                              num_classes=self.rel_2d_pos_onehot_size).to(
                                  hidden_states.dtype)
        rel_pos_x = torch.matmul(
            rel_pos_x, self.rel_pos_x_bias).permute([0, 3, 1, 2])
        rel_pos_y = torch.matmul(
            rel_pos_y, self.rel_pos_y_bias).permute([0, 3, 1, 2])
        rel_2d_pos = rel_pos_x + rel_pos_y
        return rel_2d_pos

    def forward(
        self,
        hidden_states,
        attention_mask=None,
        head_mask=None,
        encoder_hidden_states=None,
        encoder_attention_mask=None,
        past_key_values=None,
        output_attentions=False,
        use_cache=None,
        output_hidden_states=False,
        return_dict=True,
        bbox=None,
        position_ids=None,
    ):
        use_cache = None  # 暂时不支持该参数

        all_hidden_states = () if output_hidden_states else None
        all_self_attentions = () if output_attentions else None
        all_cross_attentions = () if output_attentions and self.config.add_cross_attention else None
        next_decoder_cache = () if use_cache else None

        rel_pos = self._cal_1d_pos_emb(
            hidden_states,
            position_ids) if self.has_relative_attention_bias else None
        rel_2d_pos = self._cal_2d_pos_emb(
            hidden_states, bbox) if self.has_spatial_attention_bias else None

        hidden_save = dict()
        hidden_save["input_hidden_states"] = hidden_states

        for i, layer_module in enumerate(self.layer):
            if output_hidden_states:
                all_hidden_states = all_hidden_states + (hidden_states, )

            layer_head_mask = head_mask[i] if head_mask is not None else None
            past_key_value = past_key_values[
                i] if past_key_values is not None else None

            # Ernie layout 暂时不支持gradient checkpointing。
            # gradient_checkpointing is set as False here so we remove some codes here
            hidden_save["input_attention_mask"] = attention_mask
            hidden_save["input_layer_head_mask"] = layer_head_mask
            layer_outputs = layer_module(
                hidden_states,
                attention_mask,
                layer_head_mask,
                encoder_hidden_states,
                encoder_attention_mask,
                past_key_value,
                output_attentions,
                rel_pos=rel_pos,
                rel_2d_pos=rel_2d_pos,
            )

            hidden_states = layer_outputs[0]
            if use_cache:
                next_decoder_cache += (layer_outputs[-1],)
            if output_attentions:
                all_self_attentions = all_self_attentions + (layer_outputs[1],)
                if self.config.add_cross_attention:
                    all_cross_attentions = all_cross_attentions + (layer_outputs[2],)

            hidden_save["{}_data".format(i)] = hidden_states

        if output_hidden_states:
            all_hidden_states = all_hidden_states + (hidden_states,)

        if not return_dict:
            return tuple(
                v
                for v in [
                    hidden_states,
                    next_decoder_cache,
                    all_hidden_states,
                    all_self_attentions,
                    all_cross_attentions,
                ]
                if v is not None
            )

        return BaseModelOutputWithPastAndCrossAttentions(
            last_hidden_state=hidden_states,
            past_key_values=next_decoder_cache,
            hidden_states=all_hidden_states,
            attentions=all_self_attentions,
            cross_attentions=all_cross_attentions,
        )


class ErnieLayoutIntermediate(nn.Module):

    def __init__(self, config):
        super(ErnieLayoutIntermediate, self).__init__()
        self.dense = nn.Linear(config.hidden_size,
                               config.intermediate_size)
        if config.hidden_act == "gelu":
            self.intermediate_act_fn = nn.GELU()
        else:
            assert False, "hidden_act is set as: {}, please check it..".format(
                config.hidden_act)

    def forward(self, hidden_states):
        hidden_states = self.dense(hidden_states)
        hidden_states = self.intermediate_act_fn(hidden_states)
        return hidden_states


class ErnieLayoutOutput(nn.Module):

    def __init__(self, config):
        super(ErnieLayoutOutput, self).__init__()
        self.dense = nn.Linear(config.intermediate_size,
                               config.hidden_size)
        self.LayerNorm = nn.LayerNorm(config.hidden_size,
                                      eps=config.layer_norm_eps)
        self.dropout = nn.Dropout(config.hidden_dropout_prob)

    def forward(self, hidden_states, input_tensor):
        hidden_states = self.dense(hidden_states)
        hidden_states = self.dropout(hidden_states)
        hidden_states = self.LayerNorm(hidden_states + input_tensor)
        return hidden_states


class ErnieLayoutLayer(nn.Module):

    def __init__(self, config):
        super(ErnieLayoutLayer, self).__init__()
        # since chunk_size_feed_forward is 0 as default, no chunk is needed here.
        self.seq_len_dim = 1
        self.attention = ErnieLayoutAttention(config)
        self.is_decoder = False  # 暂不支持
        self.add_cross_attention = False  # 暂时不支持

        self.intermediate = ErnieLayoutIntermediate(config)
        self.output = ErnieLayoutOutput(config)

    def feed_forward_chunk(self, attention_output):
        intermediate_output = self.intermediate(attention_output)
        layer_output = self.output(intermediate_output, attention_output)
        return layer_output

    def forward(
        self,
        hidden_states,
        attention_mask=None,
        head_mask=None,
        encoder_hidden_states=None,
        encoder_attention_mask=None,
        past_key_value=None,
        output_attentions=False,
        rel_pos=None,
        rel_2d_pos=None,
    ):
        if encoder_hidden_states is not None and encoder_attention_mask is not None:
            raise ValueError('Ernie layout 暂时不支持参数encoder_hidden_states、encoder_attention_mask。')
        # decoder uni-directional self-attention cached key/values tuple is at positions 1,2
        self_attn_past_key_value = past_key_value[:
                                                  2] if past_key_value is not None else None
        self_attention_outputs = self.attention(
            hidden_states,
            attention_mask,
            head_mask,
            output_attentions=output_attentions,
            past_key_value=self_attn_past_key_value,
            rel_pos=rel_pos,
            rel_2d_pos=rel_2d_pos,
        )
        attention_output = self_attention_outputs[0]

        # if decoder, the last output is tuple of self-attn cache
        if self.is_decoder:
            outputs = tuple(self_attention_outputs[1:-1])
            present_key_value = self_attention_outputs[-1]
        else:
            outputs = tuple(self_attention_outputs[1:])  # add self attentions

        cross_attn_present_key_value = None
        if self.is_decoder and encoder_hidden_states is not None:
            # 暂不支持
            pass

        layer_output = self.feed_forward_chunk(attention_output)
        outputs = (layer_output,) + outputs
        # if decoder, return the attn key/values as the last output
        if self.is_decoder:
            outputs = outputs + (present_key_value,)

        return outputs


class VisualBackbone(nn.Module):

    def __init__(self, config):
        super(VisualBackbone, self).__init__()

        self.backbone = ResNet(layers=101)

        self.register_buffer(
            "pixel_mean",
            torch.tensor([103.53, 116.28, 123.675]).view([3, 1, 1]))
        self.register_buffer(
            "pixel_std",
            torch.tensor([57.375, 57.12, 58.395]).view([3, 1, 1]))

        self.pool = nn.AdaptiveAvgPool2d(config.image_feature_pool_shape[:2])

    def forward(self, images):
        images_input = (images -
                        self.pixel_mean) / self.pixel_std
        features = self.backbone(images_input)
        features = self.pool(features).flatten(start_dim=2).permute(
            [0, 2, 1])
        return features


# @register_base_model
class ErnieLayoutModel(ErnieLayoutPretrainedModel):
    """
    The bare ErnieLayout Model outputting raw hidden-states.

    This model inherits from :class:`~uiex_p.transformers.model_utils.PretrainedModel`.
    Refer to the superclass documentation for the generic methods.

    This model is also a Paddle `paddle.nn.Layer <https://www.paddlepaddle.org.cn/documentation
    /docs/en/api/paddle/fluid/dygraph/layers/Layer_en.html>`__ subclass. Use it as a regular Paddle Layer
    and refer to the Paddle documentation for all matter related to general usage and behavior.

    Args:
        vocab_size (`int`):
            Vocabulary size of the XLNet model. Defines the number of different tokens that can
            be represented by the `inputs_ids` passed when calling ErnieLayoutModel.
        hidden_size (`int`, optional):
            Dimensionality of the encoder layers and the pooler layer. Defaults to ``768``.
        num_hidden_layers (`int`, optional):
            Number of hidden layers in the Transformer encoder. Defaults to ``12``.
        num_attention_heads (`int`, optional):
            Number of attention heads for each attention layer in the Transformer encoder.
            Defaults to ``12``.
        intermediate_size (`int`, optional):
            Dimensionality of the "intermediate" (often named feed-forward) layer in the Transformer encoder.
            Defaults to ``3072``.
        hidden_act (`str`, optional):
            The non-linear activation function in the feed-forward layer.
            ``"gelu"``, ``"relu"`` and any other paddle supported activation functions
            are supported. Defaults to ``"gelu"``.
        hidden_dropout_prob (`float`, optional):
            The dropout probability for all fully connected layers in the embeddings and encoder.
            Defaults to ``0.1``.
        attention_probs_dropout_prob (`float`, optional):
            The dropout probability for all fully connected layers in the pooler.
            Defaults to ``0.1``.
        initializer_range (`float`, optional):
            The standard deviation of the truncated_normal_initializer for initializing all weight matrices.
            Defaults to ``0.02``.
    """

    def __init__(
        self,
        config: ErnieLayoutConfig,
        with_pool='tanh',
    ):
        super(ErnieLayoutModel, self).__init__(config)
        self.config = config
        self.has_visual_segment_embedding = config.has_visual_segment_embedding
        self.embeddings = ErnieLayoutEmbeddings(config)

        self.visual = VisualBackbone(config)
        self.visual_proj = nn.Linear(config.image_feature_pool_shape[-1],
                                     config.hidden_size)
        self.visual_act_fn = nn.GELU()
        if self.has_visual_segment_embedding:
            self.visual_segment_embedding = nn.Parameter(torch.normal(mean=0., std=config.initializer_range,
                                                                      size=(config.hidden_size,),
                                                                      dtype=self.embedding.weight.dtype))
            # self.visual_segment_embedding = self.create_parameter(
            #     shape=[
            #         config["hidden_size"],
            #     ],
            #     dtype=self.embedding.weight.dtype)
        self.visual_LayerNorm = nn.LayerNorm(config.hidden_size,
                                             eps=config.layer_norm_eps)
        self.visual_dropout = nn.Dropout(config.hidden_dropout_prob)
        self.encoder = ErnieLayoutEncoder(config)
        self.pooler = ErnieLayoutPooler(config.hidden_size, with_pool)

    def _calc_text_embeddings(self, input_ids, bbox, position_ids,
                              token_type_ids):
        words_embeddings = self.embeddings.word_embeddings(input_ids)
        position_embeddings = self.embeddings.position_embeddings(position_ids)
        x1, y1, x2, y2, h, w = self.embeddings._cal_spatial_position_embeddings(
            bbox)
        token_type_embeddings = self.embeddings.token_type_embeddings(
            token_type_ids)
        embeddings = words_embeddings + position_embeddings + x1 + y1 + x2 + y2 + w + h + token_type_embeddings

        embeddings = self.embeddings.LayerNorm(embeddings)
        embeddings = self.embeddings.dropout(embeddings)
        return embeddings

    def _calc_img_embeddings(self, image, bbox, position_ids):
        if image is not None:
            visual_embeddings = self.visual_act_fn(
                self.visual_proj(self.visual(image)))
        position_embeddings = self.embeddings.position_embeddings(position_ids)
        x1, y1, x2, y2, h, w = self.embeddings._cal_spatial_position_embeddings(
            bbox)
        if image is not None:
            embeddings = visual_embeddings + position_embeddings + x1 + y1 + x2 + y2 + w + h
        else:
            embeddings = position_embeddings + x1 + y1 + x2 + y2 + w + h

        if self.has_visual_segment_embedding:
            embeddings += self.visual_segment_embedding
        embeddings = self.visual_LayerNorm(embeddings)
        embeddings = self.visual_dropout(embeddings)
        return embeddings

    def _calc_visual_bbox(self, image_feature_pool_shape, bbox, visual_shape):
        # visual_bbox_x = (torch.arange(
        #     0,
        #     1000 * (image_feature_pool_shape[1] + 1),
        #     1000,
        #     dtype=bbox.dtype,
        # ) // image_feature_pool_shape[1])
        #  去除__floordiv__ UserWarning.
        visual_bbox_x = torch.div(torch.arange(0, 1000 * (image_feature_pool_shape[1] + 1), 1000, dtype=bbox.dtype,),
                                  image_feature_pool_shape[1], rounding_mode='trunc')
        # visual_bbox_y = (torch.arange(
        #     0,
        #     1000 * (image_feature_pool_shape[0] + 1),
        #     1000,
        #     dtype=bbox.dtype,
        # ) // image_feature_pool_shape[0])
        visual_bbox_y = torch.div(torch.arange(0, 1000 * (image_feature_pool_shape[0] + 1), 1000, dtype=bbox.dtype,),
                                  image_feature_pool_shape[0], rounding_mode='trunc')
        expand_shape = image_feature_pool_shape[0:2]
        visual_bbox = torch.stack(
            [
                visual_bbox_x[:-1].expand(expand_shape),
                visual_bbox_y[:-1].expand(expand_shape[::-1]).transpose(1, 0),
                visual_bbox_x[1:].expand(expand_shape),
                visual_bbox_y[1:].expand(expand_shape[::-1]).transpose(1, 0),
            ],
            dim=-1,
        ).reshape([expand_shape[0] * expand_shape[1],
                   bbox.size(-1)])

        visual_bbox = visual_bbox.expand(
            [visual_shape[0], visual_bbox.shape[0], visual_bbox.shape[1]])
        return visual_bbox.to(bbox.device)

    def resize_position_embeddings(self, new_num_position_embeddings):
        """
        Resizes position embeddings of the model if `new_num_position_embeddings != config["max_position_embeddings"]`.

        Arguments:
            new_num_position_embeddings (`int`):
                The number of new position embedding matrix. If position embeddings are learned, increasing the size
                will add newly initialized vectors at the end, whereas reducing the size will remove vectors from the
                end.
        """
        num_position_embeds_diff = new_num_position_embeddings - self.config.max_position_embeddings

        # no resizing needs to be done if the length stays the same
        if num_position_embeds_diff == 0:
            return

        # logger.info(
        #     f"Setting `config.max_position_embeddings={new_num_position_embeddings}`..."
        # )
        self.config.max_position_embeddings = new_num_position_embeddings

        old_position_embeddings_weight = self.embeddings.position_embeddings.weight

        self.embeddings.position_embeddings = nn.Embedding(
            self.config.max_position_embeddings, self.config.hidden_size)

        with torch.no_grad():
            if num_position_embeds_diff > 0:
                self.embeddings.position_embeddings.weight[:
                                                           -num_position_embeds_diff] = old_position_embeddings_weight
            else:
                self.embeddings.position_embeddings.weight = old_position_embeddings_weight[:
                                                                                            num_position_embeds_diff]

    def forward(self,
                input_ids=None,
                bbox=None,
                image=None,
                token_type_ids=None,
                position_ids=None,
                attention_mask=None,
                head_mask=None,
                output_hidden_states=False,
                output_attentions=False,
                return_dict=False):
        input_shape = list(input_ids.size())
        visual_shape = list(input_shape)
        visual_shape[1] = self.config.image_feature_pool_shape[
            0] * self.config.image_feature_pool_shape[1]
        visual_bbox = self._calc_visual_bbox(
            self.config.image_feature_pool_shape, bbox, visual_shape)

        final_bbox = torch.cat([bbox, visual_bbox], dim=1)
        if attention_mask is None:
            attention_mask = torch.ones(input_shape, device=input_ids.device)

        visual_attention_mask = torch.ones(visual_shape, device=input_ids.device)

        attention_mask = attention_mask.to(visual_attention_mask.dtype)

        final_attention_mask = torch.cat(
            [attention_mask, visual_attention_mask], dim=1)

        if token_type_ids is None:
            token_type_ids = torch.zeros(input_shape, dtype=torch.int64, device=input_ids.device)

        if position_ids is None:
            seq_length = input_shape[1]
            position_ids = self.embeddings.position_ids[:, :seq_length]
            position_ids = position_ids.expand(input_shape)

        visual_position_ids = torch.arange(0, visual_shape[1], device=input_ids.device).expand(
            [input_shape[0], visual_shape[1]])
        final_position_ids = torch.cat([position_ids, visual_position_ids],
                                       dim=1)

        if bbox is None:
            bbox = torch.zeros(input_shape + [4])

        text_layout_emb = self._calc_text_embeddings(
            input_ids=input_ids,
            bbox=bbox,
            token_type_ids=token_type_ids,
            position_ids=position_ids,
        )

        visual_emb = self._calc_img_embeddings(
            image=image,
            bbox=visual_bbox,
            position_ids=visual_position_ids,
        )
        final_emb = torch.cat([text_layout_emb, visual_emb], dim=1)

        extended_attention_mask = final_attention_mask.unsqueeze(1).unsqueeze(2)

        extended_attention_mask = (1.0 - extended_attention_mask) * -10000.0

        if head_mask is not None:
            if head_mask.dim() == 1:
                head_mask = head_mask.unsqueeze(0).unsqueeze(0).unsqueeze(
                    -1).unsqueeze(-1)
                head_mask = head_mask.expand(self.config.num_hidden_layers,
                                             -1, -1, -1, -1)
            elif head_mask.dim() == 2:
                head_mask = head_mask.unsqueeze(1).unsqueeze(-1).unsqueeze(-1)
        else:
            head_mask = [None] * self.config.num_hidden_layers

        encoder_outputs = self.encoder(
            final_emb,
            extended_attention_mask,
            bbox=final_bbox,
            position_ids=final_position_ids,
            output_attentions=output_attentions,
            output_hidden_states=output_hidden_states,
            return_dict=return_dict,
        )
        sequence_output = encoder_outputs[0]
        pooled_output = self.pooler(sequence_output)

        if not return_dict:
            return (sequence_output, pooled_output) + encoder_outputs[1:]

        return BaseModelOutputWithPoolingAndCrossAttentions(
            last_hidden_state=sequence_output,
            pooler_output=pooled_output,
            past_key_values=encoder_outputs.past_key_values,
            hidden_states=encoder_outputs.hidden_states,
            attentions=encoder_outputs.attentions,
            cross_attentions=encoder_outputs.cross_attentions,
        )


class ErnieLayoutForSequenceClassification(ErnieLayoutPretrainedModel):

    def __init__(self, config):
        super(ErnieLayoutForSequenceClassification, self).__init__(config)
        self.num_labels = config.num_labels
        self.ernie_layout = ErnieLayoutModel(config)
        self.dropout = nn.Dropout(self.ernie_layout.config.hidden_dropout_prob)
        self.classifier = nn.Linear(self.ernie_layout.config.hidden_size * 3,
                                    self.num_labels)

    def get_input_embeddings(self):
        return self.ernie_layout.embeddings.word_embeddings

    def resize_position_embeddings(self, new_num_position_embeddings):
        """
        Resizes position embeddings of the model if `new_num_position_embeddings != config["max_position_embeddings"]`.

        Arguments:
            new_num_position_embeddings (`int`):
                The number of new position embedding matrix. If position embeddings are learned, increasing the size
                will add newly initialized vectors at the end, whereas reducing the size will remove vectors from the
                end.
        """
        self.ernie_layout.resize_position_embeddings(
            new_num_position_embeddings)

    def forward(
        self,
        input_ids=None,
        bbox=None,
        image=None,
        attention_mask=None,
        token_type_ids=None,
        position_ids=None,
        head_mask=None,
        labels=None,
    ):
        input_shape = input_ids.size()
        visual_shape = list(input_shape)
        visual_shape[1] = self.ernie_layout.config.image_feature_pool_shape[
            0] * self.ernie_layout.config.image_feature_pool_shape[1]
        visual_bbox = self.ernie_layout._calc_visual_bbox(
            self.ernie_layout.config.image_feature_pool_shape, bbox,
            visual_shape)

        visual_position_ids = torch.arange(0, visual_shape[1]).expand(
            [input_shape[0], visual_shape[1]])

        initial_image_embeddings = self.ernie_layout._calc_img_embeddings(
            image=image,
            bbox=visual_bbox,
            position_ids=visual_position_ids,
        )

        outputs = self.ernie_layout(
            input_ids=input_ids,
            bbox=bbox,
            image=image,
            attention_mask=attention_mask,
            token_type_ids=token_type_ids,
            position_ids=position_ids,
            head_mask=head_mask,
        )
        seq_length = input_ids.shape[1]
        # sequence out and image out
        sequence_output, final_image_embeddings = outputs[
            0][:, :seq_length], outputs[0][:, seq_length:]

        cls_final_output = sequence_output[:, 0, :]

        # average-pool the visual embeddings
        pooled_initial_image_embeddings = initial_image_embeddings.mean(axis=1)
        pooled_final_image_embeddings = final_image_embeddings.mean(axis=1)
        # concatenate with cls_final_output
        sequence_output = torch.cat([
            cls_final_output, pooled_initial_image_embeddings,
            pooled_final_image_embeddings
        ],
            dim=1)

        sequence_output = self.dropout(sequence_output)
        logits = self.classifier(sequence_output)

        outputs = logits,

        if labels is not None:
            loss_fct = nn.CrossEntropyLoss()

            loss = loss_fct(logits.view([-1, self.num_labels]),
                            labels.view([-1, ]))

            outputs = (loss, ) + outputs

        return outputs


# class ErnieLayoutPredictionHead(Layer):
#     """
#     Bert Model with a `language modeling` head on top for CLM fine-tuning.
#     """
#
#     def __init__(self,
#                  hidden_size,
#                  vocab_size,
#                  activation,
#                  embedding_weights=None):
#         super(ErnieLayoutPredictionHead, self).__init__()
#         self.transform = nn.Linear(hidden_size, hidden_size)
#         self.activation = getattr(nn.functional, activation)
#         self.layer_norm = nn.LayerNorm(hidden_size)
#         self.decoder_weight = self.create_parameter(
#             shape=[vocab_size, hidden_size],
#             dtype=self.transform.weight.dtype,
#             is_bias=False) if embedding_weights is None else embedding_weights
#         self.decoder_bias = self.create_parameter(
#             shape=[vocab_size], dtype=self.decoder_weight.dtype, is_bias=True)
#
#     def forward(self, hidden_states, masked_positions=None):
#         if masked_positions is not None:
#             hidden_states = paddle.reshape(hidden_states,
#                                            [-1, hidden_states.shape[-1]])
#             hidden_states = paddle.tensor.gather(hidden_states,
#                                                  masked_positions)
#         # gather masked tokens might be more quick
#         hidden_states = self.transform(hidden_states)
#         hidden_states = self.activation(hidden_states)
#         hidden_states = self.layer_norm(hidden_states)
#         hidden_states = paddle.tensor.matmul(
#             hidden_states, self.decoder_weight,
#             transpose_y=True) + self.decoder_bias
#         return hidden_states
#
#
# class ErnieLayoutPretrainingHeads(Layer):
#
#     def __init__(self,
#                  hidden_size,
#                  vocab_size,
#                  activation,
#                  embedding_weights=None):
#         super(ErnieLayoutPretrainingHeads, self).__init__()
#         self.predictions = ErnieLayoutPredictionHead(hidden_size, vocab_size,
#                                                      activation,
#                                                      embedding_weights)
#
#     def forward(self, sequence_output, masked_positions=None):
#         prediction_scores = self.predictions(sequence_output, masked_positions)
#         return prediction_scores
#
#
# class ErnieLayoutForPretraining(ErnieLayoutPretrainedModel):
#
#     def __init__(self, ernie_layout):
#         super(ErnieLayoutForPretraining, self).__init__()
#         self.ernie_layout = ernie_layout
#         self.cls = ErnieLayoutPretrainingHeads(
#             self.ernie_layout.config["hidden_size"],
#             self.ernie_layout.config["vocab_size"],
#             self.ernie_layout.config["hidden_act"],
#             embedding_weights=self.ernie_layout.embeddings.word_embeddings.
#             weight)
#
#     def resize_position_embeddings(self, new_num_position_embeddings):
#         """
#         Resizes position embeddings of the model if `new_num_position_embeddings != config["max_position_embeddings"]`.
#
#         Arguments:
#             new_num_position_embeddings (`int`):
#                 The number of new position embedding matrix. If position embeddings are learned, increasing the size
#                 will add newly initialized vectors at the end, whereas reducing the size will remove vectors from the
#                 end.
#         """
#         self.ernie_layout.resize_position_embeddings(
#             new_num_position_embeddings)
#
#     def forward(self,
#                 input_ids=None,
#                 bbox=None,
#                 image=None,
#                 attention_mask=None,
#                 token_type_ids=None,
#                 position_ids=None,
#                 head_mask=None,
#                 masked_positions=None):
#         outputs = self.ernie_layout(
#             input_ids=input_ids,
#             bbox=bbox,
#             image=image,
#             attention_mask=attention_mask,
#             token_type_ids=token_type_ids,
#             position_ids=position_ids,
#             head_mask=head_mask,
#         )
#         sequence_output = outputs[0]
#         prediction_scores = self.cls(sequence_output, masked_positions)
#         return prediction_scores
#
#
class ErnieLayoutForTokenClassification(ErnieLayoutPretrainedModel):

    def __init__(self, config):
        super(ErnieLayoutForTokenClassification, self).__init__(config)
        self.num_labels = config.num_labels
        self.ernie_layout = ErnieLayoutModel(config)

        self.dropout = nn.Dropout(self.ernie_layout.config.hidden_dropout_prob)
        self.classifier = nn.Linear(self.ernie_layout.config.hidden_size,
                                    self.num_labels)

    def get_input_embeddings(self):
        return self.ernie_layout.embeddings.word_embeddings

    def resize_position_embeddings(self, new_num_position_embeddings):
        """
        Resizes position embeddings of the model if `new_num_position_embeddings != config["max_position_embeddings"]`.

        Arguments:
            new_num_position_embeddings (`int`):
                The number of new position embedding matrix. If position embeddings are learned, increasing the size
                will add newly initialized vectors at the end, whereas reducing the size will remove vectors from the
                end.
        """
        self.ernie_layout.resize_position_embeddings(
            new_num_position_embeddings)

    def forward(
        self,
        input_ids=None,
        bbox=None,
        image=None,
        attention_mask=None,
        token_type_ids=None,
        position_ids=None,
        head_mask=None,
        labels=None,
    ):
        outputs = self.ernie_layout(
            input_ids=input_ids,
            bbox=bbox,
            image=image,
            attention_mask=attention_mask,
            token_type_ids=token_type_ids,
            position_ids=position_ids,
            head_mask=head_mask,
        )
        seq_length = input_ids.shape[1]
        sequence_output = outputs[0][:, :seq_length]
        sequence_output = self.dropout(sequence_output)
        logits = self.classifier(sequence_output)

        outputs = logits,

        if labels is not None:
            loss_fct = nn.CrossEntropyLoss()

            if attention_mask is not None:
                active_loss = attention_mask.reshape([
                    -1,
                ]) == 1
                active_logits = logits.reshape([-1,
                                                self.num_labels])[active_loss]
                active_labels = labels.reshape([
                    -1,
                ])[active_loss]
                loss = loss_fct(active_logits, active_labels)
            else:
                loss = loss_fct(logits.reshape([-1, self.num_labels]),
                                labels.reshape([
                                    -1,
                                ]))

            outputs = (loss, ) + outputs

        return outputs


class ErnieLayoutForQuestionAnswering(ErnieLayoutPretrainedModel):

    def __init__(self, config):
        super(ErnieLayoutForQuestionAnswering, self).__init__(config)
        self.num_labels = config.num_labels
        self.ernie_layout = ErnieLayoutModel(config)
        self.dropout = nn.Dropout(self.ernie_layout.config.hidden_dropout_prob)
        self.qa_outputs = nn.Linear(self.ernie_layout.config.hidden_size,
                                    self.num_labels)

    def get_input_embeddings(self):
        return self.ernie_layout.embeddings.word_embeddings

    def forward(self,
                input_ids=None,
                bbox=None,
                image=None,
                attention_mask=None,
                token_type_ids=None,
                position_ids=None,
                head_mask=None,
                start_positions=None,
                end_positions=None):
        outputs = self.ernie_layout(
            input_ids=input_ids,
            bbox=bbox,
            image=image,
            attention_mask=attention_mask,
            token_type_ids=token_type_ids,
            position_ids=position_ids,
            head_mask=head_mask,
        )
        seq_length = input_ids.shape[1]
        sequence_output = outputs[0][:, :seq_length]
        sequence_output = self.dropout(sequence_output)

        if token_type_ids is not None:
            span_mask = -token_type_ids * 1e8
        else:
            span_mask = 0

        logits = self.qa_outputs(sequence_output)
        start_logits, end_logits = torch.split(logits,
                                               split_size_or_sections=2,
                                               dim=-1)
        start_logits = start_logits.squeeze(-1) + span_mask
        end_logits = end_logits.squeeze(-1) + span_mask

        outputs = (start_logits, end_logits) + outputs[2:]

        total_loss = None
        if start_positions is not None and end_positions is not None:
            # If we are on multi-GPU, split_trim_underline_fake_relation add a dimension
            if len(start_positions.shape) > 1:
                start_positions = start_positions.squeeze(-1)
            if len(end_positions.shape) > 1:
                end_positions = end_positions.squeeze(-1)
            # sometimes the start/end positions are outside our model inputs, we ignore these terms
            ignored_index = start_logits.shape[1]
            start_positions = start_positions.clip(0, ignored_index)
            end_positions = end_positions.clip(0, ignored_index)

            loss_fct = nn.CrossEntropyLoss(ignore_index=ignored_index)
            start_loss = loss_fct(start_logits, start_positions)
            end_loss = loss_fct(end_logits, end_positions)
            total_loss = (start_loss + end_loss) / 2

        if not total_loss:
            return outputs
        else:
            outputs = (total_loss, ) + outputs
            return outputs


class MyLstmErnieLayoutModel(ErnieLayoutPretrainedModel):
    """
    The bare ErnieLayout Model outputting raw hidden-states.

    This model inherits from :class:`~uiex_p.transformers.model_utils.PretrainedModel`.
    Refer to the superclass documentation for the generic methods.

    This model is also a Paddle `paddle.nn.Layer <https://www.paddlepaddle.org.cn/documentation
    /docs/en/api/paddle/fluid/dygraph/layers/Layer_en.html>`__ subclass. Use it as a regular Paddle Layer
    and refer to the Paddle documentation for all matter related to general usage and behavior.

    Args:
        vocab_size (`int`):
            Vocabulary size of the XLNet model. Defines the number of different tokens that can
            be represented by the `inputs_ids` passed when calling ErnieLayoutModel.
        hidden_size (`int`, optional):
            Dimensionality of the encoder layers and the pooler layer. Defaults to ``768``.
        num_hidden_layers (`int`, optional):
            Number of hidden layers in the Transformer encoder. Defaults to ``12``.
        num_attention_heads (`int`, optional):
            Number of attention heads for each attention layer in the Transformer encoder.
            Defaults to ``12``.
        intermediate_size (`int`, optional):
            Dimensionality of the "intermediate" (often named feed-forward) layer in the Transformer encoder.
            Defaults to ``3072``.
        hidden_act (`str`, optional):
            The non-linear activation function in the feed-forward layer.
            ``"gelu"``, ``"relu"`` and any other paddle supported activation functions
            are supported. Defaults to ``"gelu"``.
        hidden_dropout_prob (`float`, optional):
            The dropout probability for all fully connected layers in the embeddings and encoder.
            Defaults to ``0.1``.
        attention_probs_dropout_prob (`float`, optional):
            The dropout probability for all fully connected layers in the pooler.
            Defaults to ``0.1``.
        initializer_range (`float`, optional):
            The standard deviation of the truncated_normal_initializer for initializing all weight matrices.
            Defaults to ``0.02``.
    """

    def __init__(
        self,
        config: ErnieLayoutConfig,
        with_pool='tanh',
    ):
        super(MyLstmErnieLayoutModel, self).__init__(config)
        self.config = config
        self.has_visual_segment_embedding = config.has_visual_segment_embedding
        self.embeddings = ErnieLayoutEmbeddings(config)

        self.visual = VisualBackbone(config)
        self.visual_proj = nn.Linear(config.image_feature_pool_shape[-1],
                                     config.hidden_size)
        self.visual_act_fn = nn.GELU()
        if self.has_visual_segment_embedding:
            self.visual_segment_embedding = nn.Parameter(torch.normal(mean=0., std=config.initializer_range,
                                                                      size=(config.hidden_size,),
                                                                      dtype=self.embedding.weight.dtype))
            # self.visual_segment_embedding = self.create_parameter(
            #     shape=[
            #         config["hidden_size"],
            #     ],
            #     dtype=self.embedding.weight.dtype)
        self.visual_LayerNorm = nn.LayerNorm(config.hidden_size,
                                             eps=config.layer_norm_eps)
        self.visual_dropout = nn.Dropout(config.hidden_dropout_prob)
        self.encoder = ErnieLayoutEncoder(config)
        self.pooler = ErnieLayoutPooler(config.hidden_size, with_pool)

        self.reduce_lstm = nn.LSTM(config.hidden_size, config.hidden_size, num_layers=1, batch_first=True,
                                   dropout=config.hidden_dropout_prob, bidirectional=False)
        self.layerNorm = nn.LayerNorm(config.hidden_size, eps=config.layer_norm_eps)

    def _calc_text_embeddings(self, input_ids, bbox, position_ids,
                              token_type_ids):
        words_embeddings = self.embeddings.word_embeddings(input_ids)
        position_embeddings = self.embeddings.position_embeddings(position_ids)
        x1, y1, x2, y2, h, w = self.embeddings._cal_spatial_position_embeddings(
            bbox)
        token_type_embeddings = self.embeddings.token_type_embeddings(
            token_type_ids)
        embeddings = words_embeddings + position_embeddings + x1 + y1 + x2 + y2 + w + h + token_type_embeddings

        embeddings = self.embeddings.LayerNorm(embeddings)
        embeddings = self.embeddings.dropout(embeddings)
        return embeddings

    def _calc_img_embeddings(self, image, bbox, position_ids):
        if image is not None:
            visual_embeddings = self.visual_act_fn(
                self.visual_proj(self.visual(image)))
        position_embeddings = self.embeddings.position_embeddings(position_ids)
        x1, y1, x2, y2, h, w = self.embeddings._cal_spatial_position_embeddings(
            bbox)
        if image is not None:
            embeddings = visual_embeddings + position_embeddings + x1 + y1 + x2 + y2 + w + h
        else:
            embeddings = position_embeddings + x1 + y1 + x2 + y2 + w + h

        if self.has_visual_segment_embedding:
            embeddings += self.visual_segment_embedding
        embeddings = self.visual_LayerNorm(embeddings)
        embeddings = self.visual_dropout(embeddings)
        return embeddings

    def _calc_visual_bbox(self, image_feature_pool_shape, bbox, visual_shape):
        # visual_bbox_x = (torch.arange(
        #     0,
        #     1000 * (image_feature_pool_shape[1] + 1),
        #     1000,
        #     dtype=bbox.dtype,
        # ) // image_feature_pool_shape[1])
        #  去除__floordiv__ UserWarning.
        visual_bbox_x = torch.div(torch.arange(0, 1000 * (image_feature_pool_shape[1] + 1), 1000, dtype=bbox.dtype,),
                                  image_feature_pool_shape[1], rounding_mode='trunc')
        # visual_bbox_y = (torch.arange(
        #     0,
        #     1000 * (image_feature_pool_shape[0] + 1),
        #     1000,
        #     dtype=bbox.dtype,
        # ) // image_feature_pool_shape[0])
        visual_bbox_y = torch.div(torch.arange(0, 1000 * (image_feature_pool_shape[0] + 1), 1000, dtype=bbox.dtype,),
                                  image_feature_pool_shape[0], rounding_mode='trunc')
        expand_shape = image_feature_pool_shape[0:2]
        visual_bbox = torch.stack(
            [
                visual_bbox_x[:-1].expand(expand_shape),
                visual_bbox_y[:-1].expand(expand_shape[::-1]).transpose(1, 0),
                visual_bbox_x[1:].expand(expand_shape),
                visual_bbox_y[1:].expand(expand_shape[::-1]).transpose(1, 0),
            ],
            dim=-1,
        ).reshape([expand_shape[0] * expand_shape[1],
                   bbox.size(-1)])

        visual_bbox = visual_bbox.expand(
            [visual_shape[0], visual_bbox.shape[0], visual_bbox.shape[1]])
        return visual_bbox.to(bbox.device)

    def resize_position_embeddings(self, new_num_position_embeddings):
        """
        Resizes position embeddings of the model if `new_num_position_embeddings != config["max_position_embeddings"]`.

        Arguments:
            new_num_position_embeddings (`int`):
                The number of new position embedding matrix. If position embeddings are learned, increasing the size
                will add newly initialized vectors at the end, whereas reducing the size will remove vectors from the
                end.
        """
        num_position_embeds_diff = new_num_position_embeddings - self.config.max_position_embeddings

        # no resizing needs to be done if the length stays the same
        if num_position_embeds_diff == 0:
            return

        # logger.info(
        #     f"Setting `config.max_position_embeddings={new_num_position_embeddings}`..."
        # )
        self.config.max_position_embeddings = new_num_position_embeddings

        old_position_embeddings_weight = self.embeddings.position_embeddings.weight

        self.embeddings.position_embeddings = nn.Embedding(
            self.config.max_position_embeddings, self.config.hidden_size)

        with torch.no_grad():
            if num_position_embeds_diff > 0:
                self.embeddings.position_embeddings.weight[:
                                                           -num_position_embeds_diff] = old_position_embeddings_weight
            else:
                self.embeddings.position_embeddings.weight = old_position_embeddings_weight[:
                                                                                            num_position_embeds_diff]

    def forward(self,
                input_ids=None,
                bbox=None,
                image=None,
                token_type_ids=None,
                position_ids=None,
                attention_mask=None,
                head_mask=None,
                output_hidden_states=False,
                output_attentions=False,
                return_dict=False):
        input_shape = list(input_ids.size())
        visual_shape = list(input_shape)
        visual_shape[1] = self.config.image_feature_pool_shape[
            0] * self.config.image_feature_pool_shape[1]
        visual_bbox = self._calc_visual_bbox(
            self.config.image_feature_pool_shape, bbox, visual_shape)

        final_bbox = torch.cat([bbox, visual_bbox], dim=1)
        if attention_mask is None:
            attention_mask = torch.ones(input_shape, device=input_ids.device)

        visual_attention_mask = torch.ones(visual_shape, device=input_ids.device)

        attention_mask = attention_mask.to(visual_attention_mask.dtype)

        final_attention_mask = torch.cat(
            [attention_mask, visual_attention_mask], dim=1)

        if token_type_ids is None:
            token_type_ids = torch.zeros(input_shape, dtype=torch.int64, device=input_ids.device)

        if position_ids is None:
            seq_length = input_shape[1]
            position_ids = self.embeddings.position_ids[:, :seq_length]
            position_ids = position_ids.expand(input_shape)

        visual_position_ids = torch.arange(0, visual_shape[1], device=input_ids.device).expand(
            [input_shape[0], visual_shape[1]])
        final_position_ids = torch.cat([position_ids, visual_position_ids],
                                       dim=1)

        if bbox is None:
            bbox = torch.zeros(input_shape + [4])

        text_layout_emb = self._calc_text_embeddings(
            input_ids=input_ids,
            bbox=bbox,
            token_type_ids=token_type_ids,
            position_ids=position_ids,
        )

        visual_emb = self._calc_img_embeddings(
            image=image,
            bbox=visual_bbox,
            position_ids=visual_position_ids,
        )
        final_emb = torch.cat([text_layout_emb, visual_emb], dim=1)
        extended_attention_mask = final_attention_mask.unsqueeze(1).unsqueeze(2)

        extended_attention_mask = (1.0 - extended_attention_mask) * -10000.0

        # 把一个文本框中的所有子词输入到一个lstm中。需要修改final_emb, final_bbox, final_position_ids, extended_attention_mask
        unique_boxes = []
        text_layout_emb_reduced = []
        length_reduced = []
        counts = []
        for box_b, mask_b, t_l_emb in zip(bbox, attention_mask, text_layout_emb):
            mask_b = mask_b.to(torch.bool)
            unique_box, count = torch.unique_consecutive(box_b[mask_b], return_counts=True, dim=0)
            unique_boxes.append(unique_box)
            counts.append(count)
            length_reduced.append(len(count))
            text_layout_emb_reduced.extend(torch.split(t_l_emb[mask_b], count.tolist()))

        counts = torch.cat(counts, dim=0)
        _, idx_sorted = torch.sort(counts, dim=0, descending=True)

        # text_layout_emb_reduced = [torch.mean(text_layout_emb_reduced[idx], dim=0, keepdim=True) for idx in idx_sorted]
        # text_layout_emb_reduced = torch.cat(text_layout_emb_reduced, dim=0)

        text_layout_emb_reduced = [text_layout_emb_reduced[idx] for idx in idx_sorted]
        text_layout_emb_reduced = pack_sequence(text_layout_emb_reduced)
        text_layout_emb_reduced = self.reduce_lstm(text_layout_emb_reduced)[1][0]
        # # text_layout_emb_reduced = (text_layout_emb_reduced[0] + text_layout_emb_reduced[1]) / 2
        text_layout_emb_reduced = text_layout_emb_reduced[0]

        text_layout_emb = torch.split(text_layout_emb_reduced[torch.sort(idx_sorted, dim=0)[1]], length_reduced)
        text_layout_emb = pad_sequence(text_layout_emb, batch_first=True)
        text_layout_emb = self.layerNorm(text_layout_emb)
        final_emb = torch.cat([text_layout_emb, visual_emb], dim=1)

        position_ids = []
        attention_mask_reduced = []
        for l in length_reduced:
            position_ids.append(torch.arange(l, dtype=torch.int64))
            attention_mask_reduced.append(torch.ones(l, dtype=visual_attention_mask.dtype))
        position_ids = pad_sequence(position_ids, batch_first=True, padding_value=0).to(input_ids.device)
        final_position_ids = torch.cat((position_ids, visual_position_ids), dim=1)
        attention_mask_reduced = pad_sequence(attention_mask_reduced, batch_first=True, padding_value=0).to(input_ids.device)
        final_attention_mask = torch.cat((attention_mask_reduced, visual_attention_mask), dim=1)
        extended_attention_mask = final_attention_mask.unsqueeze(1).unsqueeze(2)
        extended_attention_mask = (1.0 - extended_attention_mask) * -10000.0
        unique_boxes = pad_sequence(unique_boxes, batch_first=True, padding_value=0).to(input_ids.device)
        final_bbox = torch.cat((unique_boxes, visual_bbox), dim=1)


        if head_mask is not None:
            if head_mask.dim() == 1:
                head_mask = head_mask.unsqueeze(0).unsqueeze(0).unsqueeze(
                    -1).unsqueeze(-1)
                head_mask = head_mask.expand(self.config.num_hidden_layers,
                                             -1, -1, -1, -1)
            elif head_mask.dim() == 2:
                head_mask = head_mask.unsqueeze(1).unsqueeze(-1).unsqueeze(-1)
        else:
            head_mask = [None] * self.config.num_hidden_layers

        encoder_outputs = self.encoder(
            final_emb,
            extended_attention_mask,
            bbox=final_bbox,
            position_ids=final_position_ids,
            output_attentions=output_attentions,
            output_hidden_states=output_hidden_states,
            return_dict=return_dict,
        )
        sequence_output = encoder_outputs[0]
        pooled_output = self.pooler(sequence_output)

        if not return_dict:
            return (sequence_output, pooled_output) + encoder_outputs[1:]

        return BaseModelOutputWithPoolingAndCrossAttentions(
            last_hidden_state=sequence_output,
            pooler_output=pooled_output,
            past_key_values=encoder_outputs.past_key_values,
            hidden_states=encoder_outputs.hidden_states,
            attentions=encoder_outputs.attentions,
            cross_attentions=encoder_outputs.cross_attentions,
        )


class MyLstmWholeErnieLayoutModel(ErnieLayoutPretrainedModel):
    """
    The bare ErnieLayout Model outputting raw hidden-states.

    This model inherits from :class:`~uiex_p.transformers.model_utils.PretrainedModel`.
    Refer to the superclass documentation for the generic methods.

    This model is also a Paddle `paddle.nn.Layer <https://www.paddlepaddle.org.cn/documentation
    /docs/en/api/paddle/fluid/dygraph/layers/Layer_en.html>`__ subclass. Use it as a regular Paddle Layer
    and refer to the Paddle documentation for all matter related to general usage and behavior.

    Args:
        vocab_size (`int`):
            Vocabulary size of the XLNet model. Defines the number of different tokens that can
            be represented by the `inputs_ids` passed when calling ErnieLayoutModel.
        hidden_size (`int`, optional):
            Dimensionality of the encoder layers and the pooler layer. Defaults to ``768``.
        num_hidden_layers (`int`, optional):
            Number of hidden layers in the Transformer encoder. Defaults to ``12``.
        num_attention_heads (`int`, optional):
            Number of attention heads for each attention layer in the Transformer encoder.
            Defaults to ``12``.
        intermediate_size (`int`, optional):
            Dimensionality of the "intermediate" (often named feed-forward) layer in the Transformer encoder.
            Defaults to ``3072``.
        hidden_act (`str`, optional):
            The non-linear activation function in the feed-forward layer.
            ``"gelu"``, ``"relu"`` and any other paddle supported activation functions
            are supported. Defaults to ``"gelu"``.
        hidden_dropout_prob (`float`, optional):
            The dropout probability for all fully connected layers in the embeddings and encoder.
            Defaults to ``0.1``.
        attention_probs_dropout_prob (`float`, optional):
            The dropout probability for all fully connected layers in the pooler.
            Defaults to ``0.1``.
        initializer_range (`float`, optional):
            The standard deviation of the truncated_normal_initializer for initializing all weight matrices.
            Defaults to ``0.02``.
    """

    def __init__(
        self,
        config: ErnieLayoutConfig,
        with_pool='tanh',
    ):
        super(MyLstmWholeErnieLayoutModel, self).__init__(config)
        self.config = config
        self.has_visual_segment_embedding = config.has_visual_segment_embedding
        self.embeddings = ErnieLayoutEmbeddings(config)

        self.visual = VisualBackbone(config)
        self.visual_proj = nn.Linear(config.image_feature_pool_shape[-1],
                                     config.hidden_size)
        self.visual_act_fn = nn.GELU()
        if self.has_visual_segment_embedding:
            self.visual_segment_embedding = nn.Parameter(torch.normal(mean=0., std=config.initializer_range,
                                                                      size=(config.hidden_size,),
                                                                      dtype=self.embedding.weight.dtype))
            # self.visual_segment_embedding = self.create_parameter(
            #     shape=[
            #         config["hidden_size"],
            #     ],
            #     dtype=self.embedding.weight.dtype)
        self.visual_LayerNorm = nn.LayerNorm(config.hidden_size,
                                             eps=config.layer_norm_eps)
        self.visual_dropout = nn.Dropout(config.hidden_dropout_prob)
        self.encoder = ErnieLayoutEncoder(config)
        self.pooler = ErnieLayoutPooler(config.hidden_size, with_pool)

        self.reduce_lstm = nn.LSTM(config.hidden_size, config.hidden_size, num_layers=1, batch_first=True,
                                   dropout=config.hidden_dropout_prob, bidirectional=True)
        self.layerNorm = nn.LayerNorm(config.hidden_size, eps=config.layer_norm_eps)

    def _calc_text_embeddings(self, input_ids, bbox, position_ids,
                              token_type_ids):
        words_embeddings = self.embeddings.word_embeddings(input_ids)
        position_embeddings = self.embeddings.position_embeddings(position_ids)
        x1, y1, x2, y2, h, w = self.embeddings._cal_spatial_position_embeddings(
            bbox)
        token_type_embeddings = self.embeddings.token_type_embeddings(
            token_type_ids)
        embeddings = words_embeddings + position_embeddings + x1 + y1 + x2 + y2 + w + h + token_type_embeddings

        embeddings = self.embeddings.LayerNorm(embeddings)
        embeddings = self.embeddings.dropout(embeddings)
        return embeddings

    def _calc_img_embeddings(self, image, bbox, position_ids):
        if image is not None:
            visual_embeddings = self.visual_act_fn(
                self.visual_proj(self.visual(image)))
        position_embeddings = self.embeddings.position_embeddings(position_ids)
        x1, y1, x2, y2, h, w = self.embeddings._cal_spatial_position_embeddings(
            bbox)
        if image is not None:
            embeddings = visual_embeddings + position_embeddings + x1 + y1 + x2 + y2 + w + h
        else:
            embeddings = position_embeddings + x1 + y1 + x2 + y2 + w + h

        if self.has_visual_segment_embedding:
            embeddings += self.visual_segment_embedding
        embeddings = self.visual_LayerNorm(embeddings)
        embeddings = self.visual_dropout(embeddings)
        return embeddings

    def _calc_visual_bbox(self, image_feature_pool_shape, bbox, visual_shape):
        # visual_bbox_x = (torch.arange(
        #     0,
        #     1000 * (image_feature_pool_shape[1] + 1),
        #     1000,
        #     dtype=bbox.dtype,
        # ) // image_feature_pool_shape[1])
        #  去除__floordiv__ UserWarning.
        visual_bbox_x = torch.div(torch.arange(0, 1000 * (image_feature_pool_shape[1] + 1), 1000, dtype=bbox.dtype,),
                                  image_feature_pool_shape[1], rounding_mode='trunc')
        # visual_bbox_y = (torch.arange(
        #     0,
        #     1000 * (image_feature_pool_shape[0] + 1),
        #     1000,
        #     dtype=bbox.dtype,
        # ) // image_feature_pool_shape[0])
        visual_bbox_y = torch.div(torch.arange(0, 1000 * (image_feature_pool_shape[0] + 1), 1000, dtype=bbox.dtype,),
                                  image_feature_pool_shape[0], rounding_mode='trunc')
        expand_shape = image_feature_pool_shape[0:2]
        visual_bbox = torch.stack(
            [
                visual_bbox_x[:-1].expand(expand_shape),
                visual_bbox_y[:-1].expand(expand_shape[::-1]).transpose(1, 0),
                visual_bbox_x[1:].expand(expand_shape),
                visual_bbox_y[1:].expand(expand_shape[::-1]).transpose(1, 0),
            ],
            dim=-1,
        ).reshape([expand_shape[0] * expand_shape[1],
                   bbox.size(-1)])

        visual_bbox = visual_bbox.expand(
            [visual_shape[0], visual_bbox.shape[0], visual_bbox.shape[1]])
        return visual_bbox.to(bbox.device)

    def resize_position_embeddings(self, new_num_position_embeddings):
        """
        Resizes position embeddings of the model if `new_num_position_embeddings != config["max_position_embeddings"]`.

        Arguments:
            new_num_position_embeddings (`int`):
                The number of new position embedding matrix. If position embeddings are learned, increasing the size
                will add newly initialized vectors at the end, whereas reducing the size will remove vectors from the
                end.
        """
        num_position_embeds_diff = new_num_position_embeddings - self.config.max_position_embeddings

        # no resizing needs to be done if the length stays the same
        if num_position_embeds_diff == 0:
            return

        # logger.info(
        #     f"Setting `config.max_position_embeddings={new_num_position_embeddings}`..."
        # )
        self.config.max_position_embeddings = new_num_position_embeddings

        old_position_embeddings_weight = self.embeddings.position_embeddings.weight

        self.embeddings.position_embeddings = nn.Embedding(
            self.config.max_position_embeddings, self.config.hidden_size)

        with torch.no_grad():
            if num_position_embeds_diff > 0:
                self.embeddings.position_embeddings.weight[:
                                                           -num_position_embeds_diff] = old_position_embeddings_weight
            else:
                self.embeddings.position_embeddings.weight = old_position_embeddings_weight[:
                                                                                            num_position_embeds_diff]

    def forward(self,
                input_ids=None,
                bbox=None,
                image=None,
                token_type_ids=None,
                position_ids=None,
                attention_mask=None,
                head_mask=None,
                output_hidden_states=False,
                output_attentions=False,
                return_dict=False):
        input_shape = list(input_ids.size())
        visual_shape = list(input_shape)
        visual_shape[1] = self.config.image_feature_pool_shape[
            0] * self.config.image_feature_pool_shape[1]
        visual_bbox = self._calc_visual_bbox(
            self.config.image_feature_pool_shape, bbox, visual_shape)

        final_bbox = torch.cat([bbox, visual_bbox], dim=1)
        if attention_mask is None:
            attention_mask = torch.ones(input_shape, device=input_ids.device)

        visual_attention_mask = torch.ones(visual_shape, device=input_ids.device)

        attention_mask = attention_mask.to(visual_attention_mask.dtype)

        final_attention_mask = torch.cat(
            [attention_mask, visual_attention_mask], dim=1)

        if token_type_ids is None:
            token_type_ids = torch.zeros(input_shape, dtype=torch.int64, device=input_ids.device)

        if position_ids is None:
            seq_length = input_shape[1]
            position_ids = self.embeddings.position_ids[:, :seq_length]
            position_ids = position_ids.expand(input_shape)

        visual_position_ids = torch.arange(0, visual_shape[1], device=input_ids.device).expand(
            [input_shape[0], visual_shape[1]])
        final_position_ids = torch.cat([position_ids, visual_position_ids],
                                       dim=1)

        if bbox is None:
            bbox = torch.zeros(input_shape + [4])

        text_layout_emb = self._calc_text_embeddings(
            input_ids=input_ids,
            bbox=bbox,
            token_type_ids=token_type_ids,
            position_ids=position_ids,
        )

        visual_emb = self._calc_img_embeddings(
            image=image,
            bbox=visual_bbox,
            position_ids=visual_position_ids,
        )
        final_emb = torch.cat([text_layout_emb, visual_emb], dim=1)
        extended_attention_mask = final_attention_mask.unsqueeze(1).unsqueeze(2)

        extended_attention_mask = (1.0 - extended_attention_mask) * -10000.0

        # 把所有子词输入到一个lstm中。需要修改final_emb, final_bbox, final_position_ids, extended_attention_mask
        length = torch.sum(attention_mask, dim=1)
        _, idx_sorted = torch.sort(length, dim=0, descending=True)
        text_layout_emb = text_layout_emb[idx_sorted]
        text_layout_emb = pack_padded_sequence(text_layout_emb, length[idx_sorted].tolist(), batch_first=True)
        text_layout_emb = self.reduce_lstm(text_layout_emb)[0]
        text_layout_emb = pad_packed_sequence(text_layout_emb, batch_first=True)[0]
        text_layout_emb = (text_layout_emb[:, :, :self.config.hidden_size] +
                           text_layout_emb[:, :, self.config.hidden_size:]) / 2
        text_layout_emb = text_layout_emb[torch.sort(idx_sorted, dim=0)[1]]
        # length = length[torch.sort(idx_sorted, dim=0)[1]]

        unique_boxes = []
        text_layout_emb_reduced = []
        length_reduced = []
        counts = []
        for box_b, mask_b, t_l_emb in zip(bbox, attention_mask, text_layout_emb):
            mask_b = mask_b.to(torch.bool)
            unique_box, count = torch.unique_consecutive(box_b[mask_b], return_counts=True, dim=0)
            unique_boxes.append(unique_box)
            counts.append(count)
            length_reduced.append(len(count))
            text_layout_emb_reduced.append(torch.sum(pad_sequence(torch.split(t_l_emb[mask_b],
                                                                              count.tolist()),
                                                                  batch_first=True), dim=1) /
                                           torch.tensor(count, device=input_ids.device).view(-1, 1))

        text_layout_emb = pad_sequence(text_layout_emb_reduced, batch_first=True)
        text_layout_emb = self.layerNorm(text_layout_emb)
        final_emb = torch.cat([text_layout_emb, visual_emb], dim=1)

        position_ids = []
        attention_mask_reduced = []
        for l in length_reduced:
            position_ids.append(torch.arange(l, dtype=torch.int64))
            attention_mask_reduced.append(torch.ones(l, dtype=visual_attention_mask.dtype))
        position_ids = pad_sequence(position_ids, batch_first=True, padding_value=0).to(input_ids.device)
        final_position_ids = torch.cat((position_ids, visual_position_ids), dim=1)
        attention_mask_reduced = pad_sequence(attention_mask_reduced, batch_first=True, padding_value=0).to(input_ids.device)
        final_attention_mask = torch.cat((attention_mask_reduced, visual_attention_mask), dim=1)
        extended_attention_mask = final_attention_mask.unsqueeze(1).unsqueeze(2)
        extended_attention_mask = (1.0 - extended_attention_mask) * -10000.0
        unique_boxes = pad_sequence(unique_boxes, batch_first=True, padding_value=0).to(input_ids.device)
        final_bbox = torch.cat((unique_boxes, visual_bbox), dim=1)

        if head_mask is not None:
            if head_mask.dim() == 1:
                head_mask = head_mask.unsqueeze(0).unsqueeze(0).unsqueeze(
                    -1).unsqueeze(-1)
                head_mask = head_mask.expand(self.config.num_hidden_layers,
                                             -1, -1, -1, -1)
            elif head_mask.dim() == 2:
                head_mask = head_mask.unsqueeze(1).unsqueeze(-1).unsqueeze(-1)
        else:
            head_mask = [None] * self.config.num_hidden_layers

        encoder_outputs = self.encoder(
            final_emb,
            extended_attention_mask,
            bbox=final_bbox,
            position_ids=final_position_ids,
            output_attentions=output_attentions,
            output_hidden_states=output_hidden_states,
            return_dict=return_dict,
        )
        sequence_output = encoder_outputs[0]
        pooled_output = self.pooler(sequence_output)

        if not return_dict:
            return (sequence_output, pooled_output) + encoder_outputs[1:]

        return BaseModelOutputWithPoolingAndCrossAttentions(
            last_hidden_state=sequence_output,
            pooler_output=pooled_output,
            past_key_values=encoder_outputs.past_key_values,
            hidden_states=encoder_outputs.hidden_states,
            attentions=encoder_outputs.attentions,
            cross_attentions=encoder_outputs.cross_attentions,
        )


if __name__ == '__main__':
    import paddle
    pass
    # from collections import OrderedDict
    # import paddle
    # import math
    # import numpy as np
    # paddle.set_device('cpu')
    # config_t = ErnieLayoutConfig()
    # config_p = {
    #         "attention_probs_dropout_prob": 0.1,
    #         "bos_token_id": 0,
    #         "coordinate_size": 128,
    #         "eos_token_id": 2,
    #         "gradient_checkpointing": False,
    #         "has_relative_attention_bias": True,
    #         "has_spatial_attention_bias": True,
    #         "has_visual_segment_embedding": False,
    #         "hidden_act": "gelu",
    #         "hidden_dropout_prob": 0.1,
    #         "hidden_size": 768,
    #         "image_feature_pool_shape": [7, 7, 256],
    #         "initializer_range": 0.02,
    #         "intermediate_size": 3072,
    #         "layer_norm_eps": 1e-12,
    #         "max_2d_position_embeddings": 1024,
    #         "max_position_embeddings": 514,
    #         "max_rel_2d_pos": 256,
    #         "max_rel_pos": 128,
    #         "model_type": "ernie_layout",
    #         "num_attention_heads": 12,
    #         "num_hidden_layers": 12,
    #         "output_past": True,
    #         "pad_token_id": 1,
    #         "shape_size": 128,
    #         "rel_2d_pos_bins": 64,
    #         "rel_pos_bins": 32,
    #         "type_vocab_size": 100,
    #         "vocab_size": 250002,
    #     }
    # # model_t = ErnieLayoutModel(config_t)
    # model_t = ErnieLayoutForSequenceClassification(config_t)
    # print(model_t)
    # from uiex_p.transformers import ErnieLayoutModel as ErnieLayoutModel2
    # from uiex_p.transformers import ErnieLayoutForSequenceClassification as ErnieLayoutForSequenceClassification2
    #
    # # model_p = ErnieLayoutModel2(**config_p).to('cpu')
    # model_p = ErnieLayoutForSequenceClassification2.from_pretrained('ernie-layoutx-base-uncased').to('cpu')
    # print(model_p)
    #
    # model_t.eval()
    # model_p.eval()
    # mismatch = [(k1, k2) for k1, k2 in zip([k for k in model_t.state_dict().keys() if 'tracked' not in k],
    #                                        [k.replace('._mean', '.running_mean').replace('._variance', '.running_var')
    #                                         if '._mean' in k or '._variance' in k else k
    #                                         for k in model_p.state_dict().keys()])
    #             if k1 != k2]
    # print('mismatch:', mismatch)
    # state_p = model_p.state_dict()
    # state_t = OrderedDict()
    # for k, v in state_p.items():
    #     if '._mean' in k:
    #         k_ = k.replace('._mean', '.running_mean')
    #     elif '._variance' in k:
    #         k_ = k.replace('._variance', '.running_var')
    #     else:
    #         k_ = k
    #     v_arr = v.numpy()
    #     v_ = torch.tensor(v_arr.T if '.weight' in k_ and 'embeddings.' not in k_ and 'conv' not in k_ and len(v_arr.shape) == 2 else v_arr)
    #     state_t[k_] = v_
    #
    # for (k1, v1), (k2, v2) in zip(state_t.items(), [(k, v) for k, v in model_t.state_dict().items() if 'tracked' not in k]):
    #     assert k1 == k2
    #     print(k1, list(v1.size()), list(v2.size()))
    # print(model_t.load_state_dict(state_t, strict=False))
    #
    #
    # inputs_p = paddle.load('uiex_inputs.params')
    #
    # inputs_p = dict([(k, paddle.to_tensor(v, place='cpu')) for k, v in inputs_p.items()])
    # if 'labels' in inputs_p:
    #     inputs_p['labels'] = paddle.to_tensor([0] * inputs_p['labels'].shape[0])
    # # inputs_p.pop('labels')
    # inputs_t = dict([(k, torch.tensor(v.numpy())) for k, v in inputs_p.items()])
    # with torch.no_grad():
    #     output_t = model_t(**inputs_t)
    # with paddle.no_grad():
    #     output_p = model_p(**inputs_p)
    #
    # acc = 4
    # acc_ = math.pow(10, -acc)
    # acc_guaranteed = True
    # assert len(output_t) == len(output_p)
    # for i in range(len(output_t)):
    #     acc_guaranteed &= np.all(np.abs(output_t[i].numpy() - output_p[i].numpy()) < acc_)
    # print('保证小数点后%d位精度：' % acc, acc_guaranteed)



    model = ErnieLayoutForTokenClassification(ErnieLayoutConfig())
    from paddlenlp.transformers import ErnieLayoutForTokenClassification as ErnieLayoutForTokenClassification2
    paddle.set_device('cpu')
    model = ErnieLayoutForTokenClassification2.from_pretrained('ernie-layoutx-base-uncased', num_classes=4)
    print(model)