# from .utils import configMap, read_metadata, write_metadata
# from .database import DatabaseClient, session_scope
# from sqlalchemy import text
# from .sync_tugraph import TuGraphData
# from tugraph import GraphDatabase
# from common.logger import logger
# from common.http_client import hc
import os
import re
import traceback
from collections import defaultdict
from pathlib import Path
from map.sync_data_v2.database import DatabaseClient
from map.sync_data_v2.utils import configMap
from common.http_client import hc
from map.sync_data_v2.utils import read_metadata, write_metadata
from map.sync_data_v2.ocr_layout import ocr_layout
from syntactic_analysis.parsing import parse_result
from map.models import GraphItem, GraphImage, GraphWord
from common.logger import logger

# model_path = "../../static/config/rnn_standard_layer_2_bi_True_demo.parser"
model_path = Path(__file__).parent.parent.parent / "static/config/rnn_standard_layer_2_bi_True_demo.parser"


class SyncData(DatabaseClient):

    def __init__(self, database_uri=None):
        self.config = configMap()
        super().__init__(database_uri if database_uri else self.config["DATABASE_URI"])
        self.db = self.session_factory()
        self.server_api = self.config["SERVER_API"]
        self.field_schema = self.config["FIELD_SCHEMA"]
        self.server_config = dict()
        self.node_file_cache = dict()
        self.is_run = False

    def get_server_config(self):
        try:
            rep = hc.request_api(self.server_api["getFileServers"]["method"],
                                 self.server_api["getFileServers"]["url"])
            ret = rep.json()
            self.server_config = {item["alias"]: item["rootPath"] for item in ret["data"]["list"]}
        except Exception as e:
            logger.error(traceback.format_exc())

    def get_node_file(self, _id):
        try:
            rep = hc.request_api(self.server_api["getDataFileList"]["method"],
                                 self.server_api["getDataFileList"]["url"], json={
                    "structureId": _id
                })
            ret = rep.json()
            node_files = ret["data"]["page"]["list"]
            from collections import defaultdict
            node_file_info = defaultdict(lambda: [])
            for item in node_files:
                node_file_info[item["dataId"]].append(item["filePath"])
            self.node_file_cache = node_file_info
            return node_file_info
        except Exception as e:
            logger.error(traceback.format_exc())

    def get_node_data(self, _id, page_index=1):
        """
        获取档案数据
        获取档案数据对应文件信息
        """
        try:
            last_id = read_metadata(self.config.LAST_METADATA_PATH, f"{_id}")
            getDataList_pageSize = self.config["SERVER_API"]["getDataList"]["param"]["pageSize"]
            if last_id != -1:
                if last_id < getDataList_pageSize:
                    pass
                elif last_id == getDataList_pageSize:
                    page_index += 1
                else:
                    page_index = int(last_id / getDataList_pageSize) + 1
            while True:
                # print(f"********_id {_id}   page_index {page_index}*************")
                rep = hc.request_api(self.server_api["getDataList"]["method"],
                                     self.server_api["getDataList"]["url"], json={
                        "structureId": _id,
                        "pageSize": getDataList_pageSize,
                        "pageIndex": page_index
                    })
                ret = rep.json()
                node_values = ret["data"]["page"]["list"]
                if not node_values:
                    break
                node_fields = ret["data"]["viewFields"]
                node_fields = {item["title"]: item["name"] for item in node_fields}
                dataId = None
                for item in node_values:
                    dataId = item["id"]
                    if last_id != -1 and dataId <= last_id:
                        continue
                    item_v = item["values"]
                    node = {k: item_v[v] for k, v in node_fields.items()}
                    # print(node)
                    new_node = {self.field_schema[k]: v for k, v in node.items() if k in self.field_schema}
                    # print(new_node)
                    if new_node.get("dh"):
                        node_files = self.node_file_cache.get(dataId)
                        # print(node_files)
                        logger.info(f'{new_node["dh"]} 开始同步...')
                        self.sync_database(new_node, node_files)
                    else:
                        logger.warning(f"{_id} no 档号 field")
                write_metadata(self.config.LAST_METADATA_PATH, dataId, f"{_id}")
                page_index += 1
                break
        except Exception as e:
            logger.error(traceback.format_exc())

    def sync_database(self, new_node, node_files):
        """
        同步数据
        """
        graph_item_object = self.db.query(GraphItem).filter(GraphItem.dh == new_node["dh"]).first()
        if graph_item_object:
            return
        graph_item = GraphItem(**new_node)
        self.db.add(graph_item)
        self.db.flush()
        graph_item_id = graph_item.id
        self.db.commit()
        word_list = []
        for file_path in node_files:
            file_path = self.handle_file_path(file_path)
            logger.info(f"ocr解析 {file_path}...")
            file_name = os.path.basename(file_path)
            ocr_result = ocr_layout(file_path)
            ocr_content = ocr_result.get(file_path, [])
            graph_image = GraphImage(**{
                "data_id": graph_item_id,
                "file_name": file_name,
                "file_path": file_path,
                "content": "\n".join(ocr_content)
            })
            self.db.add(graph_image)
            self.db.flush()
            graph_image_id = graph_image.id
            self.db.commit()
            logger.info(f"语义分析，{file_path}, 共{len(ocr_content)}张图片...")
            for item in ocr_content:
                word_relation = parse_result(model_path, item)
                data = defaultdict(list)
                for value, relation in zip(word_relation[0], word_relation[1]):
                    for n, v in enumerate(value):
                        if len(v) > 1 and relation[n] != 0 and len(value[relation[n] - 1]) > 1:
                            if v not in data[value[relation[n] - 1]]:
                                data[value[relation[n] - 1]].append(v)
                for k, v_list in data.items():
                    word_list.append({
                        "image_id": graph_image_id,
                        "name": k,
                        "inferior_word": v_list
                    })
                    for v in v_list:
                        word_list.append({
                            "image_id": graph_image_id,
                            "name": v,
                            "epistatic_word": k,
                            "apposition_word": [item for item in v_list if item != v]
                        })
        logger.info(f"数据同步...")
        insert_w_data = []
        update_w_data = []
        for item in word_list:
            graph_word_object = self.db.query(GraphWord).filter(GraphWord.name == item["name"])
            graph_word = graph_word_object.first()
            if graph_word is not None:
                image_id = graph_word.image_id
                epistatic_word = graph_word.epistatic_word
                apposition_word = graph_word.apposition_word
                inferior_word = graph_word.inferior_word
                if image_id:
                    i_i = image_id.split("、")
                    if item.get("image_id"):
                        i_i.append(str(item.get("image_id")))
                    image_id = list(set(i_i))
                else:
                    image_id = [item.get("image_id")] if item.get("image_id") else []

                if epistatic_word:
                    e_w = epistatic_word.split("、")
                    if item.get("epistatic_word"):
                        e_w.append(item.get("epistatic_word"))
                    epistatic_word = list(set(e_w))
                else:
                    epistatic_word = [item.get("epistatic_word")] if item.get("epistatic_word") else []
                if apposition_word:
                    a_w = apposition_word.split("、")
                    a_w.extend(item.get("apposition_word", []))
                    apposition_word = list(set(a_w))
                else:
                    apposition_word = item.get("apposition_word") if item.get("apposition_word") else []
                if inferior_word:
                    i_w = inferior_word.split("、")
                    i_w.extend(item.get("inferior_word", []))
                    inferior_word = list(set(i_w))
                else:
                    inferior_word = item.get("inferior_word") if item.get("inferior_word") else []
                w_data = {
                    "id": graph_word.id,
                    "image_id": "、".join(image_id) if image_id else "",
                    "epistatic_word": "、".join(epistatic_word) if epistatic_word else "",
                    "apposition_word": "、".join(apposition_word) if apposition_word else "",
                    "inferior_word": "、".join(inferior_word) if inferior_word else ""
                }
                # print(w_data)
                update_w_data.append(w_data)
            else:
                w_data = {
                    "name": item["name"],
                    "image_id": item["image_id"],
                    "epistatic_word": item.get("epistatic_word"),
                    "apposition_word": "、".join(item.get("apposition_word", [])),
                    "inferior_word": "、".join(item.get("inferior_word", []))
                }
                insert_w_data.append(w_data)
                # graph_word = GraphWord(**w_data)
                # self.db.add(graph_word)
                # self.db.commit()
        self.engine.execute(
            GraphWord.__table__.insert(),
            insert_w_data
        )
        self.db.bulk_update_mappings(GraphWord, update_w_data)
        self.db.commit()
        logger.info(f"数据同步完成")

    def handle_file_path(self, file_path):
        server_name = list(self.server_config.keys())
        for s_n in server_name:
            if s_n in file_path:
                file_path = re.sub(f".*{s_n}", self.server_config[s_n], file_path)
                break
        new_file_path = file_path.replace("\\", "/")
        return new_file_path

    def run(self, *args, **kwargs):
        if self.is_run:
            return
        getAllStructure_method = self.server_api["getAllStructure"]["method"]
        getAllStructure_url = self.server_api["getAllStructure"]["url"]
        # 获取档案系统节点
        rep = hc.request_api(getAllStructure_method, getAllStructure_url)
        ret = rep.json()
        node_id = [item["id"] for item in ret["data"]["list"]]
        # print(node_id)
        logger.info(f"开始同步服务器配置")
        self.get_server_config()
        logger.info(f"本次同步档案数据节点 {node_id}")
        # 查询每个节点数据
        self.is_run = True
        for _id in node_id:
            try:
                logger.info(f"开始档案数据节点 {_id}")
                logger.info(f"同步档案数据文件")
                node_file_info = self.get_node_file(_id)
                if not node_file_info:
                    logger.error(f"node {_id} no files")
                    continue
                logger.info(f"同步档案数据")
                self.get_node_data(_id)
                # break
            except Exception as e:
                logger.error(traceback.format_exc())
        self.is_run = False


new_sync_data = SyncData()

if __name__ == '__main__':
    sd = SyncData()
    sd.run()
