BaseConfig: &base
  DATABASE_URI: "mysql+pymysql://root:a5211314@localhost:3306/test001"
  SQL: "select * from _tb where id > :last_id and wjlj is not null and wjlj != ''"
  PK: "id"
  LAST_ID: 0
  GR_LAET_ID: 0
  LAST_METADATA_PATH: "~/.dbstash_sa_last_run/meta_json"
  GR_METADATA_PATH: "~/.dbstash_sa_last_run/meta_json2"
  SCHEDULE_INTERVAL: 3

  BUCKET_NAME: "myjfs"
  MINIO_UPLOAD_DIR: "share/aimp/upload_file"
  IMG_DIR_PATH: "myjfs/share/aimp/tugraph_img"

  # minio
  MINIO_CONFIG: {
    'endpoint': 'ubt-B450M-02:19003',
    'access_key': 'minioadmin',
    'secret_key': 'minioadmin',
    'secure': False
  }

  # tugraph
  TUGRAPH_CONFIG: {
    "url": "http://lgraph:7090",
    "user": "admin",
    "password": "73@TuGraph",
    "graph_name": "MovieDemo1",
    #    "graph_name": "archive",
  }

  GR_TUGRAPH_CONFIG: {
    "url": "http://lgraph:7090",
    "user": "admin",
    "password": "73@TuGraph",
    "graph_name": "GR_DEMO",
    #    "graph_name": "archive",
  }

  QA_CONFIG: {
    # 问题意图为 1(实体属性)
    "1": {
      "qa_template_type": 1,
      "qa_template": "[实体类型][实体实例]的[属性]是[属性值]。",
      "qa_na_template": "[实体类型][实体实例]的[属性]暂无资料。",
      "limit": 3,  # 超过几个结果折叠
    },
    # 问题意图为 2(命名实体)
    "2": {
      "qa_template_type": 2,
      "qa_template": "[属性]是[属性值]的[实体类型]有[实体实例]。",
      "qa_na_template": "[属性]是[属性值]的[实体类型]暂无资料。",
      "limit": 3,  # 超过几个结果折叠
    },
  }

DevelopmentConfig:
  <<: *base
  DATABASE_URI: "mysql+pymysql://root:a5211314@localhost:3306/test001"

ProductionConfig1:
  <<: *base
  DATABASE_URI: ~

ProductionConfig2:
  <<: *base
  DATABASE_URI: "mysql+pymysql://root:be@MysqL@mysql:3306/aimp?charset=utf8mb4"


# 当前使用配置
run_config: "ProductionConfig2"

