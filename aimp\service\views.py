# import json
# import time as t
# # Create your views here.
# from django.core.paginator import Paginator
# from django.db.models import Q
# from django.http import JsonResponse
# from django.shortcuts import HttpResponse
#
# from service.models import Service
# from aimp.config import service_config
#
# def addService(request):
#     return HttpResponse("hello world!")
#
#
# async def getService(request):
#     r_data = request.body
#     r_data = json.loads(r_data)
#     service_type = r_data.get("service_type","")
#     input_data = r_data.get("input_data", "")
#     sort_key = r_data.get("sort_key", "id")
#     page = r_data.get("page", 2)
#     size = r_data.get("size", 70)
#     order = r_data.get("order", 2)  # 0是逆序
#     if order == 0:
#         sort_key = f"-{sort_key}"
#     server = Service.objects.filter(Q(service_name__contains=input_data)&
#                                     Q(service_type__contains=service_type)
#                                     & Q(service_status=2))
#     server = server.order_by(sort_key)
#     count = await server.acount()
#     # data = server
#     # count = data.count()
#     # data = Paginator(data, size)
#     # data = data.page(page)
#     return_data = []
#     async for i in server[page*size-size:page*size]:
#         return_data.append({
#             "key":i.id,
#             "id":i.id,
#             "service_name":i.service_name,
#             "service_time":t.strftime("%Y-%m-%d %H:%M:%S", t.localtime(i.service_time)),
#             "service_status":i.service_status,
#             # "service_position":i.server_set,
#             "service_type":i.service_type,
#         })
#     response_data = {
#         "code": 200,
#         "msg": "查找成功",
#         "data": {"service": return_data, "count": count}
#     }
#     return JsonResponse(response_data)
#
#
# def delService(request):
#     r_data = request.body
#     r_data = json.loads(r_data)
#     service_id_list = r_data.get("service_id_list",[])
#     Service.objects.filter(id__in = service_id_list).delete()
#     response_data = {
#         "code": 200,
#         "msg": "删除成功"
#     }
#     return JsonResponse(response_data)
#
#
# def getConfig(request):
#     data = json.dumps(service_config)
#     response_data = {
#         "code": 200,
#         "msg": "查找成功",
#         "data": {"service_config": [123]}
#     }
#     return JsonResponse(response_data)
#
#
# def updateConfig(request):
#     r_data = request.body
#     r_data = json.loads(r_data)
#     old_service_config = r_data.get("service_config")
#     service_config = old_service_config
#     response_data = {
#         "code": 200,
#         "msg": "修改成功"
#     }
#     return JsonResponse(response_data)
#
#
# # async def getService(request):
# #
# #     _lis =[]
# #     m = Service.objects.filter(service_name__startswith = "张三").order_by("-id")
# #     async for key in m[3:10]:
# #         _lis.append(key.id)
# #         print(_lis)
# #     # print(_lis)
# #     # book = await Service.objects.filter(service_name__startswith = "张三").all()
# #     # print(book)
# #     response_data = {
# #         "code": 200,
# #         "msg": "修改成功"
# #     }
# #     return JsonResponse(response_data)