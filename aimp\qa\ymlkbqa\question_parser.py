#!/usr/bin/env python3
# coding: utf-8
# File: question_parser.py
# Author: lhy<<EMAIL>,https://huangyong.github.io>
# Date: 18-10-4

class QuestionPaser:

    '''构建实体节点'''
    def build_entitydict(self, args):
        entity_dict = {}
        for arg, types in args.items():
            for type in types:
                if type not in entity_dict:
                    entity_dict[type] = [arg]
                else:
                    entity_dict[type].append(arg)

        return entity_dict

    '''解析主函数'''
    def parser_main(self, res_classify):
        args = res_classify['args']
        entity_dict = self.build_entitydict(args)
        question_types = res_classify['question_types']
        res = []
        sqls = []
        for question_type in question_types:
            sql_ = {}
            sql_['question_type'] = question_type
            sql = []
            if question_type == 'person_property':

                xm = entity_dict.get('姓名')
                budui = entity_dict.get('哪里人')
                sql = ["MATCH (m:人物) where m.name = '{0}' return m.name, m.birthplace".format(i) for i in entity_dict['姓名']]

                for query in sql:
                    res.append(self.g.run(query).data())
        return res


if __name__ == '__main__':
    handler = QuestionPaser()
