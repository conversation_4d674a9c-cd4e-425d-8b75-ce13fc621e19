# -*- coding: utf-8 -*-
# __author__:SH
# 2022/10/31 10:14
from asgiref.sync import sync_to_async
from django.db.models import Q
from fastapi import APIRouter,Body,Depends
from aimp.config import service_config
from .schema import *
import time as t
from model.schema import *
api = APIRouter()

# service_type = r_data.get("service_type", "")
# input_data = r_data.get("input_data", "")


def get_server(service):
    if service.serviceserver_set.first():
        server_name = service.serviceserver_set.first().server.server_name
        return server_name
    else:
        return "-"


@api.post("/servicelist",response_model=ServiceListOut)
def service_list(param_page:Paging=Body(),
                       r_search:ServiceList=Body()):
    from .models import Service
    if r_search.service_type == 0:
        service_list = Service.objects.filter(service_name__contains=r_search.input_data).all()
    else:
        service_list = Service.objects.filter(Q(service_name__contains=r_search.input_data) &
                                        Q(service_type__contains=r_search.service_type)).all()
    count = service_list.count()
    service_list = service_list.order_by(
        param_page.order_key if param_page.order == 1 else f"-{param_page.order_key}").all()[
                   param_page.page * param_page.size - param_page.size: param_page.page * param_page.size]
    print(service_list)
    m = {
        'count': count,
        "service_list": [
            {
                "key":i.id,
                "service_id": i.id,
                "server":get_server(i),
                "service_name":i.service_name,
                "service_time":t.strftime("%Y-%m-%d %H:%M:%S", t.localtime(i.create_time)),
                "service_status":i.service_status,
                "service_type":i.servicedataset_set.first().dataset.dataset_type,
            }for i in service_list
        ]
    }
    return m
    # return {
    #     'count': count,
    #     "service_list": [
    #         {
    #             "key":i.id,
    #             "service_id": i.id,
    #             "server":await sync_to_async(get_server)(i) ,
    #             "service_name":i.service_name,
    #             "service_time":t.strftime("%Y-%m-%d %H:%M:%S", t.localtime(i.create_time)),
    #             "service_status":i.service_status,
    #             "service_type":i.service_type,
    #         }async for i in service_list
    #     ]
    # }


@api.delete("/service",response_model=ResponseMsg)
def service(_id:IdInList =Body()):
    from .models import Service
    Service.objects.filter(id__in=_id.id_list).delete()
    return {"msg":"删除成功"}



@api.get("/service",response_model=ServiceOut)
def service(param:IdIn=Depends()):
    from .models import Service
    service = Service.objects.filter(id=param.id).first()
    return {
        "service_name":service.service_name,
        "service_type":service.servicedataset_set.first().dataset.dataset_type,
        "dataset_id":[
            key.dataset_id
            for key in service.servicedataset_set.all()
        ],
        "model_id":service.servicemodel_set.first().ai_model_id
    }


@api.get("/serviceconfigure",response_model=ServiceConfigure)
def service_configure():
    return service_config


@api.get("/serve",response_model=ServeOutList)
async def service(param:ServeIn=Depends()):
    from .models import Server
    serve = Server.objects.all()
    if param.serve_type:
        serve = service.filter(param.service_type).all()
    return {"server_list":[
        {
            "serve_id": i.id,
            "ip": i.ip,
            "CPU": i.CPU,
            "memory": i.RAM,
            "disk": i.disk,
            "disk_count": i.disk_count,
            "GPU": i.GPU
        }async for i in serve
    ]}


@api.post("/service",response_model=ResponseMsg)
def service(param:ServiceCrete=Body()):
    from .models import Service,Server,ServiceServer,ServiceModel,ServiceDataset
    from model.models import AiModels
    service = Service.objects.create(service_name=param.service_name,
                                     service_pos=param.service_pos,
                                     unit_type=param.mode_type,
                                     unit_count=param.mode_num)
    if param.ratio==2:
        server = Server.objects.filter(id__in=param.server_list)
        server_list = [ServiceServer(server=i, service=service) for i in server]
        ServiceServer.objects.bulk_create(server_list)
    model = AiModels.objects.filter(id=param.model_id).first()
    model_list = model.modeldataset_set.all()
    service_list = [ServiceDataset(service=service, dataset=i) for i in model_list]
    ServiceDataset.objects.bulk_create(service_list)
    model = ServiceModel(ai_model=model, service=service)
    ServiceModel.objects.bulk_create([model])
    return {"msg":"部署成功"}


@api.post("/createservice",response_model=CreateServiceOut)
def create_service(param:CreateServiceIn=Body()):
    from .models import Service,ServiceDataset
    from dataset.models import DataSet
    service = Service.objects.create(service_name=param.service_name)
    service.save()
    dataset = DataSet.objects.create(dataset_name=f"{param.service_name}的数据集",dataset_type=param.dataset_type)
    dataset.save()
    servicedataset=ServiceDataset(service=service,dataset=dataset)
    servicedataset.save()
    return {"service_id":service.id,"dataset_id":dataset.id}
