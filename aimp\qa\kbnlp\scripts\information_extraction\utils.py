# Copyright (c) 2022 PaddlePaddle Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import os
import re
import math
import json
import random
from tqdm import tqdm
import jsonlines
import numpy as np
from typing import Dict
from qa.kbnlp.scripts.information_extraction.log import logger
from qa.kbnlp.task.models.information_extraction_model import SUBJECT_PREDICATE_CONJUNCT_WORD
from typing import Optional, Dict, List, Union


"""
空格全角'\u3000'，空格半角' '。
非空格全部全角字符如下：
['＂', '＃', '＄', '％', '＆', '＇', '（', '）', '＊', '＋', '，', '－', '．', '／', '０', '１', '２', '３', '４', '５', '６', '７', '８', '９', '：', '；', '＜', '＝', '＞', '？', '＠', 'Ａ', 'Ｂ', 'Ｃ', 'Ｄ', 'Ｅ', 'Ｆ', 'Ｇ', 'Ｈ', 'Ｉ', 'Ｊ', 'Ｋ', 'Ｌ', 'Ｍ', 'Ｎ', 'Ｏ', 'Ｐ', 'Ｑ', 'Ｒ', 'Ｓ', 'Ｔ', 'Ｕ', 'Ｖ', 'Ｗ', 'Ｘ', 'Ｙ', 'Ｚ', '［', '＼', '］', '＾', '＿', '｀', 'ａ', 'ｂ', 'ｃ', 'ｄ', 'ｅ', 'ｆ', 'ｇ', 'ｈ', 'ｉ', 'ｊ', 'ｋ', 'ｌ', 'ｍ', 'ｎ', 'ｏ', 'ｐ', 'ｑ', 'ｒ', 'ｓ', 'ｔ', 'ｕ', 'ｖ', 'ｗ', 'ｘ', 'ｙ', 'ｚ', '｛', '｜', '｝']
非空格全部半角字符如下：
['"', '#', '$', '%', '&', "'", '(', ')', '*', '+', ',', '-', '.', '/', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', ':', ';', '<', '=', '>', '?', '@', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', '[', '\\', ']', '^', '_', '`', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z', '{', '|', '}']
"""


def dbc2sbc(s):
    """
    全角转半角。
    dbc2sbc('１９９６年存栏各类牲畜１６５７．２万头（只），')  # '1996年存栏各类牲畜1657.2万头(只),'
    dbc2sbc('4²')  # '4²'
    如果使用unicodedata.normalize('4²')就会变成‘42’，这可能不是想要的。
    """
    rs = ""
    for char in s:
        code = ord(char)
        if code == 0x3000:
            code = 0x0020
        else:
            code -= 0xfee0
        if not (0x0021 <= code <= 0x7e):
            rs += char
            continue
        rs += chr(code)
    return rs


def sbc2dbc(ustring):
    """
    半角转全角
    """
    rstring = ""
    for uchar in ustring:
        inside_code = ord(uchar)
        if inside_code == 0x0020:
            inside_code = 0x3000
        else:
            if not (0x0021 <= inside_code  <= 0x7e):
                rstring += uchar
                continue
            inside_code += 0xfee0
        rstring += chr(inside_code)
    return rstring


def set_seed(seed):
    random.seed(seed)
    np.random.seed(seed)


def trim_backslash(examples):
    """
        doccano在导出时在字符'/'前自动添加一个'\\'字符，但是start, end的位置信息对应于原始文本。所以需要手动去除'\\'字符。
    :return: 处理后的examples.
    """
    res = []
    for exam in examples:
        exam['text'] = exam['text'].replace('\\', '')
        res.append(exam)
    return res


def deal_next_relation(examples: Dict):
    """
        doccano在给实体打标签时，有时遇到一个实体文本处在上一行的末尾和下一行的开头的情况不能直接将他们标注在一块，
    个人使用'后续'连接两个被迫分开的文本。本函数将分开标注的本文重新连接起来。同时由于ocr在识别时有时将同一行文本识别成两个文本块，或者一个单元格内有两行文字时，
    ocr会将它们分开，此时本人在标注时也使用‘后续’关系将它们连接到了一块，但对于这种情况，不需要将他们重新连接，因为它们有不同的bbox，不影响使用layoutlm模型。
    :param examples:
    :return: 处理后的examples.
    """
    res = []
    for exam in examples:
        relations = exam['relations']
        id2entity = dict()
        for entity in exam['entities']:
            id2entity[entity['id']] = entity
        roots = set(id2entity.keys())
        start2end = {}
        t_id2real_relations = {}
        f_id2real_relations = {}
        for rel in relations:
            f_id = rel['from_id']
            t_id = rel['to_id']
            if rel['type'] == '后续':
                f_e = id2entity[f_id]
                t_e = id2entity[t_id]
                if f_e['end_offset'] == t_e['start_offset']:
                    # 这种情况下的'后续'关系被认为是一种伪的，为了应付doccano不能标注占据两行实体的问题而创建的。
                    start2end[f_id] = t_id
                    roots.remove(t_id)
                else:
                    t_id2real_relations.setdefault(t_id, []).append(rel)
                    f_id2real_relations.setdefault(f_id, []).append(rel)
            else:
                t_id2real_relations.setdefault(t_id, []).append(rel)
                f_id2real_relations.setdefault(f_id, []).append(rel)

        for e_id in roots:
            cur = e_id
            e = id2entity[e_id]
            while cur in start2end:
                cur = start2end[cur]
                e_cur = id2entity[cur]
                e['end_offset'] = e_cur['end_offset']
                if cur in t_id2real_relations:
                    for rel in t_id2real_relations[cur]:
                        rel['to_id'] = e_id
                if cur in f_id2real_relations:
                    for rel in f_id2real_relations:
                        rel['from_id'] = e_id
        entities = sorted(list([id2entity[idx] for idx in roots]), key=lambda ent: ent['id'])
        relations = sorted(sum(t_id2real_relations.values(), []), key=lambda r: r['id'])
        exam['entities'] = entities
        exam['relations'] = relations
        res.append(exam)
    return res


# def convert_example(example, tokenizer, max_seq_len):
#     """
#     example: {
#         title
#         prompt
#         text
#         result_list
#     }
#     """
#     encoded_inputs = tokenizer(text=[example["prompt"]],
#                                text_pair=[example["text"]],
#                                truncation=True,
#                                max_seq_len=max_seq_len,
#                                pad_to_max_seq_len=True,
#                                return_attention_mask=True,
#                                return_position_ids=True,
#                                return_dict=False,
#                                return_offsets_mapping=True)
#     encoded_inputs = encoded_inputs[0]
#     offset_mapping = [list(x) for x in encoded_inputs["offset_mapping"]]
#     bias = 0
#     for index in range(1, len(offset_mapping)):
#         mapping = offset_mapping[index]
#         if mapping[0] == 0 and mapping[1] == 0 and bias == 0:
#             bias = offset_mapping[index - 1][1] + 1  # Includes [SEP] token
#         if mapping[0] == 0 and mapping[1] == 0:
#             continue
#         offset_mapping[index][0] += bias
#         offset_mapping[index][1] += bias
#     start_ids = [0 for x in range(max_seq_len)]
#     end_ids = [0 for x in range(max_seq_len)]
#     for item in example["result_list"]:
#         start = map_offset(item["start"] + bias, offset_mapping)
#         end = map_offset(item["end"] - 1 + bias, offset_mapping)
#         start_ids[start] = 1.0
#         end_ids[end] = 1.0
#
#     tokenized_output = [
#         encoded_inputs["input_ids"], encoded_inputs["token_type_ids"],
#         encoded_inputs["position_ids"], encoded_inputs["attention_mask"],
#         start_ids, end_ids
#     ]
#     tokenized_output = [np.array(x, dtype="int64") for x in tokenized_output]
#     return tuple(tokenized_output)


def map_offset(ori_offset, offset_mapping):
    """
    map ori offset to token offset
    """
    for index, span in enumerate(offset_mapping):
        if span[0] <= ori_offset < span[1]:
            return index
    return -1


def reader(data_path, auto_prompt_len=0, max_seq_len=512):
    """
    read json，要求result list内span按照start升序排列。否则在超过max_seq_len的情况下，将text分割可能会使得end溢出。
    # TODO：现在的算法在处理长度超过max_seq_len时，可能会将主实体和客实体分开。还要注意主客实体的距离大于max_seq_len的特例。
    """
    # 最终模型的输入形如：[CLS] (auto prompt) prompt [SEP] text [SEP]
    special_token_len = 3  # 三个special token
    with open(data_path, 'r', encoding='utf-8') as f:
        for line in f:
            json_line = json.loads(line)
            text = json_line['text'].strip()
            prompt = json_line['prompt']
            # Model Input is as like: [CLS] prompt [SEP] text [SEP]
            # It include three summary tokens.
            if max_seq_len <= len(prompt) + auto_prompt_len + special_token_len:
                raise ValueError(
                    "The value of max_seq_len is too small, please set a larger value"
                )
            max_text_len = max_seq_len - len(prompt) - auto_prompt_len - special_token_len
            if len(text) <= max_text_len:
                yield json_line
            else:
                result_list = json_line['result_list']
                json_lines = []
                accumulate = 0
                while True:
                    cur_result_list = []

                    for result in result_list:
                        if result['start'] + 1 <= max_text_len < result[
                                'end']:
                            max_text_len = result['start']
                            break

                    cur_text = text[:max_text_len]
                    res_text = text[max_text_len:]

                    while True:
                        if len(result_list) == 0:
                            break
                        elif result_list[0]['end'] <= max_text_len:
                            if result_list[0]['end'] > 0:
                                cur_result = result_list.pop(0)
                                cur_result_list.append(cur_result)
                            else:
                                cur_result_list = [
                                    result for result in result_list
                                ]
                                break
                        else:
                            break

                    json_line = {
                        'text': cur_text,
                        'result_list': cur_result_list,
                        'prompt': prompt
                    }
                    json_lines.append(json_line)

                    for result in result_list:
                        if result['end'] <= 0:
                            break
                        result['start'] -= max_text_len
                        result['end'] -= max_text_len
                    accumulate += max_text_len
                    max_text_len = max_seq_len - len(prompt) - auto_prompt_len - special_token_len
                    if len(res_text) == 0:
                        break
                    elif len(res_text) < max_text_len:
                        json_line = {
                            'text': res_text,
                            'result_list': result_list,
                            'prompt': prompt
                        }
                        json_lines.append(json_line)
                        break
                    else:
                        text = res_text

                for json_line in json_lines:
                    yield json_line


def unify_prompt_name(prompt):
    # The classification labels are shuffled during finetuning, so they need
    # to be unified during evaluation.
    if re.search(r'\[.*?\]$', prompt):
        prompt_prefix = prompt[:prompt.find("[", 1)]
        cls_options = re.search(r'\[.*?\]$', prompt).group()[1:-1].split(",")
        cls_options = sorted(list(set(cls_options)))
        cls_options = ",".join(cls_options)
        prompt = prompt_prefix + "[" + cls_options + "]"
        return prompt
    return prompt


def add_negative_example(examples, raw_examples, prompts, label_set, negative_ratio):
    negative_examples = []
    positive_examples = []
    texts = [exam['text'] for exam in raw_examples]
    with tqdm(total=len(prompts), mininterval=10) as pbar:
        for i, prompt in enumerate(prompts):
            positive_examples.extend(examples[i])
            redundants_list = list(label_set ^ set(prompt))
            if redundants_list:  # 如果有候选负样本label
                redundants_list.sort()

                num_positive = len(examples[i])

                if num_positive == 0:
                    num_positive = 1
                actual_ratio = math.ceil(len(redundants_list) / num_positive)

                # if num_positive != 0:  # 百度实现，导致空样本会采集所有候选负样本。尽管后续会重新采样，但可能会影响效率。
                #     actual_ratio = math.ceil(len(redundants_list) / num_positive)
                # else:
                #     # Set num_positive to 1 for text without positive example
                #     num_positive, actual_ratio = 1, 0

                if actual_ratio <= negative_ratio or negative_ratio == -1:
                    idxs = [k for k in range(len(redundants_list))]
                else:
                    idxs = random.sample(range(0, len(redundants_list)),
                                         negative_ratio * num_positive)

                for idx in idxs:
                    negative_result = {
                        "text": texts[i],
                        "result_list": [],
                        "prompt": redundants_list[idx],
                    }
                    negative_examples.append(negative_result)
                    for k, v in raw_examples[i].items():
                        if k != 'predicate' and k != 'subject_label' and k not in negative_result:
                            negative_result[k] = v


            pbar.update(1)
    return positive_examples, negative_examples


def add_full_negative_example(examples, texts, relation_prompts, predicate_set,
                              subject_goldens, conjunct=SUBJECT_PREDICATE_CONJUNCT_WORD):
    with tqdm(total=len(relation_prompts), mininterval=10) as pbar:
        for i, relation_prompt in enumerate(relation_prompts):
            negative_sample = []
            for subject in subject_goldens[i]:
                for predicate in predicate_set:
                    # The relation prompted is constructed as follows:
                    # (subject_name, predicate)
                    prompt = conjunct.join((subject, predicate))
                    if prompt not in relation_prompt:
                        negative_result = {
                            "text": texts[i],
                            "result_list": [],
                            "prompt": prompt,
                            "subject_label": None
                        }
                        negative_sample.append(negative_result)
            examples[i].extend(negative_sample)
            pbar.update(1)
    return examples


def construct_prompt_relation(entity_name_set, predicate_set, conjunct=SUBJECT_PREDICATE_CONJUNCT_WORD):
    """
    Args:
        entity_name_set:
        predicate_set:
        conjunct:

    Returns:
        形如(prompt,predicate)的二元组组成的列表。
    """
    relation_prompts = []
    for entity_name in entity_name_set:
        for predicate in predicate_set:
            # The relation prompt is constructed as follows:
            # subject_name + conjunct + predicate
            prompt = conjunct.join((entity_name, predicate))
            relation_prompts.append((prompt, predicate))
    return relation_prompts


def generate_cls_example(text, labels, prompt_prefix, options):
    random.shuffle(options)
    cls_options = ",".join(options)
    prompt = prompt_prefix + "[" + cls_options + "]"

    result_list = []
    example = {"text": text, "result_list": result_list, "prompt": prompt}
    for label in labels:
        start = prompt.rfind(label[0]) - len(prompt) - 1
        end = start + len(label)
        result = {"text": label, "start": start, "end": end}
        example["result_list"].append(result)
    return example


def convert_cls_examples(raw_examples,
                         prompt_prefix="情感倾向",
                         options=["正向", "负向"]):
    """
    Convert labeled data export from doccano for classification task.
    """
    examples = []
    logger.info(f"Converting doccano data...")
    with tqdm(total=len(raw_examples), mininterval=10) as pbar:
        for items in raw_examples:
            # Compatible with doccano >= 1.6.2
            if "data" in items.keys():
                text, labels = items["data"], items["label"]
            else:
                text, labels = items["text"], items["label"]
            example = generate_cls_example(text, labels, prompt_prefix, options)
            examples.append(example)
    return examples


def convert_ext_examples(raw_examples,
                         negative_ratio,
                         prompt_prefix="情感倾向",
                         options=["正向", "负向"],
                         separator="##",
                         is_train=True,
                         conjunct=SUBJECT_PREDICATE_CONJUNCT_WORD):
    """
    Convert labeled data export from doccano for extraction and aspect-level classification task.
    """

    def _sep_cls_label(label, separator):
        label_list = label.split(separator)
        if len(label_list) == 1:
            return label_list[0], None
        return label_list[0], label_list[1:]

    def _concat_examples(positive_examples, negative_examples, negative_ratio):
        examples = []
        if math.ceil(len(negative_examples) /
                     len(positive_examples)) <= negative_ratio:
            examples = positive_examples + negative_examples
        else:
            # Random sampling the negative examples to ensure overall negative ratio unchanged.
            idxs = random.sample(range(0, len(negative_examples)),
                                 negative_ratio * len(positive_examples))
            negative_examples_sampled = []
            for idx in idxs:
                negative_examples_sampled.append(negative_examples[idx])
            examples = positive_examples + negative_examples_sampled
        return examples

    texts = []
    entity_examples = []
    relation_examples = []
    entity_cls_examples = []
    entity_prompts = []
    relation_prompts = []
    entity_label_set = set()
    entity_name_set = set()
    predicate_set = []
    subject_goldens = []

    logger.info(f"Converting doccano data...")
    with tqdm(total=len(raw_examples), mininterval=10) as pbar:
        for items in raw_examples:
            entity_id = 0
            if "data" in items.keys():  # doccano < 1.7.0,不维护，可能有错误。
                relation_mode = False
                if isinstance(items["label"],
                              dict) and "entities" in items["label"].keys():
                    relation_mode = True
                text = items["data"]
                entities = []
                relations = []
                if not relation_mode:
                    # Export file in JSONL format which
                    # e.g. {"data": "", "label": [ [0, 2, "ORG"], ... ]}
                    for item in items["label"]:
                        entity = {
                            "id": entity_id,
                            "start_offset": item[0],
                            "end_offset": item[1],
                            "label": item[2]
                        }
                        entities.append(entity)
                        entity_id += 1
                else:
                    # Export file in JSONL format for relation labeling task which doccano < 1.7.0
                    # e.g. {"data": "", "label": {"relations": [ {"id": 0, "start_offset": 0, "end_offset": 6, "label": "ORG"}, ... ], "entities": [ {"id": 0, "from_id": 0, "to_id": 1, "type": "foundedAt"}, ... ]}}
                    entities.extend(
                        [entity for entity in items["label"]["entities"]])
                    if "relations" in items["label"].keys():
                        relations.extend([
                            relation for relation in items["label"]["relations"]
                        ])
            else:
                # Export file in JSONL format which doccano >= 1.7.0
                # e.g. {"text": "", "label": [ [0, 2, "ORG"], ... ]}
                if "label" in items.keys():  # 处理纯实体的数据
                    text = items["text"]
                    entities = []
                    for item in items["label"]:
                        entity = {
                            "id": entity_id,
                            "start_offset": item[0],
                            "end_offset": item[1],
                            "label": item[2]
                        }
                        entities.append(entity)
                        entity_id += 1
                    relations = []
                else:
                    # Export file in JSONL (relation) format
                    # e.g. {"text": "", "relations": [ {"id": 0, "start_offset": 0, "end_offset": 6, "label": "ORG"}, ... ], "entities": [ {"id": 0, "from_id": 0, "to_id": 1, "type": "foundedAt"}, ... ]}
                    text, relations, entities = items["text"], items[
                        "relations"], items["entities"]
            texts.append(text)

            entity_example = []
            entity_prompt = []

            entity_example_map = {}
            entity_map = {}  # id to entity name
            for entity in entities:
                entity_name = text[entity["start_offset"]:entity["end_offset"]]
                entity_map[entity["id"]] = {
                    "name": entity_name,
                    "start": entity["start_offset"],
                    "end": entity["end_offset"],
                    "label": entity['label']
                }

                entity_label, entity_cls_label = _sep_cls_label(
                    entity["label"], separator)

                # Define the prompt prefix for entity-level classification
                entity_cls_prompt_prefix = entity_name + conjunct + prompt_prefix
                if entity_cls_label is not None:
                    entity_cls_example = generate_cls_example(
                        text, entity_cls_label, entity_cls_prompt_prefix,
                        options)

                    entity_cls_examples.append(entity_cls_example)

                result = {
                    "text": entity_name,
                    "start": entity["start_offset"],
                    "end": entity["end_offset"]
                }
                if entity_label not in entity_example_map.keys():
                    entity_example_map[entity_label] = {
                        "text": text,
                        "result_list": [result],
                        "prompt": entity_label
                    }
                    # 保留数据中其它字段
                    for k_, v_ in items.items():
                        if k_ != 'entities' and k_ != 'relations' and k_ not in entity_example_map[entity_label]:
                            entity_example_map[entity_label][k_] = v_
                else:
                    entity_example_map[entity_label]["result_list"].append(
                        result)

                entity_label_set.add(entity_label)
                entity_name_set.add(entity_name)
                entity_prompt.append(entity_label)

            for v in entity_example_map.values():
                entity_example.append(v)

            entity_examples.append(entity_example)
            entity_prompts.append(entity_prompt)

            subject_golden = []  # Golden entity inputs
            relation_example = []
            relation_prompt = []
            relation_example_map = {}

            for relation in relations:
                predicate = relation["type"]
                subject_id = relation["from_id"]
                object_id = relation["to_id"]
                # The relation prompt is constructed as follows:
                # (subject_name, predicate)
                prompt = conjunct.join((entity_map[subject_id]["name"], predicate))
                if entity_map[subject_id]["name"] not in subject_golden:
                    subject_golden.append(entity_map[subject_id]["name"])
                result = {
                    "text": entity_map[object_id]["name"],
                    "start": entity_map[object_id]["start"],
                    "end": entity_map[object_id]["end"]
                }
                if prompt not in relation_example_map.keys():
                    relation_example_map[prompt] = {
                        "text": text,
                        "result_list": [result],
                        "prompt": prompt,
                        "subject_label": entity_map[subject_id]['label'],
                        "predicate": predicate,
                    }
                    # 保留数据中其它字段
                    for k_, v_ in items.items():
                        if k_ != 'entities' and k_ != 'relations' and k_ not in relation_example_map[prompt]:
                            relation_example_map[prompt][k_] = v_
                else:
                    relation_example_map[prompt]["result_list"].append(result)

                if predicate not in predicate_set:
                    predicate_set.append(predicate)
                relation_prompt.append(prompt)

            for v in relation_example_map.values():
                relation_example.append(v)

            relation_examples.append(relation_example)
            relation_prompts.append(relation_prompt)
            subject_goldens.append(subject_golden)
            pbar.update(1)

    logger.info(f"Adding negative samples for first stage prompt...")
    positive_examples, negative_examples = add_negative_example(
        entity_examples, raw_examples, entity_prompts, entity_label_set,
        negative_ratio)
    if len(positive_examples) == 0:
        all_entity_examples = []
    elif is_train:
        all_entity_examples = _concat_examples(positive_examples,
                                               negative_examples,
                                               negative_ratio)
    else:
        all_entity_examples = positive_examples + negative_examples

    all_relation_examples = []
    if len(predicate_set) != 0:
        if is_train:
            logger.info(f"Adding negative samples for second stage prompt...")
            relation_prompt_set = construct_prompt_relation(
                entity_name_set, set(predicate_set))
            positive_examples, negative_examples = add_negative_example(
                relation_examples, raw_examples, relation_prompts, set(next(zip(*relation_prompt_set))),
                negative_ratio)
            all_relation_examples = _concat_examples(positive_examples,
                                                     negative_examples,
                                                     negative_ratio)
        else:
            logger.info(f"Adding negative samples for second stage prompt...")
            relation_examples = add_full_negative_example(
                relation_examples, texts, relation_prompts, predicate_set,
                subject_goldens, conjunct)
            all_relation_examples = [
                r for relation_example in relation_examples
                for r in relation_example
            ]
    return all_entity_examples, all_relation_examples, entity_cls_examples


def get_k_fewshot_data(examples, save_dir=None, shuffle=False, k=10):
    """
    Args:
        examples: 从doccano17中导出的json文件中读出的数据。
        save_dir: 存放路径。默认为None，不保存。
        shuffle:
        k: 保证每个label出现的次数不低于k（大致在k附件）。

    Returns: 返回k-few-shot的数据。

    """
    if k <= 0:
        return examples
    res = []
    labels = set()
    for exam in examples:
        for e in exam['entities']:
            labels.add(e['label'])
        for rel in exam['relations']:
            labels.add(rel['type'])

    label_count = dict((l, 0) for l in labels)
    if shuffle:
        idx = np.random.permutation(len(examples))
        examples = [examples[i] for i in idx]
    for d in examples:
        temp = {}
        all_overflow = True
        for entity_d in d['entities']:
            label = entity_d['label']
            if label in temp:
                temp[label] = temp[label] + 1
            else:
                temp[label] = 1
                if label_count[label] < k:
                    all_overflow = False
        for relation_d in d['relations']:
            label = relation_d['type']
            if label in temp:
                temp[label] = temp[label] + 1
            else:
                temp[label] = 1
                if label_count[label] < k:
                    all_overflow = False
        if not all_overflow:
            for l, c in temp.items():
                label_count[l] = label_count[l] + temp[l]
            res.append(d)
    if shuffle:
        idx = np.random.permutation(len(res))
        res = [res[i] for i in idx]
    if save_dir:
        with jsonlines.open(os.path.join(save_dir, 'fewshot_%d.jsonl' % k), mode='w') as jw:
            for d in res:
                jw.write(d)
    return res


if __name__ == '__main__':
    dbc = ['＂', '＃', '＄', '％', '＆', '＇', '（', '）', '＊', '＋', '，', '－', '．', '／', '０', '１', '２', '３', '４', '５', '６', '７', '８', '９', '：', '；', '＜', '＝', '＞', '？', '＠', 'Ａ', 'Ｂ', 'Ｃ', 'Ｄ', 'Ｅ', 'Ｆ', 'Ｇ', 'Ｈ', 'Ｉ', 'Ｊ', 'Ｋ', 'Ｌ', 'Ｍ', 'Ｎ', 'Ｏ', 'Ｐ', 'Ｑ', 'Ｒ', 'Ｓ', 'Ｔ', 'Ｕ', 'Ｖ', 'Ｗ', 'Ｘ', 'Ｙ', 'Ｚ', '［', '＼', '］', '＾', '＿', '｀', 'ａ', 'ｂ', 'ｃ', 'ｄ', 'ｅ', 'ｆ', 'ｇ', 'ｈ', 'ｉ', 'ｊ', 'ｋ', 'ｌ', 'ｍ', 'ｎ', 'ｏ', 'ｐ', 'ｑ', 'ｒ', 'ｓ', 'ｔ', 'ｕ', 'ｖ', 'ｗ', 'ｘ', 'ｙ', 'ｚ', '｛', '｜', '｝']
    sbc = ['"', '#', '$', '%', '&', "'", '(', ')', '*', '+', ',', '-', '.', '/', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', ':', ';', '<', '=', '>', '?', '@', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', '[', '\\', ']', '^', '_', '`', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z', '{', '|', '}']
    print(list(zip(dbc, [dbc2sbc(c) for c in dbc])))
    print(list(zip(sbc, [sbc2dbc(c) for c in sbc])))
