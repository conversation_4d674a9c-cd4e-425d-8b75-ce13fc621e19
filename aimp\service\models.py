import time

from django.db import models

# Create your models here.


class TimeClass(models.Model):
    create_time = models.IntegerField(default=0)  # 创建时间
    update_time = models.IntegerField(default=0)  # 更新时间

    def __init__(self):
        _time = int(time.time())
        self._create_time = _time
        self.update_time = _time

    # 抽象类，不创建实际数据库
    class Meta:
        abstract = True


class Service(TimeClass,models.Model):
    service_name = models.CharField(max_length=32)  # 服务名称
    service_status = models.IntegerField(default=0)  # 服务状态
    # service_position = models.CharField(max_length=255)  # 部署位置 --- 改成多对对的模式
    # service_type = models.IntegerField(default=0)  # 服务类型
    service_pos = models.CharField(max_length=255,default="") # 服务url
    model_id = models.IntegerField(default=0)  # 模型ID
    # dataset_id = models.ForeignKey("dataset.Dataset",on_delete=models.CASCADE) 3
    unit_type = models.IntegerField(default=0)  # 单元类型，1-文本，2-图形模式，3-通用模式
    unit_count = models.IntegerField(default=0)  # 单元个数

    def __init__(self, *args, **kwargs):
        "重写__init__方法"
        super(TimeClass, self).__init__(*args, **kwargs)
        super(Service, self).__init__()

    def save(self, *args, **kwargs):
        if not self.create_time:
            self.create_time = self._create_time
        self.update_time = self.update_time
        super(Service, self).save(*args, **kwargs)

class Server(models.Model):
    ip = models.CharField(max_length = 32) # 服务器ip
    CPU = models.IntegerField(default=None)  # CPU个数
    RAM = models.IntegerField(default=None)  # 内存大小
    disk = models.IntegerField(default=None)  # 硬盘大小
    disk_count = models.IntegerField(default=None)  # 硬盘个数
    GPU = models.IntegerField(default=None)  # gpu
    server_status = models.IntegerField(default=None)  # 服务器状态
    server_name = models.CharField(max_length=32, default = "")   # 服务器名称
    server_type = models.IntegerField(default=1)   # 服务器类型  1 训练服务器 2 推理服务器
    # services = models.ManyToManyField(to="Service")


class ServiceServer(models.Model):
    service = models.ForeignKey("Service",on_delete=models.CASCADE)
    server = models.ForeignKey("Server",on_delete=models.CASCADE)



# class ServiceDataset(models.Model):
#     service = models.ForeignKey("Service",on_delete=models.CASCADE)
#     dataset = models.ForeignKey("dataset.Dataset",on_delete=models.CASCADE)
#


class ServiceModel(models.Model):
    service = models.ForeignKey("Service",on_delete=models.CASCADE)
    ai_model = models.ForeignKey("model.AiModels",on_delete=models.CASCADE)
