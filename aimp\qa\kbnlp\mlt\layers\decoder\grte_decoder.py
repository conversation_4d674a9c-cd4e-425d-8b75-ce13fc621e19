import math
from time import perf_counter

from transformers.models.bert.modeling_bert import <PERSON><PERSON><PERSON><PERSON><PERSON>, BertOutput, BertAttention, BertSelfAttention, BertModel, BertPreTrainedModel
import torch
from torch import nn

from qa.kbnlp.mlt.util import merge_locals_kwargs
from qa.kbnlp.mlt.layers.biaffine import Biaffine


class GRTEDecoder(nn.Module):
    def __del__(self):
        if hasattr(self, 'time'):
            print('time', self.time)
            print('count', self.count)

    def __init__(self,
                 num_p,
                 num_label,
                 rounds=2,
                 hidden_size=768,
                 num_attention_heads=12,
                 attention_probs_dropout_prob=0.1,
                 hidden_dropout_prob=0.1,
                 max_position_embeddings=512,
                 is_decoder=False,
                 layer_norm_eps=1e-12,
                 extra_layer_normal=False,
                 intermediate_size=3072,
                 hidden_act='gelu',
                 **kwargs):
        super().__init__()
        config = GRTEConfig(**merge_locals_kwargs(locals(), kwargs, excludes=['self', 'kwargs', '__class__']))
        self.rounds = config.rounds
        self.num_p = config.num_p
        self.num_label = config.num_label
        self.hidden_size = config.hidden_size
        self.layer_norm_eps = layer_norm_eps
        self.extra_layer_normal = extra_layer_normal
        self.dropout = nn.Dropout(config.hidden_dropout_prob)
        self.Lr_e1 = nn.Linear(config.hidden_size, config.hidden_size)
        self.Lr_e2 = nn.Linear(config.hidden_size, config.hidden_size)
        self.time = 0
        self.count = 0

        # if config.method == 0:  # 方案一
        self.elu = nn.ReLU()
        self.Cr = nn.Linear(config.hidden_size, config.num_p * config.num_label)
        # elif config.method == 1:  # 方案二
        # self.biaffine = Biaffine(config.hidden_size, config.hidden_size, config.num_p * config.num_label)
        # elif config.method == 2:  # 方案三
        #     self.relu = nn.ReLU()
        #     self.cat_linear1 = nn.Linear(config.hidden_size, config.num_p * config.num_label, bias=False)
        #     self.cat_linear2 = nn.Linear(config.hidden_size, config.num_p * config.num_label, bias=False)
        #     self.bias = nn.Parameter(torch.Tensor(config.num_p * config.num_label))

        self.Lr_e1_rev = nn.Linear(config.num_p * config.num_label, config.hidden_size)
        self.Lr_e2_rev = nn.Linear(config.num_p * config.num_label, config.hidden_size)

        self.e_layer = GRTETransformerLayer(config)
        if self.extra_layer_normal:
            self.layer_norm1 = nn.LayerNorm(self.hidden_size, self.layer_norm_eps)
            self.layer_norm2 = nn.LayerNorm(self.hidden_size, self.layer_norm_eps)
        nn.init.orthogonal_(self.Lr_e1.weight, gain=1)
        nn.init.orthogonal_(self.Lr_e2.weight, gain=1)
        # if config.method == 0:  # 方案一
        nn.init.orthogonal_(self.Cr.weight, gain=1)
        # elif config.method == 1:  # 方案二
        # nn.init.orthogonal_(self.biaffine.bilinear.weight, gain=1)
        # elif config.method == 2:  # 方案三
        #     torch.nn.init.orthogonal_(self.cat_linear1.weight, gain=1)
        #     torch.nn.init.orthogonal_(self.cat_linear2.weight, gain=1)
        #     bound = 1 / math.sqrt(2 * self.cat_linear1.weight.size(0))
        #     torch.nn.init.uniform_(self.bias, -bound, bound)

        nn.init.orthogonal_(self.Lr_e1_rev.weight, gain=1)
        nn.init.orthogonal_(self.Lr_e2_rev.weight, gain=1)

    def forward(self, embed, mask_token_ids):  # embed:(B,L,H)
        B, L = embed.shape[0], embed.shape[1]  # L表示包括CLS和SEP在内的最大句长。
        e1 = self.Lr_e1(embed)  # BLH
        e2 = self.Lr_e2(embed)  # BLH
        assert self.rounds > 0
        table_logit = None
        for i in range(self.rounds):
            # if self.config.method == 0:  # 方案一
            # h = self.elu(e1.unsqueeze(2).repeat(1, 1, L, 1) * e2.unsqueeze(1).repeat(1, L, 1, 1))  # (B,L,L,H)
            # table_logit = self.Cr(self.elu(e1.unsqueeze(2).repeat(1, 1, L, 1) * e2.unsqueeze(1).repeat(1, L, 1, 1)))  # (B,L,L,n_label*n_relation)
            table_logit = self.Cr(self.elu(torch.einsum('bik,bjk->bijk', e1, e2)))  # (B,L,L,n_label*n_relation)
            # torch.cuda.empty_cache()
            # elif self.config.method == 1:  # 方案二
            # table_logit = self.biaffine(e1, e2)
            # elif self.config.method == 2:  # 方案三
            #     logit1, logit2 = self.cat_linear1(self.relu(e1)), self.cat_linear2(self.relu(e2))
            #     table_logit = logit1.unsqueeze(2).repeat(1, 1, L, 1) + logit2.unsqueeze(1).repeat(1, L, 1,
            #                                                                                       1) + self.bias
            # else:
            #     raise ValueError
            if i != self.rounds - 1:

                  # (B, L, n_label*n_relation)

                  # (B, L, H)


                # if self.config.highway:
                if self.extra_layer_normal:
                    e1 = self.layer_norm1(e1 + self.e_layer(self.Lr_e1_rev(table_logit.max(dim=2).values), embed, mask_token_ids)[0])
                    e2 = self.layer_norm2(e2 + self.e_layer(self.Lr_e2_rev(table_logit.max(dim=1).values), embed, mask_token_ids)[0])
                else:
                    t = perf_counter()
                    m1 = self.Lr_e1_rev(table_logit.max(dim=2).values)
                    m2 = self.Lr_e2_rev(table_logit.max(dim=1).values)
                    self.time += perf_counter() - t
                    self.count += 1
                    e1 = e1 + self.e_layer(m1, embed, mask_token_ids)[0]
                    e2 = e2 + self.e_layer(m2, embed, mask_token_ids)[0]
                # else:
                #e1 = self.e_layer(e1_, embed, mask_token_ids)[0]
                #e2 = self.e_layer(e2_, embed, mask_token_ids)[0]


        return table_logit.reshape([B, L, L, self.num_p, self.num_label])


class GRTEConfig:
    def __init__(self,
                 num_p,
                 num_label,
                 rounds=2,
                 hidden_size=768,
                 num_attention_heads=12,
                 attention_probs_dropout_prob=0.1,
                 hidden_dropout_prob=0.1,
                 max_position_embeddings=512,
                 is_decoder=False,
                 layer_norm_eps=1e-12,
                 intermediate_size=3072,
                 hidden_act='gelu',
                 **kwargs,
                 ):  # 默认参数是bert base的参数。
        self.num_p = num_p
        self.num_label = num_label
        self.rounds = rounds
        self.hidden_size = hidden_size
        self.num_attention_heads = num_attention_heads
        self.attention_probs_dropout_prob = attention_probs_dropout_prob
        self.hidden_dropout_prob = hidden_dropout_prob
        self.max_position_embeddings = max_position_embeddings
        self.is_decoder = is_decoder
        self.layer_norm_eps = layer_norm_eps
        self.intermediate_size = intermediate_size
        self.hidden_act = hidden_act


class GRTETransformerLayer(nn.Module):
    def __init__(self, config):
        super().__init__()
        self.attention = BertAttention(config)
        self.crossattention = BertAttention(config)
        self.intermediate = BertIntermediate(config)
        self.output = BertOutput(config)

    def forward(
            self,
            hidden_states,  # B num_generated_triples H
            encoder_hidden_states,
            encoder_attention_mask
    ):
        self_attention_outputs = self.attention(hidden_states)
        attention_output = self_attention_outputs[0]  # hidden_states.shape
        outputs = self_attention_outputs[1:]  # add self attentions if we output attention weights

        encoder_batch_size, encoder_sequence_length, _ = encoder_hidden_states.size()
        encoder_hidden_shape = (encoder_batch_size, encoder_sequence_length)
        if encoder_attention_mask.dim() == 3:
            encoder_extended_attention_mask = encoder_attention_mask[:, None, :, :]
        elif encoder_attention_mask.dim() == 2:
            encoder_extended_attention_mask = encoder_attention_mask[:, None, None, :]  # B 1 1 H
        else:
            raise ValueError(
                "Wrong shape for encoder_hidden_shape (shape {}) or encoder_attention_mask (shape {})".format(
                    encoder_hidden_shape, encoder_attention_mask.shape
                )
            )
        encoder_extended_attention_mask = (
                                                      1.0 - encoder_extended_attention_mask) * -10000.0  # 1 1 0 0 -> 0 0 -1000 -1000
        cross_attention_outputs = self.crossattention(
            hidden_states=attention_output, encoder_hidden_states=encoder_hidden_states,
            encoder_attention_mask=encoder_extended_attention_mask
        )
        attention_output = cross_attention_outputs[0]  # B m H
        outputs = outputs + cross_attention_outputs[1:]  # add cross attentions if we output attention weights

        intermediate_output = self.intermediate(attention_output)
        layer_output = self.output(intermediate_output, attention_output)  # B m H
        outputs = (layer_output,) + outputs
        return outputs
