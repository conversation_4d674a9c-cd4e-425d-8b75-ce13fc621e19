# -*- coding:utf-8 -*-
import re

import jsonlines
import os
import openpyxl
from openpyxl import load_workbook, Workbook
from datetime import datetime
from aimp.qa.kbnlp.experiment.yml.volume1_process import clean_ocr_result
"""
为了合并检验过的数据
"""


def get_unique_key(path):
    wb2 = load_workbook(path)
    ws2 = wb2.active
    names = []
    sacrifice_date = []
    birthday = []
    join_army_date = []
    troops = []
    for i, col in enumerate(ws2.columns):
        datas = [r.value for r in col]
        if datas[0] == '牺牲时间':
            sacrifice_date.extend(datas)
        if datas[0] == '出生日期':
            birthday.extend(datas)
        if datas[0] == '参军时间':
            join_army_date.extend(datas)
        if datas[0] == '姓名':
            names.extend(datas)
        if datas[0] == '所属军团':
            troops.extend(datas)

    res = [(name, ) for name, sac, birth, army, troops in zip(names, sacrifice_date, birthday, join_army_date, troops)]
    print(len(res), len(set(res)))
    return res


path1 = r'D:\workspace\pycharm\nlp_project\yml6kbqa\data\二等功及以上英雄.xlsx'
path2 = r'D:\workspace\pycharm\nlp_project\yml6kbqa\data\二等功及以上英雄_jwd.xlsx'
keys1 = get_unique_key(path1)

s = set()
namesake1 = set()
for i, (name, ) in enumerate(keys1):
    if (name, ) not in s:
        s.add((name, ))
    else:
        namesake1.add(name)

print('*' * 100)
keys2 = get_unique_key(path2)

s = set()
namesake2 = set()
for i, (name, ) in enumerate(keys2):
    if (name, ) not in s:
        s.add((name, ))
    else:
        namesake2.add(name)
print(namesake1)
print(namesake2)


conflict1 = []
for i, (name,) in enumerate(keys1):
    if (name,) not in keys2:
        conflict1.append(name)
        # print(i, name)
print(len(conflict1))
print(conflict1)

conflict2 = []
for i, (name,) in enumerate(keys2):
    if (name,) not in keys1:
        conflict2.append(name)
        # print(i, name)
print(len(conflict2))
print(conflict2)


def time_format(time):

    if isinstance(time, datetime):
        res = ''
        if time.year != 0:
            res += '%d年' % time.year
        if time.month != 0:
            res += '%d月' % time.month
        if time.day != 0:
            res += '%d日' % time.day
        return res
    return time


wb1 = load_workbook(filename=r'D:\workspace\pycharm\nlp_project\yml6kbqa\data\二等功及以上英雄.xlsx')
ws1 = wb1.active
column_names1 = []
examples1 = []

wb2 = load_workbook(filename=r'D:\workspace\pycharm\nlp_project\yml6kbqa\data\二等功及以上英雄_jwd.xlsx')
ws2 = wb2.active
column_names2 = []
examples2 = []
name2example = dict()
for i, line in enumerate(ws2.rows):
    if i == 0:
        column_names2.extend([col.value for col in line if col.value])
    else:
        examples2.append([col.value for col in line][:len(column_names2)])
        if i == 156 and examples2[-1][0] == '孟进':
            print(examples2[-1][0])
            examples2[-1][5] = '1938年10月'
        if i == 157 and examples2[-1][0] == '陈亮':
            print(examples2[-1][0])
            examples2[-1][5] = '1943年6月'
        if i == 159 and examples2[-1][0] == '贾广和':
            print(examples2[-1][0])
            examples2[-1][5] = '1939年1月'
        if i == 160 and examples2[-1][0] == '樊玉祥':
            print(examples2[-1][0])
            examples2[-1][5] = '1938年3月'
        if i == 161 and examples2[-1][0] == '林广山':
            print(examples2[-1][0])
            examples2[-1][5] = '1940年2月'
        # {164, 165, 167, 168, 169}
        # if i == :
        #     examples2[-1][5] = '1938年10月'
        name2example[examples2[-1][0]] = examples2[-1]

doubtful_id = []
for i, line in enumerate(ws1.rows):
    if i == 164:
        print()
    if i == 0:
        column_names1.extend([col.value for col in line])
    else:
        line = [col.value for col in line]
        if line[1] not in namesake1:  # 姓名
            if line[1] not in conflict1:
                modified_line = [time_format(w) if isinstance(w, datetime) else w for w in name2example[line[1]]]
                modified_line.insert(0, str(i))
                modified_line.insert(-1, line[-2])
                examples1.append(modified_line)
            else:
                doubtful_id.append((i, line[1]))
                examples1.append(line)
        else:
            examples1.append(line)
            doubtful_id.append((i, line[1]))


print(doubtful_id)
volume1_examples = clean_ocr_result(path=r'D:\解放军档案馆\ocr\中国人民志愿军烈士英名录第1卷', to_path=None, select_3_sentence=False)
volume1_153 = volume1_examples[:153]

for i, (exam1, exam2) in enumerate(zip(examples1, volume1_153)):
    if exam1[1] in exam2['text']:
        exam1[9] = exam2['text']
    else:
        if '贫宝山' in exam2['text']:
            text = re.sub('贫宝山|宝山', '贠宝山', exam2['text'])
            exam1[9] = text
        else:
            print(i + 1, exam1[1])

# excel单元格能存放的字符有限
wb = Workbook()
ws = wb.active
ws.append(column_names1)
for exam in examples1:
    ws.append(exam)
wb.save('二等功及以上英雄著录.xlsx')
print(len(examples1))
print()
res = []
for line in examples1:
    try:
        assert len(line) == len(column_names1)
    except Exception as e:
        print(len(line), len(column_names1))
    line = dict([(k, v) for k, v in zip(column_names1, line)])
    res.append(line)
with jsonlines.open('二等功及以上英雄著录.jsonl', mode='w') as jw:
    jw.write_all(res)
