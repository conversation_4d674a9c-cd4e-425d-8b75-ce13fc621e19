# -*- coding: utf-8 -*-
# __author__:SH
# 2022/9/28 10:33
import json
import os
import time

import cv2
from asgiref.sync import sync_to_async
from django.db import connection
from django.db.models import Q
from fastapi import APIRouter, Depends, UploadFile, Request
from aimp.config import *
from utils.text import *
# Path, Query, UploadFile, Header, Cookie, Response,
from .schema import *

api = APIRouter()


def get_all_count(_data, begin, end, dataset_type):
    if dataset_type > 4:
        return _data.rawimage_set.filter(raw_status__gt=begin, raw_status__lt=end).all().count()
    else:
        return _data.rawtext_set.filter(raw_status__gt=begin, raw_status__lt=end).all().count()


# 单条dataset
@api.get("/dataset", response_model=DataSetProperty)
async def dataset(dataset_id: IdIn = Depends()):
    from .models import DataSet
    obj = await DataSet.objects.filter(id=dataset_id.id).afirst()
    return {
        "dataset_id": obj.id,
        "dataset_type": obj.dataset_type,
        "dataset_name": obj.dataset_name,
        "create_user": obj.create_user,
        "create_time": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(obj.create_time)),
        "amount": await sync_to_async(get_all_count)(obj, 0, 4, obj.dataset_type),
        "dataset_amount": await sync_to_async(get_all_count)(obj, 1, 4, obj.dataset_type),
        "inspection_count": await sync_to_async(get_all_count)(obj, 2, 4, obj.dataset_type),
        "inspection_precent": f"{await sync_to_async(get_all_count)(obj, 2, 4, obj.dataset_type)}/{await sync_to_async(get_all_count)(obj, 1, 4, obj.dataset_type)}"
                              f"({round(await sync_to_async(get_all_count)(obj, 2, 4, obj.dataset_type) / await sync_to_async(get_all_count)(obj, 1, 4, obj.dataset_type) * 100, 2)}%)"
        if await sync_to_async(get_all_count)(obj, 1, 4, obj.dataset_type) != 0 else "0/0(0.00%)",
        "update_time": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(obj.update_time)),
        "rate": f"{await sync_to_async(get_all_count)(obj, 1, 4, obj.dataset_type)}/{await sync_to_async(get_all_count)(obj, 0, 4, obj.dataset_type)}"
                f"({round(await sync_to_async(get_all_count)(obj, 1, 4, obj.dataset_type) / await sync_to_async(get_all_count)(obj, 0, 4, obj.dataset_type) * 100, 2)}%)"
        if await sync_to_async(get_all_count)(obj, 0, 4, obj.dataset_type) != 0 else "0/0(0.00%)",
    }


# 单条dataset
@api.post("/dataset", response_model=ResponseMsg)
def dataset(dataset_param: DataSetIn = Body()):
    from .models import DataSet

    obj = DataSet.objects.create(
        dataset_name=dataset_param.dataset_name,
        create_user=dataset_param.create_user,
        dataset_type=dataset_param.dataset_type)

    obj.save()
    return {'msg': "添加成功"}


# 单条dataset
@api.put("/dataset", response_model=Code)
def dataset(param: DataSetUpdata = Body()):
    from .models import DataSet
    dataset = DataSet.objects.filter(id=param.dataset_id).first()
    if not dataset.datasettag_set.all() and not dataset.rawimage_set.all() and not dataset.rawtext_set.all():
        dataset.dataset_type = param.dataset_type
        dataset.dataset_name = param.dataset_name
        dataset.save()
        return {'code': 1}
    else:
        return {"code": 0}


# 单条dataset
@api.delete("/dataset", response_model=ResponseMsg)
def dataset(dataset_param: IdInList = Body()):
    from .models import DataSet
    DataSet.objects.filter(id__in=dataset_param.id_list).delete()
    return {"msg": "删除成功"}


# 图像dataset
@api.get("/rawdataset", response_model=DataSetProperty)
def dataset(raw_id: IdIn = Depends()):
    from .models import DataSet, RawImage
    raw_image = RawImage.objects.filter(id=raw_id.id).first()
    dataset_id = raw_image.data_id.id
    obj = DataSet.objects.filter(id=dataset_id).first()
    return {
        "dataset_id": obj.id,
        "dataset_type": obj.dataset_type,
        "dataset_name": obj.dataset_name,
        "create_user": obj.create_user,
        "create_time": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(obj.create_time)),
        "amount": get_all_count(obj, 0, 4, obj.dataset_type),
        "dataset_amount": get_all_count(obj, 1, 4, obj.dataset_type),
        "inspection_count": get_all_count(obj, 2, 4, obj.dataset_type),
        "inspection_precent": f"{get_all_count(obj, 2, 4, obj.dataset_type)}/{get_all_count(obj, 1, 4, obj.dataset_type)}"
                              f"({round(get_all_count(obj, 2, 4, obj.dataset_type) / get_all_count(obj, 1, 4, obj.dataset_type) * 100, 2, obj.dataset_type)}%)"
        if get_all_count(obj, 1, 4, obj.dataset_type) != 0 else "0/0(0.00%)",
        "update_time": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(obj.update_time)),
        "rate": f"{get_all_count(obj, 1, 4, obj.dataset_type)}/{get_all_count(obj, 0, 4, obj.dataset_type)}"
                f"({round(get_all_count(obj, 1, 4, obj.dataset_type) / get_all_count(obj, 0, 4, obj.dataset_type) * 100, 2, obj.dataset_type)}%)"
        if get_all_count(obj, 0, 4, obj.dataset_type) != 0 else "0/0(0.00%)",
    }


@api.get("/datasetlist")
def dataset_list():
    from .models import DataSet
    return_dict = {}
    dataset_list = DataSet.objects.filter().all()
    for dataset in dataset_list:
        if dataset.dataset_type not in return_dict:
            return_dict[dataset.dataset_type] = [{
                "name": dataset.dataset_name,
                "id": dataset.id
            }]
        else:
            return_dict[dataset.dataset_type].append({
                "name": dataset.dataset_name,
                "id": dataset.id
            })
    return return_dict


# dataset_list
@api.post('/datasetlist', response_model=DataSetOutList)
async def dataset_list(
        param: DataSetInList = Body(),
        param_page: Paging = Body()):
    from .models import DataSet
    objs = DataSet.objects.filter(
        Q(dataset_name__contains=param.search_data) & Q(dataset_type__in=param.data_type_list)).all()
    count = await objs.acount()
    objs = objs.order_by(param_page.order_key if param_page.order == 1 else f"-{param_page.order_key}").all()[
           param_page.page * param_page.size - param_page.size: param_page.page * param_page.size]
    # await sync_to_async(get_all_count)(await objs[2])
    return {
        'count': count,
        'datasets': [
            {
                "key": obj.id,
                "id": obj.id,
                "dataset_name": obj.dataset_name,
                "dataset_type": obj.dataset_type,
                "create_user": obj.create_user,
                "create_time": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(obj.create_time)),
                "amount": await sync_to_async(get_all_count)(obj, 0, 4, obj.dataset_type),
                "mark_precent": f"{await sync_to_async(get_all_count)(obj, 1, 4, obj.dataset_type)}/{await sync_to_async(get_all_count)(obj, 0, 4, obj.dataset_type)}",
                "update_time": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(obj.update_time)),
                "rate": f"{await sync_to_async(get_all_count)(obj, 1, 4, obj.dataset_type)}/{await sync_to_async(get_all_count)(obj, 0, 4, obj.dataset_type)}"
                        f"({round(await sync_to_async(get_all_count)(obj, 1, 4, obj.dataset_type) / await sync_to_async(get_all_count)(obj, 0, 4, obj.dataset_type), 4) * 100}%)"
                if await sync_to_async(get_all_count)(obj, 0, 4, obj.dataset_type) != 0 else "0/0(0.00%)",
            } async for obj in objs
        ]
    }


@api.get("/adjacentid", response_model=IdIn)
def adjacent_id(param: AdjacentID = Depends()):
    from .models import RawText, RawImage
    if param.dataset_type > 4:
        # 获取所有文本
        raw = RawImage.objects.filter(raw_status=param.ope_type).all()
    else:
        raw = RawText.objects.filter(raw_status=param.ope_type).all()
    if param.position == 0:
        raw = raw.filter(id__lt=param.id, data_id_id=param.dataset_id).last()
    else:
        raw = raw.filter(id__gt=param.id, data_id_id=param.dataset_id).first()
    return {"id": raw.id if raw else 0 - param.position}


# 标签
@api.get("/tag", response_model=TagList)
def tag(param: IdIn = Depends()):
    from .models import DataSet
    # objs = Tag.objects.filter(data_id=dataset_param.id).all()
    dataset = DataSet.objects.filter(id=param.id).first()
    return {
        "tag_list": [
            {
                "id": obj.id,
                "tag_id": obj.tag_id.id,
                "tag": obj.tag_id.tag_name,
                "shoutcut_key": obj.shoutcut_key,
                "color": change_color(obj.color)[0],
                "fill_color": change_color(obj.color)[1],
            } for obj in dataset.datasettag_set.all()
        ]
    }


# 标签
@api.post("/tag", response_model=ResponseMsg)
def tag(param: TagIn = Body()):
    from .models import Tag, DataSet, DatasetTag
    tag = Tag.objects.filter(tag_name=param.tag_name).first()
    if not tag:
        tag = Tag.objects.create(tag_name=param.tag_name)
        tag.save()
    data_set = DataSet.objects.filter(id=param.dataset_id).first()
    dataset_tag = DatasetTag.objects.filter(data_id=data_set, tag_id=tag).first()
    if not dataset_tag:
        dataset_tag = DatasetTag.objects.create(data_id=data_set, tag_id=tag,
                                                color=param.color, shoutcut_key=param.shoutcut_key)
    else:
        dataset_tag.color = param.color
        dataset_tag.shoutcut_key = param.shoutcut_key
    dataset_tag.save()
    return {"msg": "添加成功"}


# 标签
@api.delete("/tag", response_model=ResponseMsg)
def tag(dataset_param: IdIn = Body()):
    from .models import DatasetTag
    DatasetTag.objects.filter(id=dataset_param.id).delete()
    return {"msg": "删除成功"}


# 标签
@api.get("/intentiontag", response_model=IntentionTagList)
def intention_tag(param: IntentionIn = Depends()):
    from .models import DataSet
    dataset = DataSet.objects.filter(id=param.id).first()
    return {
        "tag_list": [
            {
                "id": obj.id,
                "tag_id": obj.tag_id.id,
                "tag": obj.tag_id.tag_name,
            } for obj in dataset.tagintention_set.all()
        ]
    }


@api.get("/intentionstatus", response_model=IdIn)
def intention_tag(param: IdIn = Depends()):
    from .models import RawIntention
    raw_intention = RawIntention.objects.filter(raw_id=param.id).first()
    return {"id": raw_intention.tag_type_id if raw_intention else 0}


@api.get("/slot", response_model=SlotListOut)
def raw_slot(param: IdIn = Depends()):
    from .models import TagSlot
    tag_slots = TagSlot.objects.filter(tagintentions_id=param.id).all()
    return {
        "slots": [
            {
                "id": tag_slot.id,
                "name": tag_slot.slot
            } for tag_slot in tag_slots
        ]
    }


@api.post("/slot", response_model=ResponseMsg)
def raw_slot(param: IdIn = Body()):
    from .models import TagSlot, TagIntention
    tag_intention = TagIntention.objects.filter(id=param.id).first()
    tag_slot = TagSlot.objects.create(tagintentions=tag_intention, slot="新增槽位")
    tag_slot.save()
    return {"msg": "创建成功"}


@api.put("/slot", response_model=ResponseMsg)
def raw_slot(param: UpdateSlotIn = Body()):
    from .models import TagSlot
    tag_slot = TagSlot.objects.filter(id=param.id).first()
    tag_slot.slot = param.slot_name
    tag_slot.save()
    return {"msg": "修改成功"}


@api.delete("/slot", response_model=ResponseMsg)
def raw_slot(param: IdIn = Body()):
    from .models import TagSlot
    TagSlot.objects.filter(id=param.id).delete()
    return {"msg": "删除成功"}


@api.delete("/slottext", response_model=ResponseMsg)
def delete_slottext(param: IdIn = Body()):
    from .models import SlotText
    SlotText.objects.filter(id=param.id).delete()
    return {"msg": "删除成功"}


# 标签
@api.put("/intentiontag", response_model=ResponseMsg)
def intention_tag(param: IntentionIn = Body()):
    from .models import RawIntention, RawText
    raw_intention = RawIntention.objects.filter(raw_id=param.raw_id).first()
    if not raw_intention:
        raw_intention = RawIntention.objects.create(raw_id=param.raw_id,
                                                    tag_type_id=param.id)

    elif raw_intention.tag_type_id != param.id:
        raw_intention.tag_type_id = param.id
    raw_intention.save()
    raw_text = RawText.objects.filter(id=param.raw_id).first()
    raw_text.raw_status = 2
    raw_text.save()
    return {"msg": "创建成功"}


# 文本列表
@api.get("/rawtext", response_model=RawTextList)
async def rawtext(param: RawIn = Depends(),
                  param_page: Paging = Depends()):
    from .models import RawText, DataSet
    dataset = DataSet.objects.filter(id=param.dataset_id)
    dataset = await dataset.afirst()
    param_page.order_key = param_page.order_key if param_page.order == 1 else f"-{param_page.order_key}"
    objs = RawText.objects.filter(text__contains=param.search_data, data_id=dataset).order_by(
        param_page.order_key).all()
    count = await objs.acount()
    objs = objs.order_by().all()[
           param_page.page * param_page.size - param_page.size: param_page.page * param_page.size]
    return {
        'count': count,
        "rawtexts": [
            {
                "key": obj.id,
                "id": obj.id,
                "text": obj.text,
                "status": obj.raw_status,
            } async for obj in objs
        ]
    }


def insert_raw_text(data, _id):
    from .models import RawText
    raw = [RawText(text=i.strip(), data_id_id=_id) for i in data]
    RawText.objects.bulk_create(raw)


@api.delete("/raw", response_model=ResponseMsg)
def raw_data(param: RawStatus = Body()):
    from .models import RawText, RawImage
    if param.dataset_type > 4:
        RawImage.objects.filter(id=param.raw_id).delete()
    else:
        RawText.objects.filter(id=param.raw_id).delete()
    return {"msg": "删除成功"}


@api.post("/rawtext", response_model=ResponseMsg)
async def get_image(requests: Request, file: UploadFile):
    contents = await file.read()
    file_name = file.filename
    _id = dict(requests.headers)["dataset_id"]
    if not os.path.exists("file/dataset"):
        os.mkdir("file/dataset")
    if not os.path.exists(f"file/dataset/{_id}"):
        os.mkdir(f"file/dataset/{_id}")
    with open(f"file/dataset/{_id}/{file_name}", "wb+") as f:
        f.write(contents)
    with open(f"file/dataset/{_id}/{file_name}", "r", encoding="utf-8") as f:
        data = f.readlines()
    await sync_to_async(insert_raw_text)(data=data, _id=_id)
    return {"msg": "上传成功"}


# 文本内容
@api.delete("/rawtext", response_model=ResponseMsg)
def rawtext(rawtext: IdInList = Body()):
    from .models import RawText
    RawText.objects.filter(id__in=rawtext.id_list).delete()
    return {"msg": "删除成功"}


# 文本列表
@api.post("/rawtextlist", response_model=RawTextList)
async def rawtext(param: RawInList = Body(),
                  param_page: Paging = Body()):
    from .models import RawText, DataSet
    dataset = DataSet.objects.filter(id__in=param.dataset_id).all()
    param_page.order_key = param_page.order_key if param_page.order == 1 else f"-{param_page.order_key}"
    objs = RawText.objects.filter(text__contains=param.search_data, data_id__in=list(dataset)).order_by(
        param_page.order_key).all()
    count = await objs.acount()
    objs = objs.order_by().all()[
           param_page.page * param_page.size - param_page.size: param_page.page * param_page.size]
    return {
        'count': count,
        "rawtexts": [
            {
                "key": obj.id,
                "id": obj.id,
                "text": obj.text,
                "status": obj.raw_status,
            } async for obj in objs
        ]
    }


# 文本状态
@api.get("/rawstatus", response_model=ResponseInt)
def raw_status(param: RawStatus = Depends()):
    from .models import RawText, RawImage
    # 找到文本,查状态
    if param.dataset_type > 4:
        raw = RawImage.objects.filter(id=param.raw_id).first()
    else:
        raw = RawText.objects.filter(id=param.raw_id).first()
    return {"msg": raw.raw_status}


@api.get("/getrawid", response_model=IdIn)
def get_raw_id(param: IdIn = Depends()):
    from .models import DataSet
    dataset = DataSet.objects.filter(id=param.id).first()
    if dataset.dataset_type <= 4:
        raw_id = dataset.rawtext_set.first().id if dataset.rawtext_set.all() else 0
    else:
        raw_id = dataset.rawimage_set.first().id if dataset.rawimage_set.all() else 0
    return {"id": raw_id}


@api.put("/rawstatus", response_model=ResponseMsg)
def raw_status(param: RawStatus = Body()):
    from .models import RawImage, RawText
    if param.dataset_type > 4:
        raw = RawImage.objects.filter(id=param.raw_id).first()
    else:
        raw = RawText.objects.filter(id=param.raw_id).first()

    raw.raw_status = 3 if raw.raw_status < 3 else 2
    raw.save()
    return {"msg": "质检成功"}


# 文本状态
@api.delete("/rawstatus", response_model=ResponseMsg)
def raw_status(param: RawStatusDelete = Body()):
    from .models import RawText, RawImage
    # 找到文本,查状态
    if param.dataset_type > 4:
        raw = RawImage.objects.filter(id=param.raw_id).first()
        raw.tagimagetype_set.all().delete()
        raw.tagimage_set.all().delete()
    else:
        raw = RawText.objects.filter(id=param.raw_id).first()
        raw.tagtexttype_set.all().delete()
        raw.eventtext_set.all().delete()
        raw.relationtext_set.all().delete()
        raw.tagtext_set.all().delete()
        raw.slottext_set.all().delete()
        raw.rawintention_set.all().delete()
    raw.raw_status = 1 if param.status == 1 else 4
    raw.save()
    return {"msg": "删除成功"}


# def relation_ope(relations):
#     relation_pos = {}
#     return_relation = {}
#     for relation in relations:
#         relation_name = relation.relation_id.relation_name
#         line = relation.relation_line
#         relation_pos[line] = relation.relation_begin_y
#         if not return_relation.get(line):
#             return_relation[line] = []
#         return_relation[line].append(
#             {"text": relation_name,
#              "d": f"M {relation.relation_begin_x} {relation.relation_begin_y} v {relation.relation_end_y - relation.relation_begin_y} H {relation.relation_end_x - relation.relation_begin_x} v {relation.relation_begin_y - relation.relation_end_y}",
#              # 位置
#              "rx": f"""{(relation.relation_end_x + relation.relation_begin_x) / 2
#                         - len(relation_name) * 8 - 1}""",
#              "ry": f"""{relation.relation_end_y - relation.relation_end_y
#                         + 10}""",
#              "rc": "red",
#              "rw": f"{len(relation_name) * 12}",
#              "rh": f"{relation.relation_end_y + 5}",
#              "tx": f"""{(relation.relation_end_x + relation.relation_begin_x) / 2
#                         - len(relation_name) * 8}""",
#              "ty": f"{relation.relation_end_y + 5}",
#              "tc": "red"
#              })
#     return relation_pos, return_relation
#
#
# def tag_ope(tag_raws, events):
#     events = [{
#         i.event_tag.id: i.event.event_name
#     } for i in events]
#
#     return_tag = {}
#     return_events = {}
#     for tag_raw in tag_raws:
#         line = tag_raw.tag_line
#         if not return_tag.get(line):
#             return_tag[line] = []
#         if not return_tag.get(line):
#             return_events[line] = []
#         return_tag[line].append({
#             "text": tag_raw.raw_tag_id.tag_name,
#             "color": tag_raw.raw_tag_id.color,
#             "x1": tag_raw.tag_begin_x,
#             "x2": tag_raw.tag_end_x,
#             "y1": tag_raw.tag_begin_y,
#             "y2": tag_raw.tag_end_y,
#             "cx": tag_raw.tag_begin_x + 2,
#             "cy": tag_raw.tag_begin_y + 17,
#             "tx": tag_raw.tag_begin_x + 8,
#             "ty": tag_raw.tag_begin_y + 20,
#         })
#         return_events[line].append({
#             "x1": tag_raw.tag_begin_x,
#             "x2": tag_raw.tag_end_x,
#         })
#     return return_tag, return_events
#
#
# def get_ope(relations, raw_text, tag_raws, events):
#     line_relations, relations = relation_ope(relations)
#     tags, events = tag_ope(tag_raws, events)
#     texts = []
#
#     for i in range(len(raw_text)):
#         if events.get(i):
#             for j in events[i]:
#                 j["y1"] = line_relations[i] + 5 if relations.get(i) else 5
#                 j["y2"] = line_relations[i] + 21 if relations.get(i) else 21
#         texts.append({
#             "relations": relations[i] if relations.get(i) else "",
#             "text": {
#                 "text": raw_text[i],
#                 "color": "black",
#                 "x": 2,
#                 "y": line_relations[i] + 19 if relations.get(i) else 19,
#             },
#             "tags": tags[i],
#             "events": events[i]
#         })
#     return texts
#
#
# @api.post("/mark", response_model=ReturnList)
# def rawtext_status(rawid: IdIn = Body(),
#                    tagid: IdInList = Body()):
#     from .models import RawText, TagText, RelationText, EventText
#     # 获取所有文本
#
#     raw = RawText.objects.filter(id=rawid.id).first()
#     raw_texts = re.findall(r".{2,60}", raw.text)  # 多个文本行数
#     tag_raw = TagText.objects.filter(raw_text_id=rawid.id, raw_tag_id__in=tagid.id_list).all()
#     # 获取联系
#     relations = RelationText.objects.filter(rawtext_id=raw).all()
#     events = EventText.objects.filter(rawtext_id=raw).all()
#     # relations,texts,tags = get_ope(relations,raw_texts,tag_raw)
#     return_list = get_ope(relations, raw_texts, tag_raw, events)
#     return {"return_list": return_list}


def get_tag(raw, dataset_tag):
    tag = {}
    tag_max = {}
    tag_width = {}
    for tag_pos in raw:
        if not tag.get(tag_pos.tag_begin_line):
            tag[tag_pos.tag_begin_line] = []
            tag_max[tag_pos.tag_begin_line] = 0
        if not tag.get(tag_pos.tag_end_line):
            tag[tag_pos.tag_end_line] = []
            tag_max[tag_pos.tag_end_line] = 0
        if tag_pos.tag_begin_line_pos > tag_max[tag_pos.tag_begin_line]:
            tag_max[tag_pos.tag_begin_line] = tag_pos.tag_begin_line_pos
        if tag_pos.tag_end_line_pos > tag_max[tag_pos.tag_end_line]:
            tag_max[tag_pos.tag_end_line] = tag_pos.tag_end_line_pos
        if tag_pos.tag_begin_line == tag_pos.tag_end_line:
            tag[tag_pos.tag_begin_line].append({
                "id": tag_pos.id,
                "text": tag_pos.tag.tag_name,
                "color": change_color(dataset_tag[tag_pos.tag.id].color)[0],
                "fill_color": change_color(dataset_tag[tag_pos.tag.id].color)[1],
                "text_color": change_color(dataset_tag[tag_pos.tag.id].color)[2],
                "shoutcut_key": dataset_tag[tag_pos.tag.id].shoutcut_key,
                "x1": tag_pos.tag_begin_width,
                "x2": tag_pos.tag_end_width,
                "cx": tag_pos.tag_begin_width + 5,
                "tx": tag_pos.tag_begin_width + 15,
                "line_pos": tag_pos.tag_begin_line_pos
            })
            tag_width[tag_pos.id] = 0.5 * (tag_pos.tag_end_width + tag_pos.tag_begin_width)
        else:
            tag[tag_pos.tag_begin_line].append({
                "id": tag_pos.id,
                "text": tag_pos.tag.tag_name,
                "color": change_color(dataset_tag[tag_pos.tag.id].color)[0],
                "fill_color": change_color(dataset_tag[tag_pos.tag.id].color)[1],
                "text_color": change_color(dataset_tag[tag_pos.tag.id].color)[2],
                "shoutcut_key": dataset_tag[tag_pos.tag.id].shoutcut_key,
                "x1": tag_pos.tag_begin_width,
                "x2": 60 * 14,
                "cx": tag_pos.tag_begin_width + 5,
                "tx": tag_pos.tag_begin_width + 15,
                "line_pos": tag_pos.tag_begin_line_pos
            })
            tag_width[tag_pos.id] = 0.5 * (60 * 14 + tag_pos.tag_begin_width)
            tag[tag_pos.tag_end_line].append({
                "id": tag_pos.id,
                "text": tag_pos.tag.tag_name,
                "color": change_color(dataset_tag[tag_pos.tag.id].color)[0],
                "fill_color": change_color(dataset_tag[tag_pos.tag.id].color)[1],
                "text_color": change_color(dataset_tag[tag_pos.tag.id].color)[2],
                "shoutcut_key": dataset_tag[tag_pos.tag.id].shoutcut_key,
                "x1": 0,
                "x2": tag_pos.tag_end_width,
                "cx": 5,
                "tx": 15,
                "line_pos": tag_pos.tag_begin_line_pos
            })
    return tag, tag_max, tag_width


def get_intention(raw):
    tag = {}
    tag_max = {}
    tag_width = {}
    for tag_pos in raw:
        if not tag.get(tag_pos.tag_line):
            tag[tag_pos.tag_line] = []
            tag_max[tag_pos.tag_line] = 0
        if tag_pos.tag_line_pos > tag_max[tag_pos.tag_line]:
            tag_max[tag_pos.tag_line] = tag_pos.tag_line_pos
        tag[tag_pos.tag_line].append({
            "id": tag_pos.id,
            "text": tag_pos.slot.slot,
            "x1": tag_pos.tag_begin_width,
            "x2": tag_pos.tag_end_width,
            "cx": tag_pos.tag_begin_width + 5,
            "tx": tag_pos.tag_begin_width + 15,
            "line_pos": tag_pos.tag_line_pos
        })
        tag_width[tag_pos.id] = 0.5 * (tag_pos.tag_end_width + tag_pos.tag_begin_width)
        ## raw.tagtext_set.all()[0].tag.tagtext_set.filter()
    return tag, tag_max, tag_width


def get_relation(relations, tag_width):
    relation_dict = {}
    relation_max_line = {}
    for relation in relations:
        # 如果不存在这一行，就加入进去
        if not relation_dict.get(relation.relation_begin_line):
            relation_dict[relation.relation_begin_line] = []
            relation_max_line[relation.relation_begin_line] = 0
        if not relation_dict.get(relation.relation_end_line):
            relation_dict[relation.relation_begin_line] = []
            relation_max_line[relation.relation_begin_line] = 0
        # 判断这一行的最大值
        relation_max_line[relation.relation_begin_line] = max([
            relation.relation_begin_pos, relation_max_line[relation.relation_begin_line]
        ])
        relation_max_line[relation.relation_end_line] = max([
            relation.relation_end_pos, relation_max_line[relation.relation_end_line]
        ])
        if relation.relation_end_line == relation.relation_begin_line:
            relation_dict[relation.relation_begin_line].append({
                "id": relation.id,
                "relations": relation.relation_id.relation_name,
                "d1": f"M {tag_width[relation.relation_begin_id]} ",
                "d2": f"v -{relation.relation_begin_pos * 20 + 20} H {tag_width[relation.relation_end_id]} v {relation.relation_end_pos * 20 + 20}",
                "rx": f"{(tag_width[relation.relation_end_id] + tag_width[relation.relation_begin_id]) * 0.5 - len(relation.relation_id.relation_name) * 8}",
                "rc": "red",
                "rw": f"{len(relation.relation_id.relation_name) * 16}",
                "rh": "25",
                "ry": relation.relation_begin_pos * 20 + 20,
                "tx": f"{(tag_width[relation.relation_end_id] + tag_width[relation.relation_begin_id]) * 0.5 - len(relation.relation_id.relation_name) * 8}",
                "tc": "red"
            })
        else:
            pass
    return relation_dict, relation_max_line


def get_event(events, tag_width):
    event_dict = {}
    event_max_line = {}
    for event in events:
        # 如果不存在这一行，就加入进去
        if not event_dict.get(event.event_begin_line):
            event_dict[event.event_begin_line] = []
            event_max_line[event.event_begin_line] = 0
        if not event_dict.get(event.event_end_line):
            event_dict[event.event_begin_line] = []
            event_max_line[event.event_begin_line] = 0
        # 判断这一行的最大值
        event_max_line[event.event_begin_line] = max([
            event.event_begin_pos, event_max_line[event.event_begin_line]
        ])
        event_max_line[event.event_end_line] = max([
            event.event_end_pos, event_max_line[event.event_end_line]
        ])
        if event.event_end_line == event.event_begin_line:
            event_dict[event.event_begin_line].append({
                "events": event.event_id.event_name,
                "d1": f"M {tag_width[event.event_begin_id]} ",
                "d2": f"v -{event.event_begin_pos * 20 + 20} H {tag_width[event.event_end_id]} v {event.event_end_pos * 20 + 20}",
                "rx": f"{(tag_width[event.event_end_id] + tag_width[event.event_begin_id]) * 0.5 - len(event.event_id.event_name) * 8}",
                "rc": "red",
                "rw": f"{len(event.event_id.event_name) * 16}",
                "rh": "25",
                "ry": event.event_begin_pos * 20 + 20,
                "tx": f"{(tag_width[event.event_end_id] + tag_width[event.event_begin_id]) * 0.5 - len(event.event_id.event_name) * 8}",
                "tc": "red"
            })
        else:
            pass
    return event_dict, event_max_line


def get_y(relations_line, events_line, tag_line, max_line):
    return_dict = {}
    for i in range(0, max_line):
        return_dict[i] = {}
        return_dict[i]["relation"] = max([relations_line.get(i, 0), events_line.get(i, 0)]) * 25 + 5 + (
            return_dict[i - 1].get("tag", 0) + (tag_line.get(i - 1, -1) + 1) * 25 + 25 + 5 if return_dict.get(i - 1,
                                                                                                              {}) else 5 + (
                    tag_line.get(i, 0) * 25 + 25))
        return_dict[i]["event"] = max([relations_line.get(i, 0), events_line.get(i, 0)]) * 25 + 5 + (
            return_dict[i - 1].get("tag", 0) + (tag_line.get(i - 1, -1) + 1) * 25 + 25 + 5 if return_dict.get(i - 1,
                                                                                                              {}) else 5 + (
                    tag_line.get(i, 0) * 25 + 25))
        return_dict[i]["text"] = return_dict[i].get("relation") + 20
        return_dict[i]["tag"] = 5 + return_dict[i].get("text")
    return return_dict


# 获取标注
@api.post("/marktext", response_model=ReturnList)
def mark_text(rawid: IdIn = Body(),
              rawtype: RawType = Body(),
              tagid: IdInList = Body()):
    from .models import RawText, DatasetTag
    raw = RawText.objects.filter(id=rawid.id).first()
    dataset_tag = DatasetTag.objects.filter(data_id_id=raw.data_id_id, tag_id_id__in=tagid.id_list)
    dataset_tag = {i.tag_id_id: i for i in dataset_tag}
    tags, tag_line, tag_width = get_tag(raw.tagtext_set.all(), dataset_tag)
    relations, relations_line = get_relation(raw.relationtext_set.all().order_by("-relation_begin_pos"),
                                             tag_width) if rawtype.type == 2 else ({}, {})
    events, events_line = get_event(raw.eventtext_set.all().order_by("-event_begin_pos"),
                                    tag_width) if rawtype.type == 1 else ({}, {})
    raw_texts = re.findall(r".{2,60}", raw.text)
    data_y = get_y(relations_line, events_line, tag_line, len(raw_texts))
    return_list = [{
        "relations": [
            {
                "id": relation["id"],
                "text": relation["relations"],
                "d": f"{relation['d1']} {data_y[i]['relation']} {relation['d2']} ",
                "rx": relation["rx"],
                "rc": relation["rc"],
                "ry": data_y[i]['relation'] - relation["ry"] - 10,
                "rw": relation["rw"],
                "rh": relation["rh"],
                "tx": relation["tx"],
                "ty": data_y[i]['relation'] - relation["ry"] + 5,
                "tc": relation["tc"],
            }
            for relation in relations.get(i, [])
        ],
        # "relations":[],
        "text": {
            "text": raw_texts[i],
            "color": "black",
            "x": f"{2}",
            "y": data_y[i]["text"]
        },
        "tags": [
            dict(tag, **{
                "y1": data_y[i]["tag"] + tag["line_pos"] * 30 + 2,
                "y2": data_y[i]["tag"] + tag["line_pos"] * 30 + 2,
                "cy": data_y[i]["tag"] + tag["line_pos"] * 30 + 15,
                "ty": data_y[i]["tag"] + tag["line_pos"] * 30 + 20,
            }) for tag in tags.get(i, [])],
        "events": [
            {
                "text": event["events"],
                "d": f"{event['d1']} {data_y[i]['event']} {event['d2']} ",
                "rx": event["rx"],
                "rc": event["rc"],
                "ry": data_y[i]['event'] - event["ry"] - 10,
                "rw": event["rw"],
                "rh": event["rh"],
                "tx": event["tx"],
                "ty": data_y[i]['event'] - event["ry"] + 5,
                "tc": event["tc"],
            }
            for event in events.get(i, [])
        ]
    } for i in range(0, len(raw_texts))]
    return {"return_list": return_list}


@api.post("/markintention", response_model=IntentionList)
def mark_intention(rawid: IdIn = Body()):
    from .models import RawText
    raw = RawText.objects.filter(id=rawid.id).first()

    tags, tag_line, tag_width = get_intention(raw.slottext_set.all())
    raw_texts = re.findall(r".{2,60}", raw.text)
    relations_line = {}
    events_line = {}
    data_y = get_y(relations_line, events_line, tag_line, len(raw_texts))
    return_list = [{
        "text": {
            "text": raw_texts[i],
            "color": "black",
            "x": f"{2}",
            "y": data_y[i]["text"]
        },
        "tags": [
            dict(tag, **{
                "y1": data_y[i]["tag"] + tag["line_pos"] * 30 + 2,
                "y2": data_y[i]["tag"] + tag["line_pos"] * 30 + 2,
                "cy": data_y[i]["tag"] + tag["line_pos"] * 30 + 15,
                "ty": data_y[i]["tag"] + tag["line_pos"] * 30 + 20,
            }) for tag in tags.get(i, [])],
    } for i in range(0, len(raw_texts))]
    return {"return_list": return_list}


@api.post("/createrelation", response_model=ResponseMsg)
def create_relaion(param: IdIn = Body()):
    from .models import Relation, DatasetTag
    dataset_tag = DatasetTag.objects.filter(data_id_id=param.id).first()
    tag_id_id = dataset_tag.tag_id_id
    relation = Relation.objects.create(relation_name="新增关系", data_id_id=param.id, tag_begin_id=tag_id_id,
                                       tag_end_id=tag_id_id)
    relation.save()
    return {"msg": "创建成功"}


@api.post("/createevent", response_model=ResponseMsg)
def create_relaion(param: IdIn = Body()):
    from .models import Event, DatasetTag, EventTag
    dataset_tag = DatasetTag.objects.filter(data_id_id=param.id).first()
    tag_id_id = dataset_tag.tag_id_id
    event = Event.objects.create(event_name="新增事件", dataset_id=param.id)
    event.save()
    event_tag = EventTag.objects.create(event_id=event.id, tag_id=tag_id_id)
    event_tag.save()
    return {"msg": "创建成功"}


@api.put("/createrelation", response_model=ResponseMsg)
def create_relaion(param: CreateRelation = Body()):
    from .models import Relation
    relation = Relation.objects.filter(id=param.id).first()
    if param.option == 1:
        relation.tag_begin_id = param.tag_id
    else:
        relation.tag_end_id = param.tag_id
    relation.save()
    return {"msg": "修改成功"}


@api.put("/createevent", response_model=ResponseMsg)
def create_event(param: CreateEvent = Body()):
    from .models import Event, DatasetTag, EventTag
    dataset_tag = DatasetTag.objects.filter(data_id_id=param.id).first()
    tag_id_id = dataset_tag.tag_id_id
    event = Event.objects.create(event_name="新增事件", dataset_id=param.id)
    event.save()
    event_tag = EventTag.objects.create(event_id=event.id, tag_id=tag_id_id)
    event_tag.save()
    return {"msg": "创建成功"}


# @api.put("/createrelation",response_model=ResponseMsg)
# def create_relaion(param:CreateRelation=Body()):
#     from .models import Relation
#     relation = Relation.objects.filter(id=param.id).first()
#     if param.option == 1:
#         relation.tag_begin_id = param.tag_id
#     else:
#         relation.tag_end_id = param.tag_id
#     relation.save()
#     return {"msg":"修改成功"}


# @api.post("/createevent",response_model=ResponseMsg)
# def create_relaion(param:IdIn=Body()):
#     from .models import Event,DatasetTag,EventTag
#     dataset_tag = DatasetTag.objects.filter(data_id_id=param.id).first()
#     tag_id_id = dataset_tag.tag_id_id
#     event = Event.objects.create(event_name="新增事件",dataset_id=param.id)
#     event.save()
#     event_tag = EventTag.objects.create(event_id=event.id,tag_id = tag_id_id)
#     event_tag.save()
#     return {"msg":"创建成功"}


# 创建标签
@api.post("/texttag", response_model=ResponseMsg)
def text_tag(param: PostTagTextIn = Body()):
    from .models import TagText, RawText
    raw = RawText.objects.filter(id=param.raw_id).first()
    raw_texts = re.findall(r".{2,60}", raw.text)
    if raw_texts.index(param.tag_begin_text) * 60 + param.tag_begin_x < \
            raw_texts.index(param.tag_end_text) * 60 + param.tag_end_x:
        # 开始行数和结束行数
        begin_line = raw_texts.index(param.tag_begin_text)
        end_line = raw_texts.index(param.tag_end_text)
        tag_begin_x = param.tag_begin_x
        tag_end_x = param.tag_end_x
        tag_begin_width = param.tag_begin_text[0:tag_begin_x]
        tag_end_width = param.tag_end_text[0:tag_end_x]
    else:
        begin_line = raw_texts.index(param.tag_end_text)
        end_line = raw_texts.index(param.tag_begin_text)
        tag_end_x = param.tag_begin_x
        tag_begin_x = param.tag_end_x
        # 开始的位置
        tag_begin_width = param.tag_end_text[0:tag_begin_x]
        tag_end_width = param.tag_begin_text[0:tag_end_x]
    # 结束的位置
    tag_begin_width = character_classify(tag_begin_width)
    tag_end_width = character_classify(tag_end_width)
    _sql = f"""
        select * from dataset_tagtext where rawtext_id={param.raw_id} and
        (tag_text_begin<={tag_begin_x + begin_line * 60} or tag_text_end>={tag_end_x + end_line * 60})
        and tag_text_begin< {tag_end_x + end_line * 60} and  tag_text_end>{tag_begin_x + begin_line * 60}
    """
    tag_all = TagText.objects.raw(_sql)
    line_begin = 0
    line_end = 0
    for i in tag_all:
        if i.tag_text_begin <= tag_begin_x + begin_line * 60 and i.tag_text_end >= tag_begin_x + begin_line * 60:
            if begin_line == i.tag_begin_line:
                line_begin = max([line_begin, i.tag_begin_line_pos + 1])
            else:
                line_begin = max([line_begin, i.tag_end_line_pos + 1])
        if i.tag_text_begin <= tag_end_x + end_line * 60 and i.tag_text_end >= tag_end_x + end_line * 60:
            if end_line == i.tag_begin_line:
                line_end = max([line_end, i.tag_begin_line_pos + 1])
            else:
                line_end = max([line_end, i.tag_end_line_pos + 1])
    tag_text = TagText.objects.create(
        rawtext_id=param.raw_id,
        tag_id=param.tag_id,
        tag_begin_x=tag_begin_x,
        tag_end_x=tag_end_x,
        tag_begin_width=tag_begin_width,
        tag_end_width=tag_end_width,
        tag_begin_line=begin_line,
        tag_end_line=end_line,
        tag_begin_line_pos=line_begin if begin_line != end_line else max([line_begin, line_end]),
        tag_end_line_pos=line_end if begin_line != end_line else max([line_begin, line_end]),
        tag_text_begin=tag_begin_x + begin_line * 60,
        tag_text_end=tag_end_x + end_line * 60,
    )
    tag_text.save()
    raw.raw_status = 2
    raw.save()
    return {"msg": "创建成功"}


@api.delete("/tagmark", response_model=ResponseMsg)
def tag_mark(param: IdIn = Body()):
    from .models import TagText
    tag_text = TagText.objects.filter(id=param.id).first()
    tag_text_id = tag_text.rawtext_id
    begin_line = tag_text.tag_begin_line
    end_line = tag_text.tag_end_line
    begin_pos = tag_text.tag_begin_line_pos
    end_pos = tag_text.tag_end_line_pos
    text_begin = tag_text.tag_text_begin
    text_end = tag_text.tag_text_end
    _sql = f"""
    select * from dataset_tagtext where rawtext_id={tag_text_id} and
        (tag_text_begin<={text_begin} or tag_text_end>={text_end})
        and tag_text_begin< {text_end} and  tag_text_end>{text_begin}
    """
    data = TagText.objects.raw(_sql)
    begin_list = []
    end_list = []
    for i in data:
        if i.tag_begin_line_pos > begin_pos:
            if i.tag_begin_line in [begin_line, end_line]:
                begin_list.append(str(i.id))
                if i.tag_begin_line == i.tag_end_line:
                    end_list.append(str(i.id))
        if i.tag_end_line_pos > end_pos:
            if i.tag_begin_line in [begin_line, end_line]:
                end_list.append(str(i.id))
                if i.tag_begin_line == i.tag_end_line:
                    begin_list.append(str(i.id))

    if begin_list:
        _sql = f"update dataset_tagtext set tag_begin_line_pos=tag_begin_line_pos-1 where id in ({','.join(list(begin_list))})"
        with connection.cursor() as cur:
            cur.execute(_sql)
    if end_list:
        _sql = f"update dataset_tagtext set tag_end_line_pos=tag_end_line_pos-1 where id in ({','.join(list(end_list))})"
        with connection.cursor() as cur:
            cur.execute(_sql)
    tag_text.delete()
    return {"msg": "删除成功"}


@api.delete("/texttag", response_model=ResponseMsg)
def text_tag(param: IdIn = Body()):
    from .models import TagText
    tag_text = TagText.objects.filter(id=param.id).first()
    tag_text_id = tag_text.rawtext_id
    tag_begin_x = tag_text.tag_begin_x  # x位置
    tag_end_x = tag_text.tag_end_x  # y位置
    tag_text_line = tag_text.tag_line  # line位置
    tag_line_pos = tag_text.tag_line_pos
    begin = TagText.objects.filter(rawtext_id=tag_text_id, tag_line=tag_text_line,
                                   tag_begin_x__lt=tag_begin_x, tag_end_x__gt=tag_begin_x,
                                   tag_line_pos__gt=tag_line_pos).all()
    end = TagText.objects.filter(rawtext_id=tag_text_id, tag_line=tag_text_line,
                                 tag_begin_x__lt=tag_end_x, tag_end_x__gt=tag_end_x,
                                 tag_line_pos__gt=tag_line_pos).all()
    content = TagText.objects.filter(rawtext_id=tag_text_id, tag_line=tag_text_line,
                                     tag_begin_x__gt=tag_begin_x, tag_end_x__lt=tag_end_x,
                                     tag_line_pos__gt=tag_line_pos).all()
    change_id_list = set([str(i.id) for i in list(begin) + list(end) + list(content)])
    # TagText.objects.filter(id__in = list(change_id_list)).update(tag_line=TagText.tag_line-1)
    if change_id_list:
        _sql = f"update dataset_tagtext set tag_line_pos=tag_line_pos-1 where id in ({','.join(list(change_id_list))})"
        with connection.cursor() as cur:
            cur.execute(_sql)
    tag_text.delete()
    return {"msg": "删除成功"}


# 创建关系
@api.post("/createtexttag", response_model=ResponseMsg)
def create_text_tag(param: TagText = Body()):
    from .models import TagText
    tag_raw = TagText.objects.create(tag_begin_x=param.begin, tag_end_x=param.end,
                                     tag_line=param.line, tag_id=param.tag_id, rawtext_id=param.rawid,
                                     tag_begin_width=param.begin * 13,
                                     tag_end_width=param.begin * 13)
    tag_raw.save()
    return {"msg": "创建成功"}


@api.put("/textrelation", response_model=ResponseMsg)
def relation_text(param: TextRelation = Body()):
    from .models import RelationText, TagText
    begin = TagText.objects.filter(id=param.begin_id).first()
    end = TagText.objects.filter(id=param.end_id).first()
    relation_text = RelationText.objects.create(relation_id_id=param.id,
                                                relation_begin_id=param.begin_id,
                                                relation_end_id=param.end_id,
                                                rawtext_id_id=param.raw_text_id,
                                                relation_begin_x=0.5 * (begin.tag_end_width + begin.tag_begin_width),
                                                relation_end_x=0.5 * (end.tag_end_width + end.tag_begin_width),
                                                relation_begin_line=begin.tag_line,
                                                relation_end_line=end.tag_line)
    relation_text.save()
    return {"msg": "创建成功"}


@api.put("/textevent", response_model=ResponseMsg)
def event_text(param: TextRelation = Body()):
    from .models import EventText, TagText
    begin = TagText.objects.filter(id=param.begin_id).first()
    end = TagText.objects.filter(id=param.end_id).first()
    relation_text = EventText.objects.create(event_id_id=param.id,
                                             event_begin_id=param.begin_id,
                                             event_end_id=param.end_id,
                                             rawtext_id_id=param.raw_text_id,
                                             event_begin_x=0.5 * (begin.tag_end_width + begin.tag_begin_width),
                                             event_end_x=0.5 * (end.tag_end_width + end.tag_begin_width),
                                             event_begin_line=begin.tag_line,
                                             event_end_line=end.tag_line)
    relation_text.save()
    return {"msg": "创建成功"}


# 已经注销
@api.get("/relation", response_model=RelationList)
def relation(dataset_id: IdIn = Depends()):
    from .models import Relation
    # dataset = DataSet.objects.filter(id=dataset_id.id).first()
    relation = Relation.objects.filter(data_id_id=dataset_id.id).all()
    return {
        "return_list": [
            {
                "relation_id": i.id,
                "relation_name": i.relation_name,
                "relation_list": [i.tag_begin_id, i.tag_end_id]
            }
            for i in relation
        ]
    }


@api.put("/relation", response_model=ResponseMsg)
def relation(param: RelationName = Body()):
    from .models import Relation
    relation = Relation.objects.filter(id=param.id).first()
    relation.relation_name = param.relation_name
    relation.save()
    return {"msg": "修改成功"}


@api.delete("/relation", response_model=ResponseMsg)
def relation(param: IdIn = Body()):
    from .models import RelationText
    RelationText.objects.filter(id=param.id).delete()
    return {"msg": "删除成功"}


@api.get("/event", response_model=EventList)
def event(dataset_id: IdIn = Depends()):
    from .models import Event

    events = Event.objects.filter(dataset_id=dataset_id.id).all()
    return {
        "return_list": [
            {
                "event_id": event.id,
                "event_name": event.event_name,
                "enity": [{
                    "tag_id": i.tag.id,
                    "tag_name": i.tag.tag_name
                } for i in event.eventtag_set.all()]
            } for event in events
        ]
    }


@api.put("/event", response_model=ResponseMsg)
def relation(param: EventName = Body()):
    from .models import Event
    event = Event.objects.filter(id=param.id).first()
    event.event_name = param.event_name
    event.save()
    return {"msg": "修改成功"}


@api.get("/rawimage", response_model=RawImageList)
async def raw_image(param: RawIn = Depends(),
                    param_page: Paging = Depends()):
    from .models import DataSet
    dataset = DataSet.objects.filter(id=param.dataset_id)
    dataset = await dataset.afirst()
    param_page.order_key = param_page.order_key if param_page.order == 1 else f"-{param_page.order_key}"

    # objs = RawImage.objects.filter(name__contains=param.search_data, data_id=dataset).order_by(
    #     param_page.order_key).all()
    objs = dataset.rawimage_set.filter(name__contains=param.search_data)
    count = await objs.acount()

    objs = objs.order_by().all()[
           param_page.page * param_page.size - param_page.size: param_page.page * param_page.size]
    return {
        'count': count,
        "raw_images": [
            {
                "key": obj.id,
                "id": obj.id,
                "path": f"{app_url}{obj.path}",
                "name": obj.name,
                "status": obj.raw_status,
            } async for obj in objs
        ]
    }


# @api.post("/createtextmark",response_model=ResponseMsg)
# def create_text_mark():


@api.post("/rawimagelist", response_model=RawImageList)
async def raw_image(param: RawInList = Body(),
                    param_page: Paging = Body()):
    from .models import DataSet, RawImage
    dataset = DataSet.objects.filter(id__in=param.dataset_id).all()
    param_page.order_key = param_page.order_key if param_page.order == 1 else f"-{param_page.order_key}"

    objs = RawImage.objects.filter(name__contains=param.search_data, data_id__in=dataset).order_by(
        param_page.order_key).all()
    count = await objs.acount()

    objs = objs.order_by().all()[
           param_page.page * param_page.size - param_page.size: param_page.page * param_page.size]
    return {
        'count': count,
        "raw_images": [
            {
                "key": obj.id,
                "id": obj.id,
                "path": f"{app_url}{obj.path}",
                "name": obj.name,
                "status": obj.raw_status,
            } async for obj in objs
        ]
    }


@api.delete("/rawimage", response_model=ResponseMsg)
def raw_image(rawimage: IdInList = Body()):
    from .models import RawImage
    RawImage.objects.filter(id__in=rawimage.id_list).delete()
    return {"msg": "删除成功"}


@api.post("/markimage", response_model=ImageMarkOutWithPath)
def mark_image(rawid: IdIn = Body(),
               tagid: IdInList = Body()):
    from .models import Tag, RawImage
    tags = Tag.objects.filter(id__in=tagid.id_list).all()
    return_list = []
    raw_image = RawImage.objects.filter(id=rawid.id).first()
    raw_tag = {
        datasettag.tag_id_id: {
            "color": datasettag.color,
            "shoutcut_key": datasettag.shoutcut_key
        }
        for datasettag in raw_image.data_id.datasettag_set.all()
    }
    for tag in tags:
        return_list.append({
            "tag_id": tag.id,
            "tag_name": tag.tag_name,
            "color": change_color(raw_tag[tag.id]["color"])[0],
            "fill_color": change_color(raw_tag[tag.id]["color"])[1],
            "svg_param": [
                {

                    "tag_mark_id": tag_marks.id,
                    "path": f"""M {" L ".join([f" {p[0]} {p[1]}" if p else "" for p in json.loads(tag_marks.param)])}  z""",
                    "circle_list": json.loads(tag_marks.param)
                }

                for tag_marks in tag.tagimage_set.all().filter(image__id=rawid.id).all()
            ]
        })
    return {"return_list": return_list,
            "path": f"{app_url}{raw_image.path}",
            "width": 860,
            "image_width": raw_image.imgage_width if raw_image.imgage_width < 860 else 860,
            "height": raw_image.imgage_height if raw_image.imgage_width < 860 else round(
                raw_image.imgage_height / raw_image.imgage_width * 860, 1)}


@api.post("/marklist", response_model=MarkList)
def mark_image(rawid: IdIn = Body(),
               tagid: IdInList = Body()):
    from .models import TagImage
    tags = TagImage.objects.filter(tag_id__in=tagid.id_list, image_id=rawid.id).all()
    return_list = []
    for tag in tags:
        max_x = sorted(json.loads(tag.param), key=lambda x: x[0])[-1][0]
        max_y = sorted(json.loads(tag.param), key=lambda x: x[0])[-1][1]
        return_list.append({
            "mark_id": tag.id,
            "tag_name": tag.tag.tag_name,
            "path": f"""M {" L ".join([f" {round(p[0] / max_x, 2) * 40} {round(p[1] / max_y, 2) * 40}" for p in json.loads(tag.param)])}  z"""
        })
    return {"return_list": return_list}


@api.put("/canceltext", response_model=ResponseMsg)
def cancel_text(_id: IdIn = Body()):
    from .models import RawText
    RawText.objects.filter(id=_id.id).update(raw_status=3)
    obj = RawText.objects.filter(id=_id.id)
    obj.tagtext_set.delete()
    return {"msg": "删除成功"}


@api.put("/cancelimage", response_model=ResponseMsg)
def cancel_image(_id: IdIn = Body()):
    from .models import RawImage
    RawImage.objects.filter(id=_id.id).update(raw_status=3)
    obj = RawImage.objects.filter(id=_id.id)
    obj.rawimage_set.delete()
    return {"msg": "删除成功"}


@api.delete("/cleanuptext", response_model=ResponseMsg)
def cleanup_text(_id: IdIn = Body()):
    from .models import RawText
    RawText.objects.filter(id=_id.id).update(raw_status=0)
    obj = RawText.objects.filter(id=_id.id)
    obj.tegtext_set.delete()
    return {"msg": "清除成功"}


@api.delete("/cleanupimage", response_model=ResponseMsg)
def cleanup_image(_id: IdIn = Body()):
    from .models import RawImage
    RawImage.objects.filter(id=_id.id).update(raw_status=0)
    obj = RawImage.objects.filter(id=_id.id)
    obj.rawimage_set.delete()
    return {"msg": "清除成功"}


# # #上一条,已经注销
# # @api.get("/prevraw",response_model=ResponseMsg)
# # def prev_raw(_id:IdIn=int):
# #     from .models import


@api.put("/deletetext", response_model=ResponseMsg)
def delete_text(param_id: IdIn = Body()):
    from .models import RawText
    RawText.objects.filter(id=param_id.id).delete()
    return {"msg": "删除成功"}


# # 已经注销
# # @api.post("/pretext", response_model=ReturnList)
# # def get_pre_text(rawid: IdIn = Body(),
# #                 tagid: IdInList = Body()):
# #     from .models import RawText, TagText, RelationText, EventText
#
#
# # 已经注销
# # @api.put("/movemarkimage",response_model=ImageMarkOut)
# # def mark_image(rawid: IdIn = Body(),
# #              tagid: IdInList = Body()):
# #
# #     from .models import Tag
# #     tags = Tag.objects.filter(id__in=tagid.id_list).all()
# #     return_list = []
# #     for tag in tags:
# #         return_list.append({
# #             "tag_name": tag.tag_name,
# #             "path": [
# #                 f"""M {" L ".join([f" {p[0]} {p[2]}" for p in json.loads(tag_marks.param)])}  z"""
# #                 for tag_marks in tag.tagimage_set.all().filter(image__id=rawid.id).all()
# #             ],
# #             "circle_list": [json.loads(tag_marks.param)
# #         for tag_marks in tag.tagimage_set.all().filter(image__id=rawid.id).all()]
# #         })
# #     print(return_list)
# #     return {"return_list": return_list}


@api.post("/markcircle", response_model=ResponseMsg)
def mark_circle(param: AddCircleIn = Body()):
    from .models import TagImage
    obj = TagImage.objects.filter(id=param.id).first()

    obj_param = json.loads(obj.param)
    obj_param.insert(param.pos, param.param)
    obj.param = json.dumps(obj_param)
    obj.save()
    return {"msg": "操作成功"}


@api.put("/markcircle", response_model=ResponseMsg)
def mark_circle(param: AddCircleIn = Body()):
    from .models import TagImage
    obj = TagImage.objects.filter(id=param.id).first()
    obj_param = json.loads(obj.param)
    obj_param[param.pos] = param.param
    obj.param = json.dumps(obj_param)

    p = obj_param
    segmentation = []
    if obj.image.imgage_width >= 860:
        scale = obj.image.imgage_width / 860
        bbox = [round(scale * p[0][0], 2),
                round(scale * p[0][1], 2),
                round(scale * (p[1][0] - p[0][0]), 2),
                round(scale * (p[1][1] - p[0][1]), 2)]
        for i in p:
            segmentation.append(round(scale * i[0], 2))
            segmentation.append(round(scale * i[1], 2))
    else:
        bbox = [p[0][0] - 0.5 * (860 - obj.image.imgage_width),
                p[0][1], p[1][0] - p[0][0], p[1][1] - p[0][1]]
        for i in p:
            segmentation.append(round(i[0] - 0.5 * (860 - obj.image.imgage_width), 2))
            segmentation.append(round(p[0][1], 2))

    _segmentation = json.dumps(segmentation)
    _bbox = json.dumps(bbox)
    obj.bbox = _bbox
    obj.segmentation = _segmentation
    obj.area = round(bbox[2] * bbox[3], 2)

    obj.save()
    return {"msg": "修改成功"}


@api.delete("/markcircle", response_model=ResponseMsg)
def mark_circle(param: DeleteCircleIn = Body()):
    from .models import TagImage
    obj = TagImage.objects.filter(id=param.id).first()
    obj_param = json.loads(obj.param)
    del obj_param[param.pos]
    obj.param = json.dumps(obj_param)
    obj.save()
    return {"msg": "修改成功"}


@api.put("/markimage", response_model=ResponseMsg)
def mark_circle(param: UpdateImageIn = Body()):
    from .models import TagImage
    obj = TagImage.objects.filter(id=param.id).first()
    obj_param = json.loads(obj.param)
    obj_param = [
        [i[0] + param.param[0], i[1] + param.param[1]]
        for i in obj_param
    ]
    obj.param = json.dumps(obj_param)

    p = obj_param
    segmentation = []
    if obj.image.imgage_width >= 860:
        scale = obj.image.imgage_width / 860
        bbox = [round(scale * p[0][0], 2),
                round(scale * p[0][1], 2),
                round(scale * (p[1][0] - p[0][0]), 2),
                round(scale * (p[1][1] - p[0][1]), 2)]
        for i in p:
            segmentation.append(round(scale * i[0], 2))
            segmentation.append(round(scale * i[1], 2))
    else:
        bbox = [p[0][0] - 0.5 * (860 - obj.image.imgage_width),
                p[0][1], p[1][0] - p[0][0], p[1][1] - p[0][1]]
        for i in p:
            segmentation.append(i[0] - round(0.5 * (860 - obj.image.imgage_width), 2))
            segmentation.append(round(p[0][1], 2))
    # p = param.param
    _segmentation = json.dumps(segmentation)
    _bbox = json.dumps(bbox)
    obj.bbox = _bbox
    obj.segmentation = _segmentation
    obj.area = round(bbox[2] * bbox[3], 2)
    obj.save()
    return {"msg": "修改成功"}


@api.delete("/markimage", response_model=ResponseMsg)
def mark_circle(param: IdIn = Body()):
    from .models import TagImage
    TagImage.objects.filter(id=param.id).delete()
    return {"msg": "删除成功"}


@api.post("/createmark", response_model=ResponseMsg)
def create_mark(param: CreateImageMark = Body()):
    from .models import TagImage, Tag, RawImage
    _param = json.dumps(param.param)
    _tag = Tag.objects.filter(id=param.tag_id).first()
    _image = RawImage.objects.filter(id=param.image_id).first()
    RawImage.objects.filter(id=param.image_id).update(raw_status=2)
    p = param.param
    segmentation = []
    if _image.imgage_width >= 860:
        scale = _image.imgage_width / 860
        bbox = [round(scale * p[0][0], 2),
                round(scale * p[0][1], 2),
                round(scale * (p[1][0] - p[0][0]), 2),
                round(scale * (p[1][1] - p[0][1]), 2)]
        for i in p:
            segmentation.append(round(scale * i[0], 2))
            segmentation.append(round(scale * i[1], 2))
    else:
        bbox = [round(p[0][0] - 0.5 * (860 - _image.imgage_width), 2),
                p[0][1],
                p[1][0] - p[0][0],
                p[1][1] - p[0][1]]
        for i in p:
            segmentation.append(round(i[0] - 0.5 * (860 - _image.imgage_width), 2))
            segmentation.append(round(i[1], 2))
    # p = param.param
    _segmentation = json.dumps(segmentation)
    _bbox = json.dumps(bbox)
    TagImage.objects.create(param=_param, image_id=param.image_id, tag_id=param.tag_id,
                            area=round(bbox[2] * bbox[3], 2),
                            segmentation=_segmentation,
                            bbox=_bbox)
    return {"msg": "创建成功"}


@api.put("/inspection", response_model=ResponseMsg)
def inspection(param: RawStatusIn = Body()):
    from .models import RawImage
    RawImage.objects.filter(id=param.id).update(raw_status=4)
    return {"msg": "修改成功"}


@api.delete("/deleteimage", response_model=ResponseMsg)
def delete_image(_id: IdIn = Body()):
    from .models import RawImage, DataSet
    obj = RawImage.objects.filter(id=_id.id).first()
    _s = obj.raw_status
    dataset = DataSet.objects.filter(id=_id.id).first()
    DataSet.objects.filter(id=obj.data_id.id).update(amount=dataset.amount - 1,
                                                     mark_count=dataset.mark_count - 1 if _s > 1 and _s < 4 else 0,
                                                     inspection_count=dataset.mark_count - 1 if _s > 2 and _s < 4 else 0)
    RawImage.objects.filter(id=_id.id).delete()
    return {"msg": "删除成功"}


@api.put("/imagestatus", response_model=ResponseMsg)
def image_status(param: UpdateStatus = Body()):
    from .models import RawImage, TagImage
    RawImage.objects.filter(id=param.image_id).update(raw_status=param.status)
    TagImage.objects.filter(image_id=param.image_id).delete()
    return {"msg": "操作成功"}


@api.get("/imagestatus", response_model=Status)
def image_status(param: IdIn = Depends()):
    from .models import RawImage
    obj = RawImage.objects.filter(id=param.id).first()
    return {"status": obj.raw_status}


@api.put("/image", response_model=ResponseMsg)
def image_status(param: RawStatusIn = Body()):
    from .models import RawImage, DataSet
    obj = RawImage.objects.filter(id=param.id).first()
    data = DataSet.objects.filter(id=obj.data_id.id).first()
    if param.status == 2:
        RawImage.objects.filter(id=param.id, raw_status=2).update(raw_status=3)
        DataSet.objects.filter(id=obj.data_id.id).update(inspection_count=data.inspection_count + 1)
    elif param.status == 3:
        RawImage.objects.filter(id=param.id, raw_status=3).update(raw_status=2)
        DataSet.objects.filter(id=obj.data_id.id).update(inspection_count=data.inspection_count - 1)
    return {"msg": "修改成功"}


@api.get("/getrawimage", response_model=GetRaw)
def get_raw_image(param: IdIn = Depends()):
    from .models import RawImage
    raw_image = RawImage.objects.filter(id=param.id).first()
    return {
        "path": raw_image.path,
        "tag_id": raw_image.tagimage_set.all()[0].tag_id if raw_image.tagimage_set.all() else 0,
        "width": 860,
        "image_width": raw_image.imgage_width if raw_image.imgage_width < 860 else 860,
        "height": raw_image.imgage_height if raw_image.imgage_width < 860 else round(
            raw_image.imgage_height / raw_image.imgage_width * 860, 1)
    }


# 标签
@api.get("/gettag", response_model=TagList)
def tag(param: GetTag = Depends()):
    from .models import DataSet, TagImageType
    # objs = Tag.objects.filter(data_id=dataset_param.id).all()
    tag_image = TagImageType.objects.filter(image_id=param.image_id).first()
    _id = tag_image.tag_id if tag_image else 0
    dataset = DataSet.objects.filter(id=param.dataset_id).first()
    return {
        "tag_list": [
            {
                "id": obj.id,
                "tag_id": obj.tag_id.id,
                "tag": obj.tag_id.tag_name,
                "shoutcut_key": obj.shoutcut_key,
                "color": change_color(obj.color)[0],
                "fill_color": change_color(obj.color)[1] if obj.tag_id_id != _id else change_color(obj.color)[0],
            } for obj in dataset.datasettag_set.all()
        ]
    }


@api.get("/getrawtext", response_model=GetRawText)
def get_raw_text(param: IdIn = Depends()):
    from .models import RawText
    raw = RawText.objects.filter(id=param.id).first()
    return {"text": raw.text}


@api.get("/gettexttag", response_model=TagList)
def get_text_tag(param: GetTagText = Depends()):
    from .models import DataSet, TagTextType
    tag_text = TagTextType.objects.filter(text_id=param.text_id).first()
    _id = tag_text.tag_id if tag_text else 0
    dataset = DataSet.objects.filter(id=param.dataset_id).first()
    return {
        "tag_list": [
            {
                "id": obj.id,
                "tag_id": obj.tag_id.id,
                "tag": obj.tag_id.tag_name,
                "shoutcut_key": obj.shoutcut_key,
                "color": change_color(obj.color)[0],
                "fill_color": change_color(obj.color)[1] if obj.tag_id_id != _id else change_color(obj.color)[0],
            } for obj in dataset.datasettag_set.all()
        ]
    }


@api.post("/gettexttag", response_model=ResponseMsg)
def get_text_tag(param: IdIn = Body(),
                 tag: IdIn = Body()):
    from .models import TagTextType, RawText
    raw_text = TagTextType.objects.filter(text_id=param.id).first()
    if not raw_text:
        raw_text = TagTextType.objects.create(text_id=param.id, tag_id=tag.id)
    else:
        raw_text.tag_id = tag.id
    raw_text.save()
    raw = RawText.objects.filter(id=param.id).first()
    raw.raw_status = 2
    raw.save()
    return {"msg": "成功"}


@api.delete("/gettexttag", response_model=ResponseMsg)
def get_text_tag(param: IdIn = Body()):
    from .models import TagTextType
    tag_text = TagTextType.objects.filter(text_id=param.id).all()
    tag_text.delete()
    return {"msg": "删除成功"}


@api.get("/getimagetag", response_model=TagList)
def get_image_tag(param: GetTagImage = Depends()):
    from .models import DataSet, TagImageType
    tag_image = TagImageType.objects.filter(image_id=param.image_id).first()
    _id = tag_image.tag_id if tag_image else 0
    dataset = DataSet.objects.filter(id=param.dataset_id).first()
    return {
        "tag_list": [
            {
                "id": obj.id,
                "tag_id": obj.tag_id.id,
                "tag": obj.tag_id.tag_name,
                "shoutcut_key": obj.shoutcut_key,
                "color": change_color(obj.color)[0],
                "fill_color": change_color(obj.color)[1] if obj.tag_id_id != _id else change_color(obj.color)[0],
            } for obj in dataset.datasettag_set.all()
        ]
    }


@api.post("/getimagetag", response_model=ResponseMsg)
def get_image_tag(param: IdIn = Body(),
                  tag: IdIn = Body()):
    from .models import TagImageType, RawImage
    raw_image = TagImageType.objects.filter(image_id=param.id).first()
    if not raw_image:
        raw_text = TagImageType.objects.create(image_id=param.id, tag_id=tag.id)
    else:
        raw_image.tag_id = tag.id
    raw_text.save()
    raw = RawImage.objects.filter(id=param.id).first()
    raw.raw_status = 2
    raw.save()
    return {"msg": "成功"}


@api.delete("/getimagetag", response_model=ResponseMsg)
def get_text_tag(param: IdIn = Body()):
    from .models import TagImageType
    tag_image = TagImageType.objects.filter(image_id=param.id).all()
    tag_image.delete()
    return {"msg": "删除成功"}


@api.post("/getrawimage", response_model=ResponseMsg)
def get_raw_image(param: IdIn = Body(),
                  tag: IdIn = Body()):
    from .models import TagImageType, RawImage
    raw_image = TagImageType.objects.filter(image_id=param.id).first()
    raw = RawImage.objects.filter(id=param.id).first()
    if not raw_image:
        raw_image = TagImageType.objects.create(image_id=param.id, tag_id=tag.id)

    else:
        raw_image.tag_id = tag.id
    raw_image.save()
    raw.raw_status = 2
    raw.save()
    return {"msg": "成功"}


@api.post("/getimage", response_model=ResponseMsg)
async def get_image(requests: Request, file: UploadFile):
    contents = await file.read()
    file_name = file.filename
    _id = dict(requests.headers)["dataset_id"]
    if not os.path.exists(f"file/dataset/{_id}"):
        os.mkdir(f"file/dataset/{_id}")
    with open(f"file/dataset/{_id}/{file_name}", "wb+") as f:
        f.write(contents)
    img = cv2.imread(f"file/dataset/{_id}/{file_name}")
    width = img.shape[1]
    height = img.shape[0]
    await sync_to_async(create_new_file)(name=file_name, path=f"/file/dataset/{_id}/{file_name}", user="root",
                                         _id=_id, width=width, height=height)

    # dataset = await DataSet.objects.filter(id=_id).afirst()
    return {"msg": "上传成功"}


def create_new_file(name, path, user, _id, width, height):
    from .models import RawImage
    s = RawImage.objects.create(name=name, path=path, raw_status=1, tesing_user=user,
                                data_id_id=_id, imgage_width=width, imgage_height=height)
    s.save()


def update_file(_id, _amount):
    from .models import DataSet
    DataSet.objects.filter(id=_id).update(amount=_amount + 1)


def change_color(color):
    p1 = int(color[1:3], 16)
    p2 = int(color[3:5], 16)
    p3 = int(color[5:], 16)
    return f"rgba({p1},{p2},{p3},1)", f"rgba({p1},{p2},{p3},0.1)", f"rgba({p1},{p2},{p3},0.3)"


@api.post("/getnewimage", response_model=ImageMarkOut)
def get_new_image(rawid: IdIn = Body(),
                  tagid: IdInList = Body(),
                  is_pre: IsPre = Body()):
    from .models import Tag, RawImage
    if is_pre == 1:
        raw_image = RawImage.objects.filter(id__gt=rawid.id).all().order_by("id").first()
    else:
        raw_image = RawImage.objects.filter(id__lt=rawid.id).all().order_by("-id").first()
    tags = Tag.objects.filter(id__in=tagid.id_list).all()
    return_list = []
    for tag in tags:
        return_list.append({
            "tag_id": tag.id,
            "tag_name": tag.tag_name,
            "color": change_color(tag.color)[0],
            "fill_color": change_color(tag.color)[1],
            "svg_param": [
                {

                    "tag_mark_id": tag_marks.id,
                    "path": f"""M {" L ".join([f" {p[0]} {p[1]}" for p in json.loads(tag_marks.param)])}  z""",
                    "circle_list": json.loads(tag_marks.param)
                }

                for tag_marks in tag.tagimage_set.all().filter(image__id=raw_image.id).all()
            ]
        })
    return {"return_list": return_list}


@api.get("/newid", response_model=IsPre)
def get_id(param: NewId = Body()):
    from .models import RawImage
    if param.is_pre == 1:
        raw = RawImage.objects.filter(id__gt=param.raw_id, data_id__id=param.dataset_id).all().order_by("id").first()
    else:
        raw = RawImage.objects.filter(id__lt=param.raw_id, data_id__id=param.dataset_id).all().order_by("-id").first()
    return {
        "pre": raw.id
    }


# @api.put("/get_other",response_model=ResponseMsg)
#
# @api.get("/datasetlist",)
# async def dataset_list():
#     from .models import DataSet
#     dataset_list = DataSet.objects.filter().all()
#     return [{
#         "dataset_id":i.id,
#         "name":i.dataset_name
#     }async for i in dataset_list]


@api.post("/imagetype", response_model=ImageOutList)
def image_type(rawid: IdIn = Body(),
               tagid: IdInList = Body()):
    from .models import TagImage
    tags = TagImage.objects.filter(tag_id__in=tagid.id_list, image_id=rawid.id).all()
    return {
        "return_list": [
            {
                "mark_id": tag.id,
                "tag_id": tag.tag.id,
            } for tag in tags
        ]
    }


@api.get("/datasettype", response_model=ResopnseDict)
async def dataset_type():
    from .models import DataSet
    dataset_list = DataSet.objects.all()
    return {"dataset": [{
        "dataset_type": i.dataset_type,
        "dataset_name": i.dataset_name,
        "dataset_id": i.id
    }async for i in dataset_list]}


# 语义分割
# 选中状态：焦点编辑，新增，删除，拖拽。对选中框的拖拽，删除
# 未选中状态：没有焦点显示，区域单击之后为选中状态，选中出现焦点。右侧滚动。关联
# 框选为标签颜色。边框为标签颜色，内容有透明度区分
# 选中标签，全色填充。


@api.get("/intention", response_model=IntentionOut)
def intention(param: IdIn = Depends()):
    from .models import TagIntention
    tag_intentions = TagIntention.objects.filter(data_id_id=param.id).all()
    return_dict = []
    for tag_intention in tag_intentions:
        slots = []
        for tagslot in tag_intention.tagslot_set.all():
            slots.append({
                "slot_name": tagslot.slot,
                "key": tagslot.id
            })
        return_dict.append({
            "intention_name": tag_intention.tag_id.tag_name,
            "intention_id": tag_intention.id,
            "slots": slots
        })

    return {"intentions": return_dict}


@api.post("/createintention", response_model=ResponseMsg)
def create_intention(param: IdIn = Body()):
    from .models import TagIntention, Tag, TagSlot
    tag = Tag.objects.filter(tag_name="新增意图").first()
    if not tag:
        tag = Tag.objects.create(tag_name="新增意图")
        tag.save()
    tag_intention = TagIntention.objects.create(tag_id_id=tag.id, data_id_id=param.id)
    tag_intention.save()
    tag_slot = TagSlot.objects.create(slot="新增槽位", tagintentions_id=tag_intention.id)
    tag_slot.save()
    return {"msg": "创建成功"}


@api.put("/intention", response_model=ResponseMsg)
def intention(param: UpdataIntention = Body()):
    from .models import TagIntention, Tag
    tag = Tag.objects.filter(tag_name=param.intention_name).first()
    if not tag:
        tag = Tag.objects.create(tag_name=param.intention_name)
        tag.save()
    tag_intention = TagIntention.objects.filter(id=param.id).first()
    tag_intention.tag_id = tag
    tag_intention.save()
    return {"msg": "创建成功"}


@api.post("/intention", response_model=ResponseMsg)
def text_tag(param: PostTagTextIn = Body()):
    from .models import SlotText, RawText
    raw = RawText.objects.filter(id=param.raw_id).first()
    raw_texts = re.findall(r".{2,60}", raw.text)
    if raw_texts.index(param.tag_begin_text) * 60 + param.tag_begin_x < \
            raw_texts.index(param.tag_end_text) * 60 + param.tag_end_x:
        # 开始行数和结束行数
        begin_line = raw_texts.index(param.tag_begin_text)
        end_line = raw_texts.index(param.tag_end_text)
        tag_begin_x = param.tag_begin_x
        tag_end_x = param.tag_end_x
        tag_begin_width = param.tag_begin_text[0:tag_begin_x]
        tag_end_width = param.tag_end_text[0:tag_end_x]
    else:
        begin_line = raw_texts.index(param.tag_end_text)
        end_line = raw_texts.index(param.tag_begin_text)
        tag_end_x = param.tag_begin_x
        tag_begin_x = param.tag_end_x
        # 开始的位置
        tag_begin_width = param.tag_end_text[0:tag_begin_x]
        tag_end_width = param.tag_begin_text[0:tag_end_x]
    # 结束的位置
    tag_begin_width = character_classify(tag_begin_width)
    tag_end_width = character_classify(tag_end_width)
    _sql = f"""
            select * from dataset_tagtext where rawtext_id={param.raw_id} and
            (tag_text_begin<={tag_begin_x + begin_line * 60} or tag_text_end>={tag_end_x + end_line * 60})
            and tag_text_begin< {tag_end_x + end_line * 60} and  tag_text_end>{tag_begin_x + begin_line * 60}
        """
    tag_all = SlotText.objects.raw(_sql)
    line_begin = 0
    line_end = 0
    for i in tag_all:
        if i.tag_text_begin <= tag_begin_x + begin_line * 60 and i.tag_text_end >= tag_begin_x + begin_line * 60:
            if begin_line == i.tag_begin_line:
                line_begin = max([line_begin, i.tag_begin_line_pos + 1])
            else:
                line_begin = max([line_begin, i.tag_end_line_pos + 1])
        if i.tag_text_begin <= tag_end_x + end_line * 60 and i.tag_text_end >= tag_end_x + end_line * 60:
            if end_line == i.tag_begin_line:
                line_end = max([line_end, i.tag_begin_line_pos + 1])
            else:
                line_end = max([line_end, i.tag_end_line_pos + 1])
    tag_text = SlotText.objects.create(
        raw_id=param.raw_id,
        slot_id=param.tag_id,
        tag_begin_x=tag_begin_x,
        tag_end_x=tag_end_x,
        tag_begin_width=tag_begin_width,
        tag_end_width=tag_end_width,
        tag_begin_line=begin_line,
        tag_end_line=end_line,
        tag_begin_line_pos=line_begin if begin_line != end_line else max([line_begin, line_end]),
        tag_end_line_pos=line_end if begin_line != end_line else max([line_begin, line_end]),
        tag_text_begin=tag_begin_x + begin_line * 60,
        tag_text_end=tag_end_x + end_line * 60,
    )
    tag_text.save()
    raw.raw_status = 2
    raw.save()
    return {"msg": "创建成功"}


@api.delete("/intention", response_model=ResponseMsg)
def intention(param: IdIn = Body()):
    from .models import TagIntention
    TagIntention.objects.filter(id=param.id).delete()
    return {"msg": "删除成功"}


# @api.get("/test1")
# def test1():
#     rawid = {"id": 1}
#     tagid = {"id_list": [49, 50]}
#     from .models import RawText, DatasetTag
#     raw = RawText.objects.filter(id=rawid["id"]).first()
#     dataset_tag = DatasetTag.objects.filter(data_id_id=raw.data_id_id, tag_id_id__in=tagid["id_list"])
#     dataset_tag = {i.tag_id_id: i for i in dataset_tag}
#     tags, tag_line, tag_width = get_tag(raw.tagtext_set.all(), dataset_tag)
#     relation = get_relation(raw.relationtext_set.all(), tag_width)
#     return 444


# {"id": 26, "text": "如何演好自己的角色，请读《演员自我修养》《喜剧之王》周星驰崛起于穷困潦倒之中的独门秘笈",
#  "cls_label": ["意图1"],
#  "entities": [{"id": 90, "label": "人物", "start_offset": 26, "end_offset": 29},
#               {"id": 91, "label": "影视作品", "start_offset": 21, "end_offset": 25}],
#  "relations": []}


@api.post("/test", response_model=ImageMarkOut)
def test(rawid: IdIn = Body(),
         tagid: IdInList = Body()):
    from .models import Tag
    tags = Tag.objects.filter(id__in=tagid.id_list).all()
    # tag_image = TagImage.objects.filter(image=raw).all()
    return_list = []
    for tag in tags:
        return_list.append({
            "tag_name": tag.tag_name,
            "path": [
                f"""M {" L ".join([f" {p[0]} {p[1]}" for p in json.loads(tag_marks.param)])}  z"""
                for tag_marks in tag.tagimage_set.all().filter(image__id=rawid.id).all()
            ]
        })
    return {"return_list": return_list}
