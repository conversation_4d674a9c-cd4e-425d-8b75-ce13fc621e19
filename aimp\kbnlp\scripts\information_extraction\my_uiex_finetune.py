#!/usr/bin/env python
# coding=utf-8
import logging
import os
import shutil
import sys
import re
import warnings
from dataclasses import dataclass, field
from collections.abc import Sequence
from typing import Optional
import random
from functools import partial
from PIL import Image
import numpy as np
from copy import copy, deepcopy
import json
from itertools import accumulate
import torch
from torch.utils.data.dataset import Dataset as TorchDataset
import transformers
from transformers import (
    AutoConfig,
    AutoModelForTokenClassification,
    AutoTokenizer,
    XLMRobertaTokenizerFast,
    PreTrainedTokenizerFast,
    TrainerCallback,
    TrainerState,
    TrainerControl,
    Trainer,
    HfArgumentParser,
    TrainingArguments,
)
from transformers.trainer_utils import get_last_checkpoint, is_main_process
from datasets import load_dataset, load_metric, Dataset
from datasets.packaged_modules import _PACKAGED_DATASETS_MODULES, _hash_python_lines
import inspect

from kbnlp.scripts.information_extraction.temp_ie_utils import map_offset
from kbnlp.scripts.information_extraction.temp_doc_parser import DocParser
from kbnlp.taskflow.models.information_extraction.information_extraction_model import UIEX
from kbnlp.pretrained.ernie_layout.configuration_ernie_layout import ErnieLayoutConfig
from temp_ie_utils import compute_metrics
from kbnlp.scripts.information_extraction import label_studio_document_uiex_builder, doccano_document_uiex_builder, aidp_document_uiex_builder, aidp_draw_box_document_uiex_builder
from kbnlp.scripts.information_extraction.my_convert import LabelStudioConvertorForUIEX, ConvertorForUIEX, AIDPConvertorForUIEX, AIDPDrawBoxConvertorForUIEX


torch._C._jit_set_profiling_mode(False)  # 需要在最开始处执行，解决torch1.9~1.12script模型可能执行不正常的bug

data_source2builder = {"aidp3.0": aidp_document_uiex_builder,
                       "aidp3.1": aidp_draw_box_document_uiex_builder}
data_source2reader_module = {"aidp3.0": AIDPConvertorForUIEX,
                             "aidp3.1": AIDPDrawBoxConvertorForUIEX}


# script_identifier = doccano_document_uiex_builder.__name__.split('.')[-1]
# _PACKAGED_DATASETS_MODULES[script_identifier] = (doccano_document_uiex_builder.__name__,
#                                                  _hash_python_lines(
#                                                      inspect.getsource(doccano_document_uiex_builder).splitlines()))
# reader_module = ConvertorForUIEX


# script_identifier = label_studio_document_uiex_builder.__name__.split('.')[-1]
# _PACKAGED_DATASETS_MODULES[script_identifier] = (label_studio_document_uiex_builder.__name__,
#                                                  _hash_python_lines(
#                                                      inspect.getsource(label_studio_document_uiex_builder).splitlines()))
# reader_module = LabelStudioConvertorForUIEX


# script_identifier = doccano_for_close_domain_builder.__name__.split('.')[-1]
# _PACKAGED_DATASETS_MODULES[script_identifier] = (doccano_for_close_domain_builder.__name__,
#                                                  _hash_python_lines(
#                                                      inspect.getsource(doccano_for_close_domain_builder).splitlines()))
# reader_module = DoccanoConvertorForCloseDomain
p = re.compile('\[[.+,]*.+\]')


def _callback(*args, **kwargs):
    return


def _setup_post_callback():
    if os.environ.get('SLURM_PROCID', '0') != '0':
        return
    job_id = int(os.environ.get('SLURM_JOB_ID', '-1'))
    import requests
    url = os.environ.get('CALLBACK_URL', '')
    global _callback
    if not url:
        return

    def callback(*args, **kwargs):
        res = requests.post(url, json={'msg': args, 'detail': kwargs, 'JobId': job_id}, timeout=10)
        if res.ok:
            j = res.json()  # ; print(j)
        else:
            print('NOTE callback code={} error={}'.format(res.status_code, res.text))
        return

    _callback = callback


def box_norm(box, width, height):
    def clip(min_num, num, max_num):
        return min(max(num, min_num), max_num)

    x0, y0, x1, y1 = box
    x0 = clip(0, int((x0 / width) * 1000), 1000)
    y0 = clip(0, int((y0 / height) * 1000), 1000)
    x1 = clip(0, int((x1 / width) * 1000), 1000)
    y1 = clip(0, int((y1 / height) * 1000), 1000)
    if x1 < x0:
        warnings.warn('x0大于x1，文本可能过于倾斜或垂直文本框。')
        x0, x1 = x1, x0
    if y1 < y0:
        warnings.warn('y0大于y1，文本可能过于倾斜。')
        y0, y1 = y1, y0

    return [x0, y0, x1, y1]


def seed_everything(seed):
    torch.manual_seed(seed)       # Current CPU
    torch.cuda.manual_seed(seed)  # Current GPU
    np.random.seed(seed)          # Numpy module
    random.seed(seed)             # Python random module
    # torch.backends.cudnn.benchmark = False    # Close optimization
    # torch.backends.cudnn.deterministic = True  # Close optimization
    torch.cuda.manual_seed_all(seed)  # All GPU (Optional)


logger = logging.getLogger(__name__)


@dataclass
class ModelArguments:
    """
    Arguments pertaining to which model/config/tokenizer we are going to fine-tune from.
    """

    model_name_or_path: str = field(
        metadata={"help": "Path to pretrained model or model identifier from huggingface.co/models"}
    )
    config_name: Optional[str] = field(
        default=None,
        metadata={"help": "Pretrained config name or path if not the same as model_name"}
    )
    tokenizer_name: Optional[str] = field(
        default=None,
        metadata={"help": "Pretrained config name or path if not the same as model_name"}
    )


@dataclass
class DataTrainingArguments:
    """
    Arguments pertaining to what data we are going to input our model for training and eval.
    """

    train_path: str = field(
        metadata={"help": "The input training data file (a JSON file)."})
    dev_path: str = field(
        metadata={"help": "An optional input evaluation data file to evaluate on (a JSON file)."})
    data_source: str = field(
        default='aidp3.1',
        metadata={"help": "数据来源, 目前可取值包括aidp3.0,aidp3,1."}
    )
    aidp_field: str = field(
        default=None,
        metadata={"help": "记录用户在aidp上配置的字段及其他们之间的关系的文件路径。目前只在task_type为monograph_record时有效。"}
    )
    max_seq_len: int = field(
        default=512,
        metadata={"help": "最长序列长度，默认值为512，不超过默认值。"}
    )
    doc_stride: int = field(
        default=64,
        metadata={"help": "Tokenizer中的stride参数，代表回退子词数。"}
    )
    max_train_samples: Optional[int] = field(
        default=None,
        metadata={"help": "For debugging purposes or quicker training,"
                          " truncate the number of training examples to this value if set."},)
    max_dev_samples: Optional[int] = field(
        default=None,
        metadata={"help": "For debugging purposes or quicker training, "
                          "truncate the number of validation examples to this value if set."},)
    negative_ratio: int = field(
        default=4,
        metadata={"help": "Used only for the extraction taskflow, the ratio of positive and negative samples, number of "
                          "negative samples = negative_ratio * number of positive samples.该参数仅对训练集有效，验证集全负采样"}
    )
    separator: str = field(
        default='##',
        metadata={"help": "Used only for entity/aspect-level classification taskflow, "
                          "separator for entity label and classification label"}
    )
    task_type: str = field(
        default='ext',
        metadata={"help": "任务类型，'ext'代表信息抽取，'cls'代表文本分类,'monograph_record'代表专题著录，document_record代表文档著录。", }  # 本脚本不支持文本分类。
    )
    prompt_prefix: str = field(
        default='情感倾向',
        metadata={"help": "Used only for the classification taskflow, the prompt prefix for classification."
                 "如，'文本分类'、'情感倾向'等。当task_type不是cls时无效。"}
    )
    layout_analysis: bool = field(
        default=False,
        metadata={"help": "是否使用布局分析优化图片的ocr结果。"}
    )
    anno_type: str = field(
        default="image",
        metadata={"help": "表示提供训练数据的标注类型，image表示图片文档标注，text表示纯文本标注。"}
    )
    schema_lang: str = field(
        default='ch',
        metadata={"help": "信息抽取的schema的语言，一般是中文，可以是en。"}
    )
    train_image_base_path: str = field(
        default=None,
        metadata={"help": "当anno_type是image时，该参数有效，表示存放train图片的基目录，如果该参数没有提供，则直接用train_path/dev_path的父目录代替。"
                          "本脚本要求提供的数据中包含'image'这一项，且image_base_path + 该项代表图片的路径。"}
    )
    dev_image_base_path: str = field(
        default=None,
        metadata={"help": "当anno_type是image时，该参数有效，表示存放dev图片的基目录，如果该参数没有提供，则直接用train_path/dev_path的父目录代替。"
                          "本脚本要求提供的数据中包含'image'这一项，且image_base_path + 该项代表图片的路径。"}
    )
    return_entity_level_metrics: bool = field(
        default=False,
        metadata={"help": "Whether to return all the entity levels during evaluation or just the overall ones."},)
    use_cache: bool = field(
        default=False,
        metadata={"help": "是否使用datasets的缓存机制,普通用户不使用该参数。"
                          "如果值为True，则会将使用load_dataset方法加载；否则使用Datasets.from_dict方法加载，不使用缓存机制。"}
    )
    force_reload: bool = field(
        default=False,
        metadata={"help": "当use_cache为True时，本参数生效。默认值为False。当值为True时，会重新加载数据，不管有没有缓存；否则优先从缓存中加载。"}
    )
    shuffle: bool = field(
        default=True,
        metadata={"help": "当值为True是表示shuffle训练集。"})


class DocumentDataset(TorchDataset):

    def __init__(self, examples: Dataset, image_base_path, image_size=224, interp=1,
                 mean=(123.675, 116.280, 103.530), std=(58.395, 57.120, 57.375),
                 bbox_scale=1000):
        super(DocumentDataset, self).__init__()
        self.image_base_path = image_base_path
        assert isinstance(examples, Dataset), "仅支持HF Dataset。"
        self.examples = examples
        self.file_name2image = {}  # 图片名到图片（resize、取均值、换通道后的图片）
        self.file_name2image_size = {}  # 图片名到图片的size，(height, width)
        self.image_size = image_size
        self.interp = interp
        self.mean = mean
        self.std = std
        self.bbox_scale = bbox_scale

    def __len__(self):
        return len(self.examples)

    def __getitem__(self, item):
        # HF Dataset获取第item项样本example时相当于做了deepcopy，所以对example做的改变不影响HF Dataset本身。
        example = self.examples[item]
        if example['image'] not in self.file_name2image:
            image = DocParser.read_image(os.path.join(self.image_base_path, *re.split(r'\\|/', example['image'])))
            # resize image
            image = Image.fromarray(image)
            # image_width = image.width
            # image_height = image.height
            # self.file_name2image_size[example['image']] = (image_width, image_height)
            image = np.array(image.resize((self.image_size, self.image_size), self.interp))
            # norm image, channel last.
            image = image.astype(np.float32, copy=False)
            mean = np.array(self.mean)[np.newaxis, np.newaxis, :]
            std = np.array(self.std)[np.newaxis, np.newaxis, :]
            image -= mean
            image /= std
            # change to channel fist
            image = np.swapaxes(image, 1, 2)
            image = np.swapaxes(image, 1, 0)
            self.file_name2image[example['image']] = image
        else:
            image = self.file_name2image[example['image']]
            # image_width, image_height = self.file_name2image_size[example['image']]
        # bbox = []
        # for box in example['bbox']:
        #     bbox.append(DocParser.normalize_box(box, old_size=(image_width, image_height),
        #                                         new_size=(self.bbox_scale, self.bbox_scale)))
        # example['bbox'] = bbox
        example['image'] = image
        return example

    # def get_segment_ids(self, bboxs):  # ernielayout没有segment_ids，只有token_type_ids，且和bert是一致的。
    #     segment_ids = []
    #     for i in range(len(bboxs)):
    #         if i == 0:
    #             segment_ids.append(0)
    #         else:
    #             if bboxs[i - 1] == bboxs[i]:
    #                 segment_ids.append(segment_ids[-1])
    #             else:
    #                 segment_ids.append(segment_ids[-1] + 1)
    #     return segment_ids

    # def get_position_ids(self, segment_ids):  # ernielayout没有segment_ids直接从0到length-1
    #     position_ids = []
    #     for i in range(len(segment_ids)):
    #         if i == 0:
    #             position_ids.append(2)
    #         else:
    #             if segment_ids[i] == segment_ids[i - 1]:
    #                 position_ids.append(position_ids[-1] + 1)
    #             else:
    #                 position_ids.append(2)
    #     return position_ids


def main():
    _setup_post_callback()
    parser = HfArgumentParser((ModelArguments, DataTrainingArguments, TrainingArguments))
    if len(sys.argv) == 2 and sys.argv[1].endswith(".json"):
        # If we pass only one argument to the script and it's the path to a json file,
        # let's parse it to get our arguments.
        model_args, data_args, training_args = parser.parse_json_file(json_file=os.path.abspath(sys.argv[1]))
    else:
        model_args, data_args, training_args = parser.parse_args_into_dataclasses()
    if training_args.label_names is not None:
        training_args.label_names = sum([[w_ for w_ in w.strip().split(',') if w_] for w in training_args.label_names], [])
    seed_everything(training_args.seed)

    class MyCallBack(TrainerCallback):

        def on_train_begin(self, args: TrainingArguments, state: TrainerState, control: TrainerControl, **kwargs):
            if state.is_world_process_zero:
                _callback(step='start training', device=str(training_args.device),
                          cuda_available=str(torch.cuda.is_available()),
                          start_epoch=state.epoch, epochs=state.num_train_epochs)

        # def on_step_begin(self, args: TrainingArguments, state: TrainerState, control: TrainerControl, **kwargs):
        #     _callback(step='begin step', epoch=state.global_step)

        def on_epoch_begin(self, args: TrainingArguments, state: TrainerState, control: TrainerControl, **kwargs):
            if state.is_world_process_zero:
                _callback(step='begin epoch', epoch=state.epoch)

        def on_epoch_end(self, args: TrainingArguments, state: TrainerState, control: TrainerControl, **kwargs):
            if state.is_world_process_zero:
                _val = _callback(step='end epoch', epoch=state.epoch)
                if _val:
                    print('Issued exit by callback reply={}'.format(_val))
                    control.should_training_stop = True
    logging.basicConfig(
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        datefmt="%m/%d/%Y %H:%M:%S",
        handlers=[logging.StreamHandler(sys.stdout)],
    )
    logger.setLevel(logging.INFO if is_main_process(training_args.local_rank) else logging.WARN)

    # Log on each process the small summary:
    logger.warning(
        f"Process rank: {training_args.local_rank}, device: {training_args.device}, n_gpu: {training_args.n_gpu}"
        + f"distributed training: {bool(training_args.local_rank != -1)}, 16-bits training: {training_args.fp16}"
    )

    # Detecting last checkpoint.
    last_checkpoint = None
    if os.path.isdir(
            training_args.output_dir) and training_args.do_train and not training_args.overwrite_output_dir:
        last_checkpoint = get_last_checkpoint(training_args.output_dir)
        if last_checkpoint is None and len(os.listdir(training_args.output_dir)) > 0:
            raise ValueError(
                f"Output directory ({training_args.output_dir}) already exists and is not empty. "
                "Use --overwrite_output_dir to overcome."
            )
        elif last_checkpoint is not None:
            logger.info(
                f"Checkpoint detected, resuming training at {last_checkpoint}. To avoid this behavior, change "
                "the `--output_dir` or add `--overwrite_output_dir` to train from scratch."
            )

    if training_args.resume_from_checkpoint is not None:
        if isinstance(training_args.resume_from_checkpoint, bool) and training_args.resume_from_checkpoint:
            last_checkpoint = get_last_checkpoint(training_args.output_dir)
            if last_checkpoint is None:
                raise ValueError(f"No valid checkpoint found in output directory ({training_args.output_dir})")
            checkpoint = last_checkpoint
        else:
            checkpoint = training_args.resume_from_checkpoint
    else:
        checkpoint = last_checkpoint if last_checkpoint else None

    # Set the verbosity to info of the Transformers logger (on main process only):
    if is_main_process(training_args.local_rank):
        transformers.utils.logging.set_verbosity_info()
        transformers.utils.logging.enable_default_handler()
        transformers.utils.logging.enable_explicit_format()
    logger.info(f"Training/evaluation parameters:{training_args}")
    logger.info(f"model_arg:{model_args}")
    logger.info(f"data_args:{data_args}")
    options = []
    if data_args.task_type == 'document_record':
        options = ['否', '是']

    # monograph_record
    schema = None
    if data_args.aidp_field and os.path.isfile(data_args.aidp_field):
        with open(data_args.aidp_field, encoding='utf-8') as fr:
            head_tails = json.loads(fr.read())

        e2in_degree = {}
        # head_tails.pop('无', None)
        # head_tails.pop('关键字段', None)
        for h, ts in head_tails.items():
            e2in_degree.setdefault(h, 0)
            if ts:
                for t in ts:
                    e2in_degree[t] = e2in_degree.setdefault(t, 0) + 1
        if '无' in head_tails:
            for t in head_tails['无']:
                e2in_degree[t] = 0
            e2in_degree.pop('无', None)
        if '关键字段' in head_tails:
            for t in head_tails['关键字段']:
                e2in_degree[t] = 0
            e2in_degree.pop('关键字段', None)

        def dfs(root):
            if root in head_tails and head_tails[root]:
                res = {}
                for t in head_tails[root]:
                    res.setdefault(root, []).append(dfs(t))
                temp = {}
                for x in [_ for _ in res[root] if isinstance(_, dict)]:
                    temp.update(x)
                    res[root].remove(x)
                if temp:
                    res[root].append(temp)
            else:
                res = root
            return res
        schema = []
        for e, degree in e2in_degree.items():
            if degree == 0:
                schema.append(dfs(e))
        print('schema:', '\n', schema)


    kwargs = {'negative_ratio': data_args.negative_ratio,
              'prompt_prefix': data_args.prompt_prefix,
              'options': options,
              'separator': data_args.separator,
              'schema_lang': data_args.schema_lang,
              'task_type': data_args.task_type,
              'layout_analysis': data_args.layout_analysis,
              'anno_type': data_args.anno_type, }
    train_ds = []
    dev_ds = []
    if data_args.use_cache:
        builder = data_source2builder[data_args.data_source]
        script_identifier = builder.__name__.split('.')[-1]
        _PACKAGED_DATASETS_MODULES[script_identifier] = (builder.__name__,
                                                         _hash_python_lines(
                                                             inspect.getsource(
                                                                 builder).splitlines()))
        # 以下是脚本中datasets.BuilderConfig子类的参数。
        kwargs['is_train'] = True
        kwargs['image_base_path'] = data_args.train_image_base_path
        # if script_identifier == doccano_for_close_domain_builder.__name__.split('.')[-1]:
        #     kwargs.pop('layout_analysis')
        if training_args.do_train:
            train_ds = load_dataset(script_identifier,
                                    data_files=data_args.train_path,
                                    download_mode='force_redownload' if data_args.force_reload else None,
                                    **kwargs
                                    )['train']
        kwargs['is_train'] = False
        kwargs['image_base_path'] = data_args.dev_image_base_path
        if training_args.do_eval:
            dev_ds = load_dataset(script_identifier,
                                  data_files=data_args.dev_path,
                                  download_mode='force_redownload' if data_args.force_reload else None,
                                  # 以下是脚本中datasets.BuilderConfig子类的参数。
                                  **kwargs
                                  )['train']
    else:
        reader_module = data_source2reader_module[data_args.data_source]
        #  InMemoryTable,不会缓存到cache目录下。
        kwargs['image_base_path'] = data_args.train_image_base_path
        if training_args.do_train:
            train_ds, _, _ = reader_module(data_args.train_path,
                                           splits=[1., 0, 0],
                                           is_shuffle=False,
                                           seed=None,
                                           verbose=False,
                                           **kwargs
                                           ).convert()
        kwargs['image_base_path'] = data_args.dev_image_base_path
        if training_args.do_eval:
            _, dev_ds, _ = reader_module(data_args.dev_path,
                                         splits=[0, 1., 0],
                                         is_shuffle=False,
                                         seed=0,
                                         verbose=False,
                                         **kwargs
                                         ).convert()

        train_ds = {k: [d[k] for d in train_ds] for k in train_ds[0].keys()}
        dev_ds = {k: [d[k] for d in dev_ds] for k in dev_ds[0].keys()}
        train_ds = Dataset.from_dict(train_ds)
        dev_ds = Dataset.from_dict(dev_ds)

    if training_args.do_train and data_args.max_train_samples is not None and data_args.max_train_samples > 0:
        train_ds = train_ds.select(range(data_args.max_train_samples))
    if training_args.do_train and data_args.shuffle:
        train_ds = train_ds.shuffle(seed=training_args.seed)
    if training_args.do_eval and data_args.max_dev_samples is not None and data_args.max_dev_samples > 0:
        dev_ds = dev_ds.select(range(data_args.max_dev_samples))
    classes2document_record_schema = {None: []}
    print(f'task_type:{data_args.task_type}')
    #  从prompt格式数据中提取options和schema（仅支持实体级别的提取。）
    if data_args.task_type != 'ext':
        for data in train_ds:
            if not options:
                match = re.search(p, data['prompt'])
                if match is not None:
                    options = match.group()[1:-1].split(',')
        options = sorted(list(set(options)))
        print(f'options:{options}')
        if data_args.task_type != 'cls':  # intent
            text2cls = {}  # Dict[str, List[str]]
            for data in train_ds:
                if re.search(p, data['prompt']) is not None:
                    text = ''.join(data['word'])
                    cls_label = [data['prompt'][r['start'] + len(data['prompt']) + 1:
                                                r['end'] + len(data['prompt']) + 1] for r in data['result_list']]
                    text2cls[text] = cls_label
            for data in train_ds:
                if re.search(p, data['prompt']) is None and len(data['result_list']) > 0:
                    if data['prompt'] not in classes2document_record_schema[None]:
                        classes2document_record_schema[None].append(data['prompt'])
                    text = ''.join(data['word'])
                    cls_label = text2cls[text] if text in text2cls else []
                    for c_l in cls_label:
                        if c_l not in classes2document_record_schema:
                            classes2document_record_schema[c_l] = []
                        if data['prompt'] not in classes2document_record_schema[c_l]:
                            classes2document_record_schema[c_l].append(data['prompt'])
            print(f'classes2document_record_schema:{classes2document_record_schema}')
    else:
        for data in train_ds:
            if re.search(p, data['prompt']) is None and len(data['result_list']) > 0:
                if data['prompt'] not in classes2document_record_schema[None]:
                    classes2document_record_schema[None].append(data['prompt'])
        print(f'entity_schema:{classes2document_record_schema[None]}')

    tokenizer = XLMRobertaTokenizerFast.from_pretrained(model_args.tokenizer_name if model_args.tokenizer_name
                                                        else model_args.model_name_or_path)

    # TODO:放到torch.utils.data.Dataset中
    def prepare_features(examples):
        """
            Tokenize our examples with truncation and padding, but keep the overflows using a stride. This results
        in one example possible giving several features when a context is long, each of those features having a
        context that overlaps a bit the context of the previous feature.
        Args:
            examples:
        Returns:

        """
        tokenized_examples = tokenizer(
            [[p] for p in examples['prompt']],
            examples["word"],
            is_split_into_words=True,
            truncation=True,
            max_length=data_args.max_seq_len,
            stride=data_args.doc_stride,
            return_overflowing_tokens=True,
            return_offsets_mapping=True,
            padding=False,
        )

        # Since one example might give us several features if it has a long context, we need a map from a feature to
        # its corresponding example. This key gives us just that.
        sample_mapping = tokenized_examples.pop("overflow_to_sample_mapping")
        # The offset mappings will give us a map from token to character position in the original context. This will
        # help us compute the start_positions and end_positions.
        offset_mapping = tokenized_examples.pop("offset_mapping")
        input_ids_all = []
        attention_mask_all = []
        start_positions_all = []
        end_positions_all = []
        bbox_all = []
        token_type_ids_all = []
        position_ids_all = []
        for exam_id, offsets in enumerate(offset_mapping):
            # if exam_id == 45:
            #     print()
            # One example can give several spans, this is the index of the example containing this span of text.
            sample_index = sample_mapping[exam_id]
            input_ids, word_ids, offsets = tuple(zip(*[(ind, word_ind, off) for ind, word_ind, off in
                                                       zip(tokenized_examples['input_ids'][exam_id],
                                                           tokenized_examples.word_ids(exam_id),
                                                           offsets) if ind != 6]))
            offsets = [list(off) for off in offsets]
            sep_index = input_ids.index(tokenizer.sep_token_id)  # 中间第一个sep索引
            #  处理bbox（此前未缩放）
            new_bbox = [[0, 0, 0, 0]] * len(offsets)  # uiex, cls和中间两个sep以及text的box都是[0, 0, 0 ,0 ]
            new_bbox[-1] = [1000, 1000, 1000, 1000]
            img_w = examples['image_width'][sample_index]
            img_h = examples['image_height'][sample_index]
            bbox = [box_norm(box, img_w, img_h) for box in examples['bbox'][sample_index]]
            for j, word_id in enumerate(word_ids):
                if word_id is not None and j > sep_index:
                    new_bbox[j] = bbox[word_id]
            input_ids_all.append(input_ids)
            bbox_all.append(new_bbox)
            position_ids_all.append(list(range(len(input_ids))))
            token_type_ids_all.append([1] * (sep_index + 1) + [0] * (len(input_ids) - sep_index - 1))  # uiex独特方式
            attention_mask_all.append([1] * len(input_ids))
            if 'result_list' in examples:
                result_list = examples["result_list"][sample_index]
                bias = offsets[sep_index - 1][1] + 1
                cur_word_total_length = list(accumulate(list(map(len, examples['word'][sample_index]))))
                # temp = deepcopy(offsets)
                for index in range(sep_index + 2, len(offsets) - 1):
                    word_id = word_ids[index]
                    mapping = offsets[index]
                    if word_id != 0:
                        offsets[index] = (cur_word_total_length[word_id - 1] + bias + mapping[0],
                                          cur_word_total_length[word_id - 1] + bias + mapping[1])
                    else:
                        offsets[index] = (bias + mapping[0], bias + mapping[1])
                ori_l = offsets[sep_index + 2][0] - bias
                ori_r = offsets[-2][1] - bias

                result_list_overflowing = []
                for result in result_list:
                    if result['start'] < 0:
                        result_list_overflowing.append(result)
                        continue
                    if result['start'] >= ori_l and result['end'] - 1 < ori_r:
                        result_list_overflowing.append(result)
                    elif result['start'] < ori_l <= result['end'] - 1:
                        result_list_overflowing.append({'text': result['text'],
                                                        'start': ori_l,
                                                        'end': result['end']})
                        continue
                    elif result['start'] < ori_r <= result['end'] - 1:
                        result_list_overflowing.append({'text': result['text'],
                                                        'start': result['start'],
                                                        'end': ori_r})
                    else:
                        a = 0

                start_positions_exam = [0] * (len(offsets) + 49)
                end_positions_exam = [0] * (len(offsets) + 49)
                start, end = 0, 0
                for item in result_list_overflowing:
                    start = map_offset(item["start"] + bias, offsets)
                    end = map_offset(item["end"] - 1 + bias, offsets)
                    start_positions_exam[start] = 1.0
                    end_positions_exam[end] = 1.0
                start_positions_all.append(start_positions_exam)
                end_positions_all.append(end_positions_exam)

        tokenized_examples['input_ids'] = input_ids_all
        tokenized_examples['bbox'] = bbox_all
        tokenized_examples['position_ids'] = position_ids_all
        tokenized_examples['attention_mask'] = attention_mask_all
        tokenized_examples['token_type_ids'] = token_type_ids_all
        tokenized_examples['image'] = [examples['image'][sample_index] for sample_index in sample_mapping]
        if 'result_list' in examples:
            tokenized_examples['start_positions'] = start_positions_all
            tokenized_examples['end_positions'] = end_positions_all

        return tokenized_examples

    if training_args.do_train:
        train_ds = train_ds.map(partial(prepare_features),
                                batched=True, remove_columns=train_ds.column_names)
        train_ds = DocumentDataset(train_ds,
                                   image_base_path=data_args.train_image_base_path if data_args.train_image_base_path
                                   else os.path.dirname(data_args.train_path))
    if training_args.do_eval:
        dev_ds = dev_ds.map(partial(prepare_features),
                            batched=True, remove_columns=dev_ds.column_names)
        dev_ds = DocumentDataset(dev_ds,
                                 image_base_path=data_args.dev_image_base_path if data_args.dev_image_base_path else (
                                     data_args.train_image_base_path if data_args.train_image_base_path else os.path.dirname(
                                         data_args.dev_path)))

    def collate(features):  # list[dict]
        max_length = max(map(lambda f: len(f['input_ids']), features))
        ans = []
        for feat in features:
            diff = max_length - len(feat['bbox'])
            d = dict()
            for key, value in feat.items():
                if key == 'image':
                    d[key] = value
                    continue
                pad_id = 0
                if key == 'bbox':
                    pad_id = [0, 0, 0, 0]
                elif key == 'input_ids':
                    pad_id = tokenizer.pad_token_id
                try:
                    d[key] = value + [pad_id] * diff
                except KeyError as e:
                    raise e
            ans.append(d)
        batch = dict()
        first = ans[0]
        for k, v in first.items():
            if v is not None and not isinstance(v, str) and not (isinstance(v, Sequence) and isinstance(v[0], str)):
                if isinstance(v, torch.Tensor):
                    batch[k] = torch.stack([f[k] for f in ans])
                else:
                    batch[k] = torch.tensor([f[k] for f in ans], dtype=torch.long if k != 'image' else torch.float)
        return batch
    model = UIEX.from_pretrained(
        model_args.model_name_or_path if not checkpoint else checkpoint,
        # config=config,
    )
    for name, params in model.named_parameters():
        if 'word_embeddings' in name:
            params.requires_grad = False
    if not isinstance(tokenizer, PreTrainedTokenizerFast):
        raise ValueError(
            "This script only works for models that have a fast tokenizer."
        )

    class MyTrainer(Trainer):
        def save_model(self, output_dir: Optional[str] = None, _internal_call: bool = False):
            super().save_model(output_dir, _internal_call=_internal_call)
            if schema:
                if output_dir is None:
                    output_dir = self.args.output_dir
                with open(os.path.join(output_dir, 'schema.json'), mode='w', encoding='utf-8') as fw:
                    json.dump(schema, fw, ensure_ascii=False)

    trainer = MyTrainer(
        model=model,
        args=training_args,
        train_dataset=train_ds if training_args.do_train else None,
        eval_dataset=dev_ds if training_args.do_eval else None,
        tokenizer=tokenizer,
        compute_metrics=compute_metrics,
        data_collator=collate,
        callbacks=[MyCallBack()],
    )

    print(os.environ)

    # Training
    if training_args.do_train:
        train_result = trainer.train(resume_from_checkpoint=checkpoint)
        metrics = train_result.metrics
        trainer.save_model()  # Saves the tokenizer too for easy upload
        trainer.log_metrics("train", metrics)
        trainer.save_metrics("train", metrics)
        trainer.save_state()
    if os.path.exists(training_args.output_dir):
        for dir in os.listdir(training_args.output_dir):
            if 'checkpoint' in os.path.basename(dir):
                shutil.rmtree(os.path.join(training_args.output_dir, dir))
    # Evaluation
    if training_args.do_eval:
        metrics = trainer.evaluate()
        trainer.log_metrics("eval", metrics)
        trainer.save_metrics("eval", metrics)


if __name__ == '__main__':
    # '--logging_steps 5 --save_steps 15 --eval_steps 15 --seed 42 --model_name_or_path C:/Users/<USER>/Desktop/uie-x-base-v1 --output_dir ../../data/finetuned/doc_ie_close_domain_test/checkpoints --train_path ../../raw_data/real_estate/split_trim_underline_fake_relation/train.txt --dev_path ../../raw_data/real_estate/split_trim_underline_fake_relation/dev.txt --max_seq_len 512 --per_device_train_batch_size 2 --gradient_accumulation_steps 1 --per_device_eval_batch_size 2 --num_train_epochs 80 --learning_rate 1e-5 --label_names labels --do_train --do_eval --evaluation_strategy steps --overwrite_output_dir --disable_tqdm True --metric_for_best_model eval_f1 --load_best_model_at_end False --save_total_limit 1 --no_cuda --use_cache 1 --force_reload 0 --train_image_base_path C:/Users/<USER>/Desktop/不动产登记申请审批表/images --dev_image_base_path C:/Users/<USER>/Desktop/不动产登记申请审批表/images'
    main()
# [INFO|trainer.py:2891] 2022-12-24 20:17:53,223 >> ***** Running Evaluation *****
# [INFO|trainer.py:2893] 2022-12-24 20:17:53,223 >>   Num examples = 489
# [INFO|trainer.py:2896] 2022-12-24 20:17:53,223 >>   Batch size = 4

# ***** eval metrics *****
#   epoch                   =       20.0
#   eval_f1                 =     0.9957
#   eval_loss               =     0.0001
#   eval_precision          =     0.9913
#   eval_recall             =        1.0
#   eval_runtime            = 0:00:48.42
#   eval_samples_per_second =     10.098
#   eval_steps_per_second   =       2.54

#
# if __name__ == '__main__':
#     # '--logging_steps 5 --save_steps 15 --eval_steps 15 --seed 42 --model_name_or_path C:/Users/<USER>/Desktop/uie-x-base-v1 --output_dir ../../data/finetuned/doc_ie_close_domain_test/checkpoints --train_path ../../raw_data/real_estate/split_trim_underline_fake_relation/train.txt --dev_path ../../raw_data/real_estate/split_trim_underline_fake_relation/dev.txt --max_seq_len 512 --per_device_train_batch_size 2 --gradient_accumulation_steps 1 --per_device_eval_batch_size 2 --num_train_epochs 80 --learning_rate 1e-5 --label_names labels --do_train --do_eval --evaluation_strategy steps --overwrite_output_dir --disable_tqdm True --metric_for_best_model eval_f1 --load_best_model_at_end False --save_total_limit 1 --no_cuda --use_cache 1 --force_reload 0 --train_image_base_path C:/Users/<USER>/Desktop/不动产登记申请审批表/images --dev_image_base_path C:/Users/<USER>/Desktop/不动产登记申请审批表/images'
#     main()
#
#
#
