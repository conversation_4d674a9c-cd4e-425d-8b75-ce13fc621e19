# encoding=utf-8
from importlib_resources import files
from tables import *


__all__ = ['get_w2v', 'W2V']


#def singleton(cls):
#    _instance = {}
#
#    def inner():
#        if cls not in _instance:
#            _instance[cls] = cls()
#        return _instance[cls]
#    return inner


_model_path = str(files('dtm')/'index.h5')


# NOTE PyTables share the same opened H5 among multiple opens
class W2V:
    def __init__(self, model_path=_model_path):
        self.h5f = open_file(model_path, 'r')
        self.w2i = { w.decode('utf-8'):i for i,w in enumerate(self.h5f.root.w2v.axis1[:]) }
        self.arr = self.h5f.root.w2v.block0_values

    def __contains__(self, key):
        return self.w2i.__contains__(key)

    def __getitem__(self, idx):
        if isinstance(idx, str):
            return self.arr[self.w2i[idx],:]
        else:
            return self.arr[[self.w2i[i] for i in idx],:]

    def __iter__(self):
        return self.w2i.__iter__()

    def __len__(self):
        return self.w2i.__len__()

    def __del__(self):
        if hasattr(self, 'h5f'):
            self.h5f.close()

    def __repr__(self):
        return self.h5f.__repr__()

    def __str__(self):
        return self.h5f.__str__()


def get_w2v(model_path=_model_path):
    return W2V(model_path)


if __name__ == '__main__':
    w2v1 = get_w2v()
    w2v2 = get_w2v()
    print(w2v1)
    print(w2v2)
