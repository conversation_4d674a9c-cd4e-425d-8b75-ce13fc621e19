import os
from collections import defaultdict
from .information_extraction import UIETask


class IntentDetectionTask(UIETask):
    def __init__(self, task, model, prompt_prefix='意图识别', schema=None, device='cpu', **kwargs):
        super().__init__(task=task, model=model, schema=None, device=device, **kwargs)
        if "task_path" in self.kwargs:
            self._task_path = self.kwargs["task_path"]
            self._custom_model = True
        else:
            self._task_path = os.path.join(self._home_path, "taskflow", 'information_extraction', self.model)

        self._prompt_prefix = prompt_prefix
        self._options = sorted(schema.keys())
        # self._skip_cls = kwargs.get('skip_cls', False)
        self._cls_prompt = self._prompt_prefix + '[' + ','.join(self._options) + ']'
        self._cls_schema_tree = [self._cls_prompt]
        self._cls_schema_tree = self._build_tree(self._cls_schema_tree)
        self._cls_max_seq_len = kwargs.get('cls_max_seq_len', 512)
        self._return_max_cls = kwargs.get('return_max_cls', False)
        self.exam_schema(schema)
        self._ext_schema_tree_dict = {k: self._build_tree(v) for k, v in schema.items()}

    def exam_schema(self, schema):
        if not isinstance(schema, dict):
            raise ValueError('请输入正确字典格式的schema参数。key代表意图，value列表代表意图下的槽位字段。')
        else:
            for k, v in schema.items():
                if not(isinstance(v, (list, str))):
                    raise ValueError('请输入正确字典格式的schema参数。key代表意图，value列表代表意图下的槽位字段。')

    def set_schema(self, schema):
        self.exam_schema(schema)
        self._options = sorted(schema.keys())
        self._cls_prompt = self._prompt_prefix + '[' + ','.join(self._options) + ']'
        self._cls_schema_tree = [self._cls_prompt]
        if isinstance(schema, dict) or isinstance(schema, str):
            schema = [schema]
        self._ext_schema_tree_dict = {k: self._build_tree(v) for k, v in schema.items()}

    def _predict(self, examples):
        intent2hash_map = defaultdict(list)
        intent2data_ext = defaultdict(list)
        intent2cls_score = defaultdict(list)
        ans = [{} for _ in examples]
        self._schema_tree = self._cls_schema_tree
        ori_max_seq_len = self._max_seq_len
        self._max_seq_len = self._cls_max_seq_len
        # if not self._skip_cls:
        cls_ans = self._multi_stage_predict(examples)

        for i, c_ans in enumerate(cls_ans):
            if self._cls_prompt in c_ans:
                for cls_res in c_ans[self._cls_prompt]:
                    cls_type = cls_res['text']
                    if cls_type in self._options:
                        intent2hash_map[cls_type].append(i)
                        intent2data_ext[cls_type].append(examples[i])
                        intent2cls_score[cls_type].append(cls_res['probability'])
                        break
        self._max_seq_len = ori_max_seq_len
        for intent_name, schema in self._ext_schema_tree_dict.items():
            self._schema_tree = schema
            ext_ans = self._multi_stage_predict(intent2data_ext[intent_name])
            for e_ans, ori_id, cls_score in zip(ext_ans, intent2hash_map[intent_name], intent2cls_score[intent_name]):
                if '意图类型' not in ans[ori_id]:
                    ans[ori_id]['意图类型'] = []
                d = {'text': intent_name, 'relations': e_ans, 'probability': cls_score}
                ans[ori_id]['意图类型'].append(d)
        self._schema_tree = None
        return ans

    def _auto_joiner(self, short_results, input_mapping):
        concat_results = []
        is_cls_task = False
        for short_result in short_results:
            if short_result == []:
                continue
            elif "start" not in short_result[0].keys() and "end" not in short_result[0].keys():
                is_cls_task = True
                break
            else:
                break
        for k, vs in input_mapping.items():
            if is_cls_task:
                cls_options = {}
                single_results = []
                for v in vs:
                    if len(short_results[v]) == 0:
                        continue
                    if short_results[v][0]["text"] not in cls_options.keys():
                        cls_options[short_results[v][0]["text"]] = [1, short_results[v][0]["probability"]]
                    else:
                        cls_options[short_results[v][0]["text"]][0] += 1
                        cls_options[short_results[v][0]["text"]][1] += short_results[v][0]["probability"]
                if len(cls_options) != 0:
                    if self._return_max_cls:
                        cls_res, cls_info = max(cls_options.items(),
                                                key=lambda x: x[1])
                        concat_results.append([{
                            'text': cls_res,
                            'probability': cls_info[1] / cls_info[0]
                        }])
                    else:
                        cls_res_list = []
                        for cls_res, cls_info in cls_options.items():
                            cls_res_list.append({
                                'text': cls_res,
                                'probability': cls_info[1] / cls_info[0]
                            })
                        concat_results.append(cls_res_list)
                else:
                    concat_results.append([])
            else:
                single_results = []
                for v in vs:
                    single_results.extend(short_results[v])
                concat_results.append(single_results)
        return concat_results
