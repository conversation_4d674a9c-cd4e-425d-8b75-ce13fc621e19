BaseConfig: &base
  # 数据库连接符
  DATABASE_URI: "mysql+pymysql://root:be@MysqL@mysql:3306/aimp?charset=utf8"
  # 历史记录
  METADATA_PATH: "~/.dbstash_sa_last_run/meta_json2"

  # ***************** 自定义配置 ***********************
  SERVER_API: {
    # 获取档案系统全部节点
    "getAllStructure": {
      "method": "POST",
      "url": "http://710c9565j7.yicp.fun:13779/api/public/getAllStructure"
    },
    # 获取服务器配置
    "getFileServers": {
      "method": "POST",
      "url": "http://710c9565j7.yicp.fun:13779/api/public/getFileServers"
    },
    # 获取档案数据
    "getDataList": {
      "method": "POST",
      "url": "http://710c9565j7.yicp.fun:13779/api/public/getDataList",
      "param": {
        "pageSize": 1000,
      }
    },
    # 获取档案数据对应文件信息
    "getDataFileList": {
      "method": "POST",
      "url": "http://710c9565j7.yicp.fun:13779/api/public/getDataFileList"
    }
  }

  # 字段映射
  FIELD_SCHEMA: {
      "档号": "dh",
      "文件级档号": "dh",
      "题名": "tm",
      "正题名": "tm",
      "全宗号": "qzh",
      "全宗名称": "qzmc",
      "文号": "wh",
      "页数": "ys",
      "总页数": "ys",
      "密级": "mj",
      "成文时间": "cwsj",
      "保管期限": "bgqx"
  }

DevelopmentConfig:
  <<: *base

ProductionConfig1:
  <<: *base
  DATABASE_URI: ~

ProductionConfig2:
  <<: *base

# 当前使用配置
run_config: "ProductionConfig2"

