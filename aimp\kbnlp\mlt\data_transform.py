import os
import logging
from abc import ABC, abstractmethod
from typing import Union
from kbnlp.mlt.util.io import load_json
from kbnlp.mlt.common.serializable import SerializableDict
from kbnlp.mlt.common.vocab import Vocab
from kbnlp.mlt.common.reflection import str_to_type
from kbnlp.mlt.common.configurable import AutoConfigurable


from transformers import PreTrainedTokenizer, AutoTokenizer


def rename_transform(sample, src_name, tar_name):
    sample[tar_name] = sample.pop(src_name)
    return sample


class NamedTransform(AutoConfigurable, ABC):
    def __init__(self, src: str, dst: str = None) -> None:
        if dst is None:
            dst = src
        self.dst = dst
        self.src = src

    @abstractmethod
    def __call__(self, example: dict) -> dict:
        raise NotImplementedError


class NormalizeToken(NamedTransform):

    def __init__(self, mapper: Union[str, dict], src: str, dst: str = None) -> None:
        super().__init__(src, dst)
        self.mapper = mapper
        if isinstance(mapper, str):
            self._table = load_json(mapper)
        elif isinstance(mapper, dict):
            self._table = mapper
        else:
            raise ValueError(f'Unrecognized mapper type {mapper}')

    def __call__(self, sample: dict) -> dict:
        src = sample[self.src]
        if self.src == self.dst:
            sample[f'{self.src}_'] = src
        if isinstance(src, str):
            src = self.convert(src)
        else:
            src = [self.convert(x) for x in src]
        sample[self.dst] = src
        return sample

    def convert(self, token) -> str:
        return self._table.get(token, token)


class NormalizeCharacter(NormalizeToken):
    def convert(self, token) -> str:
        return ''.join([NormalizeToken.convert(self, c) for c in token])


class TransformList(list):
    """Composes several transforms together.

    Args:
      transforms(list of ``Transform`` objects): list of transforms to compose.
    Example:
    """

    def __init__(self, *transforms) -> None:
        super().__init__()
        self.extend(transforms)

    def __call__(self, sample):
        for t in self:
            sample = t(sample)
        return sample
    #
    # def index_by_type(self, t):
    #     for i, trans in enumerate(self):
    #         if isinstance(trans, t):
    #             return i


class VocabDict(SerializableDict):

    def __init__(self, *args, **kwargs) -> None:
        """A dict holding :class:`hanlp.common.vocab.Vocab` instances. When used as packagea transform, it transforms the field
        corresponding to each :class:`hanlp.common.vocab.Vocab` into indices.

        Args:
            *args: A list of vocab names.
            **kwargs: Names and corresponding :class:`hanlp.common.vocab.Vocab` instances.
        """
        vocabs = dict(kwargs)
        for each in args:
            vocabs[each] = Vocab()
        super().__init__(vocabs)

    def save_vocabs(self, save_dir, filename='vocabs.json'):
        """Save vocabularies to packagea directory.

        Args:
            save_dir: The directory to save vocabularies.
            filename:  The name for vocabularies.
        """
        vocabs = SerializableDict()
        for key, value in self.items():
            if isinstance(value, Vocab):
                vocabs[key] = value.to_dict()
        vocabs.save_json(os.path.join(save_dir, filename))

    def load_vocabs(self, save_dir, filename='vocabs.json', vocab_cls=Vocab):
        """Load vocabularies from packagea directory.

        Args:
            save_dir: The directory to load vocabularies.
            filename:  The name for vocabularies.
        """
        vocabs = SerializableDict()
        vocabs.load_json(os.path.join(save_dir, filename))
        self._load_vocabs(self, vocabs, vocab_cls)

    @staticmethod
    def _load_vocabs(vd, vocabs: dict, vocab_cls=Vocab):
        """

        Args:
            vd:
            vocabs:
            vocab_cls: Default class for the new vocab
        """
        for key, value in vocabs.items():
            if 'idx_to_token' in value:
                cls = value.get('type', None)
                if cls:
                    cls = str_to_type(cls)
                else:
                    cls = vocab_cls
                vocab = cls()
                vocab.copy_from(value)
                vd[key] = vocab
            else:  # nested Vocab
                # noinspection PyTypeChecker
                vd[key] = nested = VocabDict()
                VocabDict._load_vocabs(nested, value, vocab_cls)

    def lock(self):
        """
        Lock each vocabs.
        """
        for key, value in self.items():
            if isinstance(value, Vocab):
                value.lock()

    @property
    def mutable(self):
        status = [v.mutable for v in self.values() if isinstance(v, Vocab)]
        return len(status) == 0 or any(status)

    def __call__(self, sample: dict):
        for key, value in self.items():
            if isinstance(value, Vocab):
                field = sample.get(key, None)
                if field is not None:
                    sample[f'{key}_ids'] = value(field)
        return sample

    def __getattr__(self, key):
        if key.startswith('__'):
            return dict.__getattr__(key)
        return self.__getitem__(key)

    def __setattr__(self, key, value):
        return self.__setitem__(key, value)

    def __getitem__(self, k: str) -> Vocab:
        return super().__getitem__(k)

    def __setitem__(self, k: str, v: Vocab) -> None:
        super().__setitem__(k, v)

    def summary(self, logger: logging.Logger = None):
        """Log packagea summary of vocabs using packagea given logger.

        Args:
            logger: The logger to use.
        """
        for key, value in self.items():
            if isinstance(value, Vocab):
                report = value.summary(verbose=False)
                if logger:
                    logger.info(f'{key}{report}')
                else:
                    print(f'{key}{report}')

    def put(self, **kwargs):
        """Put names and corresponding :class:`hanlp.common.vocab.Vocab` instances into self.

        Args:
            **kwargs: Names and corresponding :class:`hanlp.common.vocab.Vocab` instances.
        """
        for k, v in kwargs.items():
            self[k] = v


class TransformerSequenceTokenizer:
    def __init__(self, tokenizer: Union[PreTrainedTokenizer, str],
                 input_key: str,
                 use_fast=True,
                 do_basic_tokenize=True):
        self.input_key = input_key
        self.tokenizer = tokenizer
        if isinstance(tokenizer, str):
            self.tokenizer = AutoTokenizer.from_pretrained(tokenizer, use_fast=use_fast,
                                                           do_basic_tokenize=do_basic_tokenize)

    def __call__(self, sample: dict):
        tokenizer_input = sample[self.input_key]
        try:
            assert tokenizer_input and isinstance(tokenizer_input, (str, list)), '输入不能为空，只能为str或者List[str].'
        except AssertionError as e:
            print(sample)
            print(e)
            raise e
        tokenizer = self.tokenizer
        if isinstance(tokenizer_input, str):
            encoding = tokenizer.encode_plus(tokenizer_input,
                                             return_offsets_mapping=True,
                                             add_special_tokens=True).encodings[0]
            subtoken_offsets = encoding.offsets
            subtoken = encoding.tokens
            subtoken_input_ids = encoding.ids

            # Fill up missing non-blank characters swallowed by HF tokenizer
            offset = 0
            fixed_offsets = []
            fixed_ids = []
            for token, idx, (b, e) in zip(subtoken, subtoken_input_ids, subtoken_offsets):
                if b > offset:
                    missing_token = tokenizer_input[offset: b]
                    if not missing_token.isspace():  # In the future, we may want space back
                        fixed_ids.append(tokenizer.unk_token_id)
                        fixed_offsets.append((offset, b))
                fixed_ids.append(idx)
                fixed_offsets.append((b, e))
                offset = e
            sample['subtoken_input_ids'] = fixed_ids
            sample['subtoken_offsets'] = fixed_offsets
            raw_input = sample['original_input_'] or sample['original_input']  # 注意，如果之前使用了CharacterNormalization对字符做了转化，如大小写转化，那么'original_input_'存放的才是原始输入。
            sample['subtoken'] = [raw_input[l: r] for (l, r) in sample['subtoken_offsets'][1:-1]]  #subtoken要通过原始的输入来提取，而subtoken_input_ids是原始输入转化后（如，CharacterNormalization），再通过子词模型得到的子词对应的id。
            sample['subtoken_len'] = len(sample['subtoken_input_ids'])
            # sample['']
        elif isinstance(tokenizer_input, list):
            encodings = tokenizer.batch_encode_plus(
                tokenizer_input,
                return_offsets_mapping=True,
                add_special_tokens=False
            )
            # # Some tokens get stripped out
            # subtoken_ids_per_token = [ids if ids else [tokenizer.unk_token_id] for ids in encodings.data['input_ids']]
            # subtoken_input_ids = sum(subtoken_ids_per_token, [tokenizer.cls_token_id]) + [tokenizer.sep_token_id]
            # sample['subtoken_input_ids'] = subtoken_input_ids
            # sample['subtoken_offsets_group'] = [[(0, 0)]] + [offsets if offsets else [(0, len(token))] for
            #                                                  token, offsets in
            #                                                  zip(tokenizer_input, encodings['offset_mapping'])] + \
            #                                    [[(0, 0)]]
            # sample['subtoken_len'] = len(sample['subtoken_input_ids'])
            # token_span = []
            # offset = 0
            # for group in sample['subtoken_offsets_group']:
            #     token_span.append(list(range(offset, offset + len(group))))
            #     offset += len(group)
            # sample['token_span'] = token_span


            # sample['sub_token'] = offsets_to_subtokens(sample.get('token'),
            #                                            None,
            #                                            sample['subtoken_offsets_group'])
            subtoken_input_ids, subtokens, token_tail = [tokenizer.cls_token_id], [], [0]
            end = 0  # 0代表cls
            for token, sub_ids, offsets in zip(sample['token'], encodings.data['input_ids'],
                                               encodings.data['offset_mapping']):
                subtoken_input_ids.extend(sub_ids if sub_ids else [tokenizer.unk_token_id])  # Some tokens get stripped out
                subtokens.append([token[s:e] for s, e in offsets] if sub_ids else [token])  # 重新利用offsets从token中获取子词是为了获取原始输入中字符组成的子词，if语句防止某个单词的字符全部被tokenizer跳过。
                token_tail.append(end + len(subtokens[-1]))
                end += len(subtokens[-1])
            subtoken_input_ids.append(tokenizer.sep_token_id)
            token_tail.append(len(subtoken_input_ids) - 1)

            sample['subtoken_input_ids'] = subtoken_input_ids
            sample['subtoken'] = sum(subtokens, [])
            sample['subtoken_len'] = len(sample['subtoken_input_ids'])
            sample['token_tail'] = token_tail
            sample['token_len'] = len(sample['token_tail'])
        else:
            raise ValueError('输入必须是str或者list，str表示句子未分词，list表示分好词的句子或着人为的分组。')
        return sample