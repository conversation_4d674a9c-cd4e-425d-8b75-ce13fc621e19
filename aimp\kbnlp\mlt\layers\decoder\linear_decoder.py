import torch
from typing import Dict
from torch import nn

class LinearCRFDecoder(torch.nn.Module):
    def __init__(self,
                 encoder_size,
                 num_labels,
                 crf=False) -> None:
        super().__init__()
        self.classifier = torch.nn.Linear(encoder_size, num_labels)
        nn.init.orthogonal_(self.classifier.weight, gain=1)
        # self.crf = CRF(num_labels) if crf else None

    def forward(self, contextualized_embeddings: torch.FloatTensor, batch: Dict, mask=None):
        return self.classifier(contextualized_embeddings[:, 1:-1, :])
