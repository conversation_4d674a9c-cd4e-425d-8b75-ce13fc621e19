('G:\\kubao-aimp\\aimp\\tools\\kbnlp\\scripts\\information_extraction\\build\\document_record_finetune\\PYZ-00.pyz',
 [('win32com',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\win32com\\__init__.py',
   'PYMODULE'),
  ('win32com.server.util',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\win32com\\server\\util.py',
   'PYMODULE'),
  ('win32com.server.dispatcher',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\win32com\\server\\dispatcher.py',
   'PYMODULE'),
  ('win32traceutil',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\win32\\lib\\win32traceutil.py',
   'PYMODULE'),
  ('win32com.util',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\win32com\\util.py',
   'PYMODULE'),
  ('win32con',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE'),
  ('win32com.server.exception',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\win32com\\server\\exception.py',
   'PYMODULE'),
  ('winerror',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\win32\\lib\\winerror.py',
   'PYMODULE'),
  ('win32com.server.policy',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\win32com\\server\\policy.py',
   'PYMODULE'),
  ('win32com.client',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\win32com\\client\\__init__.py',
   'PYMODULE'),
  ('win32com.client.makepy',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\win32com\\client\\makepy.py',
   'PYMODULE'),
  ('getopt',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\getopt.py',
   'PYMODULE'),
  ('gettext',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\gettext.py',
   'PYMODULE'),
  ('struct',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\struct.py',
   'PYMODULE'),
  ('pywin.dialogs.status',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\Pythonwin\\pywin\\dialogs\\status.py',
   'PYMODULE'),
  ('threading',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\threading.py',
   'PYMODULE'),
  ('_threading_local',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\_threading_local.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\contextlib.py',
   'PYMODULE'),
  ('pywin.mfc.thread',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\thread.py',
   'PYMODULE'),
  ('pywin.mfc.object',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\object.py',
   'PYMODULE'),
  ('pywin.mfc.dialog',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\dialog.py',
   'PYMODULE'),
  ('pywin.mfc.window',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\window.py',
   'PYMODULE'),
  ('pywin.mfc',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\__init__.py',
   'PYMODULE'),
  ('pywin.dialogs',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\Pythonwin\\pywin\\dialogs\\__init__.py',
   'PYMODULE'),
  ('pywin',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\Pythonwin\\pywin\\__init__.py',
   'PYMODULE'),
  ('importlib',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\importlib\\resources.py',
   'PYMODULE'),
  ('typing',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\typing.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib._common',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\importlib\\_common.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\zipfile.py',
   'PYMODULE'),
  ('py_compile',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\py_compile.py',
   'PYMODULE'),
  ('lzma',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\lzma.py',
   'PYMODULE'),
  ('_compression',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\_compression.py',
   'PYMODULE'),
  ('bz2',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\bz2.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\importlib\\metadata.py',
   'PYMODULE'),
  ('configparser',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\configparser.py',
   'PYMODULE'),
  ('email',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\email\\__init__.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\email\\utils.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('calendar',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\calendar.py',
   'PYMODULE'),
  ('datetime',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\datetime.py',
   'PYMODULE'),
  ('_strptime',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\_strptime.py',
   'PYMODULE'),
  ('socket',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\socket.py',
   'PYMODULE'),
  ('selectors',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\selectors.py',
   'PYMODULE'),
  ('random',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\random.py',
   'PYMODULE'),
  ('statistics',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\statistics.py',
   'PYMODULE'),
  ('decimal',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\decimal.py',
   'PYMODULE'),
  ('_pydecimal',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\_pydecimal.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\contextvars.py',
   'PYMODULE'),
  ('fractions',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\fractions.py',
   'PYMODULE'),
  ('numbers',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\numbers.py',
   'PYMODULE'),
  ('hashlib',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\hashlib.py',
   'PYMODULE'),
  ('logging',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('pickle',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\pickle.py',
   'PYMODULE'),
  ('pprint',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\pprint.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('string',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\string.py',
   'PYMODULE'),
  ('bisect',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\bisect.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.message',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\base64.py',
   'PYMODULE'),
  ('quopri',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\quopri.py',
   'PYMODULE'),
  ('uu',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\uu.py',
   'PYMODULE'),
  ('optparse',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\optparse.py',
   'PYMODULE'),
  ('textwrap',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\textwrap.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email.header',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\email\\errors.py',
   'PYMODULE'),
  ('csv',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\csv.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('win32com.client.gencache',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\win32com\\client\\gencache.py',
   'PYMODULE'),
  ('glob',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\glob.py',
   'PYMODULE'),
  ('win32com.client.selecttlb',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\win32com\\client\\selecttlb.py',
   'PYMODULE'),
  ('pywin.dialogs.list',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\Pythonwin\\pywin\\dialogs\\list.py',
   'PYMODULE'),
  ('commctrl',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\win32\\lib\\commctrl.py',
   'PYMODULE'),
  ('win32com.client.genpy',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\win32com\\client\\genpy.py',
   'PYMODULE'),
  ('win32com.client.CLSIDToClass',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\win32com\\client\\CLSIDToClass.py',
   'PYMODULE'),
  ('win32com.client.dynamic',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\win32com\\client\\dynamic.py',
   'PYMODULE'),
  ('win32com.client.build',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\win32com\\client\\build.py',
   'PYMODULE'),
  ('pywintypes',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\win32\\lib\\pywintypes.py',
   'PYMODULE'),
  ('pywin32_system32', '-', 'PYMODULE'),
  ('win32com.server',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\win32com\\server\\__init__.py',
   'PYMODULE'),
  ('win32com.universal',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\win32com\\universal.py',
   'PYMODULE'),
  ('win32com.client.util',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\win32com\\client\\util.py',
   'PYMODULE'),
  ('pythoncom',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\pythoncom.py',
   'PYMODULE'),
  ('tempfile',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\tempfile.py',
   'PYMODULE'),
  ('pkg_resources',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('packaging.version',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('packaging.utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.tags',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.requirements',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('pyparsing',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\pyparsing\\__init__.py',
   'PYMODULE'),
  ('pyparsing.common',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\pyparsing\\common.py',
   'PYMODULE'),
  ('pyparsing.testing',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\pyparsing\\testing.py',
   'PYMODULE'),
  ('pyparsing.unicode',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\pyparsing\\unicode.py',
   'PYMODULE'),
  ('pyparsing.helpers',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\pyparsing\\helpers.py',
   'PYMODULE'),
  ('html.entities',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\html\\entities.py',
   'PYMODULE'),
  ('html',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\html\\__init__.py',
   'PYMODULE'),
  ('pyparsing.results',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\pyparsing\\results.py',
   'PYMODULE'),
  ('pyparsing.core',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\pyparsing\\core.py',
   'PYMODULE'),
  ('pyparsing.diagram',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\pyparsing\\diagram\\__init__.py',
   'PYMODULE'),
  ('pdb',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\pdb.py',
   'PYMODULE'),
  ('pydoc',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\pydoc.py',
   'PYMODULE'),
  ('webbrowser',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\webbrowser.py',
   'PYMODULE'),
  ('http.server',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\http\\server.py',
   'PYMODULE'),
  ('http',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\http\\__init__.py',
   'PYMODULE'),
  ('socketserver',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\socketserver.py',
   'PYMODULE'),
  ('mimetypes',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\mimetypes.py',
   'PYMODULE'),
  ('http.client',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\http\\client.py',
   'PYMODULE'),
  ('ssl',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\ssl.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pydoc_data',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('tty',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\tty.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\subprocess.py',
   'PYMODULE'),
  ('runpy',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\runpy.py',
   'PYMODULE'),
  ('shlex',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\shlex.py',
   'PYMODULE'),
  ('signal',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\signal.py',
   'PYMODULE'),
  ('code',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\code.py',
   'PYMODULE'),
  ('codeop',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\codeop.py',
   'PYMODULE'),
  ('__future__',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\__future__.py',
   'PYMODULE'),
  ('dis',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\dis.py',
   'PYMODULE'),
  ('opcode',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\opcode.py',
   'PYMODULE'),
  ('bdb',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\bdb.py',
   'PYMODULE'),
  ('cmd',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\cmd.py',
   'PYMODULE'),
  ('pyparsing.actions',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\pyparsing\\actions.py',
   'PYMODULE'),
  ('pyparsing.exceptions',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\pyparsing\\exceptions.py',
   'PYMODULE'),
  ('pyparsing.util',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\pyparsing\\util.py',
   'PYMODULE'),
  ('packaging.markers',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging._structures',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('ctypes',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('packaging.__about__',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\packaging\\__about__.py',
   'PYMODULE'),
  ('packaging',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.zipp',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\pkg_resources\\_vendor\\zipp.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.util',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\util.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.unicode',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\unicode.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.testing',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\testing.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.results',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\results.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.helpers',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\helpers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.exceptions',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\exceptions.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.core',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\core.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.diagram',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\diagram\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.common',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\common.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.actions',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\actions.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.version',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.tags',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.specifiers',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.requirements',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.markers',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._structures',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._musllinux',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._manylinux',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.__about__',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__about__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.recipes',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.more',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('queue',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\queue.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.text',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.functools',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\functools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.context',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.simple',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\simple.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.readers',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.abc',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._legacy',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_legacy.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._itertools',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._compat',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_compat.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._common',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._adapters',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.appdirs',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\pkg_resources\\_vendor\\appdirs.py',
   'PYMODULE'),
  ('win32com.shell.shellcon',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\win32comext\\shell\\shellcon.py',
   'PYMODULE'),
  ('win32com.shell',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\win32comext\\shell\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\pkg_resources\\_vendor\\__init__.py',
   'PYMODULE'),
  ('sysconfig',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\sysconfig.py',
   'PYMODULE'),
  ('_aix_support',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\_aix_support.py',
   'PYMODULE'),
  ('_bootsubprocess',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\_bootsubprocess.py',
   'PYMODULE'),
  ('pkg_resources.extern',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\pkg_resources\\extern\\__init__.py',
   'PYMODULE'),
  ('imp',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\imp.py',
   'PYMODULE'),
  ('inspect',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\inspect.py',
   'PYMODULE'),
  ('ast',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\ast.py',
   'PYMODULE'),
  ('plistlib',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\plistlib.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('platform',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\platform.py',
   'PYMODULE'),
  ('pkgutil',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\pkgutil.py',
   'PYMODULE'),
  ('zipimport',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\zipimport.py',
   'PYMODULE'),
  ('_distutils_hack',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('setuptools',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('distutils.version',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\distutils\\version.py',
   'PYMODULE'),
  ('distutils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\distutils\\__init__.py',
   'PYMODULE'),
  ('distutils.archive_util',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\distutils\\archive_util.py',
   'PYMODULE'),
  ('tarfile',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\tarfile.py',
   'PYMODULE'),
  ('gzip',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\gzip.py',
   'PYMODULE'),
  ('distutils.dir_util',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\distutils\\dir_util.py',
   'PYMODULE'),
  ('distutils.file_util',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\distutils\\file_util.py',
   'PYMODULE'),
  ('distutils.dep_util',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\distutils\\dep_util.py',
   'PYMODULE'),
  ('distutils.spawn',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\distutils\\spawn.py',
   'PYMODULE'),
  ('distutils.debug',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\distutils\\debug.py',
   'PYMODULE'),
  ('distutils.sysconfig',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\distutils\\sysconfig.py',
   'PYMODULE'),
  ('distutils.text_file',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\distutils\\text_file.py',
   'PYMODULE'),
  ('_osx_support',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\_osx_support.py',
   'PYMODULE'),
  ('distutils.log',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.unixccompiler',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_distutils\\unixccompiler.py',
   'PYMODULE'),
  ('distutils.ccompiler',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\distutils\\ccompiler.py',
   'PYMODULE'),
  ('distutils.fancy_getopt',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.py39compat',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_distutils\\py39compat.py',
   'PYMODULE'),
  ('setuptools._distutils.py38compat',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_distutils\\py38compat.py',
   'PYMODULE'),
  ('setuptools._distutils.msvccompiler',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_distutils\\msvccompiler.py',
   'PYMODULE'),
  ('distutils.msvc9compiler',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('setuptools._distutils.msvc9compiler',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('distutils.versionpredicate',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\distutils\\versionpredicate.py',
   'PYMODULE'),
  ('distutils.command',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\distutils\\command\\__init__.py',
   'PYMODULE'),
  ('distutils.cmd',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\distutils\\cmd.py',
   'PYMODULE'),
  ('distutils.dist',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dep_util',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_distutils\\dep_util.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.cygwinccompiler',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_distutils\\cygwinccompiler.py',
   'PYMODULE'),
  ('distutils.unixccompiler',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\distutils\\unixccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('distutils.extension',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\distutils\\extension.py',
   'PYMODULE'),
  ('distutils.config',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\distutils\\config.py',
   'PYMODULE'),
  ('cgi',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\cgi.py',
   'PYMODULE'),
  ('setuptools._distutils.config',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_distutils\\config.py',
   'PYMODULE'),
  ('setuptools._distutils.command.upload',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_distutils\\command\\upload.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('distutils.filelist',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.register',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_distutils\\command\\register.py',
   'PYMODULE'),
  ('getpass',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\getpass.py',
   'PYMODULE'),
  ('setuptools._distutils.command.py37compat',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_distutils\\command\\py37compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_scripts',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_distutils\\command\\install_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_lib',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_distutils\\command\\install_lib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_headers',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_distutils\\command\\install_headers.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_egg_info',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_distutils\\command\\install_egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_data',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_distutils\\command\\install_data.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_distutils\\command\\install.py',
   'PYMODULE'),
  ('site',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site.py',
   'PYMODULE'),
  ('rlcompleter',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\rlcompleter.py',
   'PYMODULE'),
  ('_sitebuiltins',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('setuptools._distutils.command.config',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_distutils\\command\\config.py',
   'PYMODULE'),
  ('setuptools._distutils.command.clean',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_distutils\\command\\clean.py',
   'PYMODULE'),
  ('setuptools._distutils.command.check',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_distutils\\command\\check.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_scripts',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_distutils\\command\\build_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_py',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_distutils\\command\\build_py.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('distutils._msvccompiler',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\secrets.py',
   'PYMODULE'),
  ('hmac',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\hmac.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('xmlrpc',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('multiprocessing',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_clib',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_distutils\\command\\build_clib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_rpm',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_distutils\\command\\bdist_rpm.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_dumb',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_distutils\\command\\bdist_dumb.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command._framework_compat',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_distutils\\command\\_framework_compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.bcppcompiler',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_distutils\\bcppcompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('unittest.mock',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\unittest\\mock.py',
   'PYMODULE'),
  ('unittest',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest.async_case',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.signals',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.main',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.runner',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.loader',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.suite',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.case',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest._log',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\unittest\\_log.py',
   'PYMODULE'),
  ('difflib',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\difflib.py',
   'PYMODULE'),
  ('unittest.result',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.util',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\unittest\\util.py',
   'PYMODULE'),
  ('asyncio',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.log',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.queues',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.locks',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.futures',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.events',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('setuptools._distutils._macos_compat',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_distutils\\_macos_compat.py',
   'PYMODULE'),
  ('setuptools._distutils._functools',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_distutils\\_functools.py',
   'PYMODULE'),
  ('setuptools._distutils._collections',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_distutils\\_collections.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_vendor\\zipp.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.util',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.unicode',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\unicode.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.testing',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\testing.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.results',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\results.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.helpers',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\helpers.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.exceptions',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\exceptions.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.core',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\core.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.diagram',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\diagram\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.common',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\common.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.actions',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\actions.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.__about__',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_vendor\\packaging\\__about__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.ordered_set',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_vendor\\ordered_set.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools.extern',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\extern\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.simple',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\simple.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.readers',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.abc',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._legacy',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_legacy.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._itertools',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._compat',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._common',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._adapters',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_vendor\\__init__.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('json',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.decoder',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('distutils.command.build_ext',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools.logging',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.errors',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.depends',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools._imp',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools.py34compat',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\py34compat.py',
   'PYMODULE'),
  ('setuptools.dist',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.installer',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools._path',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.glob',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('typing_extensions',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('distutils.command.build',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.py36compat',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\command\\py36compat.py',
   'PYMODULE'),
  ('distutils.command.sdist',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.config',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.command',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('distutils.command.bdist',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('setuptools.extension',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.version',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools._deprecation_warning',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\_deprecation_warning.py',
   'PYMODULE'),
  ('distutils.util',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\distutils\\util.py',
   'PYMODULE'),
  ('distutils.errors',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\distutils\\errors.py',
   'PYMODULE'),
  ('distutils.core',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\distutils\\core.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('nturl2path',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\nturl2path.py',
   'PYMODULE'),
  ('ftplib',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\ftplib.py',
   'PYMODULE'),
  ('netrc',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\netrc.py',
   'PYMODULE'),
  ('http.cookiejar',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('stringprep',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\stringprep.py',
   'PYMODULE'),
  ('tracemalloc',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('_py_abc',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\_py_abc.py',
   'PYMODULE'),
  ('requests',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.status_codes',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.compat',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('http.cookies',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\http\\cookies.py',
   'PYMODULE'),
  ('requests.models',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('idna',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.intranges',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.core',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.uts46data',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('idna.idnadata',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.package_data',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('requests.hooks',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.cookies',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.auth',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('urllib3.util',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.packages.six',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\urllib3\\packages\\six.py',
   'PYMODULE'),
  ('urllib3.packages',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\urllib3\\packages\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib._appengine_environ',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\urllib3\\contrib\\_appengine_environ.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.fields',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('requests.api',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.sessions',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.adapters',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('ipaddress',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\ipaddress.py',
   'PYMODULE'),
  ('urllib3.util.queue',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\urllib3\\util\\queue.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.request',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\urllib3\\request.py',
   'PYMODULE'),
  ('urllib3.connection',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3._version',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3._collections',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3.response',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('requests.__version__',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests.utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('requests.certs',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('certifi',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('requests.packages',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.packages.backports.makefile',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\urllib3\\packages\\backports\\makefile.py',
   'PYMODULE'),
  ('urllib3.packages.backports',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\urllib3\\packages\\backports\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.assets',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\charset_normalizer\\assets\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.md',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\charset_normalizer\\md.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('requests.exceptions',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('urllib3',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('kbnlp.scripts.information_extraction.utils',
   'G:\\kubao-aimp\\aimp\\tools\\kbnlp\\scripts\\information_extraction\\utils.py',
   'PYMODULE'),
  ('kbnlp.scripts.information_extraction',
   'G:\\kubao-aimp\\aimp\\tools\\kbnlp\\scripts\\information_extraction\\__init__.py',
   'PYMODULE'),
  ('kbnlp.scripts',
   'G:\\kubao-aimp\\aimp\\tools\\kbnlp\\scripts\\__init__.py',
   'PYMODULE'),
  ('kbnlp', 'G:\\kubao-aimp\\aimp\\tools\\kbnlp\\__init__.py', 'PYMODULE'),
  ('kbnlp.scripts.information_extraction.log',
   'G:\\kubao-aimp\\aimp\\tools\\kbnlp\\scripts\\information_extraction\\log.py',
   'PYMODULE'),
  ('tqdm',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\tqdm\\__init__.py',
   'PYMODULE'),
  ('tqdm.notebook',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\tqdm\\notebook.py',
   'PYMODULE'),
  ('tqdm.utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\tqdm\\utils.py',
   'PYMODULE'),
  ('colorama',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('colorama.ansi',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.initialise',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('tqdm.version',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\tqdm\\version.py',
   'PYMODULE'),
  ('tqdm._dist_ver',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\tqdm\\_dist_ver.py',
   'PYMODULE'),
  ('tqdm.std',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\tqdm\\std.py',
   'PYMODULE'),
  ('tqdm.gui',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\tqdm\\gui.py',
   'PYMODULE'),
  ('tqdm.cli',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\tqdm\\cli.py',
   'PYMODULE'),
  ('tqdm._tqdm_pandas',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\tqdm\\_tqdm_pandas.py',
   'PYMODULE'),
  ('tqdm._monitor',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\tqdm\\_monitor.py',
   'PYMODULE'),
  ('kbnlp.scripts.information_extraction.doccano',
   'G:\\kubao-aimp\\aimp\\tools\\kbnlp\\scripts\\information_extraction\\doccano.py',
   'PYMODULE'),
  ('kbnlp.scripts.information_extraction.evaluate',
   'G:\\kubao-aimp\\aimp\\tools\\kbnlp\\scripts\\information_extraction\\evaluate.py',
   'PYMODULE'),
  ('kbnlp.task.information_extraction',
   'G:\\kubao-aimp\\aimp\\tools\\kbnlp\\task\\information_extraction.py',
   'PYMODULE'),
  ('kbnlp.task.utils',
   'G:\\kubao-aimp\\aimp\\tools\\kbnlp\\task\\utils.py',
   'PYMODULE'),
  ('kbnlp.task',
   'G:\\kubao-aimp\\aimp\\tools\\kbnlp\\task\\__init__.py',
   'PYMODULE'),
  ('kbnlp.pretrained.ernie.configuration_ernie',
   'G:\\kubao-aimp\\aimp\\tools\\kbnlp\\pretrained\\ernie\\configuration_ernie.py',
   'PYMODULE'),
  ('kbnlp.pretrained.ernie',
   'G:\\kubao-aimp\\aimp\\tools\\kbnlp\\pretrained\\ernie\\__init__.py',
   'PYMODULE'),
  ('kbnlp.pretrained',
   'G:\\kubao-aimp\\aimp\\tools\\kbnlp\\pretrained\\__init__.py',
   'PYMODULE'),
  ('kbnlp.task.models.information_extraction_model',
   'G:\\kubao-aimp\\aimp\\tools\\kbnlp\\task\\models\\information_extraction_model.py',
   'PYMODULE'),
  ('kbnlp.task.models',
   'G:\\kubao-aimp\\aimp\\tools\\kbnlp\\task\\models\\__init__.py',
   'PYMODULE'),
  ('kbnlp.task.models.information_extraction.information_extraction_model',
   'G:\\kubao-aimp\\aimp\\tools\\kbnlp\\task\\models\\information_extraction\\information_extraction_model.py',
   'PYMODULE'),
  ('kbnlp.pretrained.ernie_layout.modeling_ernie_layout',
   'G:\\kubao-aimp\\aimp\\tools\\kbnlp\\pretrained\\ernie_layout\\modeling_ernie_layout.py',
   'PYMODULE'),
  ('kbnlp.pretrained.ernie_layout',
   'G:\\kubao-aimp\\aimp\\tools\\kbnlp\\pretrained\\ernie_layout\\__init__.py',
   'PYMODULE'),
  ('kbnlp.pretrained.ernie_layout.configuration_ernie_layout',
   'G:\\kubao-aimp\\aimp\\tools\\kbnlp\\pretrained\\ernie_layout\\configuration_ernie_layout.py',
   'PYMODULE'),
  ('kbnlp.pretrained.ernie_layout.visual_model',
   'G:\\kubao-aimp\\aimp\\tools\\kbnlp\\pretrained\\ernie_layout\\visual_model.py',
   'PYMODULE'),
  ('transformers.modeling_outputs',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\modeling_outputs.py',
   'PYMODULE'),
  ('torch.nn.functional',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\functional.py',
   'PYMODULE'),
  ('torch.nn.modules.utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\modules\\utils.py',
   'PYMODULE'),
  ('torch.nn.modules',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\modules\\__init__.py',
   'PYMODULE'),
  ('torch.nn.modules.channelshuffle',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\modules\\channelshuffle.py',
   'PYMODULE'),
  ('torch.nn.modules.flatten',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\modules\\flatten.py',
   'PYMODULE'),
  ('torch.types',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\types.py',
   'PYMODULE'),
  ('torch.nn.modules.transformer',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\modules\\transformer.py',
   'PYMODULE'),
  ('torch.nn.init',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\init.py',
   'PYMODULE'),
  ('torch.nn.modules.adaptive',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\modules\\adaptive.py',
   'PYMODULE'),
  ('torch.nn.modules.fold',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\modules\\fold.py',
   'PYMODULE'),
  ('torch.nn.common_types',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\common_types.py',
   'PYMODULE'),
  ('torch.nn.modules.distance',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\modules\\distance.py',
   'PYMODULE'),
  ('torch.nn.modules.upsampling',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\modules\\upsampling.py',
   'PYMODULE'),
  ('torch.nn.modules.pixelshuffle',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\modules\\pixelshuffle.py',
   'PYMODULE'),
  ('torch.nn.modules.rnn',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\modules\\rnn.py',
   'PYMODULE'),
  ('torch.backends.cudnn.rnn',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\backends\\cudnn\\rnn.py',
   'PYMODULE'),
  ('torch.backends.cudnn',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\backends\\cudnn\\__init__.py',
   'PYMODULE'),
  ('torch.backends',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\backends\\__init__.py',
   'PYMODULE'),
  ('torch.cuda',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\cuda\\__init__.py',
   'PYMODULE'),
  ('torch.cuda.nccl',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\cuda\\nccl.py',
   'PYMODULE'),
  ('torch.cuda.amp',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\cuda\\amp\\__init__.py',
   'PYMODULE'),
  ('torch.cuda.amp.grad_scaler',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\cuda\\amp\\grad_scaler.py',
   'PYMODULE'),
  ('torch.cuda.amp.common',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\cuda\\amp\\common.py',
   'PYMODULE'),
  ('torch.cuda.amp.autocast_mode',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\cuda\\amp\\autocast_mode.py',
   'PYMODULE'),
  ('torch._six',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\_six.py',
   'PYMODULE'),
  ('torch.cuda.nvtx',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\cuda\\nvtx.py',
   'PYMODULE'),
  ('torch.cuda.profiler',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\cuda\\profiler.py',
   'PYMODULE'),
  ('torch.cuda.sparse',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\cuda\\sparse.py',
   'PYMODULE'),
  ('torch.storage',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\storage.py',
   'PYMODULE'),
  ('torch.multiprocessing',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('torch.multiprocessing.spawn',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('torch.multiprocessing.reductions',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\multiprocessing\\reductions.py',
   'PYMODULE'),
  ('torch._namedtensor_internals',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\_namedtensor_internals.py',
   'PYMODULE'),
  ('torch.utils.hooks',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\utils\\hooks.py',
   'PYMODULE'),
  ('torch.utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\utils\\__init__.py',
   'PYMODULE'),
  ('torch.utils.tensorboard',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\utils\\tensorboard\\__init__.py',
   'PYMODULE'),
  ('torch.utils.tensorboard.writer',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\utils\\tensorboard\\writer.py',
   'PYMODULE'),
  ('torch.utils.tensorboard._caffe2_graph',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\utils\\tensorboard\\_caffe2_graph.py',
   'PYMODULE'),
  ('caffe2.python.core',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\caffe2\\python\\core.py',
   'PYMODULE'),
  ('caffe2.python.net_builder',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\caffe2\\python\\net_builder.py',
   'PYMODULE'),
  ('caffe2.python.control_ops_util',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\caffe2\\python\\control_ops_util.py',
   'PYMODULE'),
  ('caffe2.python.task',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\caffe2\\python\\task.py',
   'PYMODULE'),
  ('caffe2.python.context',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\caffe2\\python\\context.py',
   'PYMODULE'),
  ('caffe2.python.schema',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\caffe2\\python\\schema.py',
   'PYMODULE'),
  ('caffe2.python._import_c_extension',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\caffe2\\python\\_import_c_extension.py',
   'PYMODULE'),
  ('caffe2.python.extension_loader',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\caffe2\\python\\extension_loader.py',
   'PYMODULE'),
  ('caffe2.python.control_ops_grad',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\caffe2\\python\\control_ops_grad.py',
   'PYMODULE'),
  ('caffe2.python.lazy',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\caffe2\\python\\lazy.py',
   'PYMODULE'),
  ('caffe2.python.utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\caffe2\\python\\utils.py',
   'PYMODULE'),
  ('caffe2.python.scope',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\caffe2\\python\\scope.py',
   'PYMODULE'),
  ('six',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\six.py',
   'PYMODULE'),
  ('future.utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\future\\utils\\__init__.py',
   'PYMODULE'),
  ('future.types.newdict',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\future\\types\\newdict.py',
   'PYMODULE'),
  ('future.types.newobject',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\future\\types\\newobject.py',
   'PYMODULE'),
  ('future.types.newint',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\future\\types\\newint.py',
   'PYMODULE'),
  ('future.types.newstr',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\future\\types\\newstr.py',
   'PYMODULE'),
  ('future.types.newbytes',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\future\\types\\newbytes.py',
   'PYMODULE'),
  ('future.utils.surrogateescape',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\future\\utils\\surrogateescape.py',
   'PYMODULE'),
  ('future.types',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\future\\types\\__init__.py',
   'PYMODULE'),
  ('future.types.newrange',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\future\\types\\newrange.py',
   'PYMODULE'),
  ('future.backports.misc',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\future\\backports\\misc.py',
   'PYMODULE'),
  ('future.backports',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\future\\backports\\__init__.py',
   'PYMODULE'),
  ('future.standard_library',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\future\\standard_library\\__init__.py',
   'PYMODULE'),
  ('future.moves.dbm.ndbm',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\future\\moves\\dbm\\ndbm.py',
   'PYMODULE'),
  ('dbm.ndbm',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\dbm\\ndbm.py',
   'PYMODULE'),
  ('future.moves.dbm.gnu',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\future\\moves\\dbm\\gnu.py',
   'PYMODULE'),
  ('dbm.gnu',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\dbm\\gnu.py',
   'PYMODULE'),
  ('future.moves.dbm.dumb',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\future\\moves\\dbm\\dumb.py',
   'PYMODULE'),
  ('dbm.dumb',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\dbm\\dumb.py',
   'PYMODULE'),
  ('future.moves.dbm',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\future\\moves\\dbm\\__init__.py',
   'PYMODULE'),
  ('future.moves',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\future\\moves\\__init__.py',
   'PYMODULE'),
  ('dbm',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\dbm\\__init__.py',
   'PYMODULE'),
  ('future.moves.test.support',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\future\\moves\\test\\support.py',
   'PYMODULE'),
  ('test.support',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\test\\support\\__init__.py',
   'PYMODULE'),
  ('doctest',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\doctest.py',
   'PYMODULE'),
  ('tkinter',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\tkinter\\__init__.py',
   'PYMODULE'),
  ('tkinter.constants',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('ctypes.util',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes._aix',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('test.support.testresult',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\test\\support\\testresult.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('future.moves.test',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\future\\moves\\test\\__init__.py',
   'PYMODULE'),
  ('test',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\test\\__init__.py',
   'PYMODULE'),
  ('future.backports.urllib.robotparser',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\future\\backports\\urllib\\robotparser.py',
   'PYMODULE'),
  ('future.builtins',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\future\\builtins\\__init__.py',
   'PYMODULE'),
  ('future.builtins.misc',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\future\\builtins\\misc.py',
   'PYMODULE'),
  ('future.builtins.new_min_max',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\future\\builtins\\new_min_max.py',
   'PYMODULE'),
  ('future.builtins.newsuper',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\future\\builtins\\newsuper.py',
   'PYMODULE'),
  ('future.builtins.newround',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\future\\builtins\\newround.py',
   'PYMODULE'),
  ('future.builtins.newnext',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\future\\builtins\\newnext.py',
   'PYMODULE'),
  ('future.builtins.iterators',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\future\\builtins\\iterators.py',
   'PYMODULE'),
  ('future.backports.urllib.error',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\future\\backports\\urllib\\error.py',
   'PYMODULE'),
  ('future.backports.urllib.parse',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\future\\backports\\urllib\\parse.py',
   'PYMODULE'),
  ('future.backports.urllib.response',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\future\\backports\\urllib\\response.py',
   'PYMODULE'),
  ('future.backports.urllib.request',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\future\\backports\\urllib\\request.py',
   'PYMODULE'),
  ('future.backports.email.utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\future\\backports\\email\\utils.py',
   'PYMODULE'),
  ('future.backports.email.charset',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\future\\backports\\email\\charset.py',
   'PYMODULE'),
  ('future.backports.email.errors',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\future\\backports\\email\\errors.py',
   'PYMODULE'),
  ('future.backports.email.encoders',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\future\\backports\\email\\encoders.py',
   'PYMODULE'),
  ('future.backports.email._parseaddr',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\future\\backports\\email\\_parseaddr.py',
   'PYMODULE'),
  ('future.backports.http.cookiejar',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\future\\backports\\http\\cookiejar.py',
   'PYMODULE'),
  ('future.backports.http.client',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\future\\backports\\http\\client.py',
   'PYMODULE'),
  ('future.backports.email.message',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\future\\backports\\email\\message.py',
   'PYMODULE'),
  ('future.backports.email.iterators',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\future\\backports\\email\\iterators.py',
   'PYMODULE'),
  ('future.backports.email.generator',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\future\\backports\\email\\generator.py',
   'PYMODULE'),
  ('future.backports.email.header',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\future\\backports\\email\\header.py',
   'PYMODULE'),
  ('future.backports.email.quoprimime',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\future\\backports\\email\\quoprimime.py',
   'PYMODULE'),
  ('future.backports.email.base64mime',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\future\\backports\\email\\base64mime.py',
   'PYMODULE'),
  ('future.backports.email._encoded_words',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\future\\backports\\email\\_encoded_words.py',
   'PYMODULE'),
  ('future.backports.email._policybase',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\future\\backports\\email\\_policybase.py',
   'PYMODULE'),
  ('future.backports.email.parser',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\future\\backports\\email\\parser.py',
   'PYMODULE'),
  ('future.backports.email.feedparser',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\future\\backports\\email\\feedparser.py',
   'PYMODULE'),
  ('future.backports.http',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\future\\backports\\http\\__init__.py',
   'PYMODULE'),
  ('future.backports.urllib',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\future\\backports\\urllib\\__init__.py',
   'PYMODULE'),
  ('future.backports.email',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\future\\backports\\email\\__init__.py',
   'PYMODULE'),
  ('future.backports.datetime',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\future\\backports\\datetime.py',
   'PYMODULE'),
  ('future.types.newlist',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\future\\types\\newlist.py',
   'PYMODULE'),
  ('future',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\future\\__init__.py',
   'PYMODULE'),
  ('past.builtins',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\past\\builtins\\__init__.py',
   'PYMODULE'),
  ('past.utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\past\\utils\\__init__.py',
   'PYMODULE'),
  ('past.builtins.misc',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\past\\builtins\\misc.py',
   'PYMODULE'),
  ('past.types.oldstr',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\past\\types\\oldstr.py',
   'PYMODULE'),
  ('past.types.olddict',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\past\\types\\olddict.py',
   'PYMODULE'),
  ('past.types.basestring',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\past\\types\\basestring.py',
   'PYMODULE'),
  ('past.types',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\past\\types\\__init__.py',
   'PYMODULE'),
  ('past.builtins.noniterators',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\past\\builtins\\noniterators.py',
   'PYMODULE'),
  ('past',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\past\\__init__.py',
   'PYMODULE'),
  ('caffe2.proto.caffe2_pb2',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\caffe2\\proto\\caffe2_pb2.py',
   'PYMODULE'),
  ('caffe2.proto',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\caffe2\\proto\\__init__.py',
   'PYMODULE'),
  ('caffe2.proto.torch_pb2',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\caffe2\\proto\\torch_pb2.py',
   'PYMODULE'),
  ('caffe2.proto.metanet_pb2',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\caffe2\\proto\\metanet_pb2.py',
   'PYMODULE'),
  ('caffe2',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\caffe2\\__init__.py',
   'PYMODULE'),
  ('caffe2.python.workspace',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\caffe2\\python\\workspace.py',
   'PYMODULE'),
  ('caffe2.python.mint.app',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\caffe2\\python\\mint\\app.py',
   'PYMODULE'),
  ('caffe2.python.mint',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\caffe2\\python\\mint\\__init__.py',
   'PYMODULE'),
  ('caffe2.python',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\caffe2\\python\\__init__.py',
   'PYMODULE'),
  ('torch.utils.tensorboard.summary',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\utils\\tensorboard\\summary.py',
   'PYMODULE'),
  ('wave',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\wave.py',
   'PYMODULE'),
  ('chunk',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\chunk.py',
   'PYMODULE'),
  ('PIL.Image',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.features',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FitsStubImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\FitsStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('colorsys',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\colorsys.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.PyAccess',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\PyAccess.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL._util',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._binary',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ImageFont',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\ImageFont.py',
   'PYMODULE'),
  ('PIL.ImageDraw',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\ImageDraw.py',
   'PYMODULE'),
  ('PIL.ImageDraw2',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\ImageDraw2.py',
   'PYMODULE'),
  ('PIL.ImagePath',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\ImagePath.py',
   'PYMODULE'),
  ('PIL',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL._version',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('torch.utils.tensorboard._utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\utils\\tensorboard\\_utils.py',
   'PYMODULE'),
  ('torch.utils.tensorboard._pytorch_graph',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\utils\\tensorboard\\_pytorch_graph.py',
   'PYMODULE'),
  ('torch.utils.tensorboard._proto_graph',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\utils\\tensorboard\\_proto_graph.py',
   'PYMODULE'),
  ('torch.utils.tensorboard._onnx_graph',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\utils\\tensorboard\\_onnx_graph.py',
   'PYMODULE'),
  ('torch.utils.tensorboard._embedding',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\utils\\tensorboard\\_embedding.py',
   'PYMODULE'),
  ('torch.utils.tensorboard._convert_np',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\utils\\tensorboard\\_convert_np.py',
   'PYMODULE'),
  ('torch.utils._crash_handler',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\utils\\_crash_handler.py',
   'PYMODULE'),
  ('torch.utils.throughput_benchmark',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\utils\\throughput_benchmark.py',
   'PYMODULE'),
  ('torch._utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\_utils.py',
   'PYMODULE'),
  ('torch.cuda.random',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\cuda\\random.py',
   'PYMODULE'),
  ('torch.cuda.memory',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\cuda\\memory.py',
   'PYMODULE'),
  ('torch.cuda.streams',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\cuda\\streams.py',
   'PYMODULE'),
  ('torch.cuda._utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\cuda\\_utils.py',
   'PYMODULE'),
  ('torch.nn.utils.rnn',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\utils\\rnn.py',
   'PYMODULE'),
  ('torch.nn.utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\utils\\__init__.py',
   'PYMODULE'),
  ('torch.nn.utils.parametrizations',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\utils\\parametrizations.py',
   'PYMODULE'),
  ('torch.nn.utils.parametrize',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\utils\\parametrize.py',
   'PYMODULE'),
  ('torch.nn.utils.memory_format',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\utils\\memory_format.py',
   'PYMODULE'),
  ('torch.nn.utils.fusion',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\utils\\fusion.py',
   'PYMODULE'),
  ('torch.nn.utils.spectral_norm',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\utils\\spectral_norm.py',
   'PYMODULE'),
  ('torch.nn.utils.convert_parameters',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\utils\\convert_parameters.py',
   'PYMODULE'),
  ('torch.nn.utils.weight_norm',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\utils\\weight_norm.py',
   'PYMODULE'),
  ('torch.nn.utils.clip_grad',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\utils\\clip_grad.py',
   'PYMODULE'),
  ('torch.nn.parameter',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\parameter.py',
   'PYMODULE'),
  ('torch.nn.modules.sparse',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\modules\\sparse.py',
   'PYMODULE'),
  ('torch.nn.modules.padding',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\modules\\padding.py',
   'PYMODULE'),
  ('torch.nn.modules.dropout',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\modules\\dropout.py',
   'PYMODULE'),
  ('torch.nn.modules.normalization',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\modules\\normalization.py',
   'PYMODULE'),
  ('torch.nn.modules._functions',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\modules\\_functions.py',
   'PYMODULE'),
  ('torch.autograd.function',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\autograd\\function.py',
   'PYMODULE'),
  ('torch.autograd',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\autograd\\__init__.py',
   'PYMODULE'),
  ('torch.autograd.profiler',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\autograd\\profiler.py',
   'PYMODULE'),
  ('sqlite3',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\sqlite3\\__init__.py',
   'PYMODULE'),
  ('sqlite3.dump',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\sqlite3\\dump.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('torch.futures',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\futures\\__init__.py',
   'PYMODULE'),
  ('torch.autograd.forward_ad',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\autograd\\forward_ad.py',
   'PYMODULE'),
  ('torch.autograd.functional',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\autograd\\functional.py',
   'PYMODULE'),
  ('torch._vmap_internals',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\_vmap_internals.py',
   'PYMODULE'),
  ('torch.utils._pytree',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\utils\\_pytree.py',
   'PYMODULE'),
  ('torch.autograd.anomaly_mode',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\autograd\\anomaly_mode.py',
   'PYMODULE'),
  ('torch.autograd.grad_mode',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\autograd\\grad_mode.py',
   'PYMODULE'),
  ('torch.autograd.gradcheck',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\autograd\\gradcheck.py',
   'PYMODULE'),
  ('torch.testing',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\testing\\__init__.py',
   'PYMODULE'),
  ('torch.testing._check_kernel_launches',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\testing\\_check_kernel_launches.py',
   'PYMODULE'),
  ('torch.testing._asserts',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\testing\\_asserts.py',
   'PYMODULE'),
  ('torch.testing._core',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\testing\\_core.py',
   'PYMODULE'),
  ('torch.autograd.variable',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\autograd\\variable.py',
   'PYMODULE'),
  ('torch.distributed',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\distributed\\__init__.py',
   'PYMODULE'),
  ('torch.distributed.distributed_c10d',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\distributed\\distributed_c10d.py',
   'PYMODULE'),
  ('torch.distributed.rendezvous',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\distributed\\rendezvous.py',
   'PYMODULE'),
  ('torch.distributed.constants',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\distributed\\constants.py',
   'PYMODULE'),
  ('torch.nn.modules.instancenorm',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\modules\\instancenorm.py',
   'PYMODULE'),
  ('torch.nn.modules.batchnorm',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\modules\\batchnorm.py',
   'PYMODULE'),
  ('torch.nn.modules.lazy',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\modules\\lazy.py',
   'PYMODULE'),
  ('torch.nn.modules.pooling',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\modules\\pooling.py',
   'PYMODULE'),
  ('torch.nn.modules.container',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\modules\\container.py',
   'PYMODULE'),
  ('torch.nn.modules.loss',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\modules\\loss.py',
   'PYMODULE'),
  ('torch.nn.modules.activation',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\modules\\activation.py',
   'PYMODULE'),
  ('torch.nn.modules.conv',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\modules\\conv.py',
   'PYMODULE'),
  ('torch.nn.modules.linear',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\modules\\linear.py',
   'PYMODULE'),
  ('torch.nn.modules.module',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\modules\\module.py',
   'PYMODULE'),
  ('torch.nn.grad',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\grad.py',
   'PYMODULE'),
  ('torch.nn._reduction',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\_reduction.py',
   'PYMODULE'),
  ('torch.overrides',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\overrides.py',
   'PYMODULE'),
  ('torch._jit_internal',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\_jit_internal.py',
   'PYMODULE'),
  ('torch.package._mangling',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\package\\_mangling.py',
   'PYMODULE'),
  ('torch.package',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\package\\__init__.py',
   'PYMODULE'),
  ('torch.package.package_importer',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\package\\package_importer.py',
   'PYMODULE'),
  ('torch.package._package_unpickler',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\package\\_package_unpickler.py',
   'PYMODULE'),
  ('torch.package._mock_zipreader',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\package\\_mock_zipreader.py',
   'PYMODULE'),
  ('torch.package._importlib',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\package\\_importlib.py',
   'PYMODULE'),
  ('torch.serialization',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\serialization.py',
   'PYMODULE'),
  ('torch.package.package_exporter',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\package\\package_exporter.py',
   'PYMODULE'),
  ('torch.package.find_file_dependencies',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\package\\find_file_dependencies.py',
   'PYMODULE'),
  ('torch.package._stdlib',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\package\\_stdlib.py',
   'PYMODULE'),
  ('torch.package._package_pickler',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\package\\_package_pickler.py',
   'PYMODULE'),
  ('torch.package._digraph',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\package\\_digraph.py',
   'PYMODULE'),
  ('pickletools',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\pickletools.py',
   'PYMODULE'),
  ('torch.package.importer',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\package\\importer.py',
   'PYMODULE'),
  ('torch.package.glob_group',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\package\\glob_group.py',
   'PYMODULE'),
  ('torch.package.file_structure_representation',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\package\\file_structure_representation.py',
   'PYMODULE'),
  ('torch.package.analyze.is_from_package',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\package\\analyze\\is_from_package.py',
   'PYMODULE'),
  ('torch.package.analyze',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\package\\analyze\\__init__.py',
   'PYMODULE'),
  ('torch.package.analyze.trace_dependencies',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\package\\analyze\\trace_dependencies.py',
   'PYMODULE'),
  ('torch._utils_internal',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\_utils_internal.py',
   'PYMODULE'),
  ('torch.distributed.rpc',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\distributed\\rpc\\__init__.py',
   'PYMODULE'),
  ('torch.distributed.rpc.server_process_global_profiler',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\distributed\\rpc\\server_process_global_profiler.py',
   'PYMODULE'),
  ('torch.distributed.rpc.options',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\distributed\\rpc\\options.py',
   'PYMODULE'),
  ('torch.distributed.rpc.constants',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\distributed\\rpc\\constants.py',
   'PYMODULE'),
  ('torch.distributed.autograd',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\distributed\\autograd\\__init__.py',
   'PYMODULE'),
  ('torch.distributed.rpc.functions',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\distributed\\rpc\\functions.py',
   'PYMODULE'),
  ('torch.distributed.rpc.backend_registry',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\distributed\\rpc\\backend_registry.py',
   'PYMODULE'),
  ('torch.distributed.rpc.api',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\distributed\\rpc\\api.py',
   'PYMODULE'),
  ('torch.distributed.rpc.internal',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\distributed\\rpc\\internal.py',
   'PYMODULE'),
  ('torch._torch_docs',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\_torch_docs.py',
   'PYMODULE'),
  ('torch._VF',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\_VF.py',
   'PYMODULE'),
  ('kbnlp.pretrained.ernie.modeling_ernie',
   'G:\\kubao-aimp\\aimp\\tools\\kbnlp\\pretrained\\ernie\\modeling_ernie.py',
   'PYMODULE'),
  ('transformers.models.bert.modeling_bert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\bert\\modeling_bert.py',
   'PYMODULE'),
  ('transformers.models.bert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\bert\\__init__.py',
   'PYMODULE'),
  ('transformers.models.bert.modeling_flax_bert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\bert\\modeling_flax_bert.py',
   'PYMODULE'),
  ('transformers.modeling_flax_utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\modeling_flax_utils.py',
   'PYMODULE'),
  ('transformers.models.auto',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\auto\\__init__.py',
   'PYMODULE'),
  ('transformers.models.auto.modeling_flax_auto',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\auto\\modeling_flax_auto.py',
   'PYMODULE'),
  ('transformers.models.auto.modeling_tf_auto',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\auto\\modeling_tf_auto.py',
   'PYMODULE'),
  ('transformers.models.auto.modeling_auto',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\auto\\modeling_auto.py',
   'PYMODULE'),
  ('transformers.models.auto.tokenization_auto',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\auto\\tokenization_auto.py',
   'PYMODULE'),
  ('transformers.models.encoder_decoder',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\encoder_decoder\\__init__.py',
   'PYMODULE'),
  ('transformers.models.encoder_decoder.modeling_flax_encoder_decoder',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\encoder_decoder\\modeling_flax_encoder_decoder.py',
   'PYMODULE'),
  ('transformers.models.encoder_decoder.modeling_tf_encoder_decoder',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\encoder_decoder\\modeling_tf_encoder_decoder.py',
   'PYMODULE'),
  ('transformers.tf_utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\tf_utils.py',
   'PYMODULE'),
  ('transformers.modeling_tf_utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\modeling_tf_utils.py',
   'PYMODULE'),
  ('transformers.modeling_tf_pytorch_utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\modeling_tf_pytorch_utils.py',
   'PYMODULE'),
  ('transformers.modelcard',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\modelcard.py',
   'PYMODULE'),
  ('tokenizers',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\tokenizers\\__init__.py',
   'PYMODULE'),
  ('tokenizers.implementations',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\tokenizers\\implementations\\__init__.py',
   'PYMODULE'),
  ('tokenizers.implementations.bert_wordpiece',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\tokenizers\\implementations\\bert_wordpiece.py',
   'PYMODULE'),
  ('tokenizers.implementations.sentencepiece_unigram',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\tokenizers\\implementations\\sentencepiece_unigram.py',
   'PYMODULE'),
  ('tokenizers.implementations.sentencepiece_bpe',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\tokenizers\\implementations\\sentencepiece_bpe.py',
   'PYMODULE'),
  ('tokenizers.implementations.char_level_bpe',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\tokenizers\\implementations\\char_level_bpe.py',
   'PYMODULE'),
  ('tokenizers.implementations.byte_level_bpe',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\tokenizers\\implementations\\byte_level_bpe.py',
   'PYMODULE'),
  ('tokenizers.implementations.base_tokenizer',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\tokenizers\\implementations\\base_tokenizer.py',
   'PYMODULE'),
  ('tokenizers.trainers',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\tokenizers\\trainers\\__init__.py',
   'PYMODULE'),
  ('tokenizers.decoders',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\tokenizers\\decoders\\__init__.py',
   'PYMODULE'),
  ('tokenizers.processors',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\tokenizers\\processors\\__init__.py',
   'PYMODULE'),
  ('tokenizers.pre_tokenizers',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\tokenizers\\pre_tokenizers\\__init__.py',
   'PYMODULE'),
  ('tokenizers.normalizers',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\tokenizers\\normalizers\\__init__.py',
   'PYMODULE'),
  ('tokenizers.models',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\tokenizers\\models\\__init__.py',
   'PYMODULE'),
  ('transformers.training_args',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\training_args.py',
   'PYMODULE'),
  ('transformers.deepspeed',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\deepspeed.py',
   'PYMODULE'),
  ('accelerate.utils.deepspeed',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\accelerate\\utils\\deepspeed.py',
   'PYMODULE'),
  ('accelerate.utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\accelerate\\utils\\__init__.py',
   'PYMODULE'),
  ('accelerate.utils.random',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\accelerate\\utils\\random.py',
   'PYMODULE'),
  ('accelerate.state',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\accelerate\\state.py',
   'PYMODULE'),
  ('accelerate.utils.other',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\accelerate\\utils\\other.py',
   'PYMODULE'),
  ('accelerate.commands.config.config_args',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\accelerate\\commands\\config\\config_args.py',
   'PYMODULE'),
  ('accelerate.commands.config',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\accelerate\\commands\\config\\__init__.py',
   'PYMODULE'),
  ('accelerate.commands',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\accelerate\\commands\\__init__.py',
   'PYMODULE'),
  ('accelerate.commands.config.sagemaker',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\accelerate\\commands\\config\\sagemaker.py',
   'PYMODULE'),
  ('accelerate.commands.config.config_utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\accelerate\\commands\\config\\config_utils.py',
   'PYMODULE'),
  ('accelerate.commands.config.cluster',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\accelerate\\commands\\config\\cluster.py',
   'PYMODULE'),
  ('accelerate.utils.memory',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\accelerate\\utils\\memory.py',
   'PYMODULE'),
  ('accelerate.utils.launch',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\accelerate\\utils\\launch.py',
   'PYMODULE'),
  ('accelerate.utils.operations',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\accelerate\\utils\\operations.py',
   'PYMODULE'),
  ('accelerate',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\accelerate\\__init__.py',
   'PYMODULE'),
  ('accelerate.launchers',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\accelerate\\launchers.py',
   'PYMODULE'),
  ('accelerate.big_modeling',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\accelerate\\big_modeling.py',
   'PYMODULE'),
  ('accelerate.hooks',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\accelerate\\hooks.py',
   'PYMODULE'),
  ('accelerate.accelerator',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\accelerate\\accelerator.py',
   'PYMODULE'),
  ('accelerate.tracking',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\accelerate\\tracking.py',
   'PYMODULE'),
  ('accelerate.scheduler',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\accelerate\\scheduler.py',
   'PYMODULE'),
  ('accelerate.logging',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\accelerate\\logging.py',
   'PYMODULE'),
  ('accelerate.data_loader',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\accelerate\\data_loader.py',
   'PYMODULE'),
  ('torch.utils.data.datapipes.iter.combinatorics',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\utils\\data\\datapipes\\iter\\combinatorics.py',
   'PYMODULE'),
  ('torch.utils.data.datapipes.iter',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\utils\\data\\datapipes\\iter\\__init__.py',
   'PYMODULE'),
  ('torch.utils.data.datapipes.iter.selecting',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\utils\\data\\datapipes\\iter\\selecting.py',
   'PYMODULE'),
  ('torch.utils.data.datapipes.iter.grouping',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\utils\\data\\datapipes\\iter\\grouping.py',
   'PYMODULE'),
  ('torch.utils.data.datapipes.iter.combining',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\utils\\data\\datapipes\\iter\\combining.py',
   'PYMODULE'),
  ('torch.utils.data.datapipes.iter.callable',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\utils\\data\\datapipes\\iter\\callable.py',
   'PYMODULE'),
  ('torch.utils.data._utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\utils\\data\\_utils\\__init__.py',
   'PYMODULE'),
  ('torch.utils.data._utils.fetch',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\utils\\data\\_utils\\fetch.py',
   'PYMODULE'),
  ('torch.utils.data._utils.collate',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\utils\\data\\_utils\\collate.py',
   'PYMODULE'),
  ('torch.utils.data._utils.pin_memory',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\utils\\data\\_utils\\pin_memory.py',
   'PYMODULE'),
  ('torch.utils.data._utils.worker',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\utils\\data\\_utils\\worker.py',
   'PYMODULE'),
  ('torch.utils.data._utils.signal_handling',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\utils\\data\\_utils\\signal_handling.py',
   'PYMODULE'),
  ('torch.utils.data.datapipes.iter.routeddecoder',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\utils\\data\\datapipes\\iter\\routeddecoder.py',
   'PYMODULE'),
  ('torch.utils.data.datapipes.utils.decoder',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\utils\\data\\datapipes\\utils\\decoder.py',
   'PYMODULE'),
  ('torch.utils.data.datapipes.utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\utils\\data\\datapipes\\utils\\__init__.py',
   'PYMODULE'),
  ('torch.utils.data.datapipes.iter.readfilesfromzip',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\utils\\data\\datapipes\\iter\\readfilesfromzip.py',
   'PYMODULE'),
  ('torch.utils.data.datapipes.utils.common',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\utils\\data\\datapipes\\utils\\common.py',
   'PYMODULE'),
  ('torch.utils.data.datapipes.iter.readfilesfromtar',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\utils\\data\\datapipes\\iter\\readfilesfromtar.py',
   'PYMODULE'),
  ('torch.utils.data.datapipes.iter.loadfilesfromdisk',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\utils\\data\\datapipes\\iter\\loadfilesfromdisk.py',
   'PYMODULE'),
  ('torch.utils.data.datapipes.iter.listdirfiles',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\utils\\data\\datapipes\\iter\\listdirfiles.py',
   'PYMODULE'),
  ('torch.utils.data.datapipes',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\utils\\data\\datapipes\\__init__.py',
   'PYMODULE'),
  ('accelerate.checkpointing',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\accelerate\\checkpointing.py',
   'PYMODULE'),
  ('accelerate.utils.offload',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\accelerate\\utils\\offload.py',
   'PYMODULE'),
  ('accelerate.utils.modeling',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\accelerate\\utils\\modeling.py',
   'PYMODULE'),
  ('psutil',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._psaix',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\psutil\\_psaix.py',
   'PYMODULE'),
  ('psutil._pssunos',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\psutil\\_pssunos.py',
   'PYMODULE'),
  ('psutil._psbsd',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\psutil\\_psbsd.py',
   'PYMODULE'),
  ('psutil._psosx',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\psutil\\_psosx.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('psutil._pslinux',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\psutil\\_pslinux.py',
   'PYMODULE'),
  ('psutil._psposix',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\psutil\\_psposix.py',
   'PYMODULE'),
  ('psutil._compat',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\psutil\\_compat.py',
   'PYMODULE'),
  ('psutil._common',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('curses',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\curses\\__init__.py',
   'PYMODULE'),
  ('curses.has_key',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\curses\\has_key.py',
   'PYMODULE'),
  ('accelerate.utils.dataclasses',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\accelerate\\utils\\dataclasses.py',
   'PYMODULE'),
  ('accelerate.utils.constants',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\accelerate\\utils\\constants.py',
   'PYMODULE'),
  ('accelerate.utils.imports',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\accelerate\\utils\\imports.py',
   'PYMODULE'),
  ('accelerate.optimizer',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\accelerate\\optimizer.py',
   'PYMODULE'),
  ('transformers.dependency_versions_check',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\dependency_versions_check.py',
   'PYMODULE'),
  ('transformers.utils.versions',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\utils\\versions.py',
   'PYMODULE'),
  ('transformers.dependency_versions_table',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\dependency_versions_table.py',
   'PYMODULE'),
  ('transformers.integrations',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\integrations.py',
   'PYMODULE'),
  ('transformers.trainer',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\trainer.py',
   'PYMODULE'),
  ('transformers.utils.generic',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\utils\\generic.py',
   'PYMODULE'),
  ('transformers.utils.import_utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\utils\\import_utils.py',
   'PYMODULE'),
  ('torch.fx',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\fx\\__init__.py',
   'PYMODULE'),
  ('torch.fx.subgraph_rewriter',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\fx\\subgraph_rewriter.py',
   'PYMODULE'),
  ('torch.fx.interpreter',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\fx\\interpreter.py',
   'PYMODULE'),
  ('torch.fx.proxy',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\fx\\proxy.py',
   'PYMODULE'),
  ('torch.fx.node',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\fx\\node.py',
   'PYMODULE'),
  ('torch.fx.operator_schemas',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\fx\\operator_schemas.py',
   'PYMODULE'),
  ('torch.fx.immutable_collections',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\fx\\immutable_collections.py',
   'PYMODULE'),
  ('torch.fx.graph',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\fx\\graph.py',
   'PYMODULE'),
  ('torch.fx.symbolic_trace',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\fx\\symbolic_trace.py',
   'PYMODULE'),
  ('torch.fx.graph_module',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\fx\\graph_module.py',
   'PYMODULE'),
  ('torch.fx._pytree',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\fx\\_pytree.py',
   'PYMODULE'),
  ('transformers.trainer_pt_utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\trainer_pt_utils.py',
   'PYMODULE'),
  ('torch.optim.lr_scheduler',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\optim\\lr_scheduler.py',
   'PYMODULE'),
  ('torch.optim.optimizer',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\optim\\optimizer.py',
   'PYMODULE'),
  ('transformers.optimization',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\optimization.py',
   'PYMODULE'),
  ('transformers.data.data_collator',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\data\\data_collator.py',
   'PYMODULE'),
  ('transformers.data',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\data\\__init__.py',
   'PYMODULE'),
  ('transformers.data.processors',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\data\\processors\\__init__.py',
   'PYMODULE'),
  ('transformers.data.processors.xnli',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\data\\processors\\xnli.py',
   'PYMODULE'),
  ('transformers.data.processors.utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\data\\processors\\utils.py',
   'PYMODULE'),
  ('transformers.data.processors.squad',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\data\\processors\\squad.py',
   'PYMODULE'),
  ('transformers.data.processors.glue',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\data\\processors\\glue.py',
   'PYMODULE'),
  ('transformers.data.metrics',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\data\\metrics\\__init__.py',
   'PYMODULE'),
  ('torch.utils.data.distributed',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\utils\\data\\distributed.py',
   'PYMODULE'),
  ('tqdm.auto',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\tqdm\\auto.py',
   'PYMODULE'),
  ('tqdm.asyncio',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\tqdm\\asyncio.py',
   'PYMODULE'),
  ('tqdm.autonotebook',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\tqdm\\autonotebook.py',
   'PYMODULE'),
  ('transformers.utils.notebook',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\utils\\notebook.py',
   'PYMODULE'),
  ('transformers.trainer_callback',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\trainer_callback.py',
   'PYMODULE'),
  ('transformers.trainer_utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\trainer_utils.py',
   'PYMODULE'),
  ('accelerate.memory_utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\accelerate\\memory_utils.py',
   'PYMODULE'),
  ('transformers.debug_utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\debug_utils.py',
   'PYMODULE'),
  ('yaml',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\yaml\\__init__.py',
   'PYMODULE'),
  ('yaml.cyaml',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\yaml\\cyaml.py',
   'PYMODULE'),
  ('yaml.resolver',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\yaml\\resolver.py',
   'PYMODULE'),
  ('yaml.representer',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\yaml\\representer.py',
   'PYMODULE'),
  ('yaml.serializer',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\yaml\\serializer.py',
   'PYMODULE'),
  ('yaml.constructor',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\yaml\\constructor.py',
   'PYMODULE'),
  ('yaml.dumper',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\yaml\\dumper.py',
   'PYMODULE'),
  ('yaml.emitter',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\yaml\\emitter.py',
   'PYMODULE'),
  ('yaml.loader',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\yaml\\loader.py',
   'PYMODULE'),
  ('yaml.composer',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\yaml\\composer.py',
   'PYMODULE'),
  ('yaml.parser',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\yaml\\parser.py',
   'PYMODULE'),
  ('yaml.scanner',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\yaml\\scanner.py',
   'PYMODULE'),
  ('yaml.reader',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\yaml\\reader.py',
   'PYMODULE'),
  ('yaml.nodes',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\yaml\\nodes.py',
   'PYMODULE'),
  ('yaml.events',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\yaml\\events.py',
   'PYMODULE'),
  ('yaml.tokens',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\yaml\\tokens.py',
   'PYMODULE'),
  ('yaml.error',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\yaml\\error.py',
   'PYMODULE'),
  ('transformers.generation_tf_utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\generation_tf_utils.py',
   'PYMODULE'),
  ('transformers.generation_tf_logits_process',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\generation_tf_logits_process.py',
   'PYMODULE'),
  ('transformers.activations_tf',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\activations_tf.py',
   'PYMODULE'),
  ('huggingface_hub',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\huggingface_hub\\__init__.py',
   'PYMODULE'),
  ('huggingface_hub.utils.endpoint_helpers',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\huggingface_hub\\utils\\endpoint_helpers.py',
   'PYMODULE'),
  ('huggingface_hub.utils.logging',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\huggingface_hub\\utils\\logging.py',
   'PYMODULE'),
  ('huggingface_hub.utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\huggingface_hub\\utils\\__init__.py',
   'PYMODULE'),
  ('huggingface_hub.utils._validators',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\huggingface_hub\\utils\\_validators.py',
   'PYMODULE'),
  ('huggingface_hub.utils._subprocess',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\huggingface_hub\\utils\\_subprocess.py',
   'PYMODULE'),
  ('huggingface_hub.utils._runtime',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\huggingface_hub\\utils\\_runtime.py',
   'PYMODULE'),
  ('huggingface_hub.utils._paths',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\huggingface_hub\\utils\\_paths.py',
   'PYMODULE'),
  ('huggingface_hub.utils._http',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\huggingface_hub\\utils\\_http.py',
   'PYMODULE'),
  ('huggingface_hub.utils._typing',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\huggingface_hub\\utils\\_typing.py',
   'PYMODULE'),
  ('huggingface_hub.utils._hf_folder',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\huggingface_hub\\utils\\_hf_folder.py',
   'PYMODULE'),
  ('huggingface_hub.utils._headers',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\huggingface_hub\\utils\\_headers.py',
   'PYMODULE'),
  ('huggingface_hub.utils._git_credential',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\huggingface_hub\\utils\\_git_credential.py',
   'PYMODULE'),
  ('huggingface_hub.utils._deprecation',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\huggingface_hub\\utils\\_deprecation.py',
   'PYMODULE'),
  ('huggingface_hub.utils._fixes',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\huggingface_hub\\utils\\_fixes.py',
   'PYMODULE'),
  ('huggingface_hub.utils._errors',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\huggingface_hub\\utils\\_errors.py',
   'PYMODULE'),
  ('huggingface_hub.utils._datetime',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\huggingface_hub\\utils\\_datetime.py',
   'PYMODULE'),
  ('huggingface_hub.utils._chunk_utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\huggingface_hub\\utils\\_chunk_utils.py',
   'PYMODULE'),
  ('huggingface_hub.utils._cache_manager',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\huggingface_hub\\utils\\_cache_manager.py',
   'PYMODULE'),
  ('huggingface_hub.utils._cache_assets',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\huggingface_hub\\utils\\_cache_assets.py',
   'PYMODULE'),
  ('huggingface_hub.utils.tqdm',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\huggingface_hub\\utils\\tqdm.py',
   'PYMODULE'),
  ('huggingface_hub.repository',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\huggingface_hub\\repository.py',
   'PYMODULE'),
  ('huggingface_hub.lfs',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\huggingface_hub\\lfs.py',
   'PYMODULE'),
  ('huggingface_hub.utils.sha',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\huggingface_hub\\utils\\sha.py',
   'PYMODULE'),
  ('huggingface_hub.repocard_data',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\huggingface_hub\\repocard_data.py',
   'PYMODULE'),
  ('huggingface_hub.repocard',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\huggingface_hub\\repocard.py',
   'PYMODULE'),
  ('huggingface_hub.keras_mixin',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\huggingface_hub\\keras_mixin.py',
   'PYMODULE'),
  ('huggingface_hub._commit_api',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\huggingface_hub\\_commit_api.py',
   'PYMODULE'),
  ('huggingface_hub.inference_api',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\huggingface_hub\\inference_api.py',
   'PYMODULE'),
  ('huggingface_hub.hub_mixin',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\huggingface_hub\\hub_mixin.py',
   'PYMODULE'),
  ('huggingface_hub.hf_api',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\huggingface_hub\\hf_api.py',
   'PYMODULE'),
  ('huggingface_hub.utils._pagination',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\huggingface_hub\\utils\\_pagination.py',
   'PYMODULE'),
  ('huggingface_hub.file_download',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\huggingface_hub\\file_download.py',
   'PYMODULE'),
  ('filelock',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\filelock\\__init__.py',
   'PYMODULE'),
  ('filelock.version',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\filelock\\version.py',
   'PYMODULE'),
  ('filelock._windows',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\filelock\\_windows.py',
   'PYMODULE'),
  ('filelock._util',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\filelock\\_util.py',
   'PYMODULE'),
  ('filelock._unix',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\filelock\\_unix.py',
   'PYMODULE'),
  ('filelock._soft',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\filelock\\_soft.py',
   'PYMODULE'),
  ('filelock._error',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\filelock\\_error.py',
   'PYMODULE'),
  ('filelock._api',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\filelock\\_api.py',
   'PYMODULE'),
  ('uuid',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\uuid.py',
   'PYMODULE'),
  ('huggingface_hub.fastai_utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\huggingface_hub\\fastai_utils.py',
   'PYMODULE'),
  ('huggingface_hub.constants',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\huggingface_hub\\constants.py',
   'PYMODULE'),
  ('huggingface_hub.community',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\huggingface_hub\\community.py',
   'PYMODULE'),
  ('huggingface_hub._snapshot_download',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\huggingface_hub\\_snapshot_download.py',
   'PYMODULE'),
  ('huggingface_hub._login',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\huggingface_hub\\_login.py',
   'PYMODULE'),
  ('huggingface_hub.commands.delete_cache',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\huggingface_hub\\commands\\delete_cache.py',
   'PYMODULE'),
  ('huggingface_hub.commands',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\huggingface_hub\\commands\\__init__.py',
   'PYMODULE'),
  ('huggingface_hub.commands._cli_utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\huggingface_hub\\commands\\_cli_utils.py',
   'PYMODULE'),
  ('transformers.modeling_tf_outputs',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\modeling_tf_outputs.py',
   'PYMODULE'),
  ('transformers.models.encoder_decoder.modeling_encoder_decoder',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\encoder_decoder\\modeling_encoder_decoder.py',
   'PYMODULE'),
  ('transformers.models.encoder_decoder.configuration_encoder_decoder',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\encoder_decoder\\configuration_encoder_decoder.py',
   'PYMODULE'),
  ('transformers.tokenization_utils_fast',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\tokenization_utils_fast.py',
   'PYMODULE'),
  ('transformers.convert_slow_tokenizer',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\convert_slow_tokenizer.py',
   'PYMODULE'),
  ('transformers.utils.sentencepiece_model_pb2',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\utils\\sentencepiece_model_pb2.py',
   'PYMODULE'),
  ('transformers.models.roformer.tokenization_utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\roformer\\tokenization_utils.py',
   'PYMODULE'),
  ('transformers.models.roformer',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\roformer\\__init__.py',
   'PYMODULE'),
  ('transformers.models.roformer.modeling_flax_roformer',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\roformer\\modeling_flax_roformer.py',
   'PYMODULE'),
  ('transformers.models.roformer.modeling_tf_roformer',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\roformer\\modeling_tf_roformer.py',
   'PYMODULE'),
  ('transformers.models.roformer.modeling_roformer',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\roformer\\modeling_roformer.py',
   'PYMODULE'),
  ('transformers.models.roformer.tokenization_roformer_fast',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\roformer\\tokenization_roformer_fast.py',
   'PYMODULE'),
  ('transformers.models.roformer.tokenization_roformer',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\roformer\\tokenization_roformer.py',
   'PYMODULE'),
  ('transformers.models.roformer.configuration_roformer',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\roformer\\configuration_roformer.py',
   'PYMODULE'),
  ('transformers.onnx',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\onnx\\__init__.py',
   'PYMODULE'),
  ('transformers.onnx.utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\onnx\\utils.py',
   'PYMODULE'),
  ('transformers.onnx.features',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\onnx\\features.py',
   'PYMODULE'),
  ('transformers.onnx.convert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\onnx\\convert.py',
   'PYMODULE'),
  ('torch.onnx',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\onnx\\__init__.py',
   'PYMODULE'),
  ('torch.onnx.utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\onnx\\utils.py',
   'PYMODULE'),
  ('torch.onnx.symbolic_caffe2',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\onnx\\symbolic_caffe2.py',
   'PYMODULE'),
  ('torch.onnx.symbolic_opset9',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\onnx\\symbolic_opset9.py',
   'PYMODULE'),
  ('torch.onnx.symbolic_registry',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\onnx\\symbolic_registry.py',
   'PYMODULE'),
  ('torch.onnx.symbolic_helper',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\onnx\\symbolic_helper.py',
   'PYMODULE'),
  ('torch.onnx.symbolic_opset13',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\onnx\\symbolic_opset13.py',
   'PYMODULE'),
  ('torch.onnx.symbolic_opset11',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\onnx\\symbolic_opset11.py',
   'PYMODULE'),
  ('torch.onnx.symbolic_opset10',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\onnx\\symbolic_opset10.py',
   'PYMODULE'),
  ('torch.jit',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\jit\\__init__.py',
   'PYMODULE'),
  ('torch.jit._freeze',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\jit\\_freeze.py',
   'PYMODULE'),
  ('torch.jit._fuser',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\jit\\_fuser.py',
   'PYMODULE'),
  ('torch.jit._serialization',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\jit\\_serialization.py',
   'PYMODULE'),
  ('torch.jit._recursive',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\jit\\_recursive.py',
   'PYMODULE'),
  ('torch.jit._check',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\jit\\_check.py',
   'PYMODULE'),
  ('torch.jit._builtins',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\jit\\_builtins.py',
   'PYMODULE'),
  ('torch.jit.frontend',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\jit\\frontend.py',
   'PYMODULE'),
  ('torch.jit.annotations',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\jit\\annotations.py',
   'PYMODULE'),
  ('torch.jit._state',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\jit\\_state.py',
   'PYMODULE'),
  ('torch.jit._monkeytype_config',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\jit\\_monkeytype_config.py',
   'PYMODULE'),
  ('torch.jit._async',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\jit\\_async.py',
   'PYMODULE'),
  ('torch.jit._trace',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\jit\\_trace.py',
   'PYMODULE'),
  ('torch.jit._script',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\jit\\_script.py',
   'PYMODULE'),
  ('transformers.processing_utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\processing_utils.py',
   'PYMODULE'),
  ('transformers.feature_extraction_utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\feature_extraction_utils.py',
   'PYMODULE'),
  ('transformers.onnx.config',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\onnx\\config.py',
   'PYMODULE'),
  ('transformers.tokenization_utils_base',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\tokenization_utils_base.py',
   'PYMODULE'),
  ('transformers.tokenization_utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\tokenization_utils.py',
   'PYMODULE'),
  ('transformers.models.auto.processing_auto',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\auto\\processing_auto.py',
   'PYMODULE'),
  ('transformers.models.auto.feature_extraction_auto',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\auto\\feature_extraction_auto.py',
   'PYMODULE'),
  ('transformers.models.auto.configuration_auto',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\auto\\configuration_auto.py',
   'PYMODULE'),
  ('transformers.models.auto.auto_factory',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\auto\\auto_factory.py',
   'PYMODULE'),
  ('transformers.utils.hub',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\utils\\hub.py',
   'PYMODULE'),
  ('transformers.modeling_flax_pytorch_utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\modeling_flax_pytorch_utils.py',
   'PYMODULE'),
  ('transformers.generation_flax_utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\generation_flax_utils.py',
   'PYMODULE'),
  ('transformers.generation_flax_logits_process',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\generation_flax_logits_process.py',
   'PYMODULE'),
  ('transformers.dynamic_module_utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\dynamic_module_utils.py',
   'PYMODULE'),
  ('transformers.configuration_utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\configuration_utils.py',
   'PYMODULE'),
  ('transformers.modeling_flax_outputs',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\modeling_flax_outputs.py',
   'PYMODULE'),
  ('transformers.models.bert.tokenization_bert_tf',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\bert\\tokenization_bert_tf.py',
   'PYMODULE'),
  ('transformers.models.bert.modeling_tf_bert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\bert\\modeling_tf_bert.py',
   'PYMODULE'),
  ('transformers.models',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\__init__.py',
   'PYMODULE'),
  ('transformers.models.yoso',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\yoso\\__init__.py',
   'PYMODULE'),
  ('transformers.models.yoso.modeling_yoso',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\yoso\\modeling_yoso.py',
   'PYMODULE'),
  ('torch.utils.cpp_extension',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\utils\\cpp_extension.py',
   'PYMODULE'),
  ('setuptools.command.build_ext',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\setuptools\\command\\build_ext.py',
   'PYMODULE'),
  ('torch.utils.hipify.hipify_python',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\utils\\hipify\\hipify_python.py',
   'PYMODULE'),
  ('torch.utils.hipify.cuda_to_hip_mappings',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\utils\\hipify\\cuda_to_hip_mappings.py',
   'PYMODULE'),
  ('torch.utils.hipify.constants',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\utils\\hipify\\constants.py',
   'PYMODULE'),
  ('torch.utils.hipify',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\utils\\hipify\\__init__.py',
   'PYMODULE'),
  ('torch.utils.hipify.version',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\utils\\hipify\\version.py',
   'PYMODULE'),
  ('torch.utils._cpp_extension_versioner',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\utils\\_cpp_extension_versioner.py',
   'PYMODULE'),
  ('torch.utils.file_baton',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\utils\\file_baton.py',
   'PYMODULE'),
  ('torch._appdirs',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\_appdirs.py',
   'PYMODULE'),
  ('transformers.models.yoso.configuration_yoso',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\yoso\\configuration_yoso.py',
   'PYMODULE'),
  ('transformers.models.yolos',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\yolos\\__init__.py',
   'PYMODULE'),
  ('transformers.models.yolos.modeling_yolos',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\yolos\\modeling_yolos.py',
   'PYMODULE'),
  ('transformers.models.detr.feature_extraction_detr',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\detr\\feature_extraction_detr.py',
   'PYMODULE'),
  ('transformers.image_utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\image_utils.py',
   'PYMODULE'),
  ('transformers.models.yolos.feature_extraction_yolos',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\yolos\\feature_extraction_yolos.py',
   'PYMODULE'),
  ('transformers.models.yolos.configuration_yolos',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\yolos\\configuration_yolos.py',
   'PYMODULE'),
  ('transformers.models.xlnet',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\xlnet\\__init__.py',
   'PYMODULE'),
  ('transformers.models.xlnet.modeling_tf_xlnet',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\xlnet\\modeling_tf_xlnet.py',
   'PYMODULE'),
  ('transformers.models.xlnet.modeling_xlnet',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\xlnet\\modeling_xlnet.py',
   'PYMODULE'),
  ('transformers.models.xlnet.tokenization_xlnet_fast',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\xlnet\\tokenization_xlnet_fast.py',
   'PYMODULE'),
  ('transformers.models.xlnet.tokenization_xlnet',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\xlnet\\tokenization_xlnet.py',
   'PYMODULE'),
  ('transformers.models.xlnet.configuration_xlnet',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\xlnet\\configuration_xlnet.py',
   'PYMODULE'),
  ('transformers.models.xlm_roberta_xl',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\xlm_roberta_xl\\__init__.py',
   'PYMODULE'),
  ('transformers.models.xlm_roberta_xl.modeling_xlm_roberta_xl',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\xlm_roberta_xl\\modeling_xlm_roberta_xl.py',
   'PYMODULE'),
  ('transformers.models.xlm_roberta_xl.configuration_xlm_roberta_xl',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\xlm_roberta_xl\\configuration_xlm_roberta_xl.py',
   'PYMODULE'),
  ('transformers.models.xlm_roberta',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\xlm_roberta\\__init__.py',
   'PYMODULE'),
  ('transformers.models.xlm_roberta.modeling_flax_xlm_roberta',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\xlm_roberta\\modeling_flax_xlm_roberta.py',
   'PYMODULE'),
  ('transformers.models.roberta.modeling_flax_roberta',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\roberta\\modeling_flax_roberta.py',
   'PYMODULE'),
  ('transformers.models.roberta.configuration_roberta',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\roberta\\configuration_roberta.py',
   'PYMODULE'),
  ('transformers.models.xlm_roberta.modeling_tf_xlm_roberta',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\xlm_roberta\\modeling_tf_xlm_roberta.py',
   'PYMODULE'),
  ('transformers.models.roberta.modeling_tf_roberta',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\roberta\\modeling_tf_roberta.py',
   'PYMODULE'),
  ('transformers.models.xlm_roberta.modeling_xlm_roberta',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\xlm_roberta\\modeling_xlm_roberta.py',
   'PYMODULE'),
  ('transformers.models.roberta.modeling_roberta',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\roberta\\modeling_roberta.py',
   'PYMODULE'),
  ('transformers.models.xlm_roberta.tokenization_xlm_roberta_fast',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\xlm_roberta\\tokenization_xlm_roberta_fast.py',
   'PYMODULE'),
  ('transformers.models.xlm_roberta.tokenization_xlm_roberta',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\xlm_roberta\\tokenization_xlm_roberta.py',
   'PYMODULE'),
  ('transformers.models.xlm_roberta.configuration_xlm_roberta',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\xlm_roberta\\configuration_xlm_roberta.py',
   'PYMODULE'),
  ('transformers.models.xlm_prophetnet',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\xlm_prophetnet\\__init__.py',
   'PYMODULE'),
  ('transformers.models.xlm_prophetnet.modeling_xlm_prophetnet',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\xlm_prophetnet\\modeling_xlm_prophetnet.py',
   'PYMODULE'),
  ('transformers.models.prophetnet.modeling_prophetnet',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\prophetnet\\modeling_prophetnet.py',
   'PYMODULE'),
  ('transformers.models.prophetnet.configuration_prophetnet',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\prophetnet\\configuration_prophetnet.py',
   'PYMODULE'),
  ('transformers.models.xlm_prophetnet.tokenization_xlm_prophetnet',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\xlm_prophetnet\\tokenization_xlm_prophetnet.py',
   'PYMODULE'),
  ('transformers.models.xlm_prophetnet.configuration_xlm_prophetnet',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\xlm_prophetnet\\configuration_xlm_prophetnet.py',
   'PYMODULE'),
  ('transformers.models.xlm',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\xlm\\__init__.py',
   'PYMODULE'),
  ('transformers.models.xlm.modeling_tf_xlm',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\xlm\\modeling_tf_xlm.py',
   'PYMODULE'),
  ('transformers.models.xlm.modeling_xlm',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\xlm\\modeling_xlm.py',
   'PYMODULE'),
  ('transformers.models.xlm.tokenization_xlm',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\xlm\\tokenization_xlm.py',
   'PYMODULE'),
  ('jieba',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\jieba\\__init__.py',
   'PYMODULE'),
  ('jieba.lac_small.predict',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\jieba\\lac_small\\predict.py',
   'PYMODULE'),
  ('jieba.lac_small',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\jieba\\lac_small\\__init__.py',
   'PYMODULE'),
  ('jieba.lac_small.reader_small',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\jieba\\lac_small\\reader_small.py',
   'PYMODULE'),
  ('jieba.lac_small.creator',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\jieba\\lac_small\\creator.py',
   'PYMODULE'),
  ('jieba.lac_small.nets',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\jieba\\lac_small\\nets.py',
   'PYMODULE'),
  ('jieba.lac_small.utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\jieba\\lac_small\\utils.py',
   'PYMODULE'),
  ('jieba._compat',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\jieba\\_compat.py',
   'PYMODULE'),
  ('jieba.finalseg',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\jieba\\finalseg\\__init__.py',
   'PYMODULE'),
  ('jieba.finalseg.prob_emit',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\jieba\\finalseg\\prob_emit.py',
   'PYMODULE'),
  ('jieba.finalseg.prob_trans',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\jieba\\finalseg\\prob_trans.py',
   'PYMODULE'),
  ('jieba.finalseg.prob_start',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\jieba\\finalseg\\prob_start.py',
   'PYMODULE'),
  ('sacremoses',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\sacremoses\\__init__.py',
   'PYMODULE'),
  ('sacremoses.normalize',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\sacremoses\\normalize.py',
   'PYMODULE'),
  ('regex',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\regex\\__init__.py',
   'PYMODULE'),
  ('regex.regex',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\regex\\regex.py',
   'PYMODULE'),
  ('regex._regex_core',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\regex\\_regex_core.py',
   'PYMODULE'),
  ('sacremoses.truecase',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\sacremoses\\truecase.py',
   'PYMODULE'),
  ('sacremoses.util',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\sacremoses\\util.py',
   'PYMODULE'),
  ('joblib',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\joblib\\__init__.py',
   'PYMODULE'),
  ('joblib._cloudpickle_wrapper',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\joblib\\_cloudpickle_wrapper.py',
   'PYMODULE'),
  ('joblib.externals.loky',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\joblib\\externals\\loky\\__init__.py',
   'PYMODULE'),
  ('joblib.externals',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\joblib\\externals\\__init__.py',
   'PYMODULE'),
  ('joblib.externals.cloudpickle',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\joblib\\externals\\cloudpickle\\__init__.py',
   'PYMODULE'),
  ('joblib.externals.cloudpickle.cloudpickle_fast',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\joblib\\externals\\cloudpickle\\cloudpickle_fast.py',
   'PYMODULE'),
  ('joblib.externals.cloudpickle.compat',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\joblib\\externals\\cloudpickle\\compat.py',
   'PYMODULE'),
  ('joblib.externals.cloudpickle.cloudpickle',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\joblib\\externals\\cloudpickle\\cloudpickle.py',
   'PYMODULE'),
  ('joblib.externals.loky.process_executor',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\joblib\\externals\\loky\\process_executor.py',
   'PYMODULE'),
  ('joblib.externals.loky.initializers',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\joblib\\externals\\loky\\initializers.py',
   'PYMODULE'),
  ('joblib.externals.loky.backend.utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\joblib\\externals\\loky\\backend\\utils.py',
   'PYMODULE'),
  ('joblib.externals.loky.backend.queues',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\joblib\\externals\\loky\\backend\\queues.py',
   'PYMODULE'),
  ('joblib.externals.loky.backend',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\joblib\\externals\\loky\\backend\\__init__.py',
   'PYMODULE'),
  ('joblib.externals.loky.backend.spawn',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\joblib\\externals\\loky\\backend\\spawn.py',
   'PYMODULE'),
  ('joblib.externals.loky.backend.resource_tracker',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\joblib\\externals\\loky\\backend\\resource_tracker.py',
   'PYMODULE'),
  ('joblib.externals.loky.backend.fork_exec',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\joblib\\externals\\loky\\backend\\fork_exec.py',
   'PYMODULE'),
  ('joblib.externals.loky.backend._win_reduction',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\joblib\\externals\\loky\\backend\\_win_reduction.py',
   'PYMODULE'),
  ('joblib.externals.loky.cloudpickle_wrapper',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\joblib\\externals\\loky\\cloudpickle_wrapper.py',
   'PYMODULE'),
  ('joblib.externals.loky.reusable_executor',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\joblib\\externals\\loky\\reusable_executor.py',
   'PYMODULE'),
  ('joblib.externals.loky.backend.reduction',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\joblib\\externals\\loky\\backend\\reduction.py',
   'PYMODULE'),
  ('joblib.externals.loky.backend._posix_reduction',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\joblib\\externals\\loky\\backend\\_posix_reduction.py',
   'PYMODULE'),
  ('joblib.externals.loky.backend.context',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\joblib\\externals\\loky\\backend\\context.py',
   'PYMODULE'),
  ('joblib.externals.loky.backend.synchronize',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\joblib\\externals\\loky\\backend\\synchronize.py',
   'PYMODULE'),
  ('joblib.externals.loky.backend.process',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\joblib\\externals\\loky\\backend\\process.py',
   'PYMODULE'),
  ('joblib.externals.loky.backend.popen_loky_posix',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\joblib\\externals\\loky\\backend\\popen_loky_posix.py',
   'PYMODULE'),
  ('joblib.externals.loky.backend.popen_loky_win32',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\joblib\\externals\\loky\\backend\\popen_loky_win32.py',
   'PYMODULE'),
  ('joblib.externals.loky._base',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\joblib\\externals\\loky\\_base.py',
   'PYMODULE'),
  ('joblib._multiprocessing_helpers',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\joblib\\_multiprocessing_helpers.py',
   'PYMODULE'),
  ('joblib.parallel',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\joblib\\parallel.py',
   'PYMODULE'),
  ('joblib._dask',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\joblib\\_dask.py',
   'PYMODULE'),
  ('joblib._utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\joblib\\_utils.py',
   'PYMODULE'),
  ('joblib._parallel_backends',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\joblib\\_parallel_backends.py',
   'PYMODULE'),
  ('joblib.executor',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\joblib\\executor.py',
   'PYMODULE'),
  ('joblib._memmapping_reducer',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\joblib\\_memmapping_reducer.py',
   'PYMODULE'),
  ('joblib.backports',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\joblib\\backports.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.lib',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy.testing',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.nosetester',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\testing\\_private\\nosetester.py',
   'PYMODULE'),
  ('numpy.testing._private.noseclasses',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\testing\\_private\\noseclasses.py',
   'PYMODULE'),
  ('numpy.core',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._dtype',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\core\\_dtype.py',
   'PYMODULE'),
  ('numpy.core._internal',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\core\\_internal.py',
   'PYMODULE'),
  ('numpy.core._dtype_ctypes',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs_scalars',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy.core.einsumfunc',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.core._machar',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\core\\_machar.py',
   'PYMODULE'),
  ('numpy.core._ufunc_config',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy.core.function_base',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\core\\function_base.py',
   'PYMODULE'),
  ('numpy.core.memmap',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\core\\memmap.py',
   'PYMODULE'),
  ('numpy.compat',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\compat\\__init__.py',
   'PYMODULE'),
  ('numpy.compat.py3k',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\compat\\py3k.py',
   'PYMODULE'),
  ('numpy.compat._pep440',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\compat\\_pep440.py',
   'PYMODULE'),
  ('numpy.compat._inspect',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\compat\\_inspect.py',
   'PYMODULE'),
  ('numpy.core.records',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\core\\records.py',
   'PYMODULE'),
  ('numpy.core.arrayprint',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\core\\arrayprint.py',
   'PYMODULE'),
  ('numpy.core.defchararray',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\core\\defchararray.py',
   'PYMODULE'),
  ('numpy.core._asarray',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\core\\_asarray.py',
   'PYMODULE'),
  ('numpy.core.shape_base',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\core\\shape_base.py',
   'PYMODULE'),
  ('numpy.core.fromnumeric',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy.core._methods',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\core\\_methods.py',
   'PYMODULE'),
  ('numpy._globals',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy.core.getlimits',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\core\\getlimits.py',
   'PYMODULE'),
  ('numpy.core._exceptions',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\core\\_exceptions.py',
   'PYMODULE'),
  ('numpy.core.numerictypes',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\core\\numerictypes.py',
   'PYMODULE'),
  ('numpy.core._type_aliases',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy.core._string_helpers',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy.core.umath',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\core\\umath.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('numpy.testing._private.decorators',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\testing\\_private\\decorators.py',
   'PYMODULE'),
  ('numpy.testing._private.parameterized',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\testing\\_private\\parameterized.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.linalg',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.array_api',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\array_api\\__init__.py',
   'PYMODULE'),
  ('numpy.array_api._utility_functions',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\array_api\\_utility_functions.py',
   'PYMODULE'),
  ('numpy.array_api._array_object',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\array_api\\_array_object.py',
   'PYMODULE'),
  ('numpy.typing',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._generic_alias',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\_typing\\_generic_alias.py',
   'PYMODULE'),
  ('numpy._typing',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy.array_api._typing',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\array_api\\_typing.py',
   'PYMODULE'),
  ('numpy.array_api._statistical_functions',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\array_api\\_statistical_functions.py',
   'PYMODULE'),
  ('numpy.array_api._sorting_functions',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\array_api\\_sorting_functions.py',
   'PYMODULE'),
  ('numpy.array_api._set_functions',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\array_api\\_set_functions.py',
   'PYMODULE'),
  ('numpy.array_api._searching_functions',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\array_api\\_searching_functions.py',
   'PYMODULE'),
  ('numpy.array_api._manipulation_functions',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\array_api\\_manipulation_functions.py',
   'PYMODULE'),
  ('numpy.array_api.linalg',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\array_api\\linalg.py',
   'PYMODULE'),
  ('numpy.array_api._elementwise_functions',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\array_api\\_elementwise_functions.py',
   'PYMODULE'),
  ('numpy.array_api._dtypes',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\array_api\\_dtypes.py',
   'PYMODULE'),
  ('numpy.array_api._data_type_functions',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\array_api\\_data_type_functions.py',
   'PYMODULE'),
  ('numpy.array_api._creation_functions',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\array_api\\_creation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._constants',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\array_api\\_constants.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.arraypad',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\lib\\arraypad.py',
   'PYMODULE'),
  ('numpy.lib.arrayterator',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\lib\\arrayterator.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.ma',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.arraysetops',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\lib\\arraysetops.py',
   'PYMODULE'),
  ('numpy.lib.utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\lib\\utils.py',
   'PYMODULE'),
  ('numpy.lib.polynomial',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\lib\\polynomial.py',
   'PYMODULE'),
  ('numpy.lib.histograms',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\lib\\histograms.py',
   'PYMODULE'),
  ('numpy.lib.ufunclike',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\lib\\ufunclike.py',
   'PYMODULE'),
  ('numpy.lib.twodim_base',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\lib\\twodim_base.py',
   'PYMODULE'),
  ('numpy.lib.shape_base',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\lib\\shape_base.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib.nanfunctions',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\lib\\nanfunctions.py',
   'PYMODULE'),
  ('numpy.lib.function_base',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\lib\\function_base.py',
   'PYMODULE'),
  ('numpy.lib.index_tricks',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\lib\\index_tricks.py',
   'PYMODULE'),
  ('numpy.lib.type_check',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\lib\\type_check.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.version',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('numpy._version',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\_version.py',
   'PYMODULE'),
  ('numpy.core.overrides',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\core\\overrides.py',
   'PYMODULE'),
  ('numpy.core.numeric',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\core\\numeric.py',
   'PYMODULE'),
  ('joblib.pool',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\joblib\\pool.py',
   'PYMODULE'),
  ('joblib.my_exceptions',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\joblib\\my_exceptions.py',
   'PYMODULE'),
  ('joblib.disk',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\joblib\\disk.py',
   'PYMODULE'),
  ('joblib._deprecated_my_exceptions',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\joblib\\_deprecated_my_exceptions.py',
   'PYMODULE'),
  ('joblib.compressor',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\joblib\\compressor.py',
   'PYMODULE'),
  ('joblib.numpy_pickle',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\joblib\\numpy_pickle.py',
   'PYMODULE'),
  ('joblib.numpy_pickle_compat',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\joblib\\numpy_pickle_compat.py',
   'PYMODULE'),
  ('joblib.numpy_pickle_utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\joblib\\numpy_pickle_utils.py',
   'PYMODULE'),
  ('joblib.logger',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\joblib\\logger.py',
   'PYMODULE'),
  ('joblib.memory',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\joblib\\memory.py',
   'PYMODULE'),
  ('joblib._store_backends',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\joblib\\_store_backends.py',
   'PYMODULE'),
  ('joblib.func_inspect',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\joblib\\func_inspect.py',
   'PYMODULE'),
  ('joblib.hashing',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\joblib\\hashing.py',
   'PYMODULE'),
  ('sacremoses.tokenize',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\sacremoses\\tokenize.py',
   'PYMODULE'),
  ('sacremoses.indic',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\sacremoses\\indic.py',
   'PYMODULE'),
  ('sacremoses.corpus',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\sacremoses\\corpus.py',
   'PYMODULE'),
  ('transformers.models.xlm.configuration_xlm',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\xlm\\configuration_xlm.py',
   'PYMODULE'),
  ('transformers.models.xglm',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\xglm\\__init__.py',
   'PYMODULE'),
  ('transformers.models.xglm.modeling_flax_xglm',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\xglm\\modeling_flax_xglm.py',
   'PYMODULE'),
  ('transformers.models.xglm.modeling_xglm',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\xglm\\modeling_xglm.py',
   'PYMODULE'),
  ('transformers.models.xglm.tokenization_xglm_fast',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\xglm\\tokenization_xglm_fast.py',
   'PYMODULE'),
  ('transformers.models.xglm.tokenization_xglm',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\xglm\\tokenization_xglm.py',
   'PYMODULE'),
  ('transformers.models.xglm.configuration_xglm',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\xglm\\configuration_xglm.py',
   'PYMODULE'),
  ('transformers.models.wavlm',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\wavlm\\__init__.py',
   'PYMODULE'),
  ('transformers.models.wavlm.modeling_wavlm',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\wavlm\\modeling_wavlm.py',
   'PYMODULE'),
  ('transformers.models.wavlm.configuration_wavlm',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\wavlm\\configuration_wavlm.py',
   'PYMODULE'),
  ('transformers.models.wav2vec2_with_lm',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\wav2vec2_with_lm\\__init__.py',
   'PYMODULE'),
  ('transformers.models.wav2vec2_with_lm.processing_wav2vec2_with_lm',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\wav2vec2_with_lm\\processing_wav2vec2_with_lm.py',
   'PYMODULE'),
  ('transformers.models.wav2vec2_phoneme',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\wav2vec2_phoneme\\__init__.py',
   'PYMODULE'),
  ('transformers.models.wav2vec2_phoneme.tokenization_wav2vec2_phoneme',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\wav2vec2_phoneme\\tokenization_wav2vec2_phoneme.py',
   'PYMODULE'),
  ('transformers.models.wav2vec2_conformer',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\wav2vec2_conformer\\__init__.py',
   'PYMODULE'),
  ('transformers.models.wav2vec2_conformer.modeling_wav2vec2_conformer',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\wav2vec2_conformer\\modeling_wav2vec2_conformer.py',
   'PYMODULE'),
  ('transformers.models.wav2vec2_conformer.configuration_wav2vec2_conformer',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\wav2vec2_conformer\\configuration_wav2vec2_conformer.py',
   'PYMODULE'),
  ('transformers.models.wav2vec2',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\wav2vec2\\__init__.py',
   'PYMODULE'),
  ('transformers.models.wav2vec2.modeling_tf_wav2vec2',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\wav2vec2\\modeling_tf_wav2vec2.py',
   'PYMODULE'),
  ('transformers.models.wav2vec2.modeling_wav2vec2',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\wav2vec2\\modeling_wav2vec2.py',
   'PYMODULE'),
  ('transformers.models.wav2vec2.tokenization_wav2vec2',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\wav2vec2\\tokenization_wav2vec2.py',
   'PYMODULE'),
  ('transformers.models.wav2vec2.processing_wav2vec2',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\wav2vec2\\processing_wav2vec2.py',
   'PYMODULE'),
  ('transformers.models.wav2vec2.feature_extraction_wav2vec2',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\wav2vec2\\feature_extraction_wav2vec2.py',
   'PYMODULE'),
  ('transformers.feature_extraction_sequence_utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\feature_extraction_sequence_utils.py',
   'PYMODULE'),
  ('transformers.models.wav2vec2.configuration_wav2vec2',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\wav2vec2\\configuration_wav2vec2.py',
   'PYMODULE'),
  ('transformers.models.vit_mae',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\vit_mae\\__init__.py',
   'PYMODULE'),
  ('transformers.models.vit_mae.modeling_tf_vit_mae',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\vit_mae\\modeling_tf_vit_mae.py',
   'PYMODULE'),
  ('transformers.file_utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\file_utils.py',
   'PYMODULE'),
  ('transformers.models.vit_mae.modeling_vit_mae',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\vit_mae\\modeling_vit_mae.py',
   'PYMODULE'),
  ('transformers.models.vit_mae.configuration_vit_mae',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\vit_mae\\configuration_vit_mae.py',
   'PYMODULE'),
  ('transformers.models.vit',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\vit\\__init__.py',
   'PYMODULE'),
  ('transformers.models.vit.modeling_flax_vit',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\vit\\modeling_flax_vit.py',
   'PYMODULE'),
  ('transformers.models.vit.modeling_tf_vit',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\vit\\modeling_tf_vit.py',
   'PYMODULE'),
  ('transformers.models.vit.modeling_vit',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\vit\\modeling_vit.py',
   'PYMODULE'),
  ('transformers.models.vit.feature_extraction_vit',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\vit\\feature_extraction_vit.py',
   'PYMODULE'),
  ('transformers.models.vit.configuration_vit',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\vit\\configuration_vit.py',
   'PYMODULE'),
  ('transformers.models.visual_bert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\visual_bert\\__init__.py',
   'PYMODULE'),
  ('transformers.models.visual_bert.modeling_visual_bert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\visual_bert\\modeling_visual_bert.py',
   'PYMODULE'),
  ('transformers.models.visual_bert.configuration_visual_bert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\visual_bert\\configuration_visual_bert.py',
   'PYMODULE'),
  ('transformers.models.vision_text_dual_encoder',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\vision_text_dual_encoder\\__init__.py',
   'PYMODULE'),
  ('transformers.models.vision_text_dual_encoder.modeling_vision_text_dual_encoder',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\vision_text_dual_encoder\\modeling_vision_text_dual_encoder.py',
   'PYMODULE'),
  ('transformers.models.clip.modeling_clip',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\clip\\modeling_clip.py',
   'PYMODULE'),
  ('transformers.models.clip.configuration_clip',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\clip\\configuration_clip.py',
   'PYMODULE'),
  ('transformers.models.vision_text_dual_encoder.configuration_vision_text_dual_encoder',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\vision_text_dual_encoder\\configuration_vision_text_dual_encoder.py',
   'PYMODULE'),
  ('transformers.models.vision_encoder_decoder',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\vision_encoder_decoder\\__init__.py',
   'PYMODULE'),
  ('transformers.models.vision_encoder_decoder.modeling_flax_vision_encoder_decoder',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\vision_encoder_decoder\\modeling_flax_vision_encoder_decoder.py',
   'PYMODULE'),
  ('transformers.models.vision_encoder_decoder.modeling_tf_vision_encoder_decoder',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\vision_encoder_decoder\\modeling_tf_vision_encoder_decoder.py',
   'PYMODULE'),
  ('transformers.models.vision_encoder_decoder.modeling_vision_encoder_decoder',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\vision_encoder_decoder\\modeling_vision_encoder_decoder.py',
   'PYMODULE'),
  ('transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\vision_encoder_decoder\\configuration_vision_encoder_decoder.py',
   'PYMODULE'),
  ('transformers.models.vilt',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\vilt\\__init__.py',
   'PYMODULE'),
  ('transformers.models.vilt.modeling_vilt',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\vilt\\modeling_vilt.py',
   'PYMODULE'),
  ('transformers.models.vilt.processing_vilt',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\vilt\\processing_vilt.py',
   'PYMODULE'),
  ('transformers.models.vilt.feature_extraction_vilt',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\vilt\\feature_extraction_vilt.py',
   'PYMODULE'),
  ('transformers.models.vilt.configuration_vilt',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\vilt\\configuration_vilt.py',
   'PYMODULE'),
  ('transformers.models.van',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\van\\__init__.py',
   'PYMODULE'),
  ('transformers.models.van.modeling_van',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\van\\modeling_van.py',
   'PYMODULE'),
  ('transformers.models.van.configuration_van',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\van\\configuration_van.py',
   'PYMODULE'),
  ('transformers.models.unispeech_sat',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\unispeech_sat\\__init__.py',
   'PYMODULE'),
  ('transformers.models.unispeech_sat.modeling_unispeech_sat',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\unispeech_sat\\modeling_unispeech_sat.py',
   'PYMODULE'),
  ('transformers.models.unispeech_sat.configuration_unispeech_sat',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\unispeech_sat\\configuration_unispeech_sat.py',
   'PYMODULE'),
  ('transformers.models.unispeech',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\unispeech\\__init__.py',
   'PYMODULE'),
  ('transformers.models.unispeech.modeling_unispeech',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\unispeech\\modeling_unispeech.py',
   'PYMODULE'),
  ('transformers.models.unispeech.configuration_unispeech',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\unispeech\\configuration_unispeech.py',
   'PYMODULE'),
  ('transformers.models.trocr',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\trocr\\__init__.py',
   'PYMODULE'),
  ('transformers.models.trocr.modeling_trocr',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\trocr\\modeling_trocr.py',
   'PYMODULE'),
  ('transformers.models.trocr.processing_trocr',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\trocr\\processing_trocr.py',
   'PYMODULE'),
  ('transformers.models.trocr.configuration_trocr',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\trocr\\configuration_trocr.py',
   'PYMODULE'),
  ('transformers.models.transfo_xl',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\transfo_xl\\__init__.py',
   'PYMODULE'),
  ('transformers.models.transfo_xl.modeling_tf_transfo_xl',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\transfo_xl\\modeling_tf_transfo_xl.py',
   'PYMODULE'),
  ('transformers.models.transfo_xl.modeling_tf_transfo_xl_utilities',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\transfo_xl\\modeling_tf_transfo_xl_utilities.py',
   'PYMODULE'),
  ('transformers.models.transfo_xl.modeling_transfo_xl',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\transfo_xl\\modeling_transfo_xl.py',
   'PYMODULE'),
  ('transformers.models.transfo_xl.modeling_transfo_xl_utilities',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\transfo_xl\\modeling_transfo_xl_utilities.py',
   'PYMODULE'),
  ('transformers.models.transfo_xl.tokenization_transfo_xl',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\transfo_xl\\tokenization_transfo_xl.py',
   'PYMODULE'),
  ('transformers.models.transfo_xl.configuration_transfo_xl',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\transfo_xl\\configuration_transfo_xl.py',
   'PYMODULE'),
  ('transformers.models.trajectory_transformer',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\trajectory_transformer\\__init__.py',
   'PYMODULE'),
  ('transformers.models.trajectory_transformer.modeling_trajectory_transformer',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\trajectory_transformer\\modeling_trajectory_transformer.py',
   'PYMODULE'),
  ('transformers.models.trajectory_transformer.configuration_trajectory_transformer',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\trajectory_transformer\\configuration_trajectory_transformer.py',
   'PYMODULE'),
  ('transformers.models.tapex',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\tapex\\__init__.py',
   'PYMODULE'),
  ('transformers.models.tapex.tokenization_tapex',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\tapex\\tokenization_tapex.py',
   'PYMODULE'),
  ('transformers.models.tapas',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\tapas\\__init__.py',
   'PYMODULE'),
  ('transformers.models.tapas.modeling_tf_tapas',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\tapas\\modeling_tf_tapas.py',
   'PYMODULE'),
  ('transformers.models.tapas.modeling_tapas',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\tapas\\modeling_tapas.py',
   'PYMODULE'),
  ('transformers.models.tapas.tokenization_tapas',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\tapas\\tokenization_tapas.py',
   'PYMODULE'),
  ('transformers.models.tapas.configuration_tapas',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\tapas\\configuration_tapas.py',
   'PYMODULE'),
  ('transformers.models.t5',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\t5\\__init__.py',
   'PYMODULE'),
  ('transformers.models.t5.modeling_flax_t5',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\t5\\modeling_flax_t5.py',
   'PYMODULE'),
  ('transformers.models.t5.modeling_tf_t5',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\t5\\modeling_tf_t5.py',
   'PYMODULE'),
  ('transformers.models.t5.modeling_t5',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\t5\\modeling_t5.py',
   'PYMODULE'),
  ('transformers.utils.model_parallel_utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\utils\\model_parallel_utils.py',
   'PYMODULE'),
  ('transformers.models.t5.tokenization_t5_fast',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\t5\\tokenization_t5_fast.py',
   'PYMODULE'),
  ('transformers.models.t5.tokenization_t5',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\t5\\tokenization_t5.py',
   'PYMODULE'),
  ('transformers.models.t5.configuration_t5',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\t5\\configuration_t5.py',
   'PYMODULE'),
  ('transformers.models.swin',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\swin\\__init__.py',
   'PYMODULE'),
  ('transformers.models.swin.modeling_tf_swin',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\swin\\modeling_tf_swin.py',
   'PYMODULE'),
  ('transformers.models.swin.modeling_swin',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\swin\\modeling_swin.py',
   'PYMODULE'),
  ('transformers.models.swin.configuration_swin',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\swin\\configuration_swin.py',
   'PYMODULE'),
  ('transformers.models.squeezebert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\squeezebert\\__init__.py',
   'PYMODULE'),
  ('transformers.models.squeezebert.modeling_squeezebert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\squeezebert\\modeling_squeezebert.py',
   'PYMODULE'),
  ('transformers.models.squeezebert.tokenization_squeezebert_fast',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\squeezebert\\tokenization_squeezebert_fast.py',
   'PYMODULE'),
  ('transformers.models.squeezebert.tokenization_squeezebert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\squeezebert\\tokenization_squeezebert.py',
   'PYMODULE'),
  ('transformers.models.squeezebert.configuration_squeezebert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\squeezebert\\configuration_squeezebert.py',
   'PYMODULE'),
  ('transformers.models.splinter',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\splinter\\__init__.py',
   'PYMODULE'),
  ('transformers.models.splinter.modeling_splinter',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\splinter\\modeling_splinter.py',
   'PYMODULE'),
  ('transformers.models.splinter.tokenization_splinter_fast',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\splinter\\tokenization_splinter_fast.py',
   'PYMODULE'),
  ('transformers.models.splinter.tokenization_splinter',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\splinter\\tokenization_splinter.py',
   'PYMODULE'),
  ('transformers.models.splinter.configuration_splinter',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\splinter\\configuration_splinter.py',
   'PYMODULE'),
  ('transformers.models.speech_to_text_2',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\speech_to_text_2\\__init__.py',
   'PYMODULE'),
  ('transformers.models.speech_to_text_2.modeling_speech_to_text_2',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\speech_to_text_2\\modeling_speech_to_text_2.py',
   'PYMODULE'),
  ('transformers.models.speech_to_text_2.tokenization_speech_to_text_2',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\speech_to_text_2\\tokenization_speech_to_text_2.py',
   'PYMODULE'),
  ('transformers.models.speech_to_text_2.processing_speech_to_text_2',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\speech_to_text_2\\processing_speech_to_text_2.py',
   'PYMODULE'),
  ('transformers.models.speech_to_text_2.configuration_speech_to_text_2',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\speech_to_text_2\\configuration_speech_to_text_2.py',
   'PYMODULE'),
  ('transformers.models.speech_to_text',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\speech_to_text\\__init__.py',
   'PYMODULE'),
  ('transformers.models.speech_to_text.modeling_speech_to_text',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\speech_to_text\\modeling_speech_to_text.py',
   'PYMODULE'),
  ('transformers.models.speech_to_text.modeling_tf_speech_to_text',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\speech_to_text\\modeling_tf_speech_to_text.py',
   'PYMODULE'),
  ('transformers.models.speech_to_text.processing_speech_to_text',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\speech_to_text\\processing_speech_to_text.py',
   'PYMODULE'),
  ('transformers.models.speech_to_text.feature_extraction_speech_to_text',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\speech_to_text\\feature_extraction_speech_to_text.py',
   'PYMODULE'),
  ('transformers.models.speech_to_text.tokenization_speech_to_text',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\speech_to_text\\tokenization_speech_to_text.py',
   'PYMODULE'),
  ('transformers.models.speech_to_text.configuration_speech_to_text',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\speech_to_text\\configuration_speech_to_text.py',
   'PYMODULE'),
  ('transformers.models.speech_encoder_decoder',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\speech_encoder_decoder\\__init__.py',
   'PYMODULE'),
  ('transformers.models.speech_encoder_decoder.modeling_flax_speech_encoder_decoder',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\speech_encoder_decoder\\modeling_flax_speech_encoder_decoder.py',
   'PYMODULE'),
  ('transformers.models.speech_encoder_decoder.modeling_speech_encoder_decoder',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\speech_encoder_decoder\\modeling_speech_encoder_decoder.py',
   'PYMODULE'),
  ('transformers.models.speech_encoder_decoder.configuration_speech_encoder_decoder',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\speech_encoder_decoder\\configuration_speech_encoder_decoder.py',
   'PYMODULE'),
  ('transformers.models.sew_d',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\sew_d\\__init__.py',
   'PYMODULE'),
  ('transformers.models.sew_d.modeling_sew_d',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\sew_d\\modeling_sew_d.py',
   'PYMODULE'),
  ('transformers.models.sew_d.configuration_sew_d',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\sew_d\\configuration_sew_d.py',
   'PYMODULE'),
  ('transformers.models.sew',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\sew\\__init__.py',
   'PYMODULE'),
  ('transformers.models.sew.modeling_sew',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\sew\\modeling_sew.py',
   'PYMODULE'),
  ('transformers.models.sew.configuration_sew',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\sew\\configuration_sew.py',
   'PYMODULE'),
  ('transformers.models.segformer',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\segformer\\__init__.py',
   'PYMODULE'),
  ('transformers.models.segformer.modeling_tf_segformer',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\segformer\\modeling_tf_segformer.py',
   'PYMODULE'),
  ('transformers.models.segformer.modeling_segformer',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\segformer\\modeling_segformer.py',
   'PYMODULE'),
  ('transformers.models.segformer.feature_extraction_segformer',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\segformer\\feature_extraction_segformer.py',
   'PYMODULE'),
  ('transformers.models.segformer.configuration_segformer',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\segformer\\configuration_segformer.py',
   'PYMODULE'),
  ('transformers.models.roberta',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\roberta\\__init__.py',
   'PYMODULE'),
  ('transformers.models.roberta.tokenization_roberta_fast',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\roberta\\tokenization_roberta_fast.py',
   'PYMODULE'),
  ('transformers.models.roberta.tokenization_roberta',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\roberta\\tokenization_roberta.py',
   'PYMODULE'),
  ('transformers.models.retribert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\retribert\\__init__.py',
   'PYMODULE'),
  ('transformers.models.retribert.modeling_retribert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\retribert\\modeling_retribert.py',
   'PYMODULE'),
  ('transformers.models.retribert.tokenization_retribert_fast',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\retribert\\tokenization_retribert_fast.py',
   'PYMODULE'),
  ('transformers.models.retribert.tokenization_retribert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\retribert\\tokenization_retribert.py',
   'PYMODULE'),
  ('transformers.models.retribert.configuration_retribert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\retribert\\configuration_retribert.py',
   'PYMODULE'),
  ('transformers.models.resnet',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\resnet\\__init__.py',
   'PYMODULE'),
  ('transformers.models.resnet.modeling_tf_resnet',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\resnet\\modeling_tf_resnet.py',
   'PYMODULE'),
  ('transformers.models.resnet.modeling_resnet',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\resnet\\modeling_resnet.py',
   'PYMODULE'),
  ('transformers.models.resnet.configuration_resnet',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\resnet\\configuration_resnet.py',
   'PYMODULE'),
  ('transformers.models.rembert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\rembert\\__init__.py',
   'PYMODULE'),
  ('transformers.models.rembert.modeling_tf_rembert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\rembert\\modeling_tf_rembert.py',
   'PYMODULE'),
  ('transformers.models.rembert.modeling_rembert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\rembert\\modeling_rembert.py',
   'PYMODULE'),
  ('transformers.models.rembert.tokenization_rembert_fast',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\rembert\\tokenization_rembert_fast.py',
   'PYMODULE'),
  ('transformers.models.rembert.tokenization_rembert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\rembert\\tokenization_rembert.py',
   'PYMODULE'),
  ('transformers.models.rembert.configuration_rembert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\rembert\\configuration_rembert.py',
   'PYMODULE'),
  ('transformers.models.regnet',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\regnet\\__init__.py',
   'PYMODULE'),
  ('transformers.models.regnet.modeling_tf_regnet',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\regnet\\modeling_tf_regnet.py',
   'PYMODULE'),
  ('transformers.models.regnet.modeling_regnet',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\regnet\\modeling_regnet.py',
   'PYMODULE'),
  ('transformers.models.regnet.configuration_regnet',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\regnet\\configuration_regnet.py',
   'PYMODULE'),
  ('transformers.models.reformer',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\reformer\\__init__.py',
   'PYMODULE'),
  ('transformers.models.reformer.modeling_reformer',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\reformer\\modeling_reformer.py',
   'PYMODULE'),
  ('transformers.models.reformer.tokenization_reformer_fast',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\reformer\\tokenization_reformer_fast.py',
   'PYMODULE'),
  ('transformers.models.reformer.tokenization_reformer',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\reformer\\tokenization_reformer.py',
   'PYMODULE'),
  ('transformers.models.reformer.configuration_reformer',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\reformer\\configuration_reformer.py',
   'PYMODULE'),
  ('transformers.models.realm',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\realm\\__init__.py',
   'PYMODULE'),
  ('transformers.models.realm.retrieval_realm',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\realm\\retrieval_realm.py',
   'PYMODULE'),
  ('transformers.models.realm.modeling_realm',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\realm\\modeling_realm.py',
   'PYMODULE'),
  ('transformers.models.realm.tokenization_realm',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\realm\\tokenization_realm.py',
   'PYMODULE'),
  ('transformers.models.realm.configuration_realm',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\realm\\configuration_realm.py',
   'PYMODULE'),
  ('transformers.models.rag',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\rag\\__init__.py',
   'PYMODULE'),
  ('transformers.models.rag.modeling_tf_rag',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\rag\\modeling_tf_rag.py',
   'PYMODULE'),
  ('transformers.models.rag.modeling_rag',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\rag\\modeling_rag.py',
   'PYMODULE'),
  ('transformers.generation_stopping_criteria',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\generation_stopping_criteria.py',
   'PYMODULE'),
  ('transformers.generation_logits_process',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\generation_logits_process.py',
   'PYMODULE'),
  ('transformers.generation_beam_search',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\generation_beam_search.py',
   'PYMODULE'),
  ('transformers.generation_beam_constraints',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\generation_beam_constraints.py',
   'PYMODULE'),
  ('transformers.models.rag.tokenization_rag',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\rag\\tokenization_rag.py',
   'PYMODULE'),
  ('transformers.models.rag.retrieval_rag',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\rag\\retrieval_rag.py',
   'PYMODULE'),
  ('transformers.models.rag.configuration_rag',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\rag\\configuration_rag.py',
   'PYMODULE'),
  ('transformers.models.qdqbert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\qdqbert\\__init__.py',
   'PYMODULE'),
  ('transformers.models.qdqbert.modeling_qdqbert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\qdqbert\\modeling_qdqbert.py',
   'PYMODULE'),
  ('transformers.models.qdqbert.configuration_qdqbert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\qdqbert\\configuration_qdqbert.py',
   'PYMODULE'),
  ('transformers.models.prophetnet',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\prophetnet\\__init__.py',
   'PYMODULE'),
  ('transformers.models.prophetnet.tokenization_prophetnet',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\prophetnet\\tokenization_prophetnet.py',
   'PYMODULE'),
  ('transformers.models.poolformer',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\poolformer\\__init__.py',
   'PYMODULE'),
  ('transformers.models.poolformer.modeling_poolformer',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\poolformer\\modeling_poolformer.py',
   'PYMODULE'),
  ('transformers.models.poolformer.feature_extraction_poolformer',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\poolformer\\feature_extraction_poolformer.py',
   'PYMODULE'),
  ('transformers.models.poolformer.configuration_poolformer',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\poolformer\\configuration_poolformer.py',
   'PYMODULE'),
  ('transformers.models.plbart',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\plbart\\__init__.py',
   'PYMODULE'),
  ('transformers.models.plbart.modeling_plbart',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\plbart\\modeling_plbart.py',
   'PYMODULE'),
  ('transformers.models.plbart.tokenization_plbart',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\plbart\\tokenization_plbart.py',
   'PYMODULE'),
  ('transformers.models.plbart.configuration_plbart',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\plbart\\configuration_plbart.py',
   'PYMODULE'),
  ('transformers.models.phobert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\phobert\\__init__.py',
   'PYMODULE'),
  ('transformers.models.phobert.tokenization_phobert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\phobert\\tokenization_phobert.py',
   'PYMODULE'),
  ('transformers.models.perceiver',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\perceiver\\__init__.py',
   'PYMODULE'),
  ('transformers.models.perceiver.modeling_perceiver',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\perceiver\\modeling_perceiver.py',
   'PYMODULE'),
  ('transformers.models.perceiver.feature_extraction_perceiver',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\perceiver\\feature_extraction_perceiver.py',
   'PYMODULE'),
  ('transformers.models.perceiver.tokenization_perceiver',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\perceiver\\tokenization_perceiver.py',
   'PYMODULE'),
  ('transformers.models.perceiver.configuration_perceiver',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\perceiver\\configuration_perceiver.py',
   'PYMODULE'),
  ('transformers.models.pegasus',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\pegasus\\__init__.py',
   'PYMODULE'),
  ('transformers.models.pegasus.modeling_flax_pegasus',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\pegasus\\modeling_flax_pegasus.py',
   'PYMODULE'),
  ('transformers.models.pegasus.modeling_tf_pegasus',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\pegasus\\modeling_tf_pegasus.py',
   'PYMODULE'),
  ('transformers.models.pegasus.modeling_pegasus',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\pegasus\\modeling_pegasus.py',
   'PYMODULE'),
  ('transformers.models.pegasus.tokenization_pegasus_fast',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\pegasus\\tokenization_pegasus_fast.py',
   'PYMODULE'),
  ('transformers.models.pegasus.tokenization_pegasus',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\pegasus\\tokenization_pegasus.py',
   'PYMODULE'),
  ('transformers.models.pegasus.configuration_pegasus',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\pegasus\\configuration_pegasus.py',
   'PYMODULE'),
  ('transformers.models.owlvit',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\owlvit\\__init__.py',
   'PYMODULE'),
  ('transformers.models.owlvit.modeling_owlvit',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\owlvit\\modeling_owlvit.py',
   'PYMODULE'),
  ('transformers.models.owlvit.feature_extraction_owlvit',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\owlvit\\feature_extraction_owlvit.py',
   'PYMODULE'),
  ('transformers.models.owlvit.processing_owlvit',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\owlvit\\processing_owlvit.py',
   'PYMODULE'),
  ('transformers.models.owlvit.configuration_owlvit',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\owlvit\\configuration_owlvit.py',
   'PYMODULE'),
  ('transformers.models.opt',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\opt\\__init__.py',
   'PYMODULE'),
  ('transformers.models.opt.modeling_flax_opt',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\opt\\modeling_flax_opt.py',
   'PYMODULE'),
  ('transformers.models.opt.modeling_tf_opt',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\opt\\modeling_tf_opt.py',
   'PYMODULE'),
  ('transformers.models.opt.modeling_opt',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\opt\\modeling_opt.py',
   'PYMODULE'),
  ('transformers.models.opt.configuration_opt',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\opt\\configuration_opt.py',
   'PYMODULE'),
  ('transformers.models.openai',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\openai\\__init__.py',
   'PYMODULE'),
  ('transformers.models.openai.modeling_tf_openai',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\openai\\modeling_tf_openai.py',
   'PYMODULE'),
  ('transformers.models.openai.modeling_openai',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\openai\\modeling_openai.py',
   'PYMODULE'),
  ('transformers.models.openai.tokenization_openai_fast',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\openai\\tokenization_openai_fast.py',
   'PYMODULE'),
  ('transformers.models.openai.tokenization_openai',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\openai\\tokenization_openai.py',
   'PYMODULE'),
  ('transformers.models.openai.configuration_openai',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\openai\\configuration_openai.py',
   'PYMODULE'),
  ('transformers.models.nystromformer',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\nystromformer\\__init__.py',
   'PYMODULE'),
  ('transformers.models.nystromformer.modeling_nystromformer',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\nystromformer\\modeling_nystromformer.py',
   'PYMODULE'),
  ('transformers.models.nystromformer.configuration_nystromformer',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\nystromformer\\configuration_nystromformer.py',
   'PYMODULE'),
  ('transformers.models.nllb',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\nllb\\__init__.py',
   'PYMODULE'),
  ('transformers.models.nllb.tokenization_nllb_fast',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\nllb\\tokenization_nllb_fast.py',
   'PYMODULE'),
  ('transformers.models.nllb.tokenization_nllb',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\nllb\\tokenization_nllb.py',
   'PYMODULE'),
  ('transformers.models.nezha',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\nezha\\__init__.py',
   'PYMODULE'),
  ('transformers.models.nezha.modeling_nezha',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\nezha\\modeling_nezha.py',
   'PYMODULE'),
  ('transformers.models.nezha.configuration_nezha',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\nezha\\configuration_nezha.py',
   'PYMODULE'),
  ('transformers.models.mvp',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\mvp\\__init__.py',
   'PYMODULE'),
  ('transformers.models.mvp.modeling_mvp',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\mvp\\modeling_mvp.py',
   'PYMODULE'),
  ('transformers.models.mvp.tokenization_mvp_fast',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\mvp\\tokenization_mvp_fast.py',
   'PYMODULE'),
  ('transformers.models.mvp.tokenization_mvp',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\mvp\\tokenization_mvp.py',
   'PYMODULE'),
  ('transformers.models.mvp.configuration_mvp',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\mvp\\configuration_mvp.py',
   'PYMODULE'),
  ('transformers.models.mt5',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\mt5\\__init__.py',
   'PYMODULE'),
  ('transformers.models.mt5.modeling_flax_mt5',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\mt5\\modeling_flax_mt5.py',
   'PYMODULE'),
  ('transformers.models.mt5.modeling_tf_mt5',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\mt5\\modeling_tf_mt5.py',
   'PYMODULE'),
  ('transformers.models.mt5.modeling_mt5',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\mt5\\modeling_mt5.py',
   'PYMODULE'),
  ('transformers.models.mt5.configuration_mt5',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\mt5\\configuration_mt5.py',
   'PYMODULE'),
  ('transformers.utils.dummy_tokenizers_objects',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\utils\\dummy_tokenizers_objects.py',
   'PYMODULE'),
  ('transformers.utils.dummy_sentencepiece_objects',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\utils\\dummy_sentencepiece_objects.py',
   'PYMODULE'),
  ('transformers.models.mpnet',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\mpnet\\__init__.py',
   'PYMODULE'),
  ('transformers.models.mpnet.modeling_tf_mpnet',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\mpnet\\modeling_tf_mpnet.py',
   'PYMODULE'),
  ('transformers.models.mpnet.modeling_mpnet',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\mpnet\\modeling_mpnet.py',
   'PYMODULE'),
  ('transformers.models.mpnet.tokenization_mpnet_fast',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\mpnet\\tokenization_mpnet_fast.py',
   'PYMODULE'),
  ('transformers.models.mpnet.tokenization_mpnet',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\mpnet\\tokenization_mpnet.py',
   'PYMODULE'),
  ('transformers.models.mpnet.configuration_mpnet',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\mpnet\\configuration_mpnet.py',
   'PYMODULE'),
  ('transformers.models.mobilevit',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\mobilevit\\__init__.py',
   'PYMODULE'),
  ('transformers.models.mobilevit.modeling_mobilevit',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\mobilevit\\modeling_mobilevit.py',
   'PYMODULE'),
  ('transformers.models.mobilevit.feature_extraction_mobilevit',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\mobilevit\\feature_extraction_mobilevit.py',
   'PYMODULE'),
  ('transformers.models.mobilevit.configuration_mobilevit',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\mobilevit\\configuration_mobilevit.py',
   'PYMODULE'),
  ('transformers.models.mobilebert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\mobilebert\\__init__.py',
   'PYMODULE'),
  ('transformers.models.mobilebert.modeling_tf_mobilebert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\mobilebert\\modeling_tf_mobilebert.py',
   'PYMODULE'),
  ('transformers.models.mobilebert.modeling_mobilebert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\mobilebert\\modeling_mobilebert.py',
   'PYMODULE'),
  ('transformers.models.mobilebert.tokenization_mobilebert_fast',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\mobilebert\\tokenization_mobilebert_fast.py',
   'PYMODULE'),
  ('transformers.models.mobilebert.tokenization_mobilebert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\mobilebert\\tokenization_mobilebert.py',
   'PYMODULE'),
  ('transformers.models.mobilebert.configuration_mobilebert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\mobilebert\\configuration_mobilebert.py',
   'PYMODULE'),
  ('transformers.models.mmbt',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\mmbt\\__init__.py',
   'PYMODULE'),
  ('transformers.models.mmbt.modeling_mmbt',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\mmbt\\modeling_mmbt.py',
   'PYMODULE'),
  ('transformers.models.mmbt.configuration_mmbt',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\mmbt\\configuration_mmbt.py',
   'PYMODULE'),
  ('transformers.models.mluke',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\mluke\\__init__.py',
   'PYMODULE'),
  ('transformers.models.mluke.tokenization_mluke',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\mluke\\tokenization_mluke.py',
   'PYMODULE'),
  ('transformers.models.megatron_gpt2',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\megatron_gpt2\\__init__.py',
   'PYMODULE'),
  ('transformers.models.megatron_bert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\megatron_bert\\__init__.py',
   'PYMODULE'),
  ('transformers.models.megatron_bert.modeling_megatron_bert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\megatron_bert\\modeling_megatron_bert.py',
   'PYMODULE'),
  ('transformers.models.megatron_bert.configuration_megatron_bert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\megatron_bert\\configuration_megatron_bert.py',
   'PYMODULE'),
  ('transformers.models.mctct',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\mctct\\__init__.py',
   'PYMODULE'),
  ('transformers.models.mctct.modeling_mctct',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\mctct\\modeling_mctct.py',
   'PYMODULE'),
  ('transformers.models.mctct.feature_extraction_mctct',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\mctct\\feature_extraction_mctct.py',
   'PYMODULE'),
  ('transformers.models.mctct.processing_mctct',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\mctct\\processing_mctct.py',
   'PYMODULE'),
  ('transformers.models.mctct.configuration_mctct',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\mctct\\configuration_mctct.py',
   'PYMODULE'),
  ('transformers.models.mbart50',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\mbart50\\__init__.py',
   'PYMODULE'),
  ('transformers.models.mbart50.tokenization_mbart50_fast',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\mbart50\\tokenization_mbart50_fast.py',
   'PYMODULE'),
  ('transformers.models.mbart50.tokenization_mbart50',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\mbart50\\tokenization_mbart50.py',
   'PYMODULE'),
  ('transformers.models.mbart',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\mbart\\__init__.py',
   'PYMODULE'),
  ('transformers.models.mbart.modeling_flax_mbart',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\mbart\\modeling_flax_mbart.py',
   'PYMODULE'),
  ('transformers.models.mbart.modeling_tf_mbart',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\mbart\\modeling_tf_mbart.py',
   'PYMODULE'),
  ('transformers.models.mbart.modeling_mbart',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\mbart\\modeling_mbart.py',
   'PYMODULE'),
  ('transformers.models.mbart.tokenization_mbart_fast',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\mbart\\tokenization_mbart_fast.py',
   'PYMODULE'),
  ('transformers.models.mbart.tokenization_mbart',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\mbart\\tokenization_mbart.py',
   'PYMODULE'),
  ('transformers.models.mbart.configuration_mbart',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\mbart\\configuration_mbart.py',
   'PYMODULE'),
  ('transformers.models.maskformer',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\maskformer\\__init__.py',
   'PYMODULE'),
  ('transformers.models.maskformer.modeling_maskformer',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\maskformer\\modeling_maskformer.py',
   'PYMODULE'),
  ('transformers.models.maskformer.feature_extraction_maskformer',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\maskformer\\feature_extraction_maskformer.py',
   'PYMODULE'),
  ('transformers.models.maskformer.configuration_maskformer',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\maskformer\\configuration_maskformer.py',
   'PYMODULE'),
  ('transformers.models.marian',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\marian\\__init__.py',
   'PYMODULE'),
  ('transformers.models.marian.modeling_flax_marian',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\marian\\modeling_flax_marian.py',
   'PYMODULE'),
  ('transformers.models.marian.modeling_tf_marian',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\marian\\modeling_tf_marian.py',
   'PYMODULE'),
  ('transformers.models.marian.modeling_marian',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\marian\\modeling_marian.py',
   'PYMODULE'),
  ('transformers.models.marian.tokenization_marian',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\marian\\tokenization_marian.py',
   'PYMODULE'),
  ('transformers.models.marian.configuration_marian',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\marian\\configuration_marian.py',
   'PYMODULE'),
  ('transformers.models.m2m_100',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\m2m_100\\__init__.py',
   'PYMODULE'),
  ('transformers.models.m2m_100.modeling_m2m_100',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\m2m_100\\modeling_m2m_100.py',
   'PYMODULE'),
  ('transformers.models.m2m_100.tokenization_m2m_100',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\m2m_100\\tokenization_m2m_100.py',
   'PYMODULE'),
  ('transformers.models.m2m_100.configuration_m2m_100',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\m2m_100\\configuration_m2m_100.py',
   'PYMODULE'),
  ('transformers.models.lxmert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\lxmert\\__init__.py',
   'PYMODULE'),
  ('transformers.models.lxmert.modeling_tf_lxmert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\lxmert\\modeling_tf_lxmert.py',
   'PYMODULE'),
  ('transformers.models.lxmert.modeling_lxmert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\lxmert\\modeling_lxmert.py',
   'PYMODULE'),
  ('transformers.models.lxmert.tokenization_lxmert_fast',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\lxmert\\tokenization_lxmert_fast.py',
   'PYMODULE'),
  ('transformers.models.lxmert.tokenization_lxmert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\lxmert\\tokenization_lxmert.py',
   'PYMODULE'),
  ('transformers.models.lxmert.configuration_lxmert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\lxmert\\configuration_lxmert.py',
   'PYMODULE'),
  ('transformers.models.luke',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\luke\\__init__.py',
   'PYMODULE'),
  ('transformers.models.luke.modeling_luke',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\luke\\modeling_luke.py',
   'PYMODULE'),
  ('transformers.models.luke.tokenization_luke',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\luke\\tokenization_luke.py',
   'PYMODULE'),
  ('transformers.models.luke.configuration_luke',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\luke\\configuration_luke.py',
   'PYMODULE'),
  ('transformers.models.longt5',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\longt5\\__init__.py',
   'PYMODULE'),
  ('transformers.models.longt5.modeling_flax_longt5',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\longt5\\modeling_flax_longt5.py',
   'PYMODULE'),
  ('transformers.models.longt5.modeling_longt5',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\longt5\\modeling_longt5.py',
   'PYMODULE'),
  ('transformers.models.longt5.configuration_longt5',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\longt5\\configuration_longt5.py',
   'PYMODULE'),
  ('transformers.models.longformer',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\longformer\\__init__.py',
   'PYMODULE'),
  ('transformers.models.longformer.modeling_tf_longformer',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\longformer\\modeling_tf_longformer.py',
   'PYMODULE'),
  ('transformers.models.longformer.modeling_longformer',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\longformer\\modeling_longformer.py',
   'PYMODULE'),
  ('transformers.models.longformer.tokenization_longformer_fast',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\longformer\\tokenization_longformer_fast.py',
   'PYMODULE'),
  ('transformers.models.longformer.tokenization_longformer',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\longformer\\tokenization_longformer.py',
   'PYMODULE'),
  ('transformers.models.longformer.configuration_longformer',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\longformer\\configuration_longformer.py',
   'PYMODULE'),
  ('transformers.models.levit',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\levit\\__init__.py',
   'PYMODULE'),
  ('transformers.models.levit.modeling_levit',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\levit\\modeling_levit.py',
   'PYMODULE'),
  ('transformers.models.levit.feature_extraction_levit',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\levit\\feature_extraction_levit.py',
   'PYMODULE'),
  ('transformers.models.levit.configuration_levit',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\levit\\configuration_levit.py',
   'PYMODULE'),
  ('transformers.models.led',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\led\\__init__.py',
   'PYMODULE'),
  ('transformers.models.led.modeling_tf_led',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\led\\modeling_tf_led.py',
   'PYMODULE'),
  ('transformers.models.led.modeling_led',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\led\\modeling_led.py',
   'PYMODULE'),
  ('transformers.models.led.tokenization_led_fast',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\led\\tokenization_led_fast.py',
   'PYMODULE'),
  ('transformers.models.bart.tokenization_bart_fast',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\bart\\tokenization_bart_fast.py',
   'PYMODULE'),
  ('transformers.models.bart.tokenization_bart',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\bart\\tokenization_bart.py',
   'PYMODULE'),
  ('transformers.models.led.tokenization_led',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\led\\tokenization_led.py',
   'PYMODULE'),
  ('transformers.models.led.configuration_led',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\led\\configuration_led.py',
   'PYMODULE'),
  ('transformers.models.layoutxlm',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\layoutxlm\\__init__.py',
   'PYMODULE'),
  ('transformers.models.layoutxlm.tokenization_layoutxlm_fast',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\layoutxlm\\tokenization_layoutxlm_fast.py',
   'PYMODULE'),
  ('transformers.models.layoutxlm.tokenization_layoutxlm',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\layoutxlm\\tokenization_layoutxlm.py',
   'PYMODULE'),
  ('transformers.models.layoutxlm.processing_layoutxlm',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\layoutxlm\\processing_layoutxlm.py',
   'PYMODULE'),
  ('transformers.models.layoutlmv3',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\layoutlmv3\\__init__.py',
   'PYMODULE'),
  ('transformers.models.layoutlmv3.feature_extraction_layoutlmv3',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\layoutlmv3\\feature_extraction_layoutlmv3.py',
   'PYMODULE'),
  ('transformers.models.layoutlmv3.modeling_layoutlmv3',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\layoutlmv3\\modeling_layoutlmv3.py',
   'PYMODULE'),
  ('transformers.models.layoutlmv3.tokenization_layoutlmv3_fast',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\layoutlmv3\\tokenization_layoutlmv3_fast.py',
   'PYMODULE'),
  ('transformers.models.layoutlmv3.tokenization_layoutlmv3',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\layoutlmv3\\tokenization_layoutlmv3.py',
   'PYMODULE'),
  ('transformers.models.layoutlmv3.processing_layoutlmv3',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\layoutlmv3\\processing_layoutlmv3.py',
   'PYMODULE'),
  ('transformers.models.layoutlmv3.configuration_layoutlmv3',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\layoutlmv3\\configuration_layoutlmv3.py',
   'PYMODULE'),
  ('transformers.models.layoutlmv2',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\layoutlmv2\\__init__.py',
   'PYMODULE'),
  ('transformers.models.layoutlmv2.modeling_layoutlmv2',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\layoutlmv2\\modeling_layoutlmv2.py',
   'PYMODULE'),
  ('transformers.models.layoutlmv2.feature_extraction_layoutlmv2',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\layoutlmv2\\feature_extraction_layoutlmv2.py',
   'PYMODULE'),
  ('transformers.models.layoutlmv2.tokenization_layoutlmv2_fast',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\layoutlmv2\\tokenization_layoutlmv2_fast.py',
   'PYMODULE'),
  ('transformers.models.layoutlmv2.tokenization_layoutlmv2',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\layoutlmv2\\tokenization_layoutlmv2.py',
   'PYMODULE'),
  ('transformers.models.layoutlmv2.processing_layoutlmv2',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\layoutlmv2\\processing_layoutlmv2.py',
   'PYMODULE'),
  ('transformers.models.layoutlmv2.configuration_layoutlmv2',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\layoutlmv2\\configuration_layoutlmv2.py',
   'PYMODULE'),
  ('transformers.models.layoutlm',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\layoutlm\\__init__.py',
   'PYMODULE'),
  ('transformers.models.layoutlm.modeling_tf_layoutlm',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\layoutlm\\modeling_tf_layoutlm.py',
   'PYMODULE'),
  ('transformers.models.layoutlm.modeling_layoutlm',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\layoutlm\\modeling_layoutlm.py',
   'PYMODULE'),
  ('transformers.models.layoutlm.tokenization_layoutlm_fast',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\layoutlm\\tokenization_layoutlm_fast.py',
   'PYMODULE'),
  ('transformers.models.layoutlm.tokenization_layoutlm',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\layoutlm\\tokenization_layoutlm.py',
   'PYMODULE'),
  ('transformers.models.layoutlm.configuration_layoutlm',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\layoutlm\\configuration_layoutlm.py',
   'PYMODULE'),
  ('transformers.models.imagegpt',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\imagegpt\\__init__.py',
   'PYMODULE'),
  ('transformers.models.imagegpt.modeling_imagegpt',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\imagegpt\\modeling_imagegpt.py',
   'PYMODULE'),
  ('transformers.models.imagegpt.feature_extraction_imagegpt',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\imagegpt\\feature_extraction_imagegpt.py',
   'PYMODULE'),
  ('transformers.models.imagegpt.configuration_imagegpt',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\imagegpt\\configuration_imagegpt.py',
   'PYMODULE'),
  ('transformers.models.ibert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\ibert\\__init__.py',
   'PYMODULE'),
  ('transformers.models.ibert.modeling_ibert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\ibert\\modeling_ibert.py',
   'PYMODULE'),
  ('transformers.models.ibert.quant_modules',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\ibert\\quant_modules.py',
   'PYMODULE'),
  ('transformers.models.ibert.configuration_ibert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\ibert\\configuration_ibert.py',
   'PYMODULE'),
  ('transformers.models.hubert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\hubert\\__init__.py',
   'PYMODULE'),
  ('transformers.models.hubert.modeling_tf_hubert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\hubert\\modeling_tf_hubert.py',
   'PYMODULE'),
  ('transformers.models.hubert.modeling_hubert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\hubert\\modeling_hubert.py',
   'PYMODULE'),
  ('transformers.models.hubert.configuration_hubert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\hubert\\configuration_hubert.py',
   'PYMODULE'),
  ('transformers.models.herbert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\herbert\\__init__.py',
   'PYMODULE'),
  ('transformers.models.herbert.tokenization_herbert_fast',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\herbert\\tokenization_herbert_fast.py',
   'PYMODULE'),
  ('transformers.models.herbert.tokenization_herbert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\herbert\\tokenization_herbert.py',
   'PYMODULE'),
  ('transformers.models.groupvit',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\groupvit\\__init__.py',
   'PYMODULE'),
  ('transformers.models.groupvit.modeling_groupvit',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\groupvit\\modeling_groupvit.py',
   'PYMODULE'),
  ('transformers.models.groupvit.configuration_groupvit',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\groupvit\\configuration_groupvit.py',
   'PYMODULE'),
  ('transformers.models.gptj',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\gptj\\__init__.py',
   'PYMODULE'),
  ('transformers.models.gptj.modeling_flax_gptj',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\gptj\\modeling_flax_gptj.py',
   'PYMODULE'),
  ('transformers.models.gptj.modeling_tf_gptj',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\gptj\\modeling_tf_gptj.py',
   'PYMODULE'),
  ('transformers.models.gptj.modeling_gptj',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\gptj\\modeling_gptj.py',
   'PYMODULE'),
  ('transformers.models.gptj.configuration_gptj',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\gptj\\configuration_gptj.py',
   'PYMODULE'),
  ('transformers.models.gpt_neox',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\gpt_neox\\__init__.py',
   'PYMODULE'),
  ('transformers.models.gpt_neox.modeling_gpt_neox',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\gpt_neox\\modeling_gpt_neox.py',
   'PYMODULE'),
  ('transformers.models.gpt_neox.tokenization_gpt_neox_fast',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\gpt_neox\\tokenization_gpt_neox_fast.py',
   'PYMODULE'),
  ('transformers.pipelines.conversational',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\pipelines\\conversational.py',
   'PYMODULE'),
  ('transformers.pipelines',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\pipelines\\__init__.py',
   'PYMODULE'),
  ('transformers.pipelines.zero_shot_image_classification',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\pipelines\\zero_shot_image_classification.py',
   'PYMODULE'),
  ('transformers.pipelines.zero_shot_classification',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\pipelines\\zero_shot_classification.py',
   'PYMODULE'),
  ('transformers.pipelines.visual_question_answering',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\pipelines\\visual_question_answering.py',
   'PYMODULE'),
  ('transformers.pipelines.token_classification',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\pipelines\\token_classification.py',
   'PYMODULE'),
  ('transformers.pipelines.text_generation',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\pipelines\\text_generation.py',
   'PYMODULE'),
  ('transformers.pipelines.text_classification',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\pipelines\\text_classification.py',
   'PYMODULE'),
  ('transformers.pipelines.text2text_generation',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\pipelines\\text2text_generation.py',
   'PYMODULE'),
  ('transformers.pipelines.table_question_answering',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\pipelines\\table_question_answering.py',
   'PYMODULE'),
  ('transformers.pipelines.question_answering',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\pipelines\\question_answering.py',
   'PYMODULE'),
  ('transformers.pipelines.object_detection',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\pipelines\\object_detection.py',
   'PYMODULE'),
  ('transformers.pipelines.image_segmentation',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\pipelines\\image_segmentation.py',
   'PYMODULE'),
  ('transformers.pipelines.image_classification',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\pipelines\\image_classification.py',
   'PYMODULE'),
  ('transformers.pipelines.fill_mask',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\pipelines\\fill_mask.py',
   'PYMODULE'),
  ('transformers.pipelines.feature_extraction',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\pipelines\\feature_extraction.py',
   'PYMODULE'),
  ('transformers.pipelines.automatic_speech_recognition',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\pipelines\\automatic_speech_recognition.py',
   'PYMODULE'),
  ('transformers.pipelines.audio_utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\pipelines\\audio_utils.py',
   'PYMODULE'),
  ('transformers.pipelines.audio_classification',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\pipelines\\audio_classification.py',
   'PYMODULE'),
  ('transformers.pipelines.base',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\pipelines\\base.py',
   'PYMODULE'),
  ('transformers.pipelines.pt_utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\pipelines\\pt_utils.py',
   'PYMODULE'),
  ('transformers.models.gpt_neox.configuration_gpt_neox',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\gpt_neox\\configuration_gpt_neox.py',
   'PYMODULE'),
  ('transformers.models.gpt_neo',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\gpt_neo\\__init__.py',
   'PYMODULE'),
  ('transformers.models.gpt_neo.modeling_flax_gpt_neo',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\gpt_neo\\modeling_flax_gpt_neo.py',
   'PYMODULE'),
  ('transformers.models.gpt_neo.modeling_gpt_neo',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\gpt_neo\\modeling_gpt_neo.py',
   'PYMODULE'),
  ('transformers.models.gpt_neo.configuration_gpt_neo',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\gpt_neo\\configuration_gpt_neo.py',
   'PYMODULE'),
  ('transformers.models.gpt2',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\gpt2\\__init__.py',
   'PYMODULE'),
  ('transformers.models.gpt2.modeling_flax_gpt2',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\gpt2\\modeling_flax_gpt2.py',
   'PYMODULE'),
  ('transformers.models.gpt2.modeling_tf_gpt2',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\gpt2\\modeling_tf_gpt2.py',
   'PYMODULE'),
  ('transformers.models.gpt2.modeling_gpt2',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\gpt2\\modeling_gpt2.py',
   'PYMODULE'),
  ('transformers.models.gpt2.tokenization_gpt2_fast',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\gpt2\\tokenization_gpt2_fast.py',
   'PYMODULE'),
  ('transformers.models.gpt2.tokenization_gpt2',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\gpt2\\tokenization_gpt2.py',
   'PYMODULE'),
  ('transformers.models.gpt2.configuration_gpt2',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\gpt2\\configuration_gpt2.py',
   'PYMODULE'),
  ('transformers.models.glpn',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\glpn\\__init__.py',
   'PYMODULE'),
  ('transformers.models.glpn.modeling_glpn',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\glpn\\modeling_glpn.py',
   'PYMODULE'),
  ('transformers.models.glpn.feature_extraction_glpn',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\glpn\\feature_extraction_glpn.py',
   'PYMODULE'),
  ('transformers.models.glpn.configuration_glpn',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\glpn\\configuration_glpn.py',
   'PYMODULE'),
  ('transformers.models.funnel',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\funnel\\__init__.py',
   'PYMODULE'),
  ('transformers.models.funnel.modeling_tf_funnel',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\funnel\\modeling_tf_funnel.py',
   'PYMODULE'),
  ('transformers.models.funnel.modeling_funnel',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\funnel\\modeling_funnel.py',
   'PYMODULE'),
  ('transformers.models.funnel.tokenization_funnel_fast',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\funnel\\tokenization_funnel_fast.py',
   'PYMODULE'),
  ('transformers.models.funnel.tokenization_funnel',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\funnel\\tokenization_funnel.py',
   'PYMODULE'),
  ('transformers.models.funnel.configuration_funnel',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\funnel\\configuration_funnel.py',
   'PYMODULE'),
  ('transformers.models.fsmt',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\fsmt\\__init__.py',
   'PYMODULE'),
  ('transformers.models.fsmt.modeling_fsmt',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\fsmt\\modeling_fsmt.py',
   'PYMODULE'),
  ('transformers.models.fsmt.tokenization_fsmt',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\fsmt\\tokenization_fsmt.py',
   'PYMODULE'),
  ('transformers.models.fsmt.configuration_fsmt',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\fsmt\\configuration_fsmt.py',
   'PYMODULE'),
  ('transformers.models.fnet',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\fnet\\__init__.py',
   'PYMODULE'),
  ('transformers.models.fnet.modeling_fnet',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\fnet\\modeling_fnet.py',
   'PYMODULE'),
  ('transformers.models.fnet.tokenization_fnet_fast',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\fnet\\tokenization_fnet_fast.py',
   'PYMODULE'),
  ('transformers.models.fnet.tokenization_fnet',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\fnet\\tokenization_fnet.py',
   'PYMODULE'),
  ('transformers.models.fnet.configuration_fnet',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\fnet\\configuration_fnet.py',
   'PYMODULE'),
  ('transformers.models.flava',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\flava\\__init__.py',
   'PYMODULE'),
  ('transformers.models.flava.modeling_flava',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\flava\\modeling_flava.py',
   'PYMODULE'),
  ('transformers.utils.doc',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\utils\\doc.py',
   'PYMODULE'),
  ('transformers.models.flava.processing_flava',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\flava\\processing_flava.py',
   'PYMODULE'),
  ('transformers.models.flava.feature_extraction_flava',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\flava\\feature_extraction_flava.py',
   'PYMODULE'),
  ('transformers.models.flava.configuration_flava',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\flava\\configuration_flava.py',
   'PYMODULE'),
  ('transformers.models.flaubert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\flaubert\\__init__.py',
   'PYMODULE'),
  ('transformers.models.flaubert.modeling_tf_flaubert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\flaubert\\modeling_tf_flaubert.py',
   'PYMODULE'),
  ('transformers.models.flaubert.modeling_flaubert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\flaubert\\modeling_flaubert.py',
   'PYMODULE'),
  ('transformers.models.flaubert.tokenization_flaubert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\flaubert\\tokenization_flaubert.py',
   'PYMODULE'),
  ('transformers.models.flaubert.configuration_flaubert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\flaubert\\configuration_flaubert.py',
   'PYMODULE'),
  ('transformers.models.electra',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\electra\\__init__.py',
   'PYMODULE'),
  ('transformers.models.electra.modeling_flax_electra',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\electra\\modeling_flax_electra.py',
   'PYMODULE'),
  ('transformers.models.electra.modeling_tf_electra',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\electra\\modeling_tf_electra.py',
   'PYMODULE'),
  ('transformers.models.electra.modeling_electra',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\electra\\modeling_electra.py',
   'PYMODULE'),
  ('transformers.models.electra.tokenization_electra_fast',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\electra\\tokenization_electra_fast.py',
   'PYMODULE'),
  ('transformers.models.electra.tokenization_electra',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\electra\\tokenization_electra.py',
   'PYMODULE'),
  ('transformers.models.electra.configuration_electra',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\electra\\configuration_electra.py',
   'PYMODULE'),
  ('transformers.models.dpt',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\dpt\\__init__.py',
   'PYMODULE'),
  ('transformers.models.dpt.modeling_dpt',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\dpt\\modeling_dpt.py',
   'PYMODULE'),
  ('transformers.models.dpt.feature_extraction_dpt',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\dpt\\feature_extraction_dpt.py',
   'PYMODULE'),
  ('transformers.models.dpt.configuration_dpt',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\dpt\\configuration_dpt.py',
   'PYMODULE'),
  ('transformers.models.dpr',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\dpr\\__init__.py',
   'PYMODULE'),
  ('transformers.models.dpr.modeling_tf_dpr',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\dpr\\modeling_tf_dpr.py',
   'PYMODULE'),
  ('transformers.models.dpr.modeling_dpr',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\dpr\\modeling_dpr.py',
   'PYMODULE'),
  ('transformers.models.dpr.tokenization_dpr_fast',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\dpr\\tokenization_dpr_fast.py',
   'PYMODULE'),
  ('transformers.models.dpr.tokenization_dpr',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\dpr\\tokenization_dpr.py',
   'PYMODULE'),
  ('transformers.models.dpr.configuration_dpr',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\dpr\\configuration_dpr.py',
   'PYMODULE'),
  ('transformers.models.dit',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\dit\\__init__.py',
   'PYMODULE'),
  ('transformers.models.distilbert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\distilbert\\__init__.py',
   'PYMODULE'),
  ('transformers.models.distilbert.modeling_flax_distilbert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\distilbert\\modeling_flax_distilbert.py',
   'PYMODULE'),
  ('transformers.models.distilbert.modeling_tf_distilbert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\distilbert\\modeling_tf_distilbert.py',
   'PYMODULE'),
  ('transformers.models.distilbert.modeling_distilbert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\distilbert\\modeling_distilbert.py',
   'PYMODULE'),
  ('transformers.models.distilbert.tokenization_distilbert_fast',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\distilbert\\tokenization_distilbert_fast.py',
   'PYMODULE'),
  ('transformers.models.distilbert.tokenization_distilbert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\distilbert\\tokenization_distilbert.py',
   'PYMODULE'),
  ('transformers.models.distilbert.configuration_distilbert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\distilbert\\configuration_distilbert.py',
   'PYMODULE'),
  ('transformers.models.dialogpt',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\dialogpt\\__init__.py',
   'PYMODULE'),
  ('transformers.models.detr',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\detr\\__init__.py',
   'PYMODULE'),
  ('transformers.models.detr.modeling_detr',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\detr\\modeling_detr.py',
   'PYMODULE'),
  ('transformers.models.detr.configuration_detr',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\detr\\configuration_detr.py',
   'PYMODULE'),
  ('transformers.models.deit',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\deit\\__init__.py',
   'PYMODULE'),
  ('transformers.models.deit.modeling_tf_deit',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\deit\\modeling_tf_deit.py',
   'PYMODULE'),
  ('transformers.models.deit.modeling_deit',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\deit\\modeling_deit.py',
   'PYMODULE'),
  ('transformers.models.deit.feature_extraction_deit',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\deit\\feature_extraction_deit.py',
   'PYMODULE'),
  ('transformers.models.deit.configuration_deit',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\deit\\configuration_deit.py',
   'PYMODULE'),
  ('transformers.models.decision_transformer',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\decision_transformer\\__init__.py',
   'PYMODULE'),
  ('transformers.models.decision_transformer.modeling_decision_transformer',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\decision_transformer\\modeling_decision_transformer.py',
   'PYMODULE'),
  ('transformers.models.decision_transformer.configuration_decision_transformer',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\decision_transformer\\configuration_decision_transformer.py',
   'PYMODULE'),
  ('transformers.models.deberta_v2',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\deberta_v2\\__init__.py',
   'PYMODULE'),
  ('transformers.models.deberta_v2.modeling_deberta_v2',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\deberta_v2\\modeling_deberta_v2.py',
   'PYMODULE'),
  ('transformers.models.deberta_v2.modeling_tf_deberta_v2',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\deberta_v2\\modeling_tf_deberta_v2.py',
   'PYMODULE'),
  ('transformers.models.deberta_v2.tokenization_deberta_v2_fast',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\deberta_v2\\tokenization_deberta_v2_fast.py',
   'PYMODULE'),
  ('transformers.models.deberta_v2.tokenization_deberta_v2',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\deberta_v2\\tokenization_deberta_v2.py',
   'PYMODULE'),
  ('transformers.models.deberta_v2.configuration_deberta_v2',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\deberta_v2\\configuration_deberta_v2.py',
   'PYMODULE'),
  ('transformers.models.deberta',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\deberta\\__init__.py',
   'PYMODULE'),
  ('transformers.models.deberta.modeling_tf_deberta',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\deberta\\modeling_tf_deberta.py',
   'PYMODULE'),
  ('transformers.models.deberta.modeling_deberta',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\deberta\\modeling_deberta.py',
   'PYMODULE'),
  ('transformers.models.deberta.tokenization_deberta_fast',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\deberta\\tokenization_deberta_fast.py',
   'PYMODULE'),
  ('transformers.models.deberta.tokenization_deberta',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\deberta\\tokenization_deberta.py',
   'PYMODULE'),
  ('transformers.models.deberta.configuration_deberta',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\deberta\\configuration_deberta.py',
   'PYMODULE'),
  ('transformers.models.data2vec',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\data2vec\\__init__.py',
   'PYMODULE'),
  ('transformers.models.data2vec.modeling_tf_data2vec_vision',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\data2vec\\modeling_tf_data2vec_vision.py',
   'PYMODULE'),
  ('transformers.models.data2vec.modeling_data2vec_vision',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\data2vec\\modeling_data2vec_vision.py',
   'PYMODULE'),
  ('transformers.models.data2vec.modeling_data2vec_text',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\data2vec\\modeling_data2vec_text.py',
   'PYMODULE'),
  ('transformers.models.data2vec.modeling_data2vec_audio',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\data2vec\\modeling_data2vec_audio.py',
   'PYMODULE'),
  ('transformers.models.data2vec.configuration_data2vec_vision',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\data2vec\\configuration_data2vec_vision.py',
   'PYMODULE'),
  ('transformers.models.data2vec.configuration_data2vec_text',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\data2vec\\configuration_data2vec_text.py',
   'PYMODULE'),
  ('transformers.models.data2vec.configuration_data2vec_audio',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\data2vec\\configuration_data2vec_audio.py',
   'PYMODULE'),
  ('transformers.models.cvt',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\cvt\\__init__.py',
   'PYMODULE'),
  ('transformers.models.cvt.modeling_cvt',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\cvt\\modeling_cvt.py',
   'PYMODULE'),
  ('transformers.models.cvt.configuration_cvt',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\cvt\\configuration_cvt.py',
   'PYMODULE'),
  ('transformers.models.ctrl',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\ctrl\\__init__.py',
   'PYMODULE'),
  ('transformers.models.ctrl.modeling_tf_ctrl',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\ctrl\\modeling_tf_ctrl.py',
   'PYMODULE'),
  ('transformers.models.ctrl.modeling_ctrl',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\ctrl\\modeling_ctrl.py',
   'PYMODULE'),
  ('transformers.models.ctrl.tokenization_ctrl',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\ctrl\\tokenization_ctrl.py',
   'PYMODULE'),
  ('transformers.models.ctrl.configuration_ctrl',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\ctrl\\configuration_ctrl.py',
   'PYMODULE'),
  ('transformers.models.cpm',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\cpm\\__init__.py',
   'PYMODULE'),
  ('transformers.models.cpm.tokenization_cpm_fast',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\cpm\\tokenization_cpm_fast.py',
   'PYMODULE'),
  ('transformers.models.cpm.tokenization_cpm',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\cpm\\tokenization_cpm.py',
   'PYMODULE'),
  ('transformers.models.convnext',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\convnext\\__init__.py',
   'PYMODULE'),
  ('transformers.models.convnext.modeling_convnext',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\convnext\\modeling_convnext.py',
   'PYMODULE'),
  ('transformers.models.convnext.feature_extraction_convnext',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\convnext\\feature_extraction_convnext.py',
   'PYMODULE'),
  ('transformers.models.convnext.configuration_convnext',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\convnext\\configuration_convnext.py',
   'PYMODULE'),
  ('transformers.models.convbert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\convbert\\__init__.py',
   'PYMODULE'),
  ('transformers.models.convbert.modeling_tf_convbert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\convbert\\modeling_tf_convbert.py',
   'PYMODULE'),
  ('transformers.models.convbert.modeling_convbert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\convbert\\modeling_convbert.py',
   'PYMODULE'),
  ('transformers.models.convbert.tokenization_convbert_fast',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\convbert\\tokenization_convbert_fast.py',
   'PYMODULE'),
  ('transformers.models.convbert.tokenization_convbert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\convbert\\tokenization_convbert.py',
   'PYMODULE'),
  ('transformers.models.convbert.configuration_convbert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\convbert\\configuration_convbert.py',
   'PYMODULE'),
  ('transformers.models.codegen',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\codegen\\__init__.py',
   'PYMODULE'),
  ('transformers.models.codegen.modeling_codegen',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\codegen\\modeling_codegen.py',
   'PYMODULE'),
  ('transformers.models.codegen.tokenization_codegen_fast',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\codegen\\tokenization_codegen_fast.py',
   'PYMODULE'),
  ('transformers.models.codegen.tokenization_codegen',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\codegen\\tokenization_codegen.py',
   'PYMODULE'),
  ('transformers.models.codegen.configuration_codegen',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\codegen\\configuration_codegen.py',
   'PYMODULE'),
  ('transformers.models.clip',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\clip\\__init__.py',
   'PYMODULE'),
  ('transformers.models.clip.modeling_flax_clip',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\clip\\modeling_flax_clip.py',
   'PYMODULE'),
  ('transformers.models.clip.modeling_tf_clip',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\clip\\modeling_tf_clip.py',
   'PYMODULE'),
  ('transformers.models.clip.processing_clip',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\clip\\processing_clip.py',
   'PYMODULE'),
  ('transformers.models.clip.feature_extraction_clip',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\clip\\feature_extraction_clip.py',
   'PYMODULE'),
  ('transformers.models.clip.tokenization_clip_fast',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\clip\\tokenization_clip_fast.py',
   'PYMODULE'),
  ('transformers.models.clip.tokenization_clip',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\clip\\tokenization_clip.py',
   'PYMODULE'),
  ('transformers.models.canine',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\canine\\__init__.py',
   'PYMODULE'),
  ('transformers.models.canine.modeling_canine',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\canine\\modeling_canine.py',
   'PYMODULE'),
  ('transformers.models.canine.tokenization_canine',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\canine\\tokenization_canine.py',
   'PYMODULE'),
  ('transformers.models.canine.configuration_canine',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\canine\\configuration_canine.py',
   'PYMODULE'),
  ('transformers.models.camembert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\camembert\\__init__.py',
   'PYMODULE'),
  ('transformers.models.camembert.modeling_tf_camembert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\camembert\\modeling_tf_camembert.py',
   'PYMODULE'),
  ('transformers.models.camembert.modeling_camembert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\camembert\\modeling_camembert.py',
   'PYMODULE'),
  ('transformers.models.camembert.tokenization_camembert_fast',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\camembert\\tokenization_camembert_fast.py',
   'PYMODULE'),
  ('transformers.models.camembert.tokenization_camembert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\camembert\\tokenization_camembert.py',
   'PYMODULE'),
  ('transformers.models.camembert.configuration_camembert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\camembert\\configuration_camembert.py',
   'PYMODULE'),
  ('transformers.models.byt5',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\byt5\\__init__.py',
   'PYMODULE'),
  ('transformers.models.byt5.tokenization_byt5',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\byt5\\tokenization_byt5.py',
   'PYMODULE'),
  ('transformers.models.bort',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\bort\\__init__.py',
   'PYMODULE'),
  ('transformers.models.bloom',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\bloom\\__init__.py',
   'PYMODULE'),
  ('transformers.models.bloom.modeling_bloom',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\bloom\\modeling_bloom.py',
   'PYMODULE'),
  ('transformers.models.bloom.tokenization_bloom_fast',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\bloom\\tokenization_bloom_fast.py',
   'PYMODULE'),
  ('transformers.models.bloom.configuration_bloom',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\bloom\\configuration_bloom.py',
   'PYMODULE'),
  ('transformers.models.blenderbot_small',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\blenderbot_small\\__init__.py',
   'PYMODULE'),
  ('transformers.models.blenderbot_small.modeling_flax_blenderbot_small',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\blenderbot_small\\modeling_flax_blenderbot_small.py',
   'PYMODULE'),
  ('transformers.models.blenderbot_small.modeling_tf_blenderbot_small',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\blenderbot_small\\modeling_tf_blenderbot_small.py',
   'PYMODULE'),
  ('transformers.models.blenderbot_small.modeling_blenderbot_small',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\blenderbot_small\\modeling_blenderbot_small.py',
   'PYMODULE'),
  ('transformers.models.blenderbot_small.tokenization_blenderbot_small_fast',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\blenderbot_small\\tokenization_blenderbot_small_fast.py',
   'PYMODULE'),
  ('transformers.models.blenderbot_small.tokenization_blenderbot_small',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\blenderbot_small\\tokenization_blenderbot_small.py',
   'PYMODULE'),
  ('transformers.models.blenderbot_small.configuration_blenderbot_small',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\blenderbot_small\\configuration_blenderbot_small.py',
   'PYMODULE'),
  ('transformers.models.blenderbot',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\blenderbot\\__init__.py',
   'PYMODULE'),
  ('transformers.models.blenderbot.modeling_flax_blenderbot',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\blenderbot\\modeling_flax_blenderbot.py',
   'PYMODULE'),
  ('transformers.models.blenderbot.modeling_tf_blenderbot',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\blenderbot\\modeling_tf_blenderbot.py',
   'PYMODULE'),
  ('transformers.models.blenderbot.modeling_blenderbot',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\blenderbot\\modeling_blenderbot.py',
   'PYMODULE'),
  ('transformers.models.blenderbot.tokenization_blenderbot_fast',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\blenderbot\\tokenization_blenderbot_fast.py',
   'PYMODULE'),
  ('transformers.models.blenderbot.tokenization_blenderbot',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\blenderbot\\tokenization_blenderbot.py',
   'PYMODULE'),
  ('transformers.models.blenderbot.configuration_blenderbot',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\blenderbot\\configuration_blenderbot.py',
   'PYMODULE'),
  ('transformers.models.bigbird_pegasus',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\bigbird_pegasus\\__init__.py',
   'PYMODULE'),
  ('transformers.models.bigbird_pegasus.modeling_bigbird_pegasus',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\bigbird_pegasus\\modeling_bigbird_pegasus.py',
   'PYMODULE'),
  ('transformers.models.bigbird_pegasus.configuration_bigbird_pegasus',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\bigbird_pegasus\\configuration_bigbird_pegasus.py',
   'PYMODULE'),
  ('transformers.models.big_bird',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\big_bird\\__init__.py',
   'PYMODULE'),
  ('transformers.models.big_bird.modeling_flax_big_bird',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\big_bird\\modeling_flax_big_bird.py',
   'PYMODULE'),
  ('transformers.models.big_bird.modeling_big_bird',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\big_bird\\modeling_big_bird.py',
   'PYMODULE'),
  ('transformers.models.big_bird.tokenization_big_bird_fast',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\big_bird\\tokenization_big_bird_fast.py',
   'PYMODULE'),
  ('transformers.models.big_bird.tokenization_big_bird',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\big_bird\\tokenization_big_bird.py',
   'PYMODULE'),
  ('transformers.models.big_bird.configuration_big_bird',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\big_bird\\configuration_big_bird.py',
   'PYMODULE'),
  ('transformers.models.bertweet',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\bertweet\\__init__.py',
   'PYMODULE'),
  ('transformers.models.bertweet.tokenization_bertweet',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\bertweet\\tokenization_bertweet.py',
   'PYMODULE'),
  ('transformers.models.bert_japanese',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\bert_japanese\\__init__.py',
   'PYMODULE'),
  ('transformers.models.bert_japanese.tokenization_bert_japanese',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\bert_japanese\\tokenization_bert_japanese.py',
   'PYMODULE'),
  ('transformers.models.bert_generation',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\bert_generation\\__init__.py',
   'PYMODULE'),
  ('transformers.models.bert_generation.modeling_bert_generation',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\bert_generation\\modeling_bert_generation.py',
   'PYMODULE'),
  ('transformers.models.bert_generation.tokenization_bert_generation',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\bert_generation\\tokenization_bert_generation.py',
   'PYMODULE'),
  ('transformers.models.bert_generation.configuration_bert_generation',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\bert_generation\\configuration_bert_generation.py',
   'PYMODULE'),
  ('transformers.models.beit',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\beit\\__init__.py',
   'PYMODULE'),
  ('transformers.models.beit.modeling_flax_beit',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\beit\\modeling_flax_beit.py',
   'PYMODULE'),
  ('transformers.models.beit.modeling_beit',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\beit\\modeling_beit.py',
   'PYMODULE'),
  ('transformers.models.beit.feature_extraction_beit',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\beit\\feature_extraction_beit.py',
   'PYMODULE'),
  ('transformers.models.beit.configuration_beit',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\beit\\configuration_beit.py',
   'PYMODULE'),
  ('transformers.models.bartpho',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\bartpho\\__init__.py',
   'PYMODULE'),
  ('transformers.models.bartpho.tokenization_bartpho',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\bartpho\\tokenization_bartpho.py',
   'PYMODULE'),
  ('transformers.models.barthez',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\barthez\\__init__.py',
   'PYMODULE'),
  ('transformers.models.barthez.tokenization_barthez_fast',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\barthez\\tokenization_barthez_fast.py',
   'PYMODULE'),
  ('transformers.models.barthez.tokenization_barthez',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\barthez\\tokenization_barthez.py',
   'PYMODULE'),
  ('transformers.models.bart',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\bart\\__init__.py',
   'PYMODULE'),
  ('transformers.models.bart.modeling_flax_bart',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\bart\\modeling_flax_bart.py',
   'PYMODULE'),
  ('transformers.models.bart.modeling_tf_bart',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\bart\\modeling_tf_bart.py',
   'PYMODULE'),
  ('transformers.models.bart.modeling_bart',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\bart\\modeling_bart.py',
   'PYMODULE'),
  ('transformers.models.bart.configuration_bart',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\bart\\configuration_bart.py',
   'PYMODULE'),
  ('transformers.models.albert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\albert\\__init__.py',
   'PYMODULE'),
  ('transformers.models.albert.modeling_flax_albert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\albert\\modeling_flax_albert.py',
   'PYMODULE'),
  ('transformers.models.albert.modeling_tf_albert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\albert\\modeling_tf_albert.py',
   'PYMODULE'),
  ('transformers.models.albert.modeling_albert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\albert\\modeling_albert.py',
   'PYMODULE'),
  ('transformers.models.albert.tokenization_albert_fast',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\albert\\tokenization_albert_fast.py',
   'PYMODULE'),
  ('transformers.models.albert.tokenization_albert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\albert\\tokenization_albert.py',
   'PYMODULE'),
  ('transformers.models.albert.configuration_albert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\albert\\configuration_albert.py',
   'PYMODULE'),
  ('transformers.models.bert.tokenization_bert_fast',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\bert\\tokenization_bert_fast.py',
   'PYMODULE'),
  ('transformers.models.bert.tokenization_bert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\bert\\tokenization_bert.py',
   'PYMODULE'),
  ('transformers.models.bert.configuration_bert',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\models\\bert\\configuration_bert.py',
   'PYMODULE'),
  ('transformers.utils.logging',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\utils\\logging.py',
   'PYMODULE'),
  ('transformers.pytorch_utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\pytorch_utils.py',
   'PYMODULE'),
  ('transformers.modeling_utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\modeling_utils.py',
   'PYMODULE'),
  ('transformers.generation_utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\generation_utils.py',
   'PYMODULE'),
  ('transformers.activations',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\activations.py',
   'PYMODULE'),
  ('torch.utils.checkpoint',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\utils\\checkpoint.py',
   'PYMODULE'),
  ('kbnlp.task.models.information_extraction',
   'G:\\kubao-aimp\\aimp\\tools\\kbnlp\\task\\models\\information_extraction\\__init__.py',
   'PYMODULE'),
  ('transformers.utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\utils\\__init__.py',
   'PYMODULE'),
  ('transformers.utils.dummy_flax_objects',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\utils\\dummy_flax_objects.py',
   'PYMODULE'),
  ('transformers.utils.dummy_tf_objects',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\utils\\dummy_tf_objects.py',
   'PYMODULE'),
  ('transformers.utils.dummy_pt_objects',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\utils\\dummy_pt_objects.py',
   'PYMODULE'),
  ('transformers.utils.dummy_scatter_objects',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\utils\\dummy_scatter_objects.py',
   'PYMODULE'),
  ('transformers.utils.dummy_timm_objects',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\utils\\dummy_timm_objects.py',
   'PYMODULE'),
  ('transformers.utils.dummy_vision_objects',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\utils\\dummy_vision_objects.py',
   'PYMODULE'),
  ('transformers.utils.dummy_sentencepiece_and_speech_objects',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\utils\\dummy_sentencepiece_and_speech_objects.py',
   'PYMODULE'),
  ('transformers.utils.dummy_tensorflow_text_objects',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\utils\\dummy_tensorflow_text_objects.py',
   'PYMODULE'),
  ('transformers.utils.dummy_speech_objects',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\utils\\dummy_speech_objects.py',
   'PYMODULE'),
  ('transformers.utils.dummy_sentencepiece_and_tokenizers_objects',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\utils\\dummy_sentencepiece_and_tokenizers_objects.py',
   'PYMODULE'),
  ('torch.nn',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\__init__.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\dataclasses.py',
   'PYMODULE'),
  ('kbnlp.scripts.information_extraction.metric',
   'G:\\kubao-aimp\\aimp\\tools\\kbnlp\\scripts\\information_extraction\\metric.py',
   'PYMODULE'),
  ('transformers',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\__init__.py',
   'PYMODULE'),
  ('transformers.trainer_tf',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\trainer_tf.py',
   'PYMODULE'),
  ('transformers.optimization_tf',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\optimization_tf.py',
   'PYMODULE'),
  ('transformers.keras_callbacks',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\keras_callbacks.py',
   'PYMODULE'),
  ('transformers.benchmark.benchmark_tf',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\benchmark\\benchmark_tf.py',
   'PYMODULE'),
  ('transformers.benchmark',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\benchmark\\__init__.py',
   'PYMODULE'),
  ('transformers.benchmark.benchmark_utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\benchmark\\benchmark_utils.py',
   'PYMODULE'),
  ('transformers.benchmark.benchmark_args_utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\benchmark\\benchmark_args_utils.py',
   'PYMODULE'),
  ('timeit',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\timeit.py',
   'PYMODULE'),
  ('transformers.benchmark.benchmark_args_tf',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\benchmark\\benchmark_args_tf.py',
   'PYMODULE'),
  ('transformers.trainer_seq2seq',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\trainer_seq2seq.py',
   'PYMODULE'),
  ('transformers.data.datasets',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\data\\datasets\\__init__.py',
   'PYMODULE'),
  ('transformers.data.datasets.squad',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\data\\datasets\\squad.py',
   'PYMODULE'),
  ('transformers.data.datasets.language_modeling',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\data\\datasets\\language_modeling.py',
   'PYMODULE'),
  ('transformers.data.datasets.glue',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\data\\datasets\\glue.py',
   'PYMODULE'),
  ('transformers.benchmark.benchmark_args',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\benchmark\\benchmark_args.py',
   'PYMODULE'),
  ('transformers.benchmark.benchmark',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\benchmark\\benchmark.py',
   'PYMODULE'),
  ('transformers.training_args_tf',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\training_args_tf.py',
   'PYMODULE'),
  ('transformers.training_args_seq2seq',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\training_args_seq2seq.py',
   'PYMODULE'),
  ('transformers.hf_argparser',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\transformers\\hf_argparser.py',
   'PYMODULE'),
  ('torch.nn.parallel',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\parallel\\__init__.py',
   'PYMODULE'),
  ('torch.nn.parallel.distributed',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\parallel\\distributed.py',
   'PYMODULE'),
  ('torch.nn.parallel._functions',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\parallel\\_functions.py',
   'PYMODULE'),
  ('torch.nn.parallel.scatter_gather',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\parallel\\scatter_gather.py',
   'PYMODULE'),
  ('torch.nn.parallel.data_parallel',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\parallel\\data_parallel.py',
   'PYMODULE'),
  ('torch.nn.parallel.replicate',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\parallel\\replicate.py',
   'PYMODULE'),
  ('torch.nn.parallel.comm',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\parallel\\comm.py',
   'PYMODULE'),
  ('torch.nn.parallel.parallel_apply',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\parallel\\parallel_apply.py',
   'PYMODULE'),
  ('torch.optim',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\optim\\__init__.py',
   'PYMODULE'),
  ('torch.optim.swa_utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\optim\\swa_utils.py',
   'PYMODULE'),
  ('torch.optim.lbfgs',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\optim\\lbfgs.py',
   'PYMODULE'),
  ('torch.optim.rmsprop',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\optim\\rmsprop.py',
   'PYMODULE'),
  ('torch.optim.rprop',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\optim\\rprop.py',
   'PYMODULE'),
  ('torch.optim.sgd',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\optim\\sgd.py',
   'PYMODULE'),
  ('torch.optim.asgd',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\optim\\asgd.py',
   'PYMODULE'),
  ('torch.optim.adamax',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\optim\\adamax.py',
   'PYMODULE'),
  ('torch.optim.sparse_adam',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\optim\\sparse_adam.py',
   'PYMODULE'),
  ('torch.optim.adamw',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\optim\\adamw.py',
   'PYMODULE'),
  ('torch.optim.adam',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\optim\\adam.py',
   'PYMODULE'),
  ('torch.optim.adagrad',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\optim\\adagrad.py',
   'PYMODULE'),
  ('torch.optim.adadelta',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\optim\\adadelta.py',
   'PYMODULE'),
  ('torch.optim._functional',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\optim\\_functional.py',
   'PYMODULE'),
  ('torch.utils.data',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\utils\\data\\__init__.py',
   'PYMODULE'),
  ('torch.utils.data._decorator',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\utils\\data\\_decorator.py',
   'PYMODULE'),
  ('torch.utils.data._typing',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\utils\\data\\_typing.py',
   'PYMODULE'),
  ('torch.utils.data.dataloader',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\utils\\data\\dataloader.py',
   'PYMODULE'),
  ('torch.utils.data.dataset',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\utils\\data\\dataset.py',
   'PYMODULE'),
  ('torch.utils.data.sampler',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\utils\\data\\sampler.py',
   'PYMODULE'),
  ('torch',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\__init__.py',
   'PYMODULE'),
  ('torch._lobpcg',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\_lobpcg.py',
   'PYMODULE'),
  ('torch.multiprocessing._atfork',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\multiprocessing\\_atfork.py',
   'PYMODULE'),
  ('torch.quasirandom',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\quasirandom.py',
   'PYMODULE'),
  ('torch._classes',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\_classes.py',
   'PYMODULE'),
  ('torch._ops',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\_ops.py',
   'PYMODULE'),
  ('torch._storage_docs',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\_storage_docs.py',
   'PYMODULE'),
  ('torch._tensor_docs',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\_tensor_docs.py',
   'PYMODULE'),
  ('torch.profiler',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\profiler\\__init__.py',
   'PYMODULE'),
  ('torch.profiler.profiler',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\profiler\\profiler.py',
   'PYMODULE'),
  ('torch.__future__',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\__future__.py',
   'PYMODULE'),
  ('torch.__config__',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\__config__.py',
   'PYMODULE'),
  ('torch.quantization',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\quantization\\__init__.py',
   'PYMODULE'),
  ('torch.quantization.fuser_method_mappings',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\quantization\\fuser_method_mappings.py',
   'PYMODULE'),
  ('torch.quantization.utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\quantization\\utils.py',
   'PYMODULE'),
  ('torch.quantization.quantization_mappings',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\quantization\\quantization_mappings.py',
   'PYMODULE'),
  ('torch.nn.qat',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\qat\\__init__.py',
   'PYMODULE'),
  ('torch.nn.qat.modules',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\qat\\modules\\__init__.py',
   'PYMODULE'),
  ('torch.nn.qat.modules.conv',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\qat\\modules\\conv.py',
   'PYMODULE'),
  ('torch.nn.qat.modules.linear',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\qat\\modules\\linear.py',
   'PYMODULE'),
  ('torch.nn.quantized.dynamic',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\quantized\\dynamic\\__init__.py',
   'PYMODULE'),
  ('torch.nn.quantized.dynamic.modules',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\quantized\\dynamic\\modules\\__init__.py',
   'PYMODULE'),
  ('torch.nn.quantized.dynamic.modules.rnn',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\quantized\\dynamic\\modules\\rnn.py',
   'PYMODULE'),
  ('torch.nn.quantized.modules.utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\quantized\\modules\\utils.py',
   'PYMODULE'),
  ('torch.nn.quantized.modules',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\quantized\\modules\\__init__.py',
   'PYMODULE'),
  ('torch.nn.quantized.modules.functional_modules',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\quantized\\modules\\functional_modules.py',
   'PYMODULE'),
  ('torch.nn.quantized.modules.embedding_ops',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\quantized\\modules\\embedding_ops.py',
   'PYMODULE'),
  ('torch.nn.quantized.modules.linear',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\quantized\\modules\\linear.py',
   'PYMODULE'),
  ('torch.nn.quantized.modules.conv',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\quantized\\modules\\conv.py',
   'PYMODULE'),
  ('torch.nn.quantized.modules.normalization',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\quantized\\modules\\normalization.py',
   'PYMODULE'),
  ('torch.nn.quantized.functional',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\quantized\\functional.py',
   'PYMODULE'),
  ('torch.nn.quantized.modules.batchnorm',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\quantized\\modules\\batchnorm.py',
   'PYMODULE'),
  ('torch.nn.quantized.modules.activation',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\quantized\\modules\\activation.py',
   'PYMODULE'),
  ('torch.nn.quantized.dynamic.modules.linear',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\quantized\\dynamic\\modules\\linear.py',
   'PYMODULE'),
  ('torch.nn.intrinsic.qat',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\intrinsic\\qat\\__init__.py',
   'PYMODULE'),
  ('torch.nn.intrinsic.qat.modules',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\intrinsic\\qat\\modules\\__init__.py',
   'PYMODULE'),
  ('torch.nn.intrinsic.qat.modules.conv_fused',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\intrinsic\\qat\\modules\\conv_fused.py',
   'PYMODULE'),
  ('torch.nn.intrinsic.qat.modules.linear_relu',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\intrinsic\\qat\\modules\\linear_relu.py',
   'PYMODULE'),
  ('torch.nn.intrinsic.quantized',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\intrinsic\\quantized\\__init__.py',
   'PYMODULE'),
  ('torch.nn.intrinsic.quantized.modules',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\intrinsic\\quantized\\modules\\__init__.py',
   'PYMODULE'),
  ('torch.nn.intrinsic.quantized.modules.bn_relu',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\intrinsic\\quantized\\modules\\bn_relu.py',
   'PYMODULE'),
  ('torch.nn.intrinsic.quantized.modules.conv_relu',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\intrinsic\\quantized\\modules\\conv_relu.py',
   'PYMODULE'),
  ('torch.nn.intrinsic.quantized.modules.linear_relu',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\intrinsic\\quantized\\modules\\linear_relu.py',
   'PYMODULE'),
  ('torch.quantization.quantize_jit',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\quantization\\quantize_jit.py',
   'PYMODULE'),
  ('torch.quantization.quant_type',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\quantization\\quant_type.py',
   'PYMODULE'),
  ('torch.quantization.stubs',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\quantization\\stubs.py',
   'PYMODULE'),
  ('torch.quantization.fuse_modules',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\quantization\\fuse_modules.py',
   'PYMODULE'),
  ('torch.quantization.fake_quantize',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\quantization\\fake_quantize.py',
   'PYMODULE'),
  ('torch.quantization.qconfig',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\quantization\\qconfig.py',
   'PYMODULE'),
  ('torch.quantization.observer',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\quantization\\observer.py',
   'PYMODULE'),
  ('torch.quantization.quantize',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\quantization\\quantize.py',
   'PYMODULE'),
  ('torch.backends.quantized',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\backends\\quantized\\__init__.py',
   'PYMODULE'),
  ('torch.backends.openmp',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\backends\\openmp\\__init__.py',
   'PYMODULE'),
  ('torch.backends.mkldnn',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\backends\\mkldnn\\__init__.py',
   'PYMODULE'),
  ('torch.backends.mkl',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\backends\\mkl\\__init__.py',
   'PYMODULE'),
  ('torch.backends.cuda',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\backends\\cuda\\__init__.py',
   'PYMODULE'),
  ('torch.distributions',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\distributions\\__init__.py',
   'PYMODULE'),
  ('torch.distributions.weibull',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\distributions\\weibull.py',
   'PYMODULE'),
  ('torch.distributions.utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\distributions\\utils.py',
   'PYMODULE'),
  ('torch.distributions.von_mises',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\distributions\\von_mises.py',
   'PYMODULE'),
  ('torch.distributions.uniform',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\distributions\\uniform.py',
   'PYMODULE'),
  ('torch.distributions.transformed_distribution',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\distributions\\transformed_distribution.py',
   'PYMODULE'),
  ('torch.distributions.studentT',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\distributions\\studentT.py',
   'PYMODULE'),
  ('torch.distributions.relaxed_categorical',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\distributions\\relaxed_categorical.py',
   'PYMODULE'),
  ('torch.distributions.relaxed_bernoulli',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\distributions\\relaxed_bernoulli.py',
   'PYMODULE'),
  ('torch.distributions.poisson',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\distributions\\poisson.py',
   'PYMODULE'),
  ('torch.distributions.pareto',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\distributions\\pareto.py',
   'PYMODULE'),
  ('torch.distributions.one_hot_categorical',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\distributions\\one_hot_categorical.py',
   'PYMODULE'),
  ('torch.distributions.normal',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\distributions\\normal.py',
   'PYMODULE'),
  ('torch.distributions.negative_binomial',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\distributions\\negative_binomial.py',
   'PYMODULE'),
  ('torch.distributions.multivariate_normal',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\distributions\\multivariate_normal.py',
   'PYMODULE'),
  ('torch.distributions.multinomial',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\distributions\\multinomial.py',
   'PYMODULE'),
  ('torch.distributions.mixture_same_family',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\distributions\\mixture_same_family.py',
   'PYMODULE'),
  ('torch.distributions.lowrank_multivariate_normal',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\distributions\\lowrank_multivariate_normal.py',
   'PYMODULE'),
  ('torch.distributions.logistic_normal',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\distributions\\logistic_normal.py',
   'PYMODULE'),
  ('torch.distributions.log_normal',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\distributions\\log_normal.py',
   'PYMODULE'),
  ('torch.distributions.lkj_cholesky',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\distributions\\lkj_cholesky.py',
   'PYMODULE'),
  ('torch.distributions.laplace',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\distributions\\laplace.py',
   'PYMODULE'),
  ('torch.distributions.kumaraswamy',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\distributions\\kumaraswamy.py',
   'PYMODULE'),
  ('torch.distributions.kl',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\distributions\\kl.py',
   'PYMODULE'),
  ('torch.distributions.independent',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\distributions\\independent.py',
   'PYMODULE'),
  ('torch.distributions.half_normal',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\distributions\\half_normal.py',
   'PYMODULE'),
  ('torch.distributions.half_cauchy',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\distributions\\half_cauchy.py',
   'PYMODULE'),
  ('torch.distributions.gumbel',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\distributions\\gumbel.py',
   'PYMODULE'),
  ('torch.distributions.geometric',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\distributions\\geometric.py',
   'PYMODULE'),
  ('torch.distributions.gamma',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\distributions\\gamma.py',
   'PYMODULE'),
  ('torch.distributions.fishersnedecor',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\distributions\\fishersnedecor.py',
   'PYMODULE'),
  ('torch.distributions.exponential',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\distributions\\exponential.py',
   'PYMODULE'),
  ('torch.distributions.exp_family',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\distributions\\exp_family.py',
   'PYMODULE'),
  ('torch.distributions.distribution',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\distributions\\distribution.py',
   'PYMODULE'),
  ('torch.distributions.dirichlet',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\distributions\\dirichlet.py',
   'PYMODULE'),
  ('torch.distributions.continuous_bernoulli',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\distributions\\continuous_bernoulli.py',
   'PYMODULE'),
  ('torch.distributions.constraint_registry',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\distributions\\constraint_registry.py',
   'PYMODULE'),
  ('torch.distributions.transforms',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\distributions\\transforms.py',
   'PYMODULE'),
  ('torch.distributions.chi2',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\distributions\\chi2.py',
   'PYMODULE'),
  ('torch.distributions.cauchy',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\distributions\\cauchy.py',
   'PYMODULE'),
  ('torch.distributions.categorical',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\distributions\\categorical.py',
   'PYMODULE'),
  ('torch.distributions.binomial',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\distributions\\binomial.py',
   'PYMODULE'),
  ('torch.distributions.beta',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\distributions\\beta.py',
   'PYMODULE'),
  ('torch.distributions.bernoulli',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\distributions\\bernoulli.py',
   'PYMODULE'),
  ('torch.distributions.constraints',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\distributions\\constraints.py',
   'PYMODULE'),
  ('torch.hub',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\hub.py',
   'PYMODULE'),
  ('torch.linalg',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\linalg\\__init__.py',
   'PYMODULE'),
  ('torch.utils.backcompat',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\utils\\backcompat\\__init__.py',
   'PYMODULE'),
  ('torch.special',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\special\\__init__.py',
   'PYMODULE'),
  ('torch.sparse',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\sparse\\__init__.py',
   'PYMODULE'),
  ('torch.optim._multi_tensor',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\optim\\_multi_tensor\\__init__.py',
   'PYMODULE'),
  ('torch.optim._multi_tensor.adadelta',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\optim\\_multi_tensor\\adadelta.py',
   'PYMODULE'),
  ('torch.optim._multi_tensor.adamax',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\optim\\_multi_tensor\\adamax.py',
   'PYMODULE'),
  ('torch.optim._multi_tensor.asgd',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\optim\\_multi_tensor\\asgd.py',
   'PYMODULE'),
  ('torch.optim._multi_tensor.rprop',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\optim\\_multi_tensor\\rprop.py',
   'PYMODULE'),
  ('torch.optim._multi_tensor.rmsprop',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\optim\\_multi_tensor\\rmsprop.py',
   'PYMODULE'),
  ('torch.optim._multi_tensor.sgd',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\optim\\_multi_tensor\\sgd.py',
   'PYMODULE'),
  ('torch.optim._multi_tensor.adamw',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\optim\\_multi_tensor\\adamw.py',
   'PYMODULE'),
  ('torch.optim._multi_tensor.adam',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\optim\\_multi_tensor\\adam.py',
   'PYMODULE'),
  ('torch.nn.quantized',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\quantized\\__init__.py',
   'PYMODULE'),
  ('torch.nn.quantizable',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\quantizable\\__init__.py',
   'PYMODULE'),
  ('torch.nn.quantizable.modules',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\quantizable\\modules\\__init__.py',
   'PYMODULE'),
  ('torch.nn.quantizable.modules.rnn',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\quantizable\\modules\\rnn.py',
   'PYMODULE'),
  ('torch.nn.quantizable.modules.activation',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\quantizable\\modules\\activation.py',
   'PYMODULE'),
  ('torch.nn.intrinsic',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\intrinsic\\__init__.py',
   'PYMODULE'),
  ('torch.nn.intrinsic.modules',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\intrinsic\\modules\\__init__.py',
   'PYMODULE'),
  ('torch.nn.intrinsic.modules.fused',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\nn\\intrinsic\\modules\\fused.py',
   'PYMODULE'),
  ('torch.fft',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\fft\\__init__.py',
   'PYMODULE'),
  ('torch.functional',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\functional.py',
   'PYMODULE'),
  ('torch._autograd_functions',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\_autograd_functions.py',
   'PYMODULE'),
  ('torch._lowrank',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\_lowrank.py',
   'PYMODULE'),
  ('torch._linalg_utils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\_linalg_utils.py',
   'PYMODULE'),
  ('torch._tensor_str',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\_tensor_str.py',
   'PYMODULE'),
  ('torch.random',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\random.py',
   'PYMODULE'),
  ('torch._tensor',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\_tensor.py',
   'PYMODULE'),
  ('torch.autograd._functions',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\autograd\\_functions\\__init__.py',
   'PYMODULE'),
  ('torch.autograd._functions.tensor',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\autograd\\_functions\\tensor.py',
   'PYMODULE'),
  ('torch.version',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\torch\\version.py',
   'PYMODULE'),
  ('numpy',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.random',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.fft',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy.__config__',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('copy',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\copy.py',
   'PYMODULE'),
  ('jsonlines',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\jsonlines\\__init__.py',
   'PYMODULE'),
  ('jsonlines.jsonlines',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\jsonlines\\jsonlines.py',
   'PYMODULE'),
  ('attr',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\attr\\__init__.py',
   'PYMODULE'),
  ('attr._next_gen',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\attr\\_next_gen.py',
   'PYMODULE'),
  ('attr._version_info',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\attr\\_version_info.py',
   'PYMODULE'),
  ('attr._make',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\attr\\_make.py',
   'PYMODULE'),
  ('attr._compat',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\attr\\_compat.py',
   'PYMODULE'),
  ('attr._funcs',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\attr\\_funcs.py',
   'PYMODULE'),
  ('attr._cmp',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\attr\\_cmp.py',
   'PYMODULE'),
  ('attr.validators',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\attr\\validators.py',
   'PYMODULE'),
  ('attr.filters',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\attr\\filters.py',
   'PYMODULE'),
  ('attr.exceptions',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\attr\\exceptions.py',
   'PYMODULE'),
  ('attr.converters',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\attr\\converters.py',
   'PYMODULE'),
  ('attr.setters',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\attr\\setters.py',
   'PYMODULE'),
  ('attr._config',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\attr\\_config.py',
   'PYMODULE'),
  ('shutil',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\shutil.py',
   'PYMODULE'),
  ('argparse',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\argparse.py',
   'PYMODULE')])
