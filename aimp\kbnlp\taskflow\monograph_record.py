import os
import json
from itertools import product
from .information_extraction import UIETask


class MonographRecordTask(UIETask):
    def __init__(self, task, model, schema=None, device='cpu', **kwargs):
        super().__init__(task=task, model=model, schema=None, device=device, **kwargs)
        if "task_path" in self.kwargs:
            self._task_path = self.kwargs["task_path"]
            self._custom_model = True
        else:
            self._task_path = os.path.join(self._home_path, "taskflow", 'information_extraction', self.model)

        self._schema_tree = schema
        if not self._schema_tree and os.path.isfile(os.path.join(self._task_path, 'schema.json')):
            with open(os.path.join(self._task_path, 'schema.json'), mode='r', encoding='utf-8') as fr:
                self._schema_tree = json.loads(fr.read())

        self.set_schema(self._schema_tree)

    def _predict(self, examples):
        ans = self._multi_stage_predict(examples)
        # print(ans)

        def flatten(ans):  # 嵌套数据扁平化
            res = []
            for k, v in ans.items():
                for e in v:
                    e['label'] = k
                    if 'relations' in e:
                        relations = e.pop('relations')
                        res.extend([[e] + _ for _ in helper(relations)])
                    else:
                        res.append([e])
            return res

        def helper(ans):
            res = []
            for k, v in ans.items():
                temp = []
                for e in v:
                    e['label'] = k
                    if 'relations' in e:
                        relations = e.pop('relations')
                        temp.extend([[e] + _ for _ in helper(relations)])
                    else:
                        temp.append([e])
                res.append(temp)
            res = [sum(_, []) for _ in product(*res)]
            return res
        ans = [flatten(ans[i]) for i in range(len(ans))]
        return ans

