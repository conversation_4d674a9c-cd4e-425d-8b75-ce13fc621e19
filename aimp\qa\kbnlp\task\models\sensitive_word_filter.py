import ahocorasick
from typing import Optional, List, Iterable, Union
import os
import jsonlines
from bisect import bisect
from PIL import Image, ImageFont
from qa.kbnlp.experiment.yml.ocr import ocr
from qa.kbnlp.experiment.yml.utils import draw_bbox_label
from qa.kbnlp.task.taskflow import Taskflow


class SWIdentifier:
    def __init__(self, use_information_extraction=True, uie_model_path=None, device='cpu', uie_max_length=256):
        self.sw_filters = dict()
        self.use_information_extraction = use_information_extraction
        if self.use_information_extraction:
            self._uie = Taskflow(task='information_extraction', model='uie-nano', task_path=uie_model_path, device=device, schema=None)
            self._filter_uie_schema = dict()
            assert uie_max_length <= 512
            self._uie_max_length = uie_max_length

    def add_filter(self, name: str, sensitive_words: Optional[List[str]], schema=None):
        self.sw_filters[name] = SWFilter(sensitive_words=sensitive_words)
        if self.use_information_extraction:
            self._uie.task_instance.set_schema(schema)
            self._filter_uie_schema[name] = self._uie.task_instance._schema_tree

    def identify(self, image: Union[str, Image.Image] = None, words: Optional[List[str]] = None, bboxes: Optional[List] = None):
        assert (image is not None and words is None and bboxes is None) or (
                    image is None and words is not None and bboxes is not None), \
            'image参数和words,bboxes参数不能同时传入，words和bboxes必须同时传入。'
        if image is not None:
            ocr_res = ocr(image)[0]
            words = ocr_res['words']
            bboxes = ocr_res['bboxes']

        if not words:
            return None
        pivots = []
        cur = 0
        for w in words:
            if w:
                pivots.append(cur)
                cur += len(w)
        pivots.append(cur)
        text = ''.join(words)
        res = dict()
        ie_text = dict()
        for filter_name, filter in self.sw_filters.items():
            for sw in filter.iter_long(text):
                keyword = sw[1][1]
                start_position = sw[0] - (len(keyword) - 1)  # 单词在句中的start position
                end_position = sw[0] + 1
                l = bisect(pivots, start_position) - 1
                r = bisect(pivots, end_position - 1)
                res.setdefault(filter_name, []).append({'keyword': keyword, 'bboxes': bboxes[l:r]})
                if self.use_information_extraction:
                    ie_text.setdefault(filter_name, []).append(self.get_nearby_words_bboxes(l, r, words, bboxes))
        if self.use_information_extraction and ie_text:
            for filter_name, nearby in ie_text.items():
                texts, nb_bboxes = [], []
                for nb in nearby:
                    texts.append(''.join(nb[0]))
                    nb_bboxes.append(nb[1])
                self._uie.task_instance._schema_tree = self._filter_uie_schema[filter_name]
                ie_res = self._uie(texts)
                for i, single_res in enumerate(ie_res):
                    res[filter_name][i]['ie_result'] = single_res
        return res

    def get_nearby_words_bboxes(self, start_of_keyword, end_of_keyword, words: Optional[List[str]] = None, bboxes: Optional[List] = None):
        length = sum(map(len, words[start_of_keyword:end_of_keyword]))
        l = start_of_keyword - 1
        r = end_of_keyword
        count = 0
        while length < self._uie_max_length and (l >= 0 or r <= len(words) - 1):
            if count % 2 == 0:
                if l >= 0:
                    length += len(words[l])
                    l -= 1
            else:
                if r <= len(words) - 1:
                    length += len(words[r])
                    r += 1
            count += 1
        return words[l + 1: r], bboxes[l + 1: r]


class SWFilter:
    def __init__(self, model_path: str = None, sensitive_words: Optional[List[str]] = None):
        self.acTree = ahocorasick.Automaton()
        self.max_word_id = -1
        if model_path:
            if not os.path.exists(model_path):
                raise ValueError('没有模型文件路径。')
            with jsonlines.open(model_path, mode='r') as jr:
                for i, line in enumerate(jr):
                    if i == 0:
                        self.max_word_id = line
                    else:
                        self.acTree.add_word(line[1], (line[0], line[1]))
                print('Load word filter model from file: %s' % model_path)
        if sensitive_words:
            self.add_words(sensitive_words)
        self.make_automaton()

    def add_words(self, words: Optional[Iterable[str]]):
        for word in words:
            self.add_word(word)

    def add_word(self, key, value=None):
        self.max_word_id += 1
        if value is None:
            value = (self.max_word_id, key)
        self.acTree.add_word(key, value)

    def add_words_from_file(self, file):
        """
        形如以下：
        keyword1
        keyword2
        keyword3
        '''

        Args:
            file: 存放单词的文件。

        """
        with open(file, encoding='utf-8', mode='r') as fr:
            for word in fr:
                self.add_word(word)

    def make_automaton(self):
        self.acTree.make_automaton()

    def pop(self, word):  # real signature unknown; restored from __doc__
        """
        pop(word)

        Remove given word from a trie and return associated values.
        Raise a KeyError if the word was not found.
        """
        return self.acTree.pop(word)

    def remove_word(self, word):  # real signature unknown; restored from __doc__
        """
        remove_word(word) -> bool

        Remove given word from a trie. Return True if words was
        found, False otherwise.
        """
        return self.acTree.remove_word(word)

    def clear(self):
        self.acTree.clear()

    def iter(self, text: str, start=None, end=None):
        start = start if start is not None else 0
        end = end if end is not None else len(text)
        return self.acTree.iter(text, start=start, end=end)

    def iter_long(self, text: str, start: Optional[int] = None, end: Optional[int] = None):
        start = start if start is not None else 0
        end = end if end is not None else len(text)
        # iter_long不能传关键字参数
        return self.acTree.iter_long(text, start, end)

    def values(self):
        return self.acTree.values()

    def save(self, path):
        if not os.path.exists(path):
            os.makedirs(path)
        path = os.path.join(path, 'ac.model')

        with jsonlines.open(path, mode='w') as fw:
            fw.write(self.max_word_id)
            for id2word in self.values():
                fw.write(id2word)
            print('Sensitive word filter model was saved in file: %s' % os.path.abspath(path))


if __name__ == '__main__':
    #  脏话检测。
    # swf = SWFilter(sensitive_words=['草泥马', '特么的', '吃翔'])
    # for word in swf.iter('特么的，今天不开心。'):
    #     print(word)
    # swf.save('swf')
    #
    # swf2 = SWFilter('swf/ac.model')
    # for word in swf.iter('特么的，今天不开心。心情像是吃翔。'):
    #     print(word)

    # img = Image.open(r'C:\Users\<USER>\Desktop\密级鉴定开放鉴定\密2.png').convert('RGB')
    name = '绝密10.png'
    save_name = 'res.'.join(name.split('.'))
    path = r'C:\Users\<USER>\Desktop\密级鉴定开放鉴定\密级鉴定图片'

    img = Image.open(os.path.join(path, name)).convert('RGB')
    identifier = SWIdentifier()
    """
    schema_trees = [{'filter_type': '明确说明不能公开', 'key_word': ['不能公开', '不宜公开', '不可公开'], 'schema': []},
     {'filter_type': '政治事件', 'key_word': ['国家重大问题', '政治事件', '党内团结', '法轮功', '邪教', '土改'], 'schema': ['国家机关', '政治事件', '处理方案']},
     {'filter_type': '党和政府领导人及爱国进步人士', 'key_word': ['历史评价'], 'schema': ['人物']},
     {'filter_type': '国家秘密', 'key_word': ['组织关系', '情报来源', '策略手段', '国家安全', '国家利益'], 'schema': ['人物', '组织']},
     {'filter_type': '领导人关系', 'key_word': ['领导人关系'], 'schema': ['人物']},
     {'filter_type': '民国时期敌特机关', 'key_word': ['捏造', '策反', '地下组织'], 'schema': ['时间', '地点', '组织', {'策反': ['策反人员', '被策反人员']}]},
     {'filter_type': '民族纠纷', 'key_word': ['民族纠纷', '民族矛盾', '内定方针'], 'schema': ['人物', '民族', '政策', '方针']},
     {'filter_type': '国内行政区域边界', 'key_word': ['边界纠纷'], 'schema': ['地点', '区域']},
     {'filter_type': '风俗民情', 'key_word': ['风俗', '民情', '经济战略'], 'schema': ['风俗', '民情', '经济战略']},
     {'filter_type': '科学技术', 'key_word': ['关键技术', '技术诀窍', '传统工艺', '配方', '重要资源'], 'schema': ['技术', '诀窍', '工艺', '配方', '资源']},
     {'filter_type': '外国在华机构形成', 'key_word': ['外国机构', '在华机构', ], 'schema': [{'形成事件': ['时间', '地点', '机构']}]},
     {'filter_type': '司法、纪检、监察、组织人事中违法违纪调查审理', 'key_word': ['司法', '纪检', '监察', '组织人事', '违法违纪', '调查', '审理'], 'schema': [
         {'调查审理': ['司法', '纪检', '监察', '组织人事']}]},
     {'filter_type': '公民隐私', 'key_word': ['隐私'], 'schema': ['人物', '年龄', '性别', '婚史', '疾病']},
     {'filter_type': '民国时期军警宪特', 'key_word': ['民国时期'], 'schema': ['人物', '时间', '军', '警', '宪', '特务']},
     {'filter_type': '建国后政治运动', 'key_word': ['政治运动', '三反', '五反', '肃反', '整党', '整队', '文化大革命', '建国后'], 'schema': ['政治运动', '人物', '时间', '死亡人数', '逮捕人数', '处分人数', {'自杀死亡': ['时间', '人物']}]},
     {'filter_type': '极左路线干扰产生的冤假错案', 'key_word': ['极左路线', '冤家错案'], 'schema': ['案件', '极左路线']},
     {'filter_type': '各种检举、揭发、个人材料、个人交代', 'key_word': ['检举', '揭发', '个人材料', '个人交代'], 'schema': [{'检举': ['检举人', '被检举人']},
                                                                                               {'揭发': ['揭发人', '被揭发人']}]},
     {'filter_type': '知识产权', 'key_word': ['知识产权', '发明', '专利'], 'schema': ['知识产权', '发明', '专利']},
     ]
    for schema_ in schema_trees:
        identifier.add_filter(schema_['filter_type'], schema=schema_['schema'])
    """

    identifier.add_filter('脏话', ['草泥马', '特么的', '吃翔'])
    identifier.add_filter('秘密', ['秘密', '密'], schema=['时间', '地点', '人物'])
    res = identifier.identify(img)

    # words = ['特么', '的，今天不', '开心。', '心情像是吃', '翔。']
    # bboxes = np.arange(len(words) * 4).reshape(-1, 4)
    # res = identifier.identify(words=words, bboxes=bboxes)
    # words = ['小明', '于', '1999年9月出生', '秘密', '天气不错。']
    # import numpy as np
    # bboxes = np.arange(len(words) * 4).reshape(-1, 4)
    # res = identifier.identify(words=words, bboxes=bboxes)


    from pprint import pprint
    pprint(res)
    all_words = []
    all_box = []
    for sec_type,  res_list in res.items():
        for out_d in res_list:
            all_box.extend(out_d['bboxes'])
            all_words.append('密级类型：%s，关键词：%s' % (sec_type, out_d['keyword']))

    # new_img = draw_bbox_label(img, all_words, all_box, font=ImageFont.truetype(r'D:\workspace\pycharm\layoutLMv3\simfang.ttf'))
    # new_img.save(os.path.join(path, save_name))
    # new_img.show()
