import argparse
import torch
from main import main


parser = argparse.ArgumentParser(description='train pretrained parameter.')
parser.add_argument('--demo', action='store_true', help='指定该参数将只训练训练集前500个句子，评估训练集前50个句子，epoch设置为5，shuffle设置为false。')
parser.add_argument('--seed', type=int, default=970388611567449291, help='长整型，当使用分布式训练时必须提供该参数。')
parser.add_argument('--data_home', type=str, required=True)  # /home/<USER>/.hanlp
parser.add_argument('--transformer', type=str, default='hfl/chinese-electra-180g-small-discriminator', help='预训练模型路径或model上的别名')
parser.add_argument('--tokenizer', type=str, help='子词模型,如果没有该参数则使用transformer参数。')
parser.add_argument('--tok', default='', type=str, help='segment task name.')
parser.add_argument('--pos', default='', type=str, help='postag task name.')
parser.add_argument('--ner', default='', type=str, help='ner task name.')
parser.add_argument('--dep', default='', type=str, help='dep parsing task name.')
parser.add_argument('--dep_full', default='', type=str, help='dep parsing task use data provided by hanlp.')
parser.add_argument('--graph', action='store_true', help='基于图算法，否则基于转化。')
parser.add_argument('--use_grte_decoder', action='store_true', help='使用该参数表示图算法使用grte decoder.')
parser.add_argument('--num_label', default=2, type=int, help='dep任务使用grte_decoder时的num_label,默认为2，可选择1.')
parser.add_argument('--rounds', default=2, type=int, help='dep任务使用grte_decoder的层数，1表示利用预训练输出计算。共循环迭代rounds-1轮。')
parser.add_argument('--dep_extra_layer_normal', action='store_true', help='GrteDecoder层与层使用layernorm，论文作者没有使用该方法。')
parser.add_argument('--ignore_proj', action='store_true', help='使用该参数，则不会剔除训练和测试语料中不符合投射性的树。')
parser.add_argument('--method', default=1, type=int, help='图算法选取loss和解码方式。')
parser.add_argument('--dep_hidden_size', default=768, type=int, help='transition base.')
parser.add_argument('--rte', default='', type=str, help='rte任务名。')
parser.add_argument('--rte_rounds', default=2, type=int, help='rte任务使用grte_decoder的层数，1表示利用预训练输出计算。共循环迭代rounds-1轮。')
parser.add_argument('--rte_extra_layer_normal', action='store_true', help='GrteDecoder层与层使用layernorm，论文作者没有使用该方法。')

parser.add_argument('--epochs', default='20', type=int)
parser.add_argument('--batch_size', default=3, type=int)
parser.add_argument('--grad_norm', default=1.0, type=float)
parser.add_argument('--lr', default=3e-3, type=float)
parser.add_argument('--encoder_lr', default=5e-5, type=float)
parser.add_argument('--warm_up', default=0.1, type=float)
parser.add_argument('--word_dropout', default=0.1, type=float)
parser.add_argument('--max_sequence_length', default=510, type=int)
parser.add_argument('--shuffle', action='store_true')
parser.add_argument('--device', default='cuda:0' if torch.cuda.is_available() else 'cpu')
parser.add_argument('--save_dir', default='', type=str, help='模型保存路径，默认不保存模型。')
args = parser.parse_args()
print(args)
main(args)

