import jsonlines
import os
import json
from py2neo import Graph, Node
from openpyxl import Workbook, load_workbook


class YML6Graph:
    def __init__(self):
        cur_dir = '/'.join(os.path.abspath(__file__).split('/')[:-1])
        print(cur_dir)
        self.data_path = os.path.join(cur_dir, 'yml6structure.jsonl')
        self.g = Graph('http://*************:7474/', auth=("neo4j", "kubao"))

    def create_node_from_excel(self, path):
        wb = load_workbook(path)
        ws = wb.worksheets[0]
        rows = ws.rows
        column_names = []
        label = ws.title
        for i, row in enumerate(rows):
            if i == 0:
                column_names = [col.value for col in row]
                print(column_names)
            else:
                self.g.create(Node(label, **dict((name, value) for name, value in zip(column_names, [col.value for col in row]))))

    def create_node_and_relation(self):
        (person_entity, troop_entity, battle_entity, relations_troop, relations_battle, person_name,
         person_alias, person_gender, person_birthplace, person_duty, person_politics_status, person_feat,
         troop_name, battle_name) = get_statistics(self.data_path)
        for person_dict in person_entity.values():
            person = Node('人物', **person_dict)
            self.g.create(person)
        for troop_dict in troop_entity.values():
            troop = Node('部队', **troop_dict)
            self.g.create(troop)
        for battle_dict in battle_entity.values():
            battle = Node('战役', **battle_dict)
            self.g.create(battle)

        for rel_tup, text in relations_troop:
            try:
                query_troop = "match(p:%s),(q:%s) where p.name='%s'and q.name='%s' " \
                              "create (p)-[rel:%s{name:'%s'}]->(q)" % rel_tup
                self.g.run(query_troop)
            except Exception as e:
                print('create_troop_relationship error:', e)
                print(text)
        for rel_tup, text in relations_battle:
            try:
                query_battle = "match(p:%s),(q:%s) where p.name='%s'and q.name='%s' " \
                               "create (p)-[rel:%s{name:'%s'}]->(q)" % rel_tup
                self.g.run(query_battle)
            except Exception as e:
                print('create_battle_relationship error:', e)
                print(text)


def get_statistics(data_path):
    person_entity, troop_entity, battle_entity = {}, {}, {}
    relations_troop, relations_battle = [], []
    person_name = set()
    person_alias = set()
    person_gender = set()
    person_birthplace = set()
    person_duty = set()
    person_politics_status = set()
    person_feat = set()
    troop_name = set()
    battle_name = set()

    datas = []
    with jsonlines.open(data_path, mode='r') as jr:
        for d in jr:
            datas.append(d)
    for data in datas:
        person_dict = {}
        if '姓名' in data:
            person_dict['name'] = data['姓名'][0]['text']
            person_name.add(data['姓名'][0]['text'])
        if '别名' in data:
            person_dict['alias'] = data['别名'][0]['text']
            person_alias.add(data['别名'][0]['text'])
        if '性别' in data:
            person_dict['gender'] = data['性别'][0]['text']
            person_gender.add(data['性别'][0]['text'])
        if '出生日期' in data:
            person_dict['birthday'] = data['出生日期'][0]['text']
        if '哪里人' in data:
            person_dict['birthplace'] = data['哪里人'][0]['text']
            person_birthplace.add(data['哪里人'][0]['text'])
        if '入伍日期' in data:
            person_dict['join_army_day'] = data['入伍日期'][0]['text']
        if '牺牲日期' in data:
            person_dict['sacrifice_day'] = data['牺牲日期'][0]['text']
        if '职务' in data:
            person_dict['duty'] = data['职务'][0]['text']
            person_duty.add(data['职务'][0]['text'])
        if '政治面貌' in data:
            person_dict['politics_status'] = data['政治面貌'][0]['text']
            person_politics_status.add(data['政治面貌'][0]['text'])
        if '战功' in data:
            person_dict['feat'] = data['战功'][0]['text']
            person_feat.add(data['战功'][0]['text'])
        if '入党日期' in data:
            person_dict['join_party_day'] = data['入党日期'][0]['text']

        if '部队' in data:
            person_dict['troop'] = data['部队'][0]['text']
            troop_name.add(data['部队'][0]['text'])

        if '战争战役' in data:
            person_dict['battle'] = data['战争战役'][0]['text']
            battle_name.add(data['战争战役'][0]['text'])

        if person_dict and 'name' in person_dict:
            person_entity[person_dict['name']] = person_dict

        # troop_dict = {}
        # if '部队' in data:
        #     troop_dict['name'] = data['部队'][0]['text']
        #     troop_name.add(data['部队'][0]['text'])
        # if troop_dict:
        #     troop_entity[troop_dict['name']] = troop_dict
        #
        # battle_dict = {}
        # if '战争战役' in data:
        #     battle_dict['name'] = data['战争战役'][0]['text']
        #     battle_name.add(data['战争战役'][0]['text'])
        # if battle_dict:
        #     battle_entity[battle_dict['name']] = battle_dict

        # if 'name' in person_dict and 'name' in troop_dict:
        #     relations_troop.append((('人物', '部队', person_dict['name'], troop_dict['name'], '属于', '所在部队'),
        #                             data['text']))
        # if 'name' in person_dict and 'name' in battle_dict:
        #     relations_battle.append((('人物', '战役', person_dict['name'], battle_dict['name'], '参与', '参与战役'),
        #                              data['text']))
    return (person_entity, troop_entity, battle_entity, relations_troop, relations_battle, person_name,
            person_alias, person_gender, person_birthplace, person_duty, person_politics_status, person_feat,
            troop_name, battle_name)


if __name__ == '__main__':
    handler = YML6Graph()
    print("step1:导入图谱节点和关系中")
    handler.create_node_from_excel('二等功及以上英雄_图库文件.xlsx')
    # handler.create_node_and_relation()
    print('finish.')

    # g = Graph("http://127.0.0.1:7474/", auth=('neo4j', 'kubao'))
    # res = g.run("MATCH (m: 人物) where m.name='王德明' return 'ID', m.ID")
    # print(type(res), res)


