#!/usr/bin/env python
# coding=utf-8
import logging
import os
import sys
from dataclasses import dataclass, field
from typing import Optional
import random
from functools import partial
import jsonlines

import numpy as np

import torch
import transformers
from transformers import (
    AutoConfig,
    AutoModelForTokenClassification,
    AutoTokenizer,
    PreTrainedTokenizerFast,
    Trainer,
    HfArgumentParser,
    TrainingArguments,
)
from transformers.trainer_utils import get_last_checkpoint, is_main_process
from datasets import load_dataset, load_metric, Dataset
from datasets.packaged_modules import _PACKAGED_DATASETS_MODULES, _hash_python_lines
import inspect
from kbnlp.scripts.information_extraction import kb_jsonlines
from kbnlp.scripts.information_extraction.temp_label_studio import do_convert
from kbnlp.scripts.information_extraction.temp_utils import cut, convert_example
script_identifier = kb_jsonlines.__name__.split('.')[-1]
_PACKAGED_DATASETS_MODULES[script_identifier] = (kb_jsonlines.__name__, _hash_python_lines(inspect.getsource(kb_jsonlines).splitlines()))


def seed_everything(seed):
    torch.manual_seed(seed)       # Current CPU
    torch.cuda.manual_seed(seed)  # Current GPU
    np.random.seed(seed)          # Numpy module
    random.seed(seed)             # Python random module
    # torch.backends.cudnn.benchmark = False    # Close optimization
    # torch.backends.cudnn.deterministic = True  # Close optimization
    torch.cuda.manual_seed_all(seed)  # All GPU (Optional)


logger = logging.getLogger(__name__)


@dataclass
class ModelArguments:
    """
    Arguments pertaining to which model/config/tokenizer we are going to fine-tune from.
    """

    model_name_or_path: str = field(
        metadata={"help": "Path to pretrained model or model identifier from huggingface.co/models"}
    )
    config_name: Optional[str] = field(
        default=None,
        metadata={"help": "Pretrained config name or path if not the same as model_name"}
    )
    tokenizer_name: Optional[str] = field(
        default=None,
        metadata={"help": "Pretrained config name or path if not the same as model_name"}
    )


@dataclass
class DataTrainingArguments:
    """
    Arguments pertaining to what data we are going to input our model for training and eval.
    """
    train_path: str = field(
        metadata={"help": "The input training data file (a JSON file)."})
    dev_path: str = field(
        metadata={"help": "An optional input evaluation data file to evaluate on (a JSON file)."})
    max_seq_len: int = field(
        default=512,
        metadata={"help": "最长序列长度，默认值为512，不超过默认值。"}
    )
    max_train_samples: Optional[int] = field(
        default=None,
        metadata={"help": "For debugging purposes or quicker training,"
                          " truncate the number of training examples to this value if set."},)
    max_dev_samples: Optional[int] = field(
        default=None,
        metadata={"help": "For debugging purposes or quicker training, "
                          "truncate the number of validation examples to this value if set."},)
    negative_ratio: Optional[int] = field(
        default=4,
        metadata={"help": "Used only for the extraction task, the ratio of positive and negative samples, "
                          "number of negative samples = negative_ratio * number of positive samples.该参数仅对训练集有效，验证集全负采样 "}
    )
    task_type: str = field(
        default='ext',
        metadata={"help": "任务类型，'ext'代表信息抽取，'cls'代表文本分类."}
    )
    prompt_prefix: str = field(
        default='情感倾向',
        metadata={"help": "文本分类的提示词前缀。可以为任何非空字符串，如'文本分类'、'意图识别'等。"}
    )
    layout_analysis: bool = field(
        default=False,
        metadata={"help": "是否使用布局分析优化图片的ocr结果。"}
    )
    separator: str = field(
        default='##',
        metadata={"help": "用来处理实体词分类任务，该值和用户标注时设置有关。"}
    )
    schema_lang: str = field(
        default='ch',
        metadata={"help": "提示词的语言种类。中文，'ch'；英文，'en'。"}
    )
    return_entity_level_metrics: bool = field(
        default=False,
        metadata={"help": "Whether to return all the entity levels during evaluation or just the overall ones."},)
    use_cache: bool = field(
        default=False,
        metadata={"help": "是否使用datasets的缓存机制,普通用户不使用该参数。"
                          "如果值为True，则会将使用load_dataset方法加载；否则使用Datasets.from_dict方法加载，不使用缓存机制。"}
    )
    force_reload: bool = field(
        default=False,
        metadata={"help": "当use_cache为True时，本参数生效。默认值为False。当值为True时，会重新加载数据，不管有没有缓存；否则优先从缓存中加载。"}
    )


def main():
    parser = HfArgumentParser((ModelArguments, DataTrainingArguments, TrainingArguments))
    if len(sys.argv) == 2 and sys.argv[1].endswith(".json"):
        # If we pass only one argument to the script and it's the path to a json file,
        # let's parse it to get our arguments.
        model_args, data_args, training_args = parser.parse_json_file(json_file=os.path.abspath(sys.argv[1]))
    else:
        model_args, data_args, training_args = parser.parse_args_into_dataclasses()

        # Detecting last checkpoint.
        last_checkpoint = None
        if os.path.isdir(
                training_args.output_dir) and training_args.do_train and not training_args.overwrite_output_dir:
            last_checkpoint = get_last_checkpoint(training_args.output_dir)
            if last_checkpoint is None and len(os.listdir(training_args.output_dir)) > 0:
                raise ValueError(
                    f"Output directory ({training_args.output_dir}) already exists and is not empty. "
                    "Use --overwrite_output_dir to overcome."
                )
            elif last_checkpoint is not None:
                logger.info(
                    f"Checkpoint detected, resuming training at {last_checkpoint}. To avoid this behavior, change "
                    "the `--output_dir` or add `--overwrite_output_dir` to train from scratch."
                )

    logging.basicConfig(
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        datefmt="%m/%d/%Y %H:%M:%S",
        handlers=[logging.StreamHandler(sys.stdout)],
    )
    logger.setLevel(logging.INFO if is_main_process(training_args.local_rank) else logging.WARN)

    # Log on each process the small summary:
    logger.warning(
        f"Process rank: {training_args.local_rank}, device: {training_args.device}, n_gpu: {training_args.n_gpu}"
        + f"distributed training: {bool(training_args.local_rank != -1)}, 16-bits training: {training_args.fp16}"
    )
    # Set the verbosity to info of the Transformers logger (on main process only):
    if is_main_process(training_args.local_rank):
        transformers.utils.logging.set_verbosity_info()
        transformers.utils.logging.enable_default_handler()
        transformers.utils.logging.enable_explicit_format()
    logger.info(f"Training/evaluation parameters {training_args}")

    # Set seed before initializing model.
    seed_everything(training_args.seed)
    if data_args.use_cache:
        # 添加负样本在script_identifier对应的脚本中进行。
        train_ds = load_dataset(script_identifier,
                                data_files=data_args.train_path,
                                download_mode='force_redownload' if data_args.force_reload else None,
                                # 以下是脚本中datasets.BuilderConfig子类的参数。
                                is_train=True,
                                negative_ratio=data_args.negative_ratio,
                                task_type=data_args.task_type,
                                prompt_prefix=data_args.prompt_prefix,
                                layout_analysis=data_args.layout_analysis,
                                separator=data_args.separator,
                                schema_lang=data_args.schema_lang,
                                )['train']
        dev_ds = load_dataset(script_identifier,
                              data_files=data_args.dev_path,
                              download_mode='force_redownload' if data_args.force_reload else None,
                              # 以下是脚本中datasets.BuilderConfig子类的参数。
                              is_train=False,
                              negative_ratio=data_args.negative_ratio,
                              task_type=data_args.task_type,
                              prompt_prefix=data_args.prompt_prefix,
                              layout_analysis=data_args.layout_analysis,
                              separator=data_args.separator,
                              schema_lang=data_args.schema_lang,
                              )['train']
    else:
        #  InMemoryTable,不会缓存到cache目录下。
        train_ds, _, _ = do_convert(data_args.train_path,
                                    save_dir=None,
                                    negative_ratio=data_args.negative_ratio,
                                    splits=[1., 0, 0],
                                    task_type=data_args.task_type,
                                    prompt_prefix=data_args.prompt_prefix,
                                    is_shuffle=False,
                                    layout_analysis=data_args.layout_analysis,
                                    seed=None,
                                    separator=data_args.separator,
                                    schema_lang=data_args.schema_lang,
                                    verbose=False, )
        _, dev_ds, _ = do_convert(data_args.dev_path,
                                  save_dir=None,
                                  negative_ratio=data_args.negative_ratio,
                                  splits=[0, 1., 0],
                                  task_type=data_args.task_type,
                                  prompt_prefix=data_args.prompt_prefix,
                                  is_shuffle=False,
                                  layout_analysis=data_args.layout_analysis,
                                  seed=None,
                                  separator=data_args.separator,
                                  schema_lang=data_args.schema_lang,
                                  verbose=False, )
        train_ds = {k: [d[k] for d in train_ds] for k in train_ds[0].keys()}
        dev_ds = {k: [d[k] for d in dev_ds] for k in dev_ds[0].keys()}
        train_ds = Dataset.from_dict(train_ds)
        dev_ds = Dataset.from_dict(dev_ds)
    if data_args.max_train_samples is not None and data_args.max_train_samples > 0:
        train_ds = train_ds.select(range(data_args.max_train_samples))
    if data_args.max_dev_samples is not None and data_args.max_dev_samples > 0:
        dev_ds = dev_ds.select(range(data_args.max_dev_samples))

    tokenizer = AutoTokenizer.from_pretrained(
        model_args.tokenizer_name if model_args.tokenizer_name else model_args.model_name_or_path,
        tokenizer_file=None,  # avoid loading from a cached file of the pre-trained model in another machine
        use_fast=True,
    )

    def fun_batched(examples):
        ans = []
        examples = [dict(zip(examples.keys(), data)) for data in zip(*[v for v in examples.values()])]
        for example in examples:
            for exam in cut(example):
                ans.append(convert_example(exam, tokenizer=tokenizer, max_seq_len=data_args.max_seq_len))
        ans = {k: [d[k] for d in ans] for k in ans[0].keys()}
        return ans

    train_ds = train_ds.map(fun_batched, batched=True, remove_columns=train_ds.column_names)
    dev_ds = dev_ds.map(fun_batched, batched=True, remove_columns=dev_ds.column_names)

    # config = AutoConfig.from_pretrained(
    #     model_args.config_name if model_args.config_name else model_args.model_name_or_path,
    #     num_labels=len(real_estate_label2ids),
    # )
    # tokenizer = AutoTokenizer.from_pretrained(
    #     model_args.tokenizer_name if model_args.tokenizer_name else model_args.model_name_or_path,
    #     tokenizer_file=None,  # avoid loading from a cached file of the pre-trained model in another machine
    #     use_fast=True,
    # )
    # model = AutoModelForTokenClassification.from_pretrained(
    #     model_args.model_name_or_path,
    #     config=config,
    # )

    if not isinstance(tokenizer, PreTrainedTokenizerFast):
        raise ValueError(
            "This script only works for models that have a fast tokenizer."
        )


if __name__ == '__main__':
    main()



