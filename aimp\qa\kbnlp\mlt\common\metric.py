from abc import ABC, abstractmethod
from  typing import List

class Metric(ABC):

    def __lt__(self, other):
        return self.score < other

    def __le__(self, other):
        return self.score <= other

    def __eq__(self, other):
        return self.score == other

    def __ge__(self, other):
        return self.score >= other

    def __gt__(self, other):
        return self.score > other

    def __ne__(self, other):
        return self.score != other

    @property
    @abstractmethod
    def score(self):
        pass

    @abstractmethod
    def __call__(self, pred, gold):
        pass

    def __repr__(self) -> str:
        return f'{self.score}:.4f'

    def __float__(self):
        return self.score

    @abstractmethod
    def reset(self):
        pass


class MetricDict(Metric, dict):

    @property
    def score(self):
        return sum(float(x) for x in self.values()) / len(self)

    def __call__(self, pred, gold):
        raise NotImplementedError  # 如果同一对pred, gold可以计算多种指标，则利用每个指标单独计算。
        # for metric.txt in self.values():
        #     metric.txt(pred, gold)

    def reset(self):
        for metric in self.values():
            metric.reset()

    def __repr__(self) -> str:
        return ' '.join(f'({k} {repr(v)})' for k, v in self.items())


class F1(Metric, ABC):
    def __init__(self, nb_pred=0, nb_true=0, nb_correct=0) -> None:
        super().__init__()
        self.nb_correct = nb_correct
        self.nb_pred = nb_pred
        self.nb_true = nb_true

    def __repr__(self) -> str:
        p, r, f = self.prf
        return f"P: {p:.2%} R: {r:.2%} F1: {f:.2%}"

    @property
    def prf(self):
        nb_correct = self.nb_correct
        nb_pred = self.nb_pred
        nb_true = self.nb_true
        p = nb_correct / nb_pred if nb_pred > 0 else .0
        r = nb_correct / nb_true if nb_true > 0 else .0
        f = 2 * p * r / (p + r) if p + r > 0 else .0
        return p, r, f

    @property
    def score(self):
        return self.prf[-1]

    def reset(self):
        self.nb_correct = 0
        self.nb_pred = 0
        self.nb_true = 0

    def __call__(self, pred: set, gold: set):
        self.nb_correct += len(pred & gold)
        self.nb_pred += len(pred)
        self.nb_true += len(gold)


class F1_(Metric):
    def __init__(self, p, r, f) -> None:
        super().__init__()
        self.f = f
        self.r = r
        self.p = p

    @property
    def score(self):
        return self.f

    def __call__(self, pred, gold):
        raise NotImplementedError()

    def reset(self):
        self.f = self.r = self.p = 0

    def __repr__(self) -> str:
        p, r, f = self.p, self.r, self.f
        return f"P: {p:.2%} R: {r:.2%} F1: {f:.2%}"


class DependencyMetric(Metric):

    def __init__(self, eps=1e-12):
        super(DependencyMetric, self).__init__()

        self.eps = eps
        self.total_sentence = 0.0
        self.total_words = 0.0
        self.correct_heads = 0.0
        self.correct_heads_rels = 0.0
        self.correct_whole_heads = 0.0
        self.correct_whole_heads_rels = 0.0
        self.correct_root_heads = 0.0
        self.correct_root_heads_rels = 0.0

    def __repr__(self):
        return f"UAS: {self.uas:.2%} LAS: {self.las:.2%} CM: {self.cm:.2%} CM’: {self.cm2:.2%}"

    # noinspection PyMethodOverriding
    def __call__(self, head_preds, rel_preds, head_golds, rel_golds, mask):  # head_preds: tensor, b * n
        head_correct = head_preds.eq(head_golds)
        # head_mask_select =
        head_rel_correct = (rel_preds.eq(rel_golds) & head_correct)

        self.total_sentence += len(head_preds)
        self.total_words += mask.sum().item()
        self.correct_heads += (head_correct & mask).sum().item()
        self.correct_heads_rels += (head_rel_correct & mask).sum().item()
        not_mask = ~mask
        self.correct_whole_heads += (head_correct | not_mask).all(dim=-1).sum().item()
        self.correct_whole_heads_rels += (head_rel_correct | not_mask).all(dim=-1).sum().item()
        self.correct_root_heads += 0.0  # 暂不处理
        self.correct_root_heads_rels += 0.0  # 暂不处理

    @property
    def score(self):
        return self.las  # 实际使用关注las，论文中往往比较uas。

    @property
    def uas(self):
        return self.correct_heads / (self.total_words + self.eps)

    @property
    def las(self):
        return self.correct_heads_rels / (self.total_words + self.eps)

    @property
    def cm(self):
        return self.correct_whole_heads / (self.total_sentence + self.eps)

    @property
    def cm2(self):
        return self.correct_whole_heads_rels / (self.total_sentence + self.eps)

    def reset(self):
        self.total_sentence = 0.0
        self.total_words = 0.0
        self.correct_heads = 0.0
        self.correct_heads_rels = 0.0
        self.correct_whole_heads = 0.0
        self.correct_whole_heads_rels = 0.0
        self.correct_root_heads = 0.0
        self.correct_root_heads_rels = 0.0