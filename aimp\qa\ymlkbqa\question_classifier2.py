from pathlib import Path

import ahocorasick
from openpyxl import Workbook, load_workbook
import re
from qa.kbnlp.task.taskflow import Taskflow
# schema = ['姓名', '性别', '出生日期', '哪里人', '入伍日期', '部队', '牺牲日期', '战争战役', '职务', '别名', '政治面貌', {'战功': ['次数']}, '次数', '入党日期']
schema_used = ['哪里人', '部队', '战争战役']
# 处理地址，将不同单位的地址分开
unit = ['省', '市', '县', '区', '乡', '镇', '村', '庄']
task_path = str(Path(__file__).absolute().parent.parent.parent / 'qa\kbnlp\data\prompted\yml_nano')
_path = Path(__file__).absolute().parent.parent.parent / "qa\ymlkbqa\二等功及以上英雄_图库文件.xlsx"


def get_max_prob_span(ner_recognized, key_word):
    span = ''
    if key_word in ner_recognized:
        max_prob = -1
        max_prob_text = ''
        for loc in ner_recognized[key_word]:
            if loc['probability'] > max_prob:
                max_prob_text = loc['text']
                max_prob = loc['probability']
        span = max_prob_text
    return span


def get_all_prob_span(ner_recognized, key_word):
    span = []
    if key_word in ner_recognized:
        for loc in ner_recognized['key_word']:
            span.append(loc['text'])
    return span


def deal_address(address: str):
    if not address:
        return []
    if address in ['湖南省湘乡县', '湖南省宁乡县', '陕西省镇安县', '陕西省镇巴县', '江苏省镇江县', '江苏省镇江市', '河南省镇平县', '广西省镇都县']:
        return [address[:3], address[3:]]
    elif address == '山东省乳山县冯家庄区吕各庄':
        return ['山东省', '乳山县', '冯家庄区', '吕各庄']
    address = address.replace('（今', '')
    address = address.replace('）', '')
    p_address = re.compile('|'.join(unit))  # 县和区的大小不确定，乡和镇是同级一般不会同时出现。
    unit_indexes = [0]
    for item in re.finditer(p_address, address):
        unit_index = item.span()[1]
        if unit_index and unit_indexes[-1] + 1 == unit_index:
            unit_indexes.pop(-1)
        unit_indexes.append(unit_index)
    unit_indexes.append(len(address))
    res = [address[s:e] for s, e in zip(unit_indexes, unit_indexes[1:]) if address[s:e]]
    return res


class QuestionClassifier:
    def __init__(self, ner_path=task_path, schema=schema_used):
        self.schema = schema
        self.ner = Taskflow('information_extraction', 'auto-uie-nano',
                            task_path=ner_path,
                            schema=self.schema)
        self.property_question_word = ['性别', '出生日期', '哪里人', '入伍日期', '部队', '牺牲日期', '战争战役', '职务', '别名', '政治面貌', '战功', '入党日期']
        self.pro_map = {'性别': 'gender', '出生日期': 'birthday', '哪里人': 'birthplace', '入伍日期': 'join_army_day',
                        '牺牲日期': 'sacrifice_day', '职务': 'duty', '别名': 'alias', '政治面貌': 'politics_status', '战功': 'feat',
                        '入党日期': 'join_party_day', '部队': 'troop', '战争战役': 'battle'}

        self.gender_query = ['性别', '是男是女', '是男', '是女']

        self.date_query = ['几几年', '时间', '时辰', '日期', '日子', '时候', '何时', '那一天', '那天', '哪天', '哪一天', '哪一年',
                           '那一年', '哪年', '那年', '哪一日', '那一日', '哪日', '那日', '哪一月', '那一月', '哪月', '那月']

        self.birth_verb = ['出生', '出生于', '出生自', '来自', '来于', '居住', '住']
        # self.birth_verb = ['出生', '出生于', '出生自', '来自', '来于', '居住', '住', '是哪', '在哪']
        self.birthday_noun = ['生日', '生辰', '诞辰']

        self.where_query = ['哪里', '那里', '地方', '地']
        # self.birthplace_verb = ['出生', '出生于', '出生自', '来自', '来于', '居住', '住', '是哪',
        #                         '在哪']
        self.birthplace_noun = ['哪里人', '那里人', '籍贯', '老家', '居住地', '家乡', '出生地']
        self.join_army_day_verb = ['参军', '从军', '入伍', '当兵', '成为军人', '变成军人', '成为志愿军', '变成志愿军']
        self.join_army_day_noun = []
        # self.join_party_day_verb = ['入党', '参党', '从党', '成为党员', '变成党员']
        # self.join_party_day_noun = []
        self.sacrifice_day_verb = ['牺牲', '去世', '去逝', '死亡', '死', '亡', '殒命', '逝世', '丧生', '卒']
        self.sacrifice_day_noun = ['忌日', '祭日', '忌辰', '祭辰', '卒日']  # 错别字也添加进去

        self.battle_query = ['战争战役', '战争', '战斗', '战役', '斗争']
        self.duty_query = ['职务', '工作', '职位', '官职', '岗位', '职责']
        self.alias_query = ['别名', '曾用名', '其它名']
        self.politics_status_query = ['政治面貌', '政治状态']
        self.troop_query = ['部队', '军队', '集团', '团体', '部门', '军团', '军', '师', '团', '营', '连', '排']
        self.feat_query = ['战功', '荣誉', '功劳', '军功', '战工', '工劳', '军工', '立的战功']

        self.date_instance_re_pattern = re.compile(r'\d{4}年\d{1,2}月\d{1,2}[日号]|\d{4}年\d{1,2}月|\d{4}年\d{1,2}[日号]|\d{4}年|\d{1,2}月\d{1,2}[日号]|\d{1,2}月|\d{1,2}[日号]')
        self.date_query_pattern = re.compile('|'.join(['出生'] + self.birthday_noun +
                                                      self.sacrifice_day_verb + self.sacrifice_day_noun +
                                                      self.join_army_day_verb + self.join_army_day_noun
                                                      # + self.join_party_day_verb + self.join_party_day_noun
                                                      ))

        # self.number_query = ['多少', '哪些', '哪个', '哪一', '哪几', '哪些', '那个', '那一', '那几', '那些', '几多', '几个']

        self.query_keywords_dict = dict([(w, 'gender_query') for w in self.gender_query] +
                                        [(w, 'date_query') for w in self.date_query] +
                                        [(w, 'birth_verb') for w in self.birth_verb] +
                                        # [(w, 'birthday_verb') for w in self.birthday_verb] +
                                        [(w, 'birthday_noun') for w in self.birthday_noun] +
                                        [(w, 'where_query') for w in self.where_query] +
                                        [(w, 'join_army_day_verb') for w in self.join_army_day_verb] +
                                        [(w, 'join_army_day_noun') for w in self.join_army_day_noun] +
                                        # [(w, 'join_party_day_verb') for w in self.join_party_day_verb] +
                                        # [(w, 'join_party_day_noun') for w in self.join_party_day_noun] +
                                        [(w, 'sacrifice_day_verb') for w in self.sacrifice_day_verb] +
                                        [(w, 'sacrifice_day_noun') for w in self.sacrifice_day_noun] +
                                        [(w, 'battle_query') for w in self.battle_query] +
                                        [(w, 'duty_query') for w in self.duty_query] +
                                        [(w, 'alias_query') for w in self.alias_query] +
                                        [(w, 'politics_status_query') for w in self.politics_status_query] +
                                        [(w, 'troop_query') for w in self.troop_query] +
                                        [(w, 'feat_query') for w in self.feat_query] +
                                        # [(w, 'birthplace_verb') for w in self.birthplace_verb] +
                                        [(w, 'birthplace_noun') for w in self.birthplace_noun]
                                        # + [(w, 'number_query') for w in self.number_query]
                                        )

        self.query_keywords_tree = self.build_actree(self.gender_query
                                                     + self.date_query
                                                     # + self.birthday_verb +
                                                     + self.birth_verb +
                                                     self.birthday_noun + self.join_army_day_verb +
                                                     self.join_army_day_noun +
                                                     # self.join_party_day_verb +
                                                     # self.join_party_day_noun +
                                                     self.sacrifice_day_verb +
                                                     self.sacrifice_day_noun + self.battle_query + self.duty_query +
                                                     self.alias_query + self.politics_status_query + self.troop_query +
                                                     self.feat_query
                                                     + self.where_query
                                                     # + self.birthplace_verb
                                                     + self.birthplace_noun
                                                     # + self.number_query
                                                     )

        # self.where_qw = ['住哪', '是哪', '在哪', '老家', '籍贯', '家乡']
        #
        # self.date_qw = ['哪一天', '哪天', '日期', '时候']
        # self.sacrifice_qw = ['牺牲', '死亡', '去世']
        # self.join_party = ['入党', '加入', '入伍日期']
        #
        # self.how_many_question_word = ['多少', '有多少', '有哪些', '哪一些', '哪几个']

        # (_, _, _, _, _, person_name, person_alias, person_gender, person_birthplace, person_duty,
        #  person_politics_status, person_feat, troop_name, battle_name) = get_statistics('yml6structure.jsonl')
        #
        # self.region_words = list(person_name) + list(person_alias) + list(person_gender) + \
        #                     list(person_birthplace) + list(person_duty) + list(person_politics_status) + \
        #                     list(person_feat) + list(troop_name) + list(battle_name)
        # self.region_tree = self.build_actree(list(self.region_words))
        # self.wdtype_dict = dict()
        # self.wdtype_dict.update({(p_name, '姓名') for p_name in person_name})
        # self.wdtype_dict.update({(p_alias, '别名') for p_alias in person_alias})
        # self.wdtype_dict.update({(p_gender, '性别') for p_gender in person_gender})
        # self.wdtype_dict.update({(p_birthplace, '哪里人') for p_birthplace in person_birthplace})
        # self.wdtype_dict.update({(p_duty, '职务') for p_duty in person_duty})
        # self.wdtype_dict.update({(p_politics_status, '政治面貌') for p_politics_status in person_politics_status})
        # self.wdtype_dict.update({(p_feat, '战功') for p_feat in person_feat})
        # self.wdtype_dict.update({(t_name, '部队') for t_name in troop_name})
        # self.wdtype_dict.update({(b_name, '战争战役') for b_name in battle_name})

        def read_columns(path):
            wb = load_workbook(path)
            ws = wb.worksheets[0]
            columns = ws.columns
            label = ws.title
            label2instance_set = dict()
            for col in columns:
                column_label = col[0].value
                for row in col[1:]:
                    if row.value:
                        if column_label == 'ID':
                            pass
                        elif column_label in ['name', 'gender', 'duty']:
                            label2instance_set.setdefault(column_label, set()).add(row.value)
                        elif column_label == 'battle':
                            # 暂行方案，之后引入实体识别模型，通过模糊匹配或者相似度查找。
                            label2instance_set.setdefault(column_label, set()).add(row.value)
                        elif column_label in ['birthplace', 'troop']:
                            # 暂时先简单的添加到actree，之后在切分。
                            label2instance_set.setdefault(column_label, set()).add(row.value)
                        elif column_label == 'feat':
                            for fe in row.value.split('，'):
                                label2instance_set.setdefault(column_label, set()).add(fe)
                        # elif column_label in ['join_army_day', 'sacrifice_day', 'birthday', 'join_party_day']:
                        #     # 暂行方案，之后日期不放入到actree中，而是通过正则匹配。
                        #     label2instance_set.setdefault(column_label, set()).add(row.value)
                        elif column_label == 'politics_status':
                            label2instance_set.setdefault(column_label, set()).add(row.value)
            label2instance_set['politics_status'].update(['党员', '团员', '青年团', '共产党'])
            for fe in ['战功', '等功', '立功']:
                if fe in label2instance_set['feat']:
                    label2instance_set['feat'].remove(fe)
            return label2instance_set
        label2instance_set = read_columns(path=_path)
        # 使用ner模型抽取以下实体，不再使用ac自动机直接匹配
        full_address = label2instance_set.pop('birthplace')
        label2instance_set.pop('troop')
        label2instance_set.pop('battle')

        self.region_tree = self.build_actree(set(sum([list(v) for v in label2instance_set.values()], [])))
        self.wdtype_dict = dict()
        for k, v in label2instance_set.items():
            for val in v:
                self.wdtype_dict.setdefault(val, []).append(k)

        split_address = [deal_address(address) for address in full_address]
        split_address = set(sum([address_list for address_list in split_address if address_list], []))
        unit2address = dict()

        for address in split_address:
            if address and len(address) > 1:
                if address[-1] in unit:
                    unit2address.setdefault(address[-1], []).append(address[:-1])
                else:
                    # 如重庆，就忽略。一般其他字符串中有重庆市。
                    pass
        unit2address.pop('区')  # 区中包含一二的数字容易出现误判。['冯家庄', '东城', '满井子', '一', '十二', '内蒙古自治', '渔沟', '广西壮族自治', '四', '贾峪', '六', '广福', '公山', '遵义地', '北仓', '二', '白畈', '三', '七']
        self.address_tree = self.build_actree(set(sum(unit2address.values(), [])))
    '''构造actree，加速过滤'''
    def build_actree(self, wordlist):
        actree = ahocorasick.Automaton()
        for index, word in enumerate(wordlist):
            actree.add_word(word, (index, word))
        actree.make_automaton()
        return actree

    def check_query_keywords(self, question):
        query_keywords = []
        for i in self.query_keywords_tree.iter_long(question):
            wd = i[1][1]
            query_keywords.append(wd)
        stop_wds = []
        for wd1 in query_keywords:
            for wd2 in query_keywords:
                if wd1 in wd2 and wd1 != wd2:
                    stop_wds.append(wd1)
        final_wds = [i for i in query_keywords if i not in stop_wds]
        final_dict = {i: self.query_keywords_dict.get(i) for i in final_wds}
        return final_dict

    def check_entity(self, question):
        region_wds = []
        for i in self.region_tree.iter(question):
            wd = i[1][1]
            region_wds.append(wd)
        stop_wds = []
        for wd1 in region_wds:
            for wd2 in region_wds:
                if wd1 in wd2 and wd1 != wd2:
                    stop_wds.append(wd1)
        final_wds = [i for i in region_wds if i not in stop_wds]
        final_dict = {i: self.wdtype_dict.get(i) for i in final_wds}
        return final_dict

    def classify2(self, question):
        # data = {}
        word2entity_type = self.check_entity(question)

        entity_type2word = {}
        for words_, types_ in word2entity_type.items():
            for type_ in types_:
                if type_ in entity_type2word:
                    entity_type2word[type_].append(words_)
                else:
                    entity_type2word[type_] = [words_]

        word2qk_type = self.check_query_keywords(question)  # (str: str)
        qk_type_set = set(list(word2qk_type.values()))

        sqls_question_class = []
        # property_question
        if 'name' in entity_type2word:
            if qk_type_set:
                pass
            xms = entity_type2word['name']
            q_or_k2entity_type = {'gender_query': 'gender', 'battle_query': 'battle', 'duty_query': 'duty',
                                  'alias_query': 'alias', 'politics_status_query': 'politics_status',
                                  'troop_query': 'troop', 'feat_query': 'feat', 'birthday_noun': 'birthday',
                                  'join_army_day_noun': 'join_army_day',
                                  # 'join_party_day_noun': 'join_party_day',
                                  'sacrifice_day_noun': 'sacrifice_day',
                                  'birthplace_noun': 'birthplace'
                                  }
            # attr types
            entity_types = [q_or_k2entity_type[qk] for qk in qk_type_set if qk in q_or_k2entity_type]

            if 'date_query' in qk_type_set:
                if 'birth_verb' in qk_type_set:
                    entity_types.append('birthday')
                if 'join_army_day_verb' in qk_type_set:
                    entity_types.append('join_army_day')
                # if 'join_party_day_verb' in qk_type_set:
                #     entity_types.append('join_party_day')
                if 'sacrifice_day_verb' in qk_type_set:
                    entity_types.append('sacrifice_day')

            if 'where_query' in qk_type_set:
                if 'birth_verb' in qk_type_set:
                    entity_types.append('birthplace')
                entity_types = list(set(entity_types))

            if not entity_types:
                entity_types = ['gender', 'birthday', 'birthplace', 'troop', 'duty', 'politics_status', 'join_army_day',
                                # 'join_party_day',
                                'feat', 'battle', 'sacrifice_day']

            if xms and entity_types:
                entity_types.append('ID')
                for xm in xms:
                    sql = "MATCH (m:人物) where m.name = '%s' return m" % xm
                    # sql += ' '.join(["'%s', m.%s" % (e_type, e_type) for e_type in entity_types])
                    sqls_question_class.append({'cypher': sql,
                                                'intent': 1,
                                                'input_fields': {'entity_name': xm, 'entity_type': '英雄'},
                                                'output_keys': entity_types[:]})
        else:  # entity_question
            date_mentions = []
            date_query_mentions = []
            for date_match in re.finditer(self.date_instance_re_pattern, question):
                span = date_match.span()
                date_mention = question[span[0]: span[1]]
                date_mentions.append((span[0], date_mention))
            for date_query_match in re.finditer(self.date_query_pattern, question):
                span = date_query_match.span()
                date_query_mention = question[span[0]: span[1]]
                date_query_mentions.append((span[0], date_query_mention))
            for m1, m2 in zip(date_mentions, date_query_mentions):
                date_mention = m1[1]
                date_query_mention = m2[1]
                date_query_type = self.query_keywords_dict[date_query_mention]
                if date_query_type in ['birth_verb', 'birthday_noun']:
                    entity_type2word.setdefault('birthday', []).append(date_mention)
                elif date_query_type in ['sacrifice_day_verb', 'sacrifice_day_noun']:
                    entity_type2word.setdefault('sacrifice_day', []).append(date_mention)
                # elif date_query_type in ['join_party_day_verb', 'join_party_day_noun']:
                #     entity_type2word.setdefault('join_party_day', []).append(date_mention)
                elif date_query_type in ['join_army_day_verb', 'join_army_day_noun']:
                    entity_type2word.setdefault('join_army_day', []).append(date_mention)
            ner_recognized = self.ner(question)[0]  # dict
            location = get_max_prob_span(ner_recognized, '哪里人')  # str
            battle = get_max_prob_span(ner_recognized, '战争战役')  # str
            troop = get_max_prob_span(ner_recognized, '部队')  # str
            # 处理出生地
            # 1.使用实体识别模型识别出地理位置；2.利用ac自动机匹配到省市县等。3.找到最精确的地理单位，如安徽省滁州市凤阳县，则找到凤阳县即可。
            full_address = location  # 这一步可以引入ner模型。
            address = full_address
            last_position = -1
            if full_address:
                for i in self.address_tree.iter_long(full_address):
                    index = i[0]
                    wd = i[1][1]
                    if index > last_position:
                        address = wd
                        last_position = index
                if address:
                    entity_type2word['birthplace'] = [address]

            # 处理战役
            if battle:
                entity_type2word['battle'] = [battle]
            # 处理部队
            if troop:
                entity_type2word['troop'] = [troop]

            if entity_type2word:
                #  除feat以外暂时只处理vals中第一个。feat需要满足feat中的所有情况。
                condition_list = ["m.%s CONTAINS '%s'" % (type_, vals[0]) for type_, vals in entity_type2word.items() if
                                  type_ != 'feat']
                if 'feat' in entity_type2word:
                    condition_list.extend(["m.%s CONTAINS '%s'" % ('feat', fe) for fe in entity_type2word['feat']])
                condition = ' and '.join(condition_list)

                sql = "MATCH(m: 人物) where %s return m" % condition

                sqls_question_class.append({'cypher': sql,
                                            'intent': 2,
                                            'input_fields': [{'attr': type_,
                                                              'attr_value': full_address if type_ == 'birthplace'
                                                              else ('和'.join(entity_type2word['feat'])
                                                                    if type_ == 'feat' else vals[0])}
                                                             for type_, vals in entity_type2word.items()],
                                            'output_keys': ['英雄/name', 'ID']})

        return sqls_question_class

    def classify(self, question):
        data = {}
        info_dict = self.check_entity(question)
        if not info_dict:
            return {}
        data['args'] = info_dict
        #收集问句当中所涉及到的实体类型
        words = []
        types = []
        for word_, type_ in info_dict.items():
            words.append(word_)
            types.append(type_)

        property_ = []
        for pro in self.property_question_word:
            if pro in question:
                property_.append(pro)
        sqls = []
        if property_ and ('姓名' in types):
            # question_type = 'person_property'
            # question_types.append(question_type)
            xm = words[types.index('姓名')]
            sqls += ["MATCH (m:人物) where m.name = '{0}' return m.name, m.{1}".format(xm, self.pro_map[i]) for i in property_]

        # if self.check_words(self.where_qw, question) and ('姓名' in types):
        #     question_type = 'where_from'
        #     question_types.append(question_type)
        #
        # if self.check_words(self.date_qw, question) and self.check_words(self.join_party, question) and ('姓名' in types):
        #     question_type = 'data_join_party'
        #     question_types.append(question_type)
        #
        # if self.check_words(self.date_qw, question) and self.check_words(self.sacrifice_qw, question) and ('姓名' in types):
        #     question_type = 'data_sacrifice'
        #     question_types.append(question_type)
        #
        if self.check_words(self.how_many_question_word, question) and set(self.property_question_word).intersection(types):
            # question_type = 'how_many'
            # question_types.append(question_type)
            e_types = set(self.property_question_word).intersection(types)
            e_val = [words[types.index(e_t)] for e_t in e_types]

            sqls += ["MATCH (m:人物) where m.{0} = '{1}' return m.name".format(self.pro_map[t], v) for t, v in zip(e_types, e_val)]
        #
        # data['question_types'] = question_types
        return sqls

    def check_words(self, wds, sent):
        for wd in wds:
            if wd in sent:
                return True
        return False

    def change_ner(self, ner_path, schema_used=None):
        try:
            self.schema = schema_used if not schema_used else self.schema
            self.ner = Taskflow('information_extraction', 'uie-nano',
                                task_path=ner_path,
                                schema=self.schema)
        except Exception as e:
            import traceback
            print(traceback.format_exc())

    def recognize(self, sentence):
        """
        返回结果是一个字典，key代表实体类型，value是一个列表，代表该类型实体的所有实体实例。
        Args:
            sentence:

        Returns:

        """
        res = dict()
        self.ner.set_schema(['姓名', '哪里人', '部队', '战争战役'])
        map_ = {'姓名': '人物', '哪里人': '籍贯', '部队': '军团', '战争战役': '战争战役'}
        for key, values in self.ner(sentence)[0].items():
            res[map_[key]] = list(set([v['text'] for v in values]))
        self.ner.set_schema(self.schema)
        return res


if __name__ == '__main__':
    classifier = QuestionClassifier()
    print(classifier.classify2('有几个人被追记特等功'))
    print('finish.')
