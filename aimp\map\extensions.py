import re
from minio import Minio
from minio.error import S3Error
from .sync_data.utils import configMap
from tugraph import GraphDatabase
from urllib import parse
from common.logger import logger

config_map = configMap()


class MyMinio:

    def __init__(self):
        self.config = config_map
        self.client = Minio(**self.config.MINIO_CONFIG)

    def put_object(self, file_path, data, size):
        """
        上传文件
        """
        try:
            self.client.put_object(self.config.BUCKET_NAME, file_path, data, size)
        except S3Error as e:
            logger.error(e)

    def get_file(self, file, file_path):
        """
        下载保存文件保存本地
        """
        try:
            self.client.fget_object(self.config.BUCKET_NAME, file, file_path)
        except S3Error as e:
            logger.error(e)


my_minio = MyMinio()


tugraph_db = GraphDatabase(**config_map.TUGRAPH_CONFIG)


def parse_uri(database_uri):
    search_ret = re.search("://.*?:(.*)@", database_uri)
    password = search_ret.group(1)
    new_password = parse.quote_plus(password)
    new_database_uri = database_uri.replace(password, new_password)

    return new_database_uri
