from itertools import product
from pprint import pprint
from kbnlp.taskflow import Taskflow
from copy import deepcopy


def flatten(ans):
    res = []
    for k, v in ans.items():
        for e in v:
            e['label'] = k
            if 'relations' in e:
                relations = e.pop('relations')
                res.extend([[e] + _ for _ in helper(relations)])
            else:
                res.append([e])
    return res


def helper(ans):
    res = []
    for k, v in ans.items():
        temp = []
        for e in v:
            e['label'] = k
            if 'relations' in e:
                relations = e.pop('relations')
                temp.extend([[e] + _ for _ in helper(relations)])
            else:
                temp.append([e])
        res.append(temp)
    res = [sum(_, []) for _ in product(*res)]
    return res


uie = Taskflow('information_extraction', model='uie-tiny')
uie.set_schema({'竞赛名称': ['主办方', '承办方', '已举办次数']})
ans = uie('2022语言与智能技术竞赛由中国中文信息学会和中国计算机学会联合主办，百度公司、中国中文信息学会评测工作委员会和中国计算机学会自然语言处理专委会承办，已连续举办4届，成为全球最热门的中文NLP赛事之一。')
pprint(ans)
pprint(flatten(ans[0]))
