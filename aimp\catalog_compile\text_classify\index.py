# encoding=utf-8
from catalog_compile.text_classify.text_sim import get_score, _q2v
import numpy as np
from jieba.analyse import extract_tags
from catalog_compile.text_classify.util import load_data_from_dir, remove_punctuation, remove_ln
from annoy import AnnoyIndex
from functools import partial
import os
import logging


log = logging.getLogger(__name__)
_APP_MODEL_DIR = os.path.expanduser('~/.kubao/smarttext/model') # backward compatibility
AnnoyIndex = partial(AnnoyIndex, metric='angular')


class Item:

    def __init__(self, terms, intent):
        self.key = ''
        self.ts = terms
        self.tw = {}
        for t in terms:
            self.tw[t] = 1
        self.intent = intent
        for term in terms:
            if term.startswith('<') and term.endswith('>'):
                self.key += term


class Index:

    def __init__(self, w2v, index_path, type=0, _score=2.4): # type=0:编目 type=1:本地训练 type=2:初始化训练
        self.filter = [' ', '#']
        self.w2v = w2v
        self._score = _score
        if 0 == type:
            self.index = {}
            self.items = {}
            self.items_biaoti = {}
            self.items = np.load(index_path + '.items.npy', allow_pickle=True).item()
            self.items_biaoti = np.load(index_path + '_biaoti.items.npy', allow_pickle=True).item()
            self.u = AnnoyIndex(300)
            self.u_biaoti = AnnoyIndex(300)
            self.index_path = index_path
            self.u.load(index_path)
            self.u_biaoti.load(index_path + '_biaoti')
            if os.path.exists(index_path + '_1'): # multi-model style
                self.u_1 = AnnoyIndex(300)
                self.u_1_biaoti = AnnoyIndex(300)
                self.u_1.load(index_path + '_1')
                self.u_1_biaoti.load(index_path + '_1_biaoti')
                self.items_1 = np.load(index_path + '_1.items.npy', allow_pickle=True).item()
                self.items_1_biaoti = np.load(index_path + '_1_biaoti.items.npy', allow_pickle=True).item()
            elif os.path.exists(os.path.join(_APP_MODEL_DIR, 'index/index.nn_1')):
                self.u_1 = AnnoyIndex(300)
                self.u_1_biaoti = AnnoyIndex(300)
                self.u_1.load(os.path.join(_APP_MODEL_DIR, 'index/index.nn_1'))
                self.u_1_biaoti.load(os.path.join(_APP_MODEL_DIR, 'index/index.nn_1_biaoti'))
                self.items_1 = np.load(os.path.join(_APP_MODEL_DIR, 'index/index.nn_1.items.npy'), allow_pickle=True).item()
                self.items_1_biaoti = np.load(os.path.join(_APP_MODEL_DIR, 'index/index.nn_1_biaoti.items.npy'), allow_pickle=True).item()
        if 1 == type:
            self.index_dir = index_path
        if 2 == type:
            self.index = {}
            self.items = {}
            self.items_biaoti = {}
            self.u = AnnoyIndex(300)
            self.u_biaoti = AnnoyIndex(300)
            self.index_path = index_path

    def __del__(self):
        if hasattr(self, 'u'):
            self.u.unload()
        if hasattr(self, 'u_biaoti'):
            self.u_biaoti.unload()
        if hasattr(self, 'u_1'):
            self.u_1.unload()
        if hasattr(self, 'u_1_biaoti'):
            self.u_1_biaoti.unload()

    def build_annoy_index(self, path, CatalogInitializer=None):
        count = 0
        for item in load_data_from_dir(os.path.join(path, 'quanwen')):
            if CatalogInitializer is not None and CatalogInitializer.stopped:
                log.info('build quanwen data stopped')
                return
            if len(item) == 2:
                terms = []
                tws = {}
                s = remove_punctuation(item[0].replace('\n', ' '))
                s = remove_ln(s)
                for k in extract_tags(s, topK=30):
                    for c in list(k):
                        if c in self.filter:
                            continue
                        terms.append(c)
                        tws[c] = 1.0
                if 0 == len(terms):
                    for c in list(s):
                        if c in self.filter:
                            continue
                        terms.append(c)
                        tws[c] = 1.0
                self.items[count] = Item(terms, item[1])
                self.u.add_item(count, self.q2v(tws) * 10)
            count += 1
        np.save(self.index_path + '.items.npy', self.items)
        self.u.build(100)
        self._save_annoy(self.u, self.index_path)
        count = 0
        for item in load_data_from_dir(os.path.join(path, 'biaoti')):
            if CatalogInitializer is not None and CatalogInitializer.stopped:
                log.info('build biaoti data stopped')
                return
            if len(item) == 2:
                terms = []
                tws = {}
                s = remove_punctuation(item[0].replace('\n', ' '))
                s = remove_ln(s)
                for c in list(s):
                    if c in self.filter:
                        continue
                    tws[c] = 1.0
                    terms.append(c)
                self.items_biaoti[count] = Item(terms, item[1])
                self.u_biaoti.add_item(count, self.q2v(tws) * 10)
            count += 1
        np.save(self.index_path + '_biaoti.items.npy', self.items_biaoti)
        if CatalogInitializer is not None and CatalogInitializer.stopped:
            log.info('build annoy stopped')
            return
        self.u_biaoti.build(100)
        self._save_annoy(self.u_biaoti, self.index_path + '_biaoti')

    def build_annoy_index_attach(self, datas):
        count_quanwen = 0
        count_biaoti = 0
        for data in datas:
            if not data.is_biaoti:
                terms = []
                tws = {}
                s = remove_punctuation(data.text.replace('\n', ' '))
                s = remove_ln(s)
                for k, v in extract_tags(s, topK=30, withWeight=True):
                    for c in list(k):
                        if c in self.filter:
                            continue
                        terms.append(c)
                        tws[c] = 1.0
                if 0 == len(terms):
                    for c in list(s):
                        if c in self.filter:
                            continue
                        terms.append(c)
                        tws[c] = 1.0
                self.items[count_quanwen] = Item(terms, data.id)
                self.u.add_item(count_quanwen, self.q2v(tws) * 10)
                count_quanwen += 1
            else:
                terms = []
                tws = {}
                s = remove_punctuation(data.text.replace('\n', ' '))
                s = remove_ln(s)
                for c in list(s):
                    if c in self.filter:
                        continue
                    tws[c] = 1.0
                    terms.append(c)
                self.items_biaoti[count_biaoti] = Item(terms, data.id)
                self.u_biaoti.add_item(count_biaoti, self.q2v(tws) * 10)
            count_biaoti += 1
        self.u.build(100)
        self.u_biaoti.build(100)

    def add_index(self, datas):
        if not os.path.exists(self.index_dir):
            os.makedirs(self.index_dir)
        count = 0
        count_biaoti = 0
        items_train = dict()
        items_train_biaoti = dict()
        u_train = AnnoyIndex(300)
        u_train_biaoti = AnnoyIndex(300)
        for data in datas:
            if len(data) == 3:
                terms = []
                tws = {}
                s = remove_punctuation(data[0])
                s = remove_ln(s)
                if data[2]:
                    # 标题
                    for c in list(s):
                        if c in self.filter:
                            continue
                        tws[c] = 1.0
                        terms.append(c)
                    items_train_biaoti[count_biaoti] = Item(terms, data[1])
                    u_train_biaoti.add_item(count_biaoti, self.q2v(tws) * 10)
                    count_biaoti += 1
                else:
                    # 全文
                    for k in extract_tags(s, topK=30):
                        for c in list(k):
                            if c in self.filter:
                                continue
                            terms.append(c)
                            tws[c] = 1.0
                    if 0 == len(terms):
                        for c in list(s):
                            if c in self.filter:
                                continue
                            terms.append(c)
                            tws[c] = 1.0
                    items_train[count] = Item(terms, data[1])
                    u_train.add_item(count, self.q2v(tws) * 10)
                    count += 1
        u_train.build(100)
        self._save_annoy(u_train, os.path.join(self.index_dir, 'index.nn_1'))
        u_train_biaoti.build(100)
        self._save_annoy(u_train_biaoti, os.path.join(self.index_dir, 'index.nn_1_biaoti'))
        np.save(os.path.join(self.index_dir, 'index.nn_1.items.npy'), items_train)
        np.save(os.path.join(self.index_dir, 'index.nn_1_biaoti.items.npy'), items_train_biaoti)

    def seach_vec(self, v, top_num=5):
        return self.u.get_nns_by_vector(v, n=top_num)

    def seach_vec_biaoti(self, v, top_num=5):
        return self.u_biaoti.get_nns_by_vector(v, n=top_num)

    def seach_vec_1(self, v, top_num=10):
        return self.u_1.get_nns_by_vector(v, n=top_num)

    def seach_vec_1_biaoti(self, v, top_num=10):
        return self.u_1_biaoti.get_nns_by_vector(v, n=top_num)

    def add(self, i):
        if i.key not in self.index:
            self.index[i.key] = []
        self.index[i.key].append(i)

    def q2v(self, tws):
        return _q2v(self.w2v, tws)

    def search(self, s, biaoti_flag):
        cands, cands_1 = self._search(s, biaoti_flag)
        cand = sorted(cands, key=lambda x: -x[1])[0]
        intent, score = cand[0].intent, cand[1]
        if len(cands_1) > 0:
            cand_1 = sorted(cands_1, key=lambda x: -x[1])[0]
            if cand_1[1] >= score:
                intent = cand_1[0].intent
                score = cand_1[1]
        if not (score > 0):
            intent = '0'
            score = 0.1
        return intent, score

    def search_attach(self, s, biaoti_flag):
        cands, _ = self._search(s, biaoti_flag)
        cands = sorted(cands, key=lambda x: -x[1])
        res = []
        for item, score in cands:
            if score > 0:
                res.append(item.intent)
            else:
                res.append('0')
        return res

    # @profile
    def _search(self, s, biaoti_flag):
        '''
        :param s:
        :return:
        '''
        s = remove_punctuation(s)
        s = remove_ln(s)
        qtw = {}
        qts = []
        cands = []
        cands_1 = []
        if biaoti_flag == True:
            for c in list(s):
                if c in self.filter:
                    continue
                qtw[c] = 1.0
                qts.append(c)
            ids = self.seach_vec_biaoti(self.q2v(qtw) * 10)
            for id in ids:
                item = self.items_biaoti[id]
                if ''.join(item.ts) == s.replace(' ', ''):
                    score = 10.0
                else:
                    score = get_score(qts, item.ts, qtw, item.tw, self.w2v, self._score)
                cands.append((item, score))
            if hasattr(self, 'u_1_biaoti'):
                ids_1 = self.seach_vec_1_biaoti(self.q2v(qtw) * 10)
                for id in ids_1:
                    item = self.items_1_biaoti[id]
                    if ''.join(item.ts) == s.replace(' ', ''):
                        score = 10.0
                    else:
                        score = get_score(qts, item.ts, qtw, item.tw, self.w2v)
                    cands_1.append((item, score))
        else:
            for k in extract_tags(s, topK=30):
                for c in list(k):
                    if c in self.filter:
                        continue
                    qts.append(c)
                    qtw[c] = 1.0
            if 0 == len(qts):
                for c in list(s):
                    if c in self.filter:
                        continue
                    qts.append(c)
                    qtw[c] = 1.0
            ids = self.seach_vec(self.q2v(qtw) * 10)
            for id in ids:
                item = self.items[id]
                if ''.join(item.ts) == s.replace(' ', ''):
                    score = 10.0
                else:
                    score = get_score(qts, item.ts, qtw, item.tw, self.w2v) - 1.0
                cands.append((item, score))
            if hasattr(self, 'u_1'):
                ids_1 = self.seach_vec_1(self.q2v(qtw) * 10)
                for id in ids_1:
                    item = self.items_1[id]
                    if ''.join(item.ts) == s.replace(' ', ''):
                        score = 10.0
                    else:
                        score = get_score(qts, item.ts, qtw, item.tw, self.w2v) - 1.0
                    cands_1.append((item, score))
        return cands, cands_1

    def search2(self, s, biaoti_flag):
        '''
        :param s:
        :return:
        '''
        s = remove_punctuation(s)
        s = remove_ln(s)
        qtw = {}
        qts = []
        cands = []
        cands_1 = []
        if biaoti_flag == True:
            for c in list(s):
                if c in self.filter:
                    continue
                qtw[c] = 1.0
                qts.append(c)
            ids = self.seach_vec_biaoti(self.q2v(qtw) * 10)
            for id in ids:
                item = self.items_biaoti[id]
                if ''.join(item.ts) == s.replace(' ', ''):
                    score = 10.0
                else:
                    score = get_score(qts, item.ts, qtw, item.tw, self.w2v)
                cands.append((item, score))
            if hasattr(self, 'u_1_biaoti'):
                ids_1 = self.seach_vec_1_biaoti(self.q2v(qtw) * 10)
                for id in ids_1:
                    item = self.items_1_biaoti[id]
                    if ''.join(item.ts) == s.replace(' ', ''):
                        score = 10.0
                    else:
                        score = get_score(qts, item.ts, qtw, item.tw, self.w2v)
                    cands_1.append((item, score))
        else:
            for k in extract_tags(s, topK=30):
                for c in list(k):
                    if c in self.filter:
                        continue
                    qts.append(c)
                    qtw[c] = 1.0
            if 0 == len(qts):
                for c in list(s):
                    if c in self.filter:
                        continue
                    qts.append(c)
                    qtw[c] = 1.0
            ids = self.seach_vec(self.q2v(qtw) * 10)
            for id in ids:
                item = self.items[id]
                if ''.join(item.ts) == s.replace(' ', ''):
                    score = 10.0
                else:
                    score = get_score(qts, item.ts, qtw, item.tw, self.w2v) - 1.0
                cands.append((item, score))
            if hasattr(self, 'u_1'):
                ids_1 = self.seach_vec_1(self.q2v(qtw) * 10)
                for id in ids_1:
                    item = self.items_1[id]
                    if ''.join(item.ts) == s.replace(' ', ''):
                        score = 10.0
                    else:
                        score = get_score(qts, item.ts, qtw, item.tw, self.w2v) - 1.0
                    cands_1.append((item, score))
        result = dict()
        for cand, score in cands:
            if score <= 0.0:
                continue
            intent = cand.intent
            if intent in result:
                score += result[intent]
            result[intent] = score
        if len(result) == 0:
            return '0', 0.1
        result = sorted(result.items(), key=lambda x: x[1], reverse=True)
        return result[0][0], result[0][1]

    @staticmethod
    def _save_annoy(annoy, path):
        if os.name == 'nt':
            from tempfile import TemporaryDirectory
            from shutil import move
            with TemporaryDirectory() as d:
                tpath = os.path.join(d, 'nn')
                annoy.save(tpath)
                annoy.unload()
                move(tpath, path)
        else:
            annoy.save(path)
            annoy.unload()


if __name__ == '__main__':
    from catalog_compile.common.nlp_res import get_w2v
    index_path = os.path.expanduser('dtm/index/model7924233/index.nn')
    w2v = get_w2v()
    index = Index(w2v, index_path)
    # print(index.items)
    # print(index.items_biaoti)
    # v        = [index.u.get_item_vector(i) for i in range(index.u.get_n_items())]
    # v_biaoti = [index.u_biaoti.get_item_vector(i) for i in range(index.u_biaoti.get_n_items())]
    # v = np.array(v); v_biaoti = np.array(v_biaoti)
    # print(v.shape, v_biaoti.shape)
    print(index.search('不动产登记申请书', True))
    print(index.search('不动产登记申请书', False))
    # for k,v in index.items.items():
    #     print(v.ts)
    #     print(v.tw)
    from timeit import Timer
    timer = Timer('index.search("不动产登记申请书", True)', globals=globals())
    c, t = timer.autorange()
    c, t = timer.autorange()
    print('time {}s/it, {} loops'.format(t/c, c))
    timer = Timer('index.search("不动产登记申请书", False)', globals=globals())
    c, t = timer.autorange()
    c, t = timer.autorange()
    print('time {}s/it, {} loops'.format(t/c, c))
    del index
    # os.makedirs('dtm/index/model0000000')
    # index = Index(w2v, 'dtm/index/model0000000/index.nn', 2)
    # index.build_annoy_index('dtm/index/model7924233/data')
    # del index
    del w2v
