# 当前示例为 slm_runtime 接口的 python 调用示例，在 python 3.8.5 运行通过。
# 运行示例需要修改init_slm_runtime中动态库的位置

import ctypes
from ctypes.wintypes import HLOCAL
import platform
import binascii

# slm_init                        @1
# slm_find_license                @2
# slm_login                       @3
# slm_get_cloud_token             @4
# slm_logout                      @5
# slm_keep_alive                  @6
# slm_check_module                @7
# slm_encrypt                     @8
# slm_decrypt                     @9
# slm_user_data_getsize           @10
# slm_user_data_read              @11
# slm_user_data_write             @12
# slm_get_info                    @13
# slm_execute_static              @14
# slm_execute_dynamic             @15
# slm_execute                     @16
# slm_mem_alloc                   @17
# slm_mem_free                    @18
# slm_mem_read                    @19
# slm_mem_write                   @20
# slm_is_debug                    @21
# slm_get_device_cert             @22
# slm_sign_by_device              @23
# slm_adjust_time_request         @24
# slm_led_control                 @25
# slm_get_version                 @26
# slm_update                      @27
# slm_update_ex                   @28
# slm_enum_device                 @29
# slm_free                        @30
# slm_get_developer_id            @31
# slm_error_format                @32
# slm_cleanup                     @33
# slm_pub_data_getsize            @36
# slm_pub_data_read               @37
# slm_d2c_update_inside           @38
# slm_enum_license_id             @39
# slm_get_license_info            @40
# slm_license_sign                @41
# slm_license_verify              @42
# slm_get_cert                    @43
# slm_enum_device_ex              @44
# slm_enum_license_id_ex          @45
# slm_get_license_info_ex         @46
# slm_extensions_config           @47


ROM = 0
RAW = 1
PUB = 2
# const
SESSION_INFO = 2
JSON = 2


class init_params(ctypes.Structure):
    _pack_ = 1
    _fields_ = [("version", ctypes.c_uint32),
                ("flag", ctypes.c_uint32),
                ("pfn", ctypes.c_void_p),
                ("timeout", ctypes.c_uint32),
                ("password", ctypes.c_byte * 16)]


class login_params(ctypes.Structure):
    _pack_ = 1
    _fields_ = [("size", ctypes.c_uint32),
                ("license_id", ctypes.c_uint32),
                ("timeout", ctypes.c_uint32),
                ("login_mode", ctypes.c_uint32),
                ("login_flag", ctypes.c_uint32),
                ("sn", ctypes.c_ubyte * 16),
                ("server", ctypes.c_byte * 32),
                ("access_token", ctypes.c_byte * 64),
                ("cloud_server", ctypes.c_byte * 100),
                ("snippet_seed", ctypes.c_ubyte * 32),
                ("user_guid", ctypes.c_byte * 128)]


def init_slm_runtime():
    ## 加载 so，初始化接口函数
    # 引用 so（x86或x64） 与当前运行环境中的 python.exe 解释器（32位、64位）保持相同，否则会加载失败。
    archi = platform.architecture()
    print(archi[0])
    if (platform.system() == "Windows"):
        if (archi[0] == '32bit'):
            runtime_dll_path = r'common/x86/slm_runtime.dll'
        elif (archi[0] == '64bit'):
            runtime_dll_path = r'common/x64/slm_runtime.dll'
    elif (platform.system() == "Linux"):
        if (archi[0] == '32bit'):
            runtime_dll_path = r'common/x86/libslm_runtime.so'
        elif (archi[0] == '64bit'):
            runtime_dll_path = r'common/x64/libslm_runtime.so'
    slm_runtime = ctypes.cdll.LoadLibrary(runtime_dll_path)

    if (platform.system() == "Windows"):
        # windows中的函数需要手动定位在动态库中的位置
        slm_runtime.slm_init = slm_runtime[1]
        slm_runtime.slm_find_license = slm_runtime[2]
        slm_runtime.slm_login = slm_runtime[3]
        slm_runtime.slm_get_cloud_token = slm_runtime[4]
        slm_runtime.slm_logout = slm_runtime[5]
        slm_runtime.slm_keep_alive = slm_runtime[6]
        slm_runtime.slm_check_module = slm_runtime[7]
        slm_runtime.slm_encrypt = slm_runtime[8]
        slm_runtime.slm_decrypt = slm_runtime[9]
        slm_runtime.slm_user_data_getsize = slm_runtime[10]
        slm_runtime.slm_user_data_read = slm_runtime[11]
        slm_runtime.slm_user_data_write = slm_runtime[12]
        slm_runtime.slm_get_info = slm_runtime[13]
        slm_runtime.slm_execute_static = slm_runtime[14]
        slm_runtime.slm_execute_dynamic = slm_runtime[15]
        slm_runtime.slm_execute = slm_runtime[16]
        slm_runtime.slm_mem_alloc = slm_runtime[17]
        slm_runtime.slm_mem_free = slm_runtime[18]
        slm_runtime.slm_mem_read = slm_runtime[19]
        slm_runtime.slm_mem_write = slm_runtime[20]
        slm_runtime.slm_is_debug = slm_runtime[21]
        slm_runtime.slm_get_device_cert = slm_runtime[22]
        slm_runtime.slm_sign_by_device = slm_runtime[23]
        slm_runtime.slm_adjust_time_request = slm_runtime[24]
        slm_runtime.slm_led_control = slm_runtime[25]
        slm_runtime.slm_get_version = slm_runtime[26]
        slm_runtime.slm_update = slm_runtime[27]
        slm_runtime.slm_update_ex = slm_runtime[28]
        slm_runtime.slm_enum_device = slm_runtime[29]
        slm_runtime.slm_free = slm_runtime[30]
        slm_runtime.slm_get_developer_id = slm_runtime[31]
        slm_runtime.slm_error_format = slm_runtime[32]
        slm_runtime.slm_cleanup = slm_runtime[33]
        slm_runtime.slm_pub_data_getsize = slm_runtime[36]
        slm_runtime.slm_pub_data_read = slm_runtime[37]
        slm_runtime.slm_d2c_update_inside = slm_runtime[38]
        slm_runtime.slm_enum_license_id = slm_runtime[39]
        slm_runtime.slm_get_license_info = slm_runtime[40]
        slm_runtime.slm_license_sign = slm_runtime[41]
        slm_runtime.slm_license_verify = slm_runtime[42]
        slm_runtime.slm_get_cert = slm_runtime[43]
        # 下面几个函数只有新版的库才能调用
        # slm_runtime.slm_enum_device_ex      = slm_runtime[44]
        # slm_runtime.slm_enum_license_id_ex  = slm_runtime[45]
        # slm_runtime.slm_get_license_info_ex = slm_runtime[46]
        # slm_runtime.slm_extensions_config   = slm_runtime[47]
    # 处理返回值
    slm_runtime.slm_init.restype = ctypes.c_uint32
    slm_runtime.slm_find_license.restype = ctypes.c_uint32
    slm_runtime.slm_login.restype = ctypes.c_uint32
    slm_runtime.slm_get_cloud_token.restype = ctypes.c_uint32
    slm_runtime.slm_logout.restype = ctypes.c_uint32
    slm_runtime.slm_keep_alive.restype = ctypes.c_uint32
    slm_runtime.slm_check_module.restype = ctypes.c_uint32
    slm_runtime.slm_encrypt.restype = ctypes.c_uint32
    slm_runtime.slm_decrypt.restype = ctypes.c_uint32
    slm_runtime.slm_user_data_getsize.restype = ctypes.c_uint32
    slm_runtime.slm_user_data_read.restype = ctypes.c_uint32
    slm_runtime.slm_user_data_write.restype = ctypes.c_uint32
    slm_runtime.slm_get_info.restype = ctypes.c_uint32
    slm_runtime.slm_execute_static.restype = ctypes.c_uint32
    slm_runtime.slm_execute_dynamic.restype = ctypes.c_uint32
    slm_runtime.slm_execute.restype = ctypes.c_uint32
    slm_runtime.slm_mem_alloc.restype = ctypes.c_uint32
    slm_runtime.slm_mem_free.restype = ctypes.c_uint32
    slm_runtime.slm_mem_read.restype = ctypes.c_uint32
    slm_runtime.slm_mem_write.restype = ctypes.c_uint32
    slm_runtime.slm_is_debug.restype = ctypes.c_uint32
    slm_runtime.slm_get_device_cert.restype = ctypes.c_uint32
    slm_runtime.slm_sign_by_device.restype = ctypes.c_uint32
    slm_runtime.slm_adjust_time_request.restype = ctypes.c_uint32
    slm_runtime.slm_led_control.restype = ctypes.c_uint32
    slm_runtime.slm_get_version.restype = ctypes.c_uint32
    slm_runtime.slm_update.restype = ctypes.c_uint32
    slm_runtime.slm_update_ex.restype = ctypes.c_uint32
    slm_runtime.slm_enum_device.restype = ctypes.c_uint32
    slm_runtime.slm_free.restype = ctypes.c_uint32
    slm_runtime.slm_get_developer_id.restype = ctypes.c_uint32
    slm_runtime.slm_error_format.restype = ctypes.c_uint32
    slm_runtime.slm_cleanup.restype = ctypes.c_uint32
    slm_runtime.slm_pub_data_getsize.restype = ctypes.c_uint32
    slm_runtime.slm_pub_data_read.restype = ctypes.c_uint32
    slm_runtime.slm_d2c_update_inside.restype = ctypes.c_uint32
    slm_runtime.slm_enum_license_id.restype = ctypes.c_uint32
    slm_runtime.slm_get_license_info.restype = ctypes.c_uint32
    slm_runtime.slm_license_sign.restype = ctypes.c_uint32
    slm_runtime.slm_license_verify.restype = ctypes.c_uint32
    slm_runtime.slm_get_cert.restype = ctypes.c_uint32
    # 下面几个函数只有新版的库才能调用
    # slm_runtime.slm_enum_device_ex      =ctypes.c_uint32 
    # slm_runtime.slm_enum_license_id_ex  =ctypes.c_uint32 
    # slm_runtime.slm_get_license_info_ex =ctypes.c_uint32 
    # slm_runtime.slm_extensions_config   =ctypes.c_uint32 
    return slm_runtime


if __name__ == "__main__":
    # 初始化运行时库
    slm_runtime = init_slm_runtime()

    # 运行时库逻辑测试
    # 获取开发者ID
    developer_id_bytes = ctypes.create_string_buffer(b'\000' * 8)
    r = slm_runtime.slm_get_developer_id(developer_id_bytes)
    print('slm_get_developer_id r', r, binascii.b2a_hex(developer_id_bytes))

    # 初始化
    slm_init_param = init_params()
    slm_init_param.version = ctypes.c_uint32(0x02)
    slm_init_param.flag = ctypes.c_uint32(0x01)
    slm_init_param.pfg = ctypes.c_void_p(0)
    slm_init_param.timeout = ctypes.c_uint32(1000 * 10)
    # 开发者 API 密码，不同的开发者不同，开发者需修改
    slm_init_param.password = (ctypes.c_byte * 16)(0xD7, 0xAA, 0x1D, 0xEE, 0x34, 0x95, 0xFA, 0xA0, 0xE8, 0x11, 0x61,
                                                   0xFA, 0xBB, 0x75, 0x26, 0x87)
    r = slm_runtime.slm_init(ctypes.byref(slm_init_param))
    print('slm_init r', r)
    if r != 0:
        if r == 0x13000038:
            print('错误的API密码，请登录开发者网站 developer.senseyun.com，查看 API 密码按照格式替换 slm_init_param.password 内容后重试。')
        exit(r)  # 初始化失败，直接退出，后续功能无执行必要。

    # 许可登录
    slm_login_param = login_params()
    slm_login_param.license_id = ctypes.c_uint32(1)
    slm_login_param.size = ctypes.sizeof(slm_login_param)
    slm_login_param.login_mode = ctypes.c_uint32(0x0001)
    slm_login_param.timeout = ctypes.c_uint32(600)
    h_login = ctypes.c_uint32(0)
    r = slm_runtime.slm_login(ctypes.byref(slm_login_param), ctypes.c_uint32(3), ctypes.byref(h_login),
                              ctypes.c_void_p(0))
    print('slm_login r', r, 'handle', h_login)
    if r == 0:
        print('slm_login ok')
        # 许可登录成功，可以操作许可接口，最后调用 slm_logout 释放句柄资源
        # TODO 许可操作
    writebuf = (ctypes.c_byte * 1024)(12, 34, 56)

    # const
    SESSION_INFO = 2
    JSON = 2
    session_info = ctypes.c_char_p()
    r = slm_runtime.slm_get_info(h_login, SESSION_INFO, JSON, ctypes.byref(session_info))
    if r != 0:
        print('slm_get_info error:0x%08x' % r)
    else:
        print('slm_get_info ok')
        print('slm_get_info r', r, session_info.value)

    r = slm_runtime.slm_user_data_write(h_login, ctypes.byref(writebuf), 0, 10)
    if r != 0:
        print('slm_user_data_write error 0x%08x' % r)
    else:
        print('slm_user_data_write ok writebuf:', writebuf[0], writebuf[1], writebuf[2])

    rom_size = ctypes.c_uint32(0)
    r = slm_runtime.slm_user_data_getsize(h_login, RAW, ctypes.byref(rom_size))
    if r != 0:
        print('slm_user_data_getsize error : 0x%08X' % r)
    else:
        print('slm_user_data_getsize ok rom_size:', rom_size)

    readbuf = (ctypes.c_byte * 1024)(1024)
    r = slm_runtime.slm_user_data_read(h_login, RAW, ctypes.byref(readbuf), 0, rom_size)
    if r != 0:
        print('slm_user_data_read error:0x%08X' % r)
    else:
        print('slm_user_data_read ok')
        print('rawbuff is:', r, readbuf[0], readbuf[1], readbuf[2])

        # 许可登出（释放登录许可句柄，一个进程最多只能打开 255 句柄）
        r = slm_runtime.slm_logout(h_login)
        print('slm_logout r', r)

    # 释放资源
    r = slm_runtime.slm_cleanup()
    print('slm_cleanup r', r)
