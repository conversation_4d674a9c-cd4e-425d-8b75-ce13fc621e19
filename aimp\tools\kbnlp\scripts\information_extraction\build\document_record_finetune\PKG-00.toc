('G:\\kubao-aimp\\aimp\\tools\\kbnlp\\scripts\\information_extraction\\build\\document_record_finetune\\document_record_finetune.pkg',
 {'BINARY': 1,
  'DATA': 1,
  'EXECUTABLE': 1,
  'EXTENSION': 1,
  'PYMODULE': 1,
  'PYSOURCE': 1,
  'PYZ': 0,
  'SPLASH': 1},
 [('PYZ-00.pyz',
   'G:\\kubao-aimp\\aimp\\tools\\kbnlp\\scripts\\information_extraction\\build\\document_record_finetune\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'G:\\kubao-aimp\\aimp\\tools\\kbnlp\\scripts\\information_extraction\\build\\document_record_finetune\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'G:\\kubao-aimp\\aimp\\tools\\kbnlp\\scripts\\information_extraction\\build\\document_record_finetune\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'G:\\kubao-aimp\\aimp\\tools\\kbnlp\\scripts\\information_extraction\\build\\document_record_finetune\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'G:\\kubao-aimp\\aimp\\tools\\kbnlp\\scripts\\information_extraction\\build\\document_record_finetune\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'G:\\kubao-aimp\\aimp\\tools\\kbnlp\\scripts\\information_extraction\\build\\document_record_finetune\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_win32comgenpy',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_win32comgenpy.py',
   'PYSOURCE'),
  ('pyi_rth_pywintypes',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks\\rthooks\\pyi_rth_pywintypes.py',
   'PYSOURCE'),
  ('pyi_rth_pythoncom',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks\\rthooks\\pyi_rth_pythoncom.py',
   'PYSOURCE'),
  ('document_record_finetune',
   'G:\\kubao-aimp\\aimp\\tools\\kbnlp\\scripts\\information_extraction\\document_record_finetune.py',
   'PYSOURCE')],
 True,
 False,
 False,
 [],
 None,
 None,
 None)
