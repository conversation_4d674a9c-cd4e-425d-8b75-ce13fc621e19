import os
import torch, torchvision
from torchvision import transforms
import torch.nn.functional as F
from PIL import Image


class ImageClassifier:
    image_processing = transforms.Compose([
        transforms.Resize(256),
        transforms.CenterCrop(224),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406],
            std=[0.229, 0.224, 0.225])
    ])

    example_data = torch.zeros([3,224,224])

    def __init__(self):
        self.location = None
        self.device = None
        self.model_pt_path = None
        self.model = None

    def initialize(self, model_pt_path, use_gpu=True, gpu_id=None):
        self.location = 'cuda' if torch.cuda.is_available() and use_gpu else 'cpu'
        gpu_id = gpu_id if isinstance(gpu_id, int) and gpu_id >= 0 else os.environ.get('_GPU_ID', '0')
        self.device = (
            torch.device(f"{self.location}:{gpu_id}")
            if torch.cuda.is_available() and use_gpu
            else self.location
        )
        self.model_pt_path = model_pt_path
        self.model = self._load_torchscript_model()

    def preprocess(self, fp):
        with Image.open(fp, 'r') as img:
            return self.image_processing(img)

    def inference(self, data, *args, **kwargs):
        with torch.inference_mode():
            data = data.to(self.device)
            return self.model(data, *args, **kwargs)

    def postprocess(self, result):
        result = F.softmax(result, dim=1)
        result = torch.threshold(result, 1e-3, 0)
        return result.tolist()

    def _load_torchscript_model(self):
        return torch.jit.load(self.model_pt_path, map_location=self.device)
