from kbnlp.utils.datareader import CoNLLReader
from kbnlp.pretrained.feature import extract_feature, extract_position_feature, print_feature
from typing import List
from collections import defaultdict
from collections import deque
SHIFT = 0
LEFT = 1
RIGHT = 2
w_root_id = 1


class Configure:
    def __init__(self, sentence_length):
        self.length = sentence_length  # 原始句子的长度，不包括root词。
        self.buffer = deque()
        for i in range(2, self.length + 2):  # root词放在buffer的最后。
            self.buffer.append(i)
        self.stack = [1]  # 直接将句子的第一个单词放入stack。句子的索引0暂时不使用，将来可能拿来处理将root词放在开头的情况。
        # head[i]代表句子中第i个单词的中心词在句子中的索引，如第2个单词的中心词是第4个单词，则head[2]= 4。-1代表当前config暂时还没找到该单词的中心词。
        self.heads = [-1] * (self.length + 1)
        self.arcs = [-1] * (self.length + 1)  # 句子中第i个单词和中心词的依存关系。-1代表当前config还没找到该单词的中心词。

    def commit(self, action: int, label):  # 不检测action是否可以执行, label是依存弧上的依存关系
        try:
            if action == SHIFT:
                self.stack.append(self.buffer[0])
                self.buffer.popleft()  # = self.buffer[1:]
            elif action == LEFT:
                self.heads[self.stack[-2]] = self.stack[-1]
                self.arcs[self.stack[-2]] = label
                self.stack = self.stack[:-2] + self.stack[-1:]
            elif action == RIGHT:
                self.heads[self.stack[-1]] = self.stack[-2]
                self.arcs[self.stack[-1]] = label
                self.stack = self.stack[:-1]
            else:
                raise ValueError("action:'%d'，无此操作。" % action)
        except (IndexError, ValueError) as e:
            print(repr(self.stack) + ' | ' + repr(list(self.buffer)), '\n', self.heads, '\n', self.arcs)
            raise e

    def is_final_state(self):
        #  stack中只有root词时，认为是终止状态。
        return len(self.stack) == 1 and self.s1 == self.length + 1 and len(self.buffer) == 0

    def stack_size(self):
        return len(self.stack)

    def buffer_size(self):
        return len(self.buffer)

    def get_head_index(self, index_dependant):  # 返回中心词在句子中的索引，第一个单词索引为1.
        if index_dependant <= 0 or index_dependant >= self.length + 2:
            raise ValueError('单词在句子中的索引从1开始，不能比1小，也不能大于句子长度＋1。')
        elif index_dependant <= self.length:
            return self.heads[index_dependant]
        else:  # root词没有中心词。
            return None

    def get_buffer3(self):  # 获取buffer中最多前三个单词在句子中的索引
        return [self.buffer[i] for i in range(3) if i < len(self.buffer)]

    def get_real_words_index_in_buffer(self):  # 将来应对root词放在句子开头或者末尾两种情况。
        buf = self.buffer.copy()
        buf.pop()
        return buf

    @property
    def s1(self):
        return self.stack[-1]

    @property
    def s2(self):
        return self.stack[-2] if self.stack_size() >= 2 else None

    @property
    def s3(self):
        return self.stack[-3] if self.stack_size() >= 3 else None

    @property
    def b1(self):
        return self.buffer[0] if self.buffer_size() >= 1 else None

    @property
    def b2(self):
        return self.buffer[1] if self.buffer_size() >= 2 else None

    @property
    def b3(self):
        return self.buffer[2] if self.buffer_size() >= 3 else None

    @property
    def s1_lc1(self):
        s1 = self.s1
        start = self.s2 + 1 if self.stack_size() >= 2 else 1
        for i in range(start, s1):
            if self.heads[i] == s1:
                return i
        return None

    @property
    def s1_lc2(self):
        s1 = self.s1
        count = 0
        start = self.s2 + 1 if self.stack_size() >= 2 else 1
        for i in range(start, s1):
            if self.heads[i] == s1:
                count += 1
                if count == 2:
                    return i
        return None

    @property
    def s1_rc1(self):
        s1 = self.s1
        if self.buffer_size() == 0:  # 此时s1是root词
            return None
        for i in range(self.b1 - 1, s1, -1):
            if self.heads[i] == s1:
                return i
        return None

    @property
    def s1_rc2(self):
        s1 = self.s1
        if self.buffer_size() == 0:  # 此时s1是root词
            return None
        count = 0
        for i in range(self.b1 - 1, s1, -1):
            if self.heads[i] == s1:
                count += 1
                if count == 2:
                    return i
        return None

    @property
    def s2_lc1(self):
        if self.stack_size() < 2:
            return None
        s2 = self.s2
        start = self.s3 + 1 if self.stack_size() >= 3 else 1
        for i in range(start, s2):
            if self.heads[i] == s2:
                return i
        return None

    @property
    def s2_lc2(self):
        if self.stack_size() < 2:
            return None
        s2 = self.s2
        count = 0
        start = self.s3 + 1 if self.stack_size() >= 3 else 1
        for i in range(start, s2):
            if self.heads[i] == s2:
                count += 1
                if count == 2:
                    return i
        return None

    @property
    def s2_rc1(self):
        if self.stack_size() < 2:
            return None
        s2 = self.s2
        for i in range(self.s1 - 1, s2, -1):
            if self.heads[i] == s2:
                return i
        return None

    @property
    def s2_rc2(self):
        if self.stack_size() < 2:
            return None
        s2 = self.s2
        count = 0
        for i in range(self.s1 - 1, s2, -1):
            if self.heads[i] == s2:
                count += 1
                if count == 2:
                    return i
        return None

    @property
    def s1_lc1_lc1(self):
        s1_lc1 = self.s1_lc1
        if s1_lc1 is None:
            return None
        start = self.s2 + 1 if self.stack_size() >= 2 else 1
        for i in range(start, s1_lc1):
            if self.heads[i] == s1_lc1:
                return i
        return None

    @property
    def s1_rc1_rc1(self):
        s1_rc1 = self.s1_rc1
        if s1_rc1 is None:
            return None
        for i in range(self.b1 - 1, s1_rc1, -1):
            if self.heads[i] == s1_rc1:
                return i
        return None

    @property
    def s2_lc1_lc1(self):
        s2_lc1 = self.s2_lc1
        if s2_lc1 is None:
            return None
        start = self.s3 + 1 if self.stack_size() >= 3 else 1
        for i in range(start, s2_lc1):
            if self.heads[i] == s2_lc1:
                return i
        return None

    @property
    def s2_rc1_rc1(self):
        s2_rc1 = self.s2_rc1
        if s2_rc1 is None:
            return None
        for i in range(self.s1 - 1, s2_rc1):
            if self.heads[i] == s2_rc1:
                return i
        return None

    def __repr__(self):
        return repr(self.stack) + ' | ' + repr(list(self.buffer))


class StaticOracle:
    @staticmethod
    def getGoldAction(heads, dep_labels, configure):
        gold_heads = heads
        gold_labels = dep_labels
        if not configure.is_final_state():
            if len(configure.stack) == 1:
                return SHIFT, None
            elif gold_heads[configure.stack[-2]] == configure.stack[-1]:
                return LEFT, gold_labels[configure.stack[-2]]
            elif gold_heads[configure.stack[-1]] == configure.stack[-2]:
                for index in configure.get_real_words_index_in_buffer():  # 不考虑root词，任何词的依存词不可能是root词。
                    if gold_heads[index] == configure.stack[-1]:
                        return SHIFT, None
                return RIGHT, gold_labels[configure.stack[-1]]
            else:
                return SHIFT, None

        else:
            return None, None

    @staticmethod
    def getPossibleAction(configure):
        possible_action = []
        if StaticOracle.isPossibleSHIFT(configure):
            possible_action.append(SHIFT)
        if StaticOracle.isPossibleLEFT(configure):
            possible_action.append(LEFT)
        if StaticOracle.isPossibleRIGHT(configure):
            possible_action.append(RIGHT)
        return tuple(possible_action)

    @staticmethod
    def isPossibleSHIFT(configure):
        if len(configure.buffer) > 1 or (len(configure.buffer) == 1 and len(configure.stack) == 1):
            return True
        else:
            return False

    @staticmethod
    def isPossibleLEFT(configure):
        if len(configure.stack) >= 2:
            return True
        else:
            return False

    @staticmethod
    def isPossibleRIGHT(configure):
        if len(configure.stack) >= 2 and len(configure.buffer) > 0:
            return True
        else:
            return False