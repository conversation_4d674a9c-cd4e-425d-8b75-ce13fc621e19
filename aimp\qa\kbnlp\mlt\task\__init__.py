import torch
from typing import Dict
from abc import ABC, abstractmethod
from qa.kbnlp.mlt.util import merge_locals_kwargs
from qa.kbnlp.mlt.common.configurable import ConfigTracker
from qa.kbnlp.mlt.data_transform import VocabDict
from qa.kbnlp.mlt.common.vocab import Vocab


class Task(ConfigTracker, ABC):
    def __init__(self,
                 trn=None,
                 dev=None,
                 tst=None,
                 batch_size=None,
                 batch_max_tokens=None,
                 eval_batch_size=None,
                 eval_batch_max_tokens=None,
                 # dependencies: str = None,
                 lr=None,
                 separate_optimizer=False,
                 cls_is_bos=False,
                 sep_is_eos=False,
                 **kwargs
                 ):
        if eval_batch_size is None:
            eval_batch_size = batch_size
        if eval_batch_max_tokens is None:
            eval_batch_max_tokens = batch_max_tokens
        super(Task, self).__init__(
            merge_locals_kwargs(locals(), kwargs, excludes=['self', 'kwargs', '__class__', 'trn', 'dev', 'tst']))
        self.tst = tst
        self.dev = dev
        self.trn = trn
        self.batch_size = batch_size
        self.eval_batch_size = eval_batch_size
        self.batch_max_token = batch_max_tokens
        self.eval_batch_max_tokens = eval_batch_max_tokens
        # self.dependencies = dependencies
        self.lr = lr
        self.separate_optimizer = separate_optimizer
        self.cls_is_bos = cls_is_bos
        self.sep_is_eos = sep_is_eos

    @abstractmethod
    def build_model(self, encoder_size, task_name, **kwargs):
        raise NotImplementedError

    def build_vocab(self, task_name):
        self.vocabs['%s_tag' % task_name] = Vocab(pad_token=None, unk_token=None)

    @abstractmethod
    def build_sample(self, data, **kwargs):
        raise NotImplementedError

    @abstractmethod
    def build_transformList(self, mode, task_name, **kwargs):
        """
        :param mode: 模型的运行方式，'train'表示训练；'eval'表示对验证集或者测试集评估；'predict'表示预测。
        :param task_name:
        :param kwargs:
        :return:
        """
        raise NotImplementedError

    @abstractmethod
    def build_dataset(self, data, transform_list, **kwargs):
        raise NotImplementedError

    @abstractmethod
    def build_dataloader(self, dataset, batch_size=None, batch_max_tokens=None, shuffle=False, generator=None, gradient_accumulation=1,
                         use_effective_tokens=False, **kwargs):
        raise NotImplementedError

    @abstractmethod
    def build_criterion(self, reduction='mean', **kwargs):
        raise NotImplementedError

    @abstractmethod
    def build_optimizer(self, decoder, **kwargs):
        raise NotImplementedError

    @abstractmethod
    def compute_loss(self, task_name, batch, out, mask, criterion):
        raise NotImplementedError

    @abstractmethod
    def build_metric(self):
        raise NotImplementedError

    def update_metric(self, prediction, batch, metric, task_name):
        raise NotImplementedError

    @abstractmethod
    def feed_batch(self,
                   mode: str,
                   h: torch.FloatTensor,
                   batch: Dict,
                   output_dict: Dict,
                   decoder: torch.nn.Module,
                   task_name):
        """

        :param mode: 模型的运行方式，'train'表示训练；'eval'表示对验证集或者测试集评估；'predict'表示预测。
        :param h: encoder hidden state
        :param batch:
        :param output_dict:
        :param decoder: 该task的decoder pretrained
        :param task_name:
        :return:
        """
        raise NotImplementedError

    @abstractmethod
    def decode_output(self, mode, output_dict, batch, model, task_name, **kwargs):
        """
        :param mode: 模型的运行方式，'train'表示训练；'eval'表示对验证集或者测试集评估；'predict'表示预测。
        :param output_dict:
        :param batch:
        :param model: 该task的model
        :param task_name:
        :param kwargs:
        :return:
        """
        raise NotImplementedError

    @abstractmethod
    def prediction_to_result(self, pred, vocab, batch, output_dict, task_name, **kwargs):
        raise NotImplementedError

    @abstractmethod
    def post_transform(self, task_name, batch, output_dict, device, **kwargs):
        """
        如果有其他任务以来本任务，则肯能需要实现本方法。
        """
        raise NotImplementedError

    def load_vocabs(self, save_dir, filename='vocabs.json'):
        """Load vocabularies from packagea directory.

        Args:
            save_dir: The directory to load vocabularies.
            filename:  The name for vocabularies.
        """
        if hasattr(self, 'vocabs'):
            self.vocabs = VocabDict()
            self.vocabs.load_vocabs(save_dir, filename)

    def save_vocabs(self, save_dir, filename='vocabs.json'):
        """Save vocabularies to packagea directory.

        Args:
            save_dir: The directory to save vocabularies.
            filename:  The name for vocabularies.
        """
        if hasattr(self, 'vocabs'):
            self.vocabs.save_vocabs(save_dir, filename)
