from kbnlp.mlt.common.reflection import str_to_type
from kbnlp.mlt.common.reflection import classpath_of
from kbnlp.mlt.common.serializable import Serializable, SerializableDict
from typing import Dict


class Configurable(object):

    @staticmethod
    def from_config(config: dict, **kwargs):
        """Build an object from config.

        Args:
          config: A ``dict`` holding parameters for its constructor. It has to contain packagea `classpath` key,
                    which has packagea classpath str as its value. ``classpath`` will determine the type of object
                    being deserialized.
          kwargs: Arguments not used.

        Returns: A deserialized object.

        """
        cls = config.get('classpath', None)
        assert cls, f'{config} doesn\'t contain classpath field'
        cls = str_to_type(cls)
        deserialized_config = dict(config)
        for k, v in config.items():
            if isinstance(v, dict) and 'classpath' in v:
                deserialized_config[k] = Configurable.from_config(v)
        if cls.from_config == Configurable.from_config:
            deserialized_config.pop('classpath')
            return cls(**deserialized_config)
        else:
            return cls.from_config(deserialized_config)


class ConfigTracker(Configurable):

    def __init__(self, locals_: Dict, exclude=('kwargs', 'self', '__class__', 'locals_')) -> None:
        """This base class helps sub-classes to capture their arguments passed to ``__init__``, and also their types so
        that they can be deserialized from packagea config in dict form.

        Args:
            locals_: Obtained by :meth:`locals`.
            exclude: Arguments to be excluded.

        Examples:
            >>> class MyClass(ConfigTracker):
            >>>     def __init__(self, i_need_this='yes') -> None:
            >>>         super().__init__(locals())
            >>> obj = MyClass()
            >>> print(obj.config)
            {'i_need_this': 'yes', 'classpath': 'test_config_tracker.MyClass'}

        """
        if 'kwargs' in locals_:
            locals_.update(locals_['kwargs'])
        self.config = SerializableDict(
            (k, v.config if hasattr(v, 'config') else v) for k, v in locals_.items() if k not in exclude)
        self.config['classpath'] = classpath_of(self)


class AutoConfigurable(Configurable):
    @property
    def config(self) -> dict:
        """
        The config of this object, which are public properties. If any properties needs to be excluded from this config,
        simply declare it with prefix ``_``.
        """
        return dict([('classpath', classpath_of(self))] +
                    [(k, v.config if hasattr(v, 'config') else v)
                     for k, v in self.__dict__.items() if
                     not k.startswith('_')])

    def __repr__(self) -> str:
        return repr(self.config)
