# coding:utf-8
# Copyright (c) 2021  PaddlePaddle Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License"
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import threading
import warnings

import torch.cuda

from .information_extraction import UIETask, AutoPromptUIETask  # GPTask
from .intent_detection import IntentDetectionTask
from .document_record import DocumentRecordTask
from .monograph_record import MonographRecordTask

# warnings.simplefilter(action="ignore", category=Warning, lineno=0, append=False)

TASKS = {
    'document_record': {
            "models": {
                "uie-nano": {
                    "task_class": DocumentRecordTask
                },
                "uie-x-base": {
                    "task_class": DocumentRecordTask
                },
            },
            "default": {
                "model": "uie-nano"
            }
        },
    'monograph_record':{
            "models": {
                "uie-nano": {
                    "task_class": MonographRecordTask
                },
                "uie-x-base": {
                    "task_class": MonographRecordTask
                },
            },
            "default": {
                "model": "uie-nano"
            }
        },
    'intent_detection': {
        "models": {
            "uie-base": {
                "task_class": IntentDetectionTask,
            },
            "uie-tiny": {
                "task_class": IntentDetectionTask,
            },
            "uie-nano": {
                "task_class": IntentDetectionTask,
            },
        },
        "default": {
            "model": "uie-tiny"
        }
    },
    "information_extraction": {
        "models": {
            "uie-base": {
                "task_class": UIETask,
            },
            "uie-tiny": {
                "task_class": UIETask,
            },
            "uie-nano": {
                "task_class": UIETask,
            },
            "uie-x-base": {
                "task_class": UIETask,
            },
            "uie-x-base-v0": {
                "task_class": UIETask,
            },
            "auto-uie-nano": {
                "task_class": AutoPromptUIETask,
            },
            "auto-uie-tiny": {
                "task_class": AutoPromptUIETask,
            },
            "auto-uie-base": {
                "task_class": AutoPromptUIETask,
            },
            # "uie-data-distill-gp": {"task_class": GPTask, "task_flag": "information_extraction-uie-data-distill-gp"},
        },
        "default": {"model": "uie-nano"},
    }
}

support_schema_list = [
    "uie-base",
    "uie-nano",
    "uie-tiny",
    "uie-x-base",
    "ie-x-base-v0",
    # "auto-uie-base"
    # "auto-uie-nano",
    # "auto-uie-tiny",
]


class Taskflow(object):
    """
    The Taskflow is the end2end interface that could convert the raw text to model result, and decode the model result to taskflow result. The main functions as follows:
        1) Convert the raw text to taskflow result.
        2) Convert the model to the inference model.
        3) Offer the usage and help message.
    Args:
        task (str): The taskflow name for the Taskflow, and get the taskflow class from the name.
        model (str, optional): The model name in the taskflow, if set None, will use the default model.

        device (str, optional): The device for the gpu devices, the defalut value is cpu.
        kwargs (dict, optional): Additional keyword arguments passed along to the specific taskflow.
    """

    def __init__(self, task, model=None, device='cpu', **kwargs):
        assert task in TASKS, f"The taskflow name:{task} is not in Taskflow list, please check your taskflow name."
        self.task = task

        tag = "models"
        ind_tag = "model"
        self.model = model

        if self.model is not None:
            assert self.model in set(TASKS[task][tag].keys()), f"The {tag} name: {model} is not in taskflow:[{task}]"
        else:
            self.model = TASKS[task]["default"][ind_tag]

        # Set the device for the taskflow
        if device == 'cpu' or device is None:
            device = 'cpu'
        elif isinstance(device, str) and device.startswith('cuda'):
            device = device if torch.cuda.is_available() else 'cpu'
        else:
            raise ValueError('device 值设置错误。')

        # Update the taskflow config to kwargs
        config_kwargs = dict(TASKS[self.task][tag][self.model])
        kwargs.update(config_kwargs)
        self.kwargs = kwargs
        task_class = TASKS[self.task][tag][self.model]["task_class"]
        self.task_instance = task_class(
            model=self.model, task=self.task, device=device, **self.kwargs
        )
        # task_list = TASKS.keys()
        # Taskflow.task_list = task_list

        # Add the lock for the concurrency requests
        # self._lock = threading.Lock()

    def __call__(self, *inputs):
        """
        The main work function in the taskflow.
        inputs:task的输入。
        """
        results = self.task_instance(inputs)
        return results

    def prepare_model(self):
        if hasattr(self.task_instance, 'prepare_model') and callable(self.task_instance.prepare_model):
            self.task_instance.prepare_model()
        else:
            warnings.warn(f"{self.task}没有'prepare_model'方法.")

    # def help(self):
    #     """
    #     Return the taskflow usage message.
    #     """
    #     return self.task_instance.help()

    def to(self, device):
        self.task_instance.to(device)

    @property
    def device(self):
        return self.task_instance.device

    def task_path(self):
        """
        Return the path of current taskflow
        """
        return self.task_instance._task_path

    # @staticmethod
    # def tasks():
    #     """
    #     Return the available taskflow list.
    #     """
    #     task_list = list(TASKS.keys())
    #     return task_list

    # def from_segments(self, *inputs):
    #     results = self.task_instance.from_segments(inputs)
    #     return results
    #
    # def interactive_mode(self, max_turn):
    #     with self.task_instance.interactive_mode(max_turn):
    #         while True:
    #             human = input("[Human]:").strip()
    #             if human.lower() == "exit":
    #                 exit()
    #             robot = self.task_instance(human)[0]
    #             print("[Bot]:%s" % robot)

    def set_schema(self, schema):
        assert (
            self.task_instance.model in support_schema_list
        ), "This method can only be used by the taskflow of uie series."
        self.task_instance.set_schema(schema)

    # def set_argument(self, argument):
    #     assert (
    #         self.task_instance.model in support_argument_list
    #     ), "This method can only be used by the taskflow with the model of text_to_image generation."
    #     self.task_instance.set_argument(argument)