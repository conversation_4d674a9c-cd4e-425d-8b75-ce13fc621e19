from pprint import pprint
import paddle
import torch
import math
from collections import OrderedDict
import numpy as np
from kbnlp.experiment.convert_pytorch.modeling_ernie_layout import <PERSON><PERSON><PERSON>out<PERSON>odel, ErnieLayoutForSequenceClassification, Ernie<PERSON>ayoutForTokenClassification, Ernie<PERSON>ayoutForQuestionAnswering
from kbnlp.experiment.convert_pytorch.configuration_ernie_layout import Ernie<PERSON><PERSON>outConfig
from paddlenlp.transformers import ErnieLayoutModel as ErnieLayoutModel2
from paddlenlp.transformers import ErnieLayoutForSequenceClassification as <PERSON><PERSON><PERSON>outForSequenceClassification2
from paddlenlp.transformers import <PERSON><PERSON><PERSON>outForTokenClassification as Ernie<PERSON>ayoutForTokenClassification2
from paddlenlp.transformers import <PERSON><PERSON><PERSON>out<PERSON>or<PERSON>uestionAnswering as ErnieLayoutForQuestionAnswering2

paddle.set_device('cpu')


def to_numpy(data):
    if isinstance(data, torch.Tensor) or isinstance(data, paddle.Tensor):
        return data.numpy()
    else:
        return data


def convert(torch_model_class, paddle_model_class, paddle_model_name_or_path, paddle_config_dict, model_inputs, num_labels=0, acc=4):
    if num_labels > 0:
        model_p = paddle_model_class.from_pretrained('ernie-layoutx-base-uncased', num_classes=num_labels).to('cpu')
    else:
        model_p = paddle_model_class.from_pretrained('ernie-layoutx-base-uncased').to('cpu')
    torch_config_dict = dict(paddle_config_dict)
    if 'model_type' in torch_config_dict:
        torch_config_dict.pop('model_type')
    if num_labels > 0:
        torch_config_dict['num_labels'] = num_labels

    config_t = ErnieLayoutConfig(**torch_config_dict)
    model_t = torch_model_class(config_t)
    model_t.eval()
    model_p.eval()
    # 修改key
    state_t = OrderedDict([(k, v) for k, v in model_t.state_dict().items() if 'tracked' not in k])
    state_p = dict([(k.replace('._mean', '.running_mean').replace('._variance', '.running_var'), v)
                    if '._mean' in k or '._variance' in k else (k, v)
                    for k, v in model_p.state_dict().items()])
    assert len(state_t) == len(state_p)

    name_mismatch = [(k1, k2) for k1, k2 in zip(state_t, state_p) if k1 != k2]
    print('name_mismatch:')
    pprint(name_mismatch)

    # 修改value
    for k, v in state_p.items():
        if '._mean' in k:
            k_ = k.replace('._mean', '.running_mean')
        elif '._variance' in k:
            k_ = k.replace('._variance', '.running_var')
        else:
            k_ = k
        v_arr = v.numpy()
        v_ = torch.tensor(v_arr.T if '.weight' in k_ and 'embeddings.' not in k_ and 'conv' not in k_ and len(v_arr.shape) == 2 else v_arr)
        state_t[k_] = v_

    shape_mismatch = []
    for (k1, v1), (k2, v2) in zip(state_t.items(),
                                  [(k, v) for k, v in model_t.state_dict().items() if 'tracked' not in k]):
        assert k1 == k2
        if list(v1.size()) != list(v2.size()):
            shape_mismatch.append((k1, list(v1.size()), list(v2.size())))
    print('shape_mismatch:')
    pprint(shape_mismatch)

    # 向torch模型中加载参数
    print(model_t.load_state_dict(state_t, strict=False))

    # 验证模型精度
    model_inputs = OrderedDict([(k, to_numpy(v)) for k, v in model_inputs.items()])
    inputs_p = dict([(k, paddle.to_tensor(v, place='cpu')) for k, v in model_inputs.items()])
    # if 'labels' in inputs_p:
    #     inputs_p['labels'] = paddle.to_tensor([0] * inputs_p['labels'].shape[0])
    # inputs_p.pop('labels')
    inputs_t = dict([(k, torch.tensor(v)) for k, v in model_inputs.items()])
    with torch.no_grad():
        output_t = model_t(**inputs_t)
        if not isinstance(output_t, tuple):
            output_t = tuple(output_t)
    with paddle.no_grad():
        output_p = model_p(**inputs_p)
        if not isinstance(output_p, tuple):
            output_p = tuple(output_p)

    acc_ = math.pow(10, -acc)
    all_guaranteed = True
    assert len(output_t) == len(output_p)
    for i in range(len(output_t)):
        acc_guaranteed = np.all(np.abs(output_t[i].numpy() - output_p[i].numpy()) < acc_)
        all_guaranteed &= acc_guaranteed
        print('结果元祖中第%d个值的shape：' % (i + 1), list(output_t[i].numpy().shape))
        print('结果元祖中第%d个值保证小数点后%d位精度：' % (i, acc), acc_guaranteed)
    print('在所有的值中，保证小数点后%d位精度：' % acc, all_guaranteed)
    return model_t


if __name__ == '__main__':

    paddle_ernie_layout_config_dict_ = {
            "attention_probs_dropout_prob": 0.1,
            "bos_token_id": 0,
            "coordinate_size": 128,
            "eos_token_id": 2,
            "gradient_checkpointing": False,
            "has_relative_attention_bias": True,
            "has_spatial_attention_bias": True,
            "has_visual_segment_embedding": False,
            "hidden_act": "gelu",
            "hidden_dropout_prob": 0.1,
            "hidden_size": 768,
            "image_feature_pool_shape": [7, 7, 256],
            "initializer_range": 0.02,
            "intermediate_size": 3072,
            "layer_norm_eps": 1e-12,
            "max_2d_position_embeddings": 1024,
            "max_position_embeddings": 514,
            "max_rel_2d_pos": 256,
            "max_rel_pos": 128,
            "model_type": "ernie_layout",
            "num_attention_heads": 12,
            "num_hidden_layers": 12,
            "output_past": True,
            "pad_token_id": 1,
            "shape_size": 128,
            "rel_2d_pos_bins": 64,
            "rel_pos_bins": 32,
            "type_vocab_size": 100,
            "vocab_size": 250002,
        }
    # inputs.params保存了ErnieLayoutForTokenClassification的带标签输入。
    # model_inputs_ = paddle.load(r'D:\workspace\pycharm\nlp_project\kbnlp\experiment\convert_pytorch\uiex_inputs.params')
    # model_inputs_['labels'] = paddle.to_tensor([0] * model_inputs_['labels'].shape[0])
    # model_inputs_.pop('labels')
    # convert(ErnieLayoutForSequenceClassification, ErnieLayoutForSequenceClassification2,
    #         paddle_config_dict_, model_inputs_, num_labels=7)

    def convert_ErnieLayoutModel(paddle_model_name_or_path='ernie-layoutx-base-uncased'):
        from kbnlp.pretrained.ernie_layout.tokenization_ernie_layout_fast import ErnieLayoutTokenizerFast
        tokenizer = ErnieLayoutTokenizerFast.from_pretrained('microsoft/layoutxlm-base')
        model_inputs_ = paddle.load(r'/kbnlp/experiment/convert_pytorch/uiex_inputs.params')
        model_inputs_.pop('labels')
        model = convert(ErnieLayoutModel, ErnieLayoutModel2, paddle_model_name_or_path=paddle_model_name_or_path,
                        paddle_config_dict=paddle_ernie_layout_config_dict_, model_inputs=model_inputs_)
        model.save_pretrained('./ernie-layoutx-base-uncased')
        tokenizer.save_pretrained('./ernie-layoutx-base-uncased')

    convert_ErnieLayoutModel()

    # model_inputs_ = paddle.load(r'D:\workspace\pycharm\nlp_project\kbnlp\experiment\convert_pytorch\uiex_inputs.params')
    # model_inputs_['labels'][model_inputs_['labels'] == -100] = 0
    # # model_inputs_.pop('labels')
    # convert(ErnieLayoutForTokenClassification, ErnieLayoutForTokenClassification2, paddle_config_dict_, model_inputs_, num_labels=7)

    # model_inputs_ = paddle.load(r'D:\workspace\pycharm\nlp_project\kbnlp\experiment\convert_pytorch\uiex_inputs.params')
    # model_inputs_.pop('labels')
    # convert(ErnieLayoutForQuestionAnswering, ErnieLayoutForQuestionAnswering2, paddle_config_dict_, model_inputs_)
