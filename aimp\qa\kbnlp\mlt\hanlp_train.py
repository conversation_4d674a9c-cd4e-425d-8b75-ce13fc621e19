from hanlp.common.dataset import Sorting<PERSON><PERSON><PERSON><PERSON><PERSON>er
from hanlp.common.transform import NormalizeCharacter
from hanlp.components.mtl.multi_task_learning import MultiTaskLearning
from hanlp.components.mtl.tasks.constituency import CRFConstituencyParsing
from hanlp.components.mtl.tasks.dep import BiaffineDependencyParsing
from hanlp.components.mtl.tasks.ner.tag_ner import TaggingNamedEntityRecognition
from hanlp.components.mtl.tasks.pos import TransformerTagging
from hanlp.components.mtl.tasks.sdp import BiaffineSemanticDependencyParsing
from hanlp.components.mtl.tasks.srl.bio_srl import SpanBIOSemanticRoleLabeling
from hanlp.components.mtl.tasks.tok.tag_tok import TaggingTokenization
from hanlp.datasets.ner.msra import MSRA_NER_TOKEN_LEVEL_SHORT_IOBES_TEST, MSRA_NER_TOKEN_LEVEL_SHORT_IOBES_DEV, \
    MSRA_NER_TOKEN_LEVEL_SHORT_IOBES_TRAIN

# from hanlp.datasets.parsing.ctb8 import CTB8_POS_TRAIN, CTB8_POS_DEV, CTB8_POS_TEST, CTB8_SD330_TEST, CTB8_SD330_DEV, \
#     CTB8_SD330_TRAIN, CTB8_CWS_TRAIN, CTB8_CWS_DEV, CTB8_CWS_TEST, CTB8_BRACKET_LINE_NOEC_TEST, \
#     CTB8_BRACKET_LINE_NOEC_DEV, CTB8_BRACKET_LINE_NOEC_TRAIN

from hanlp.datasets.parsing.semeval16 import SEMEVAL2016_TEXT_TRAIN_CONLLU, SEMEVAL2016_TEXT_TEST_CONLLU, \
    SEMEVAL2016_TEXT_DEV_CONLLU
# from hanlp.datasets.srl.ontonotes5.chinese import ONTONOTES5_CONLL12_CHINESE_TEST, ONTONOTES5_CONLL12_CHINESE_DEV, \
#     ONTONOTES5_CONLL12_CHINESE_TRAIN
from hanlp.layers.embeddings.contextual_word_embedding import ContextualWordEmbedding
from hanlp.layers.transformers.relative_transformer import RelativeTransformerEncoder
from hanlp.utils.lang.zh.char_table import HANLP_CHAR_TABLE_JSON
from hanlp.utils.log_util import cprint
import os

root = os.path.abspath(os.path.join(os.path.dirname(__file__), os.pardir))

# /home/<USER>/.hanlp/thirdparty/wakespace.lib.wfu.edu/bitstream/handle/10339/39379/LDC2013T21/data/tasks/cws/
_CTB8_HOME = '/home/<USER>/.hanlp/thirdparty/wakespace.lib.wfu.edu/bitstream/handle/10339/39379/LDC2013T21/data/'
# CTB8_CWS_TRAIN = _CTB8_HOME + 'tasks/cws/train.txt'
# '''Training set for ctb8 Chinese word segmentation.'''
# CTB8_CWS_DEV = _CTB8_HOME + 'tasks/cws/dev.txt'
# '''Dev set for ctb8 Chinese word segmentation.'''
# CTB8_CWS_TEST = _CTB8_HOME + 'tasks/cws/test.txt'
# '''Test set for ctb8 Chinese word segmentation.'''

CTB8_CWS_DEMO = _CTB8_HOME + 'tasks/cws/demo.txt'
CTB8_CWS_TRAIN = _CTB8_HOME + 'tasks/cws/train.txt'
'''Training set for ctb8 Chinese word segmentation.'''
CTB8_CWS_DEV = _CTB8_HOME + 'tasks/cws/dev.txt'
'''Dev set for ctb8 Chinese word segmentation.'''
CTB8_CWS_TEST = _CTB8_HOME + 'tasks/cws/test.txt'
'''Test set for ctb8 Chinese word segmentation.'''


CTB8_POS_DEMO = _CTB8_HOME + 'tasks/pos/demo.tsv'
CTB8_POS_TRAIN = _CTB8_HOME + 'tasks/pos/train.tsv'
'''Training set for ctb8 PoS tagging.'''
CTB8_POS_DEV = _CTB8_HOME + 'tasks/pos/dev.tsv'
'''Dev set for ctb8 PoS tagging.'''
CTB8_POS_TEST = _CTB8_HOME + 'tasks/pos/test.tsv'
'''Test set for ctb8 PoS tagging.'''

CTB8_BRACKET_LINE_TRAIN = _CTB8_HOME + 'tasks/par/train.txt'
'''Training set for ctb8 constituency parsing with empty categories.'''
CTB8_BRACKET_LINE_DEV = _CTB8_HOME + 'tasks/par/dev.txt'
'''Dev set for ctb8 constituency parsing with empty categories.'''
CTB8_BRACKET_LINE_TEST = _CTB8_HOME + 'tasks/par/test.txt'
'''Test set for ctb8 constituency parsing with empty categories.'''

CTB8_BRACKET_LINE_NOEC_DEMO = _CTB8_HOME + 'tasks/par/demo.noempty.txt'
CTB8_BRACKET_LINE_NOEC_TRAIN = _CTB8_HOME + 'tasks/par/train.noempty.txt'
'''Training set for ctb8 constituency parsing without empty categories.'''
CTB8_BRACKET_LINE_NOEC_DEV = _CTB8_HOME + 'tasks/par/dev.noempty.txt'
'''Dev set for ctb8 constituency parsing without empty categories.'''
CTB8_BRACKET_LINE_NOEC_TEST = _CTB8_HOME + 'tasks/par/test.noempty.txt'
'''Test set for ctb8 constituency parsing without empty categories.'''

CTB8_SD330_DEMO = _CTB8_HOME + 'tasks/dep/demo.conllx'
CTB8_SD330_TRAIN = _CTB8_HOME + 'tasks/dep/train.conllx'
'''Training set for ctb8 in Stanford Dependencies 3.3.0 standard.'''
CTB8_SD330_DEV = _CTB8_HOME + 'tasks/dep/dev.conllx'
'''Dev set for ctb8 in Stanford Dependencies 3.3.0 standard.'''
CTB8_SD330_TEST = _CTB8_HOME + 'tasks/dep/test.conllx'
'''Test set for ctb8 in Stanford Dependencies 3.3.0 standard.'''

MSRA_NER_TOKEN_LEVEL_SHORT_IOBES_DEMO = r'/home/<USER>/.hanlp/thirdparty/file.hankcs.com/corpus/msra_ner_token_level/word_level.demo.short.tsv'
MSRA_NER_TOKEN_LEVEL_SHORT_IOBES_TRAIN = r'/home/<USER>/.hanlp/thirdparty/file.hankcs.com/corpus/msra_ner_token_level/word_level.train.short.tsv'
MSRA_NER_TOKEN_LEVEL_SHORT_IOBES_DEV = r'/home/<USER>/.hanlp/thirdparty/file.hankcs.com/corpus/msra_ner_token_level/word_level.dev.short.tsv'
MSRA_NER_TOKEN_LEVEL_SHORT_IOBES_TEST = r'/home/<USER>/.hanlp/thirdparty/file.hankcs.com/corpus/msra_ner_token_level/word_level.test.short.tsv'



def cdroot():
    """
    cd to project root, so pretrained are saved in the root folder
    """
    os.chdir(root)


cdroot()

tasks = {
    'tok': TaggingTokenization(
        CTB8_CWS_TRAIN,
        CTB8_CWS_DEV,
        None,# CTB8_CWS_DEMO,
        SortingSamplerBuilder(batch_size=32),
        max_seq_len=510,
        hard_constraint=True,
        char_level=True,
        tagging_scheme='BMES',
        lr=1e-3,
        transform=NormalizeCharacter(HANLP_CHAR_TABLE_JSON, 'token'),
    ),
    'pos': TransformerTagging(
        CTB8_POS_TRAIN,
        CTB8_POS_DEV,
        None,  # CTB8_POS_DEMO,
        SortingSamplerBuilder(batch_size=32),
        hard_constraint=True,
        max_seq_len=510,
        char_level=True,
        dependencies='tok',
        lr=1e-3,
    ),
    'ner': TaggingNamedEntityRecognition(
        MSRA_NER_TOKEN_LEVEL_SHORT_IOBES_TRAIN,
        MSRA_NER_TOKEN_LEVEL_SHORT_IOBES_DEV,
        None,  # MSRA_NER_TOKEN_LEVEL_SHORT_IOBES_DEMO,
        SortingSamplerBuilder(batch_size=32),
        max_seq_len=510,
        hard_constraint=True,
        char_level=True,
        lr=1e-3,
        secondary_encoder=RelativeTransformerEncoder(256, k_as_x=True, feedforward_dim=128),
        dependencies='tok',
    ),
    # 'srl': SpanBIOSemanticRoleLabeling(
    #     ONTONOTES5_CONLL12_CHINESE_TRAIN,
    #     ONTONOTES5_CONLL12_CHINESE_DEV,
    #     ONTONOTES5_CONLL12_CHINESE_TEST,
    #     SortingSamplerBuilder(batch_size=32, batch_max_tokens=1280),
    #     lr=1e-3,
    #     crf=True,
    #     dependencies='tok',
    # ),
    'dep': BiaffineDependencyParsing(
        CTB8_SD330_TRAIN,
        CTB8_SD330_DEV,
        None,  # CTB8_SD330_DEMO,
        SortingSamplerBuilder(batch_size=32),
        lr=1e-3,
        tree=True,
        proj=True,
        punct=True,
        dependencies='tok',
    ),
    # 'sdp': BiaffineSemanticDependencyParsing(
    #     SEMEVAL2016_TEXT_TRAIN_CONLLU,
    #     SEMEVAL2016_TEXT_DEV_CONLLU,
    #     SEMEVAL2016_TEXT_TEST_CONLLU,
    #     SortingSamplerBuilder(batch_size=32),
    #     lr=1e-3,
    #     apply_constraint=True,
    #     punct=True,
    #     dependencies='tok',
    # ),
    # 'con': CRFConstituencyParsing(
    #     CTB8_BRACKET_LINE_NOEC_DEMO,
    #     CTB8_BRACKET_LINE_NOEC_DEMO,
    #     CTB8_BRACKET_LINE_NOEC_DEMO,
    #     SortingSamplerBuilder(batch_size=3),
    #     lr=1e-3,
    #     dependencies='tok',
    # )
}
mtl = MultiTaskLearning()
save_dir = './open_tok_pos_ner_dep_electra_small'
cprint(f'Model will be saved in [cyan]{save_dir}[/cyan]')
mtl.fit(
    ContextualWordEmbedding('token',
                            "hfl/robert",
                            average_subwords=True,
                            max_sequence_length=512,
                            word_dropout=0.),
    tasks,
    save_dir,
    epochs=60,
    lr=1e-3,
    encoder_lr=1e-4,
    grad_norm=1,
    gradient_accumulation=1,
    eval_trn=False,
    devices=1,
)
