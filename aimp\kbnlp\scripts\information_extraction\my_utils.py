# Copyright (c) 2022 PaddlePaddle Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
import os
import re
from typing import Dict, Union, List
from PIL import ImageDraw, Image, ImageFont


def dbc2sbc(s):
    """
    全角转半角。
    dbc2sbc('１９９６年存栏各类牲畜１６５７．２万头（只），')  # '1996年存栏各类牲畜1657.2万头(只),'
    dbc2sbc('4²')  # '4²'
    如果使用unicodedata.normalize('4²')就会变成‘42’，这可能不是想要的。
    """
    rs = ""
    for char in s:
        code = ord(char)
        if code == 0x3000:
            code = 0x0020
        else:
            code -= 0xfee0
        if not (0x0021 <= code <= 0x7e):
            rs += char
            continue
        rs += chr(code)
    return rs


def sbc2dbc(ustring):
    """
    半角转全角
    """
    rstring = ""
    for uchar in ustring:
        inside_code = ord(uchar)
        if inside_code == 0x0020:
            inside_code = 0x3000
        else:
            if not (0x0021 <= inside_code  <= 0x7e):
                rstring += uchar
                continue
            inside_code += 0xfee0
        rstring += chr(inside_code)
    return rstring


def trim_backslash(examples):
    """ 1.8版本不确定是否有以下问题。
        doccano在导出时在字符'/'前自动添加一个'\\'字符，但是start, end的位置信息对应于原始文本。所以需要手动去除'\\'字符。
    :return: 处理后的examples.
    """
    res = []
    for exam in examples:
        exam['text'] = exam['text'].replace('\\', '')
        res.append(exam)
    return res


def deal_next_relation(examples: Dict):
    """
        doccano在给实体打标签时，有时遇到一个实体文本处在上一行的末尾和下一行的开头的情况不能直接将他们标注在一块，
    个人使用'后续'连接两个被迫分开的文本。本函数将分开标注的本文重新连接起来。同时由于ocr在识别时有时将同一行文本识别成两个文本块，或者一个单元格内有两行文字时，
    ocr会将它们分开，此时本人在标注时也使用‘后续’关系将它们连接到了一块，但对于这种情况，不需要将他们重新连接，因为它们有不同的bbox，不影响使用layoutlm模型。
    :param examples:
    :return: 处理后的examples.
    """
    res = []
    for exam in examples:
        relations = exam['relations']
        id2entity = dict()
        for entity in exam['entities']:
            id2entity[entity['id']] = entity
        roots = set(id2entity.keys())
        start2end = {}
        t_id2real_relations = {}
        f_id2real_relations = {}
        for rel in relations:
            f_id = rel['from_id']
            t_id = rel['to_id']
            if rel['type'] == '后续':
                f_e = id2entity[f_id]
                t_e = id2entity[t_id]
                if f_e['end_offset'] == t_e['start_offset']:
                    # 这种情况下的'后续'关系被认为是一种伪的，为了应付doccano不能标注占据两行实体的问题而创建的。
                    start2end[f_id] = t_id
                    roots.remove(t_id)
                else:
                    t_id2real_relations.setdefault(t_id, []).append(rel)
                    f_id2real_relations.setdefault(f_id, []).append(rel)
            else:
                t_id2real_relations.setdefault(t_id, []).append(rel)
                f_id2real_relations.setdefault(f_id, []).append(rel)

        for e_id in roots:
            cur = e_id
            e = id2entity[e_id]
            while cur in start2end:
                cur = start2end[cur]
                e_cur = id2entity[cur]
                e['end_offset'] = e_cur['end_offset']
                if cur in t_id2real_relations:
                    for rel in t_id2real_relations[cur]:
                        rel['to_id'] = e_id
                if cur in f_id2real_relations:
                    for rel in f_id2real_relations:
                        rel['from_id'] = e_id
        entities = sorted(list([id2entity[idx] for idx in roots]), key=lambda ent: ent['id'])
        relations = sorted(sum(t_id2real_relations.values(), []), key=lambda r: r['id'])
        exam['entities'] = entities
        exam['relations'] = relations
        res.append(exam)
    return res


def map_offset(ori_offset, offset_mapping):
    """
    map ori offset to token offset
    """
    for index, span in enumerate(offset_mapping):
        if span[0] <= ori_offset < span[1]:
            return index
    return -1


def unify_prompt_name(prompt):
    # The classification labels are shuffled during finetuning, so they need
    # to be unified during evaluation.
    if re.search(r'\[.*?\]$', prompt):
        prompt_prefix = prompt[:prompt.find("[", 1)]
        cls_options = re.search(r'\[.*?\]$', prompt).group()[1:-1].split(",")
        cls_options = sorted(list(set(cls_options)))
        cls_options = ",".join(cls_options)
        prompt = prompt_prefix + "[" + cls_options + "]"
        return prompt
    return prompt


def stack_images(*images, horizontal=True, margin=10, base_color='white'):
    """
        将多张图片按行或者按列摆放。
    :param images:  PIL.Image对象。
    :param horizontal: 默认为True,水平摆放。否则垂直摆放。
    :param margin: 相邻图片之间的间隔。
    :param base_color: 底片的颜色，默认为白色。
    :return: 返回摆放好的新图片。
    """
    cum_dim = 0
    if not horizontal:
        cum_dim = 1
    cum_size_dim1 = 0
    max_size_dim2 = 0
    for image in images:
        size = image.size
        cum_size_dim1 += size[cum_dim]
        max_size_dim2 = max(max_size_dim2, size[1-cum_dim])
    cum_size_dim1 += (len(images) - 1) * margin
    if not horizontal:
        cum_size_dim1, max_size_dim2 = max_size_dim2, cum_size_dim1
    blank = Image.new("RGB", [cum_size_dim1, max_size_dim2], 'white')
    x, y = 0, 0
    for image in images:
        blank.paste(image, (x, y))
        if horizontal:
            x += image.size[0] + margin
        else:
            y += image.size[1] + margin
    return blank


def draw_bbox_label(image: Image, texts: List[str], bboxes: List[List[Union[int, float]]],
                    color: Union[List[str], str] = None, offset: int = 10, inplace=True, **kwargs):
    """
        画出box, 并将text打印在box内。
    :param image: Image对象。
    :param texts: 打印到box中的文字的列表。长度应该和bboxes相同。
    :param bboxes: 所有box的列表。任何一个box是一个长为4的序列，[x0,y0,x1,y1]。其中0,1代表box的左上角和右下角。
    :param color: 默认为空，所有字体box和文字都用green显示。可以为str，或者list[str]，前者让所有的box和文字都是用相同颜色，后者为每个（text,box）单独设置颜色。
    :param offset: 控制打印文字相对box左上角的offset像素值。默认值为10, 将文字左上角坐标x,y设置为：box[0] + 10, box[1] - 10。
    :param kwargs: 可选参数有font，用来设置字体。默认情况下使用ImageFont的默认字体。
    :param inplace : 是否在原图上修改，默认为True,否则创建一个副本，在副本上修改。
    :return: 返回修改后的image。
    """
    assert len(texts) == len(bboxes) and (not color or isinstance(color, str) or len(texts) == len(color)), \
        'texts长度应该和bboxes相同, color的长度应该于texts相同或者没有。'

    if not color:
        color = ['green'] * len(texts)
    elif isinstance(color, str):
        color = [color] * len(texts)
    if not inplace:
        image = image.copy()
    draw = ImageDraw.Draw(image)
    font = kwargs.pop('font', ImageFont.load_default())
    for text, box, co in zip(texts, bboxes, color):
        try:
            draw.rectangle(box, outline=co)
        except Exception as e:
            print(box)
            raise e
        draw.text((box[0], box[1] - offset), text=text, fill=co, font=font)
    return image


def unnormalize_box(bbox, width, height):
    """
        对于一张图片，在使用LayoutLM前会将bbox归一化到(1000, 1000)的矩形上。使用本函数可以bbox反规范化。
    :param bbox:
    :param width:  原始图片的width。
    :param height:  原始图片的height。
    :return:
    """
    return [
        width * (bbox[0] / 1000),
        height * (bbox[1] / 1000),
        width * (bbox[2] / 1000),
        height * (bbox[3] / 1000),
    ]


def sort_boxes(bboxes: List[List[Union[int, float]]], margin_patient: Union[int, float] = 18.):
    # arr = sorted([(i, box, box[0], box[1]) for i, box in enumerate(bboxes)], key=lambda x: x[1][1])
    arr = sorted([(i, box, (box[0] + box[2]) / 2, (box[1] + box[3]) / 2) for i, box in enumerate(bboxes)],
                 key=lambda x: (x[1][1] + x[1][3]) / 2)
    bucket_id = -1
    last = -100
    arr_ = []
    for ind, box, x, y in arr:
        if y - last > margin_patient:
            bucket_id += 1
        arr_.append((ind, box, x, y, bucket_id))
        last = y
    arr = arr_
    arr = sorted(arr, key=lambda tup: (tup[4], tup[2]))
    last_bucket = -1
    start_ids_per_line = []
    # ind代表在原始bbox列表中索引
    for i, (ind, box, x, y, bucket_id) in enumerate(arr):
        if bucket_id != last_bucket:
            start_ids_per_line.append(i)
            last_bucket = bucket_id
    indices = [arr_i[0]for arr_i in arr]
    bboxes = [arr_i[1]for arr_i in arr]
    return indices, bboxes, start_ids_per_line


def ocr_sort_draw(image: str, ocr_system=None,
                  use_customized_pp_ocr=False, margin_patient=18.,
                  other_word=None,
                  other_bbox=None,
                  show_word=True,
                  show_other_word=True,
                  offset: int = 20,
                  inplace=False,
                  font=None,
                  ):
    """
    1，对一张图片进行ocr；2，将返回的结果重新排序；3，将排序后的结果画到图片上，包括阿box，text，被判定为同一行的文字、文本框用相同的颜色。
    Args:
        image: 图片路径
        ocr_system: ocr system可以是pp ocr或者zhu ocr。当该值不为None时，use_customized_pp_ocr无效。该参数必须有一个方法为'ocr'。
        use_customized_pp_ocr: 默认值为False，使用zhu ocr;否则使用pp ocr。
        margin_patient: 文本框y轴相差小于或等于该值的文本框，被判定为同一行文本。
        other_word: 其他文本框对应的文字。
        other_bbox: 其他任意任意指定的文本框。如果该字段为None,则other_word、show_other_word都失效。
        show_word: 该字段表示是否展示图片的ocr结果到每个文本框上。默认为True，该文本框所在行号和该行的文本框序号以及该文本框的ocr结果；
            否则只展示该文本框所在行号和该行的文本框序号。
        show_other_word: 该字段表示是否展示other_word到other_bbox之上。默认为True,将word展示到指定的other box之上，否则不展示。不影响other box的显示。
        offset: 控制打印文字相对box左上角的offset像素值。该字段需要和设定font字体size相同。
        inplace: 是否在原图上修改，如果为False,将创建一个副本，在副本上修改。
        font: 用来设置字体。
        **kwargs:

    Returns: 返回画出box和text的图片。

    """
    if font is None:
        font = ImageFont.truetype(r'D:\workspace\pycharm\layoutLMv3\simfang.ttf', size=20)
    if ocr_system is None:
        if not use_customized_pp_ocr:
            from kbnlp.scripts.information_extraction.zhu_ocr import OCRSystem
            ocr_system = OCRSystem(det_model_path=os.path.join(os.path.dirname(os.path.abspath(__file__)),
                                                               'dtm/001.pt'),
                                   cls_model_path=os.path.join(os.path.dirname(os.path.abspath(__file__)),
                                                               'dtm/003.pt'),
                                   cls_vertical_model_path=os.path.join(os.path.dirname(os.path.abspath(__file__)),
                                                                        'dtm/006.pt'),
                                   rec_model_path=os.path.join(os.path.dirname(os.path.abspath(__file__)),
                                                               'dtm/tp1t7.pt'),
                                   rec_char_dict_path=os.path.join(os.path.dirname(os.path.abspath(__file__)),
                                                                   'dtm/ppocr_keys_v1.txt'))  # , det_max_side_len=1152)
        else:
            from kbnlp.scripts.information_extraction.pp_ocr_customized import PaddleOCRCustomized
            import paddle
            ocr_system = PaddleOCRCustomized(show_log=False, lang='ch', use_gpu=True if paddle.get_device().startswith('gpu') else False)
    ocr_res = ocr_system.ocr(image)[0]

    word = [x[1][0] for x in ocr_res]
    char_positions = None
    if len(ocr_res) > 0 and len(ocr_res[0][1]) >= 3:
        char_positions = [x[1][2] for x in ocr_res]
    bbox = [[min(x[0][0][0], x[0][3][0]), min(x[0][0][1], x[0][1][1]), max(x[0][2][0], x[0][1][0]),
             max(x[0][2][1], x[0][3][1])] for x in ocr_res]
    indices, bbox, start_ids = sort_boxes(bbox, margin_patient=margin_patient)
    word = [word[ind] for ind in indices]
    color = ['red'] * len(word)
    ids = start_ids + [len(word)]
    for i, (s, e) in enumerate(zip(ids[:-1], ids[1:])):
        for ii in range(s, e):
            if i % 2 == 0:
                color[ii] = 'blue'
            word[ii] = str(i + 1) + '-' + str(ii - s + 1) + ('：' + word[ii] if show_word else '')

    img = draw_bbox_label(Image.open(image).convert("RGB"), word, bbox, color=color,
                          font=font, offset=offset, inplace=inplace)
    if other_bbox is not None:
        if show_other_word and other_word is not None:
            assert len(other_word) == len(other_bbox)
        else:
            other_word = [''] * len(other_bbox)
        img = draw_bbox_label(img, other_word, other_bbox, color='black', font=font, offset=offset, inplace=inplace)
    if char_positions is not None:
        draw = ImageDraw.Draw(img)
        for positions in char_positions:
            for x, y in positions:
                draw.line([(x, y - 13), (x, y + 13)], fill=(0, 255, 0), width=3)
    return img


# def draw_ocr_result(tx_path, im_path=None, font=ImageFont.truetype(r'D:\workspace\pycharm\layoutLMv3\simfang.ttf')):
#     """
#
#     Args:
#         tx_path: ocr解析结果文件存放的位置。是一个jsonl文件。
#         im_path: 图片位置,默认为None，此时tx_path指定的文件中需要指定'image_path'，否则报错。如果im_path不是None,则从im_path出加载图片。
#         font: 字体。
#
#     Returns:
#
#     """
#     with jsonlines.open(tx_path, mode='r') as jr:
#         for exam in jr:
#             bboxes = exam['bboxes']
#             # for box in bboxes:
#             #     box[0] -= 8
#             #     box[1] -= 8
#             #     box[2] += 8
#             #     box[3] += 5
#             words = exam['words']
#             image = Image.open(im_path if im_path else exam.pop('image_path')).convert("RGB")
#             break
#
#     image_ = draw_bbox_label(image, words, bboxes, inplace=False, offset=font.size, font=font)
#     return image_


def box_coverage_area(box1, box2):
    """calc intersection over box1 area"""
    x1 = max(box1[0], box2[0])
    y1 = max(box1[1], box2[1])
    x2 = min(box1[2], box2[2])
    y2 = min(box1[3], box2[3])
    if x2 <= x1 or y2 <= y1:
        return 0.0
    box1_area = (box1[2] - box1[0]) * (box1[3] - box1[1])
    return (x2 - x1) * (y2 - y1) * 1.0 / box1_area


def isInside(x1, y1, x2, y2, x3, y3, x4, y4, x, y):
    """判断(x,y)是否在矩形内部, 其中1、2、3、4代表矩形按照顺时针顺序的顶点序列。"""
    def getCross(x1_, y1_, x2_, y2_, x_, y_):
        """计算(x1_,y1_)(x2_,y2_)、(x1_,y1_)(x_,y_)向量的叉乘"""
        a = (x2_ - x1_, y2_ - y1_)
        b = (x_ - x1_, y_ - y1_)
        return a[0] * b[1] - a[1] * b[0]
    return (getCross(x1, y1, x2, y2, x, y) * getCross(x3, y3, x4, y4, x, y) >= 0 and
            getCross(x2, y2, x3, y3, x, y) * getCross(x4, y4, x1, y1, x, y) >= 0)