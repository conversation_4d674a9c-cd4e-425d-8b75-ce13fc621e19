from paddlenlp.transformers import XLMTokenizer, LayoutXLMTokenizer, ErnieLayoutTokenizer
from transformers import XLMRobertaTokenizer, XLMRobertaTokenizerFast
from kbnlp.pretrained.ernie_layout.tokenization_ernie_layout import Ernie<PERSON><PERSON><PERSON><PERSON>okenizer as Ernie<PERSON><PERSON><PERSON>Tokenizer2
from kbnlp.pretrained.ernie_layout.tokenization_ernie_layout_fast import ErnieLayoutTokenizerFast as ErnieLayoutTokenizerFast2


text = '今天天气好。'
text_pair = '中华人民共和国'


xlm_roberta_tokenizer = XLMRobertaTokenizerFast.from_pretrained('xlm-roberta-base')
res = xlm_roberta_tokenizer(text, text_pair, return_token_type_ids=True)
print(res)
print(xlm_roberta_tokenizer.convert_ids_to_tokens(res['input_ids']))

xlm_roberta_tokenizer2 = ErnieLayoutTokenizerFast2.from_pretrained('xlm-roberta-base')  # 可以直接加载xlm-roberta的tokenizer
res2 = xlm_roberta_tokenizer2(text, text_pair, return_token_type_ids=True)
print(res2)
print(xlm_roberta_tokenizer2.convert_ids_to_tokens(res2['input_ids']))
xlm_roberta_tokenizer2.save_pretrained(r'D:\workspace\pycharm\nlp_project\kbnlp\pretrained\ernie_layout\temp')

tokenizer = ErnieLayoutTokenizer.from_pretrained('ernie-layoutx-base-uncased')
res3 = tokenizer(text, text_pair, return_token_type_ids=True)
print(res3)
print(tokenizer.convert_ids_to_tokens(res3['input_ids']))

