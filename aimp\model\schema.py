# -*- coding: utf-8 -*-
# __author__:SH
# 2022/10/31 10:16
import json

from fastapi import  Form, File, UploadFile
from pydantic import BaseModel, Field, validator
from typing import Union,List

class IdIn(BaseModel):
    # id单个id传入
    id: int


class IdInList(BaseModel):
    id_list:list[int]


class ResponseMsg(BaseModel):
    # 只返回内容提示信息
    msg: str

class ResponseCode(BaseModel):
    code:int

class AiModelInformation(BaseModel):
    model_name: str
    dataset_name:str
    mode:int
    test:str
    status:int
    model_precent:float
    # model_type: int
    # dataset_name: str
    # practice_status: int
    # practice_version: str
    # publish_version: str

class AiModelInformation(BaseModel):
    model_id:str
    model_name: str
    create_user: str
    create_time: str
    status:int
    publish_version: str
    current_version: str
    dataset: str
    explain: str


class IsShow(BaseModel):
    is_show:AiModelInformation




class CreateModelIn(BaseModel):
    model_name:str
    # dataset:list[int]
    # test:int
    create_user:str
    dataset:list[int]
    test:list[int]
    switch:int
    radio:str


class ModelOut(BaseModel):
    key:int
    id:int
    model_name:str
    model_type:int
    dataset_name:str
    practice_status:int
    practice_version:str
    publish_version:str


class ModelOutList(BaseModel):
    count:int
    model_list:list[ModelOut]


class ModelType(BaseModel):
    model_type:Union[int,None]=None


class ModelDrop(BaseModel):
    model_id: int
    model_name: str


class ModelDropList(BaseModel):
    return_list:list[ModelDrop]


class ModelVersion(BaseModel):
    key:int
    id:int
    name:str
    version:str
    status:int
    is_publish:str


class ModelVersionList(BaseModel):
    return_list:list[ModelVersion]


class Paging(BaseModel):
    order_key: str = Field(default="id")  # 排序条件
    order: int = Field(default=1)  # 顺序还是逆序
    page: int = Field(default=1)  # 页数
    size: int = Field(default=10)  # 大小


class SearchIn(BaseModel):
    search_data:str


class ServeIn(BaseModel):
    serve_type:int


class ServeOut(BaseModel):
    serve_id:int
    ip:str
    CPU:int
    memory:int
    disk:int
    disk_count:int
    GPU:int


class ServeOutList(BaseModel):
    server_list: list[ServeOut]




class ModelDataIn(BaseModel):
    step:str
    workdir:Union[str,None] = None
    checkpoint:Union[str,None] = None
    start_epoch:Union[str,int,None] = None
    epochs:Union[str,int,None] = None
    epoch:Union[str,int,None] = None
    device:Union[str,None]=None
    cuda_available:Union[str,None]=None


class ModelIn(BaseModel):
    detail:ModelDataIn



class GetModel(BaseModel):
    model_name:str
    dataset_type:str
    dataset_list:list[int]
    test_list:list[int]
    version:str


class VersionOut(BaseModel):
    version:str
    key:int


class VersionOutList(BaseModel):
    return_list:list[VersionOut]


class ModelTrainIn(BaseModel):
    id:int
    version:str

class ReturnModel(BaseModel):
    model_name:str
    dataset:str
    version:str
    status:int
    train:str
    precent:int


class TryOutIn(BaseModel):
    id:int
    path:str
    version:int


class Out5(BaseModel):
    color: str
    id: int
    tag_id: int
    tag_name: str


class TryOut(BaseModel):
    color: str
    id: int
    tag_id: int
    tag_name: str
    path:str
    width:int
    height:int


class Intext(BaseModel):
    text:str
    id: int
    version:int
    dataset_type:int

class ChangeModelExplain(BaseModel):
    id:int
    explain:str


class ModelIdIn(BaseModel):
    model_id:str


# class DatasetIn(BaseModel):
#     model_id:str
#     model_name:str
#     schema:list[str]
#     dataset_path:str


class DatasetIn(BaseModel):
    model_id: Union[str,None]=None
    model_name:Union[str,None]=None
    tags: list[str]
    dataset_path:str


class TrainIn(BaseModel):
    test_type: int
    model_id: Union[str, None] = None
    model_name: Union[str, None] = None
    tags:Union[list[str], None] = None
    file_tags:Union[list[str], None] = None
    relations:Union[dict, None]=None
    status_url:Union[str, None]=None
    dataset_path: str
    callback_url:str

    # @validator('relations')
    # def json_to_dict(cls, v):
    #     if v is None:
    #         return v
    #     else:
    #         for key,value in v.items():
    #             if type(value)== list:
    #                 continue
    #             else:
    #                 break
    #         else:
    #             return v




class InferenceVersionIdIn(BaseModel):
    version_id: str

    class Config:
        frozen = True


class InferenceTaskflowIn(InferenceVersionIdIn):
    tags: Union[frozenset[str],None]=None
    file_tags: Union[frozenset[str],None]=None
    class Config:
        frozen = True


class Callback(BaseModel):
    callback_url:Union[str,None]=None
    file_url:Union[str,None]=None


class InferenceIn(InferenceTaskflowIn,Callback):
    data_path: str


class InferenceImage(BaseModel):
    str111:str
    image: str


class AiModelsVersionOut(BaseModel):
    version: int
    model_id: int
    path: str
    test_type:int
    max_seq_len:int


class VersionStatus(BaseModel):
    version_id:int
    model_id:int


class ExportDatapathIn(BaseModel):
    allow_additional_training:bool
    version_id:int


class ImportDatapathIn(BaseModel):
    model_name:str
    path: str
    tags: Union[list[str], None] = None
    file_tags: Union[list[str], None] = None
    relations: Union[dict, None] = None
