import os
import torch, torchvision
from torchvision import transforms
import torch.nn.functional as F
from PIL import Image
import warnings
warnings.filterwarnings('ignore', 'RCNN|torch.meshgrid', UserWarning)


class ObjectDetector:
    image_processing = transforms.PILToTensor()

    example_data = torch.zeros([3,1,1])

    def __init__(self):
        self.location = None
        self.device = None
        self.model_pt_path = None
        self.model = None

    def initialize(self, model_pt_path, use_gpu=True, gpu_id=None):
        self.location = 'cuda' if torch.cuda.is_available() and use_gpu else 'cpu'
        gpu_id = gpu_id if isinstance(gpu_id, int) and gpu_id >= 0 else os.environ.get('_GPU_ID', '0')
        self.device = (
            torch.device(f"{self.location}:{gpu_id}")
            if torch.cuda.is_available() and use_gpu
            else self.location
        )
        self.model_pt_path = model_pt_path
        self.model = self._load_torchscript_model()

    def preprocess(self, fp):
        with Image.open(fp, 'r') as img:
            return self.image_processing(img)

    def inference(self, data, *args, **kwargs):
        with torch.inference_mode():
            data = [d.to(self.device).float() for d in data] 
            return self.model(data, *args, **kwargs)[1]

    def postprocess(self, result):
        return [{k:v.tolist() for k,v in r.items()} for r in result]

    def _load_torchscript_model(self):
        return torch.jit.load(self.model_pt_path, map_location=self.device)
