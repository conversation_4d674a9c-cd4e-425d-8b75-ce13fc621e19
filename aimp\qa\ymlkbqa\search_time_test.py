from time import time
if __name__ == '__main__':
    from chat_bot_graph import TuGraph
    from py2neo import Graph
    g = TuGraph()
    start = time()
    res = g.run("match (n: 人物 {name:'杨根思'}) return n")
    print('time cost: %.5fs.' % (time() - start))

    start = time()
    res = g.run("match (n: 人物) where n.name contains '杨根思' return n")
    print('time cost: %.5fs.' % (time() - start))

    # neo4j
    g = Graph("http://192.168.1.229:7474/", auth=('neo4j', 'kubao'))
    start = time()
    res = g.run("match (n: 人物 {name:'黄继光'}) return n").data()
    print('time cost: %.5fs.' % (time() - start))

    start = time()
    res = g.run("match (n: 人物) where n.name contains '张三' return n").data()
    print('time cost: %.5fs.' % (time() - start))

    from openpyxl import load_workbook
    wb = load_workbook(r'D:\workspace\pycharm\nlp_project\yml6kbqa\二等功及以上英雄_图库文件.xlsx')
    ws = wb.active
    datas = []
    column_names = []
    for i, row in enumerate(ws.rows):
        if i == 0:
            column_names.extend([col.value for col in row])
        else:
            datas.append(dict((name, val) for name, val in zip(column_names, [col.value for col in row])))
    start = time()
    for data in datas:
        if '杨根思' in data['name']:
            print(data)
    print(time() - start)
