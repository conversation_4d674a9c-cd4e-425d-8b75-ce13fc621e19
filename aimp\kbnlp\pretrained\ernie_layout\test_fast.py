# encoding=utf-8
from transformers import BertTokenizerFast
from datasets import Dataset


# bert_fast = BertTokenizerFast.from_pretrained('bert-base-chinese')
#
#
text = '新华社北京11月9日电11月9日，国家主席习近平向2022年世界互联网大会乌镇峰会致贺信。习近平指出，当今时代，数字技术作为世界科技革命和产业变革的先导力量，日益融入经济社会发展各领域全过程，深刻改变着生产方式、生活方式和社会治理方式。面对数字化带来的机遇和挑战，国际社会应加强对话交流、深化务实合作，携手构建更加公平合理、开放包容、安全稳定、富有生机活力的网络空间。习近平强调，中国愿同世界各国一道，携手走出一条数字资源共建共享、数字经济活力迸发、数字治理精准高效、数字文化繁荣发展、数字安全保障有力、数字合作互利共赢的全球数字发展道路，加快构建网络空间命运共同体，为世界和平发展和人类文明进步贡献智慧和力量。2022年世界互联网大会乌镇峰会当日在浙江省桐乡市乌镇开幕，主题为“共建网络世界共创数字未来——携手构建网络空间命运共同体”，由世界互联网大会主办，浙江省人民政府承办。'

# res_bert_fast = bert_fast([text] * 2, max_length=128, truncation='longest_first', stride=64, return_overflowing_tokens=True)
# for input_ids in res_bert_fast['input_ids']:
#     print(bert_fast.decode(input_ids))
# for k, v in res_bert_fast.items():
#     print(k, len(v) if isinstance(v, list) else v)


from kbnlp.pretrained.ernie_layout.tokenization_ernie_layout_fast import ErnieLayoutTokenizerFast
from kbnlp.pretrained.ernie_layout.tokenization_ernie_layout2 import ErnieLayoutTokenizer

tokenizer = ErnieLayoutTokenizerFast.from_pretrained('microsoft/layoutxlm-base')


# res = tokenizer([text[:40]], boxes=[1])
res = tokenizer(text=['你好', '上海阿斯蒂芬节流阀据了解口你急佳节快乐', '。'], boxes=[[1, 1, 1, 1], [2, 2, 2, 2], [3, 3, 3, 3]],
                max_length=8,
                truncation='only_first',
                return_offsets_mapping=True,
                stride=0,
                return_overflowing_tokens=True)
print(res)
print(len(res['input_ids']))
