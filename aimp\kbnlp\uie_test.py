import torch
# import threadpoolctl
# threadpoolctl.threadpool_limits({
#     'blas': 2, 'openmp': 4
# })
from kbnlp.taskflow.taskflow import Taskflow

auie = Taskflow('information_extraction',
                'uie-nano',
                task_path=r'C:\Users\<USER>\Desktop\project\aimp\aimp\kbnlp\data\pretrained\kbie-nano',
                # device='cuda:0',
                position_prob=0.5,
                max_seq_len=512,
                schema='姓名')
from time import time, process_time
s = time()
s1 = process_time()
print(auie(['金海青男,1926年3月出生,辽宁省开原县人,1950年9月入伍,第64军190师568团后勤处担架连战士,1951年5月在抗美援朝战争中于朝鲜临津江板桥里牺牲。']*1000)[0])
e = time()
e1 = process_time()
print(e-s)
print(e1-s1)