import json
import os
import yaml
from pathlib import Path


config_path = Path(__file__).parent.parent.parent / "static/config/base_config.yaml"


class ConfigMap(dict):
    __setattr__ = dict.__setitem__
    __getattr__ = dict.__getitem__


def configMap():
    with open(str(config_path), mode='r', encoding='utf-8') as fd:
        data = yaml.load(fd, Loader=yaml.FullLoader)
        _config = data.get(data.get("run_config"))
        _config["LAST_METADATA_PATH"] = os.path.expanduser(_config["LAST_METADATA_PATH"])
    inst = ConfigMap()
    for k, v in _config.items():
        inst[k] = v

    return inst


def read_metadata(last_metadata_path, table):
    if os.path.exists(last_metadata_path):
        with open(last_metadata_path, 'r', encoding='utf-8') as f:
            ret = f.read()
            if ret is not None:
                content = json.loads(ret)
                record = content.get(table)
                if not record:
                    record = -1
            else:
                record = -1
            return record
    else:
        return -1


def write_metadata(last_metadata_path, record, table):
    if os.path.exists(last_metadata_path):
        with open(last_metadata_path, 'r', encoding='utf-8') as f:
            ret = f.read()
            if ret is not None:
                content = json.loads(ret)
            else:
                content = {table: -1}
    else:
        dir_path, file = os.path.split(last_metadata_path)
        if not os.path.exists(dir_path):
            os.makedirs(dir_path)
        content = dict()
    with open(last_metadata_path, 'w', encoding='utf-8') as f:
        content.update({table: record})
        f.write(json.dumps(content, ensure_ascii=False))
        return record
