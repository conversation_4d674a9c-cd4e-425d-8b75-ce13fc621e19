# -*- coding: utf-8 -*-
# __author__:SH
# 2022/10/31 10:14
import base64
import datetime
import functools
import json
import os
import pathlib
import random
import shutil
import subprocess
import time
from time import sleep

import cv2
import requests
from asgiref.sync import sync_to_async
from fastapi import APIRouter, Depends, Request, \
    UploadFile, BackgroundTasks, Query,HTTPException

from aimp.config import *
from kbnlp.taskflow import Taskflow
from utils.logger import logger
from utils.text import *
from .schema import *
from PIL import Image
api = APIRouter()

env_dict = dict(os.environ)

# await sync_to_async(i.praticemodel_set.last())
def get_dataset(i, _content):
    if _content == "dataset":
        if i.modeldataset_set.last():
            return i.modeldataset_set.first().dataset.dataset_type
        else:
            return 0
    if _content == "practice_dataset":
        if i.praticedataset_set.last():
            return i.praticedataset_set.first().dataset.dataset_name
        else:
            return 0
    if _content == "dataset_str" and i.modeldataset_set.first():
        _list = ",".join([
            j.dataset.dataset_name
            for j in i.modeldataset_set.all()])
        return _list
    else:
        return ""


def get_dataset_all(ai_model):
    dataset_list = ai_model.modeldataset_set.all()
    return ",".join([i.dataset.dataset_name for i in dataset_list])


def get_version(ai_model, _type):
    if ai_model.aimodelsversion_set.all():
        if _type == 1:
            ai_model_versions = ai_model.aimodelsversion_set.all()
        elif _type == 2:
            ai_model_versions = ai_model.aimodelsversion_set.filter(is_publish=1).all()
        else:
            ai_model_versions = []
    else:
        ai_model_versions = []
    return ",".join([f"V{i.version}" for i in ai_model_versions]) if ai_model_versions else "-"


@api.get("/model", response_model=ReturnModel)
def get_model_data(param: ModelTrainIn = Depends()):
    from .models import AiModels, AiModelsVersion
    ai_model = AiModels.objects.filter(id=param.id).first()
    ai_version = AiModelsVersion.objects.filter(model_id=param.id, version=param.version[1:]).first()
    return {
        "model_name": ai_model.model_name,
        "dataset": ",".join([i.dataset.dataset_name for i in ai_model.modeldataset_set.all()]),
        "version": param.version,
        "status": ai_version.status,
        "precent": int(ai_version.epoch * 100 / ai_version.epoch_count),
        "train": ",".join([i.dataset.dataset_name for i in ai_model.praticedataset_set.all()]),
    }


def get_data(dataset, _path):
    images = []
    categories = []
    annotations = []
    for data in dataset:
        for image in data.rawimage_set.all():
            shutil.copyfile(f"./{image.path}", f"{_path}/{image.name}")
            images.append({
                "license": 1,
                "file_name": image.name,
                "coco_url": f"{_path}/{image.name}",
                "height": image.imgage_height,
                "width": image.imgage_width,
                "data_captured": data.create_time,
                "flickr_url": f"{_path}/{image.name}",
                "id": image.id
            })
            for key in image.tagimage_set.all():
                annotations.append({
                    "id": image.id,
                    "category_id": key.tag_id,
                    "image_id": key.image_id,
                    "area": key.area,
                    "bbox": json.loads(key.bbox),
                    # "segmentation": [json.loads(key.segmentation)],
                    "iscrowd": 0
                })

        categories += [
            {
                "id": tag.tag_id.id,
                "name": tag.tag_id.tag_name,
                "supercategory": tag.tag_id.tag_name
            }
            for tag in data.datasettag_set.all()
        ]
    return images, categories, annotations


@api.get("/aimodel", response_model=IsShow)
def get_aimodel(param: IdIn = Depends()):
    from .models import AiModels
    ai_model = AiModels.objects.filter(id=param.id).first()
    if not ai_model:
        raise HTTPException(detail={"code":404,"message":"缺少对应的模型！"})
    return {
        "is_show": {
            "model_id": ai_model.id,
            "model_name": ai_model.model_name,
            "status": get_model_status(ai_model),
            "create_user": ai_model.create_user,
            "create_time": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(ai_model.create_time)),
            "publish_version": get_version(ai_model, 2),
            "current_version": get_version(ai_model, 1),
            "dataset": get_dataset_all(ai_model),
            "explain": ai_model.explain if ai_model.explain else "",
        }
    }


def get_text(dataset, _path):
    with open(f"{_path}", "w", encoding="utf-8") as f:
        for data in dataset:
            for raw_text in data.rawtext_set.all():
                data = json.dumps({
                    "text": raw_text.text.strip(),
                    "id": raw_text.id,
                    "relations": [],
                    "cls_label": [raw_text.rawintention_set.all()[0].tag_type.tag_id.tag_name],
                    "entities": [
                        {
                            "id": i.id,
                            "label": i.slot.slot,
                            "start_offset": i.tag_begin_x,
                            "end_offset": i.tag_end_x
                        } for i in raw_text.slottext_set.all()
                    ]
                }, ensure_ascii=False)
                f.write(f"{data}\n")


def get_text_1(dataset, _path):
    with open(f"{_path}", "w", encoding="utf-8") as f:
        for data in dataset:
            for raw_text in data.rawtext_set.all():
                data = json.dumps({
                    "text": raw_text.text.strip(),
                    "id": raw_text.id,
                    "relations": [],
                    "cls_label": [raw_text.tagtexttype_set.all()[0].tag.tag_name],
                    "entities": []
                }, ensure_ascii=False)
                f.write(f"{data}\n")


def get_text_2(dataset, _path):
    with open(f"{_path}", "w", encoding="utf-8") as f:
        for data in dataset:
            for raw_text in data.rawtext_set.all():
                data = json.dumps({
                    "text": raw_text.text.strip(),
                    "id": raw_text.id,
                    "relations": [],
                    "cls_label": [],
                    "entities": [
                        {
                            "id": i.id,
                            "label": i.tag.tag_name,
                            "start_offset": i.tag_text_begin,
                            "end_offset": i.tag_text_end
                        } for i in raw_text.tagtext_set.all()
                    ]
                }, ensure_ascii=False)
                f.write(f"{data}\n")


def get_json(dataset, _file, model, version):
    path = "//ubt-B450M-02@19002/share/aimp_model"
    if not os.path.exists(f"{path}/{model.id}"):
        os.mkdir(f"{path}/{model.id}")

    if not os.path.exists(f"{path}/{model.id}/{version}"):
        os.mkdir(f"{path}/{model.id}/{version}")
    if not os.path.exists(f"{path}/{model.id}/{version}/annotations"):
        os.mkdir(f"{path}/{model.id}/{version}/annotations")
    if not os.path.exists(f"{path}/{model.id}/{version}/{_file}2017"):
        os.mkdir(f"{path}/{model.id}/{version}/{_file}2017")
    _data = get_data(dataset, f"{path}/{model.id}/{version}/{_file}2017")
    _json = {
        "info":
            {
                "description": model.model_name,
                "url": "http://cocodataset.org",
                "version": "2.0",
                "year": 2022,
                "contributor": model.create_user,
                "data_created": model.create_time
            },
        "licenses":
            [
                {
                    "url": "http://creativecommons.org/licenses/by-nc-sa/2.0/",
                    "id": 1,
                    "name": "test_license"
                }
            ],
        "images": _data[0],
        "categories": _data[1],
        "annotations": _data[2]
    }
    with open(f"{path}/{model.id}/{version}/annotations/instances_{_file}2017.json", "w+") as f:
        f.write(json.dumps(_json))


def get_json_1(dataset, _file, model, version):
    # path = "C:/Users/<USER>/Desktop/mm"
    path = "//ubt-B450M-02@19002/share/aimp_model"
    if not os.path.exists(f"{path}/{model.id}"):
        os.mkdir(f"{path}/{model.id}")
    if not os.path.exists(f"{path}/{model.id}/{version}"):
        os.mkdir(f"{path}/{model.id}/{version}")
    get_text_1(dataset, f"{path}/{model.id}/{version}/{_file}_doccano.jsonl")


def get_json_2(dataset, _file, model, version):
    path = "//ubt-B450M-02@19002/share/aimp_model"
    if not os.path.exists(f"{path}/{model.id}"):
        os.mkdir(f"{path}/{model.id}")
    if not os.path.exists(f"{path}/{model.id}/{version}"):
        os.mkdir(f"{path}/{model.id}/{version}")
    get_text_2(dataset, f"{path}/{model.id}/{version}/{_file}_doccano.jsonl")


def get_json_4(dataset, _file, model, version):
    path = "//ubt-B450M-02@19002/share/aimp_model"
    if not os.path.exists(f"{path}/{model.id}"):
        os.mkdir(f"{path}/{model.id}")
    if not os.path.exists(f"{path}/{model.id}/{version}"):
        os.mkdir(f"{path}/{model.id}/{version}")
    get_text(dataset, f"{path}/{model.id}/{version}/{_file}_doccano.jsonl")


def get_json_5(dataset, _file, model, version):
    # path = "C:/Users/<USER>/Desktop/mm"
    path = "//ubt-B450M-02@19002/share/aimp_model"
    if not os.path.exists(f"{path}/{model.id}"):
        os.mkdir(f"{path}/{model.id}")
    if not os.path.exists(f"{path}/{model.id}/{version}"):
        os.mkdir(f"{path}/{model.id}/{version}")
    if not os.path.exists(f"{path}/{model.id}/{version}/{_file}"):
        os.mkdir(f"{path}/{model.id}/{version}/{_file}")
    _path = f"{path}/{model.id}/{version}/{_file}"
    for i in dataset:
        for rawimage in i.rawimage_set.all():
            for image in rawimage.tagimagetype_set.all():
                if not os.path.exists(f"{_path}/N_{1000000 + image.tag_id}-{image.tag.tag_name}"):
                    os.mkdir(f"{_path}/N_{1000000 + image.tag_id}-{image.tag.tag_name}")
                shutil.copyfile(f"./{image.image.path}",
                                f"{_path}/N_{1000000 + image.tag_id}-{image.tag.tag_name}/{image.image.name}")
    # path = "//ubt-B450M-02@19002/share/aimp_model"
    # if not os.path.exists(f"{path}/{model.id}"):
    #     os.mkdir(f"{path}/{model.id}")
    # if not os.path.exists(f"{path}/{model.id}/annotations"):
    #     os.mkdir(f"{path}/{model.id}/annotations")
    # if not os.path.exists(f"{path}/{model.id}/{_file}"):
    #     os.mkdir(f"{path}/{model.id}/{_file}")
    # _data = get_data(dataset,f"{path}/{model.id}/{_file}")


@api.post("/aimodel", response_model=IdIn)
def ai_model(param: CreateModelIn = Body()):
    from .models import AiModels, ModelDataset, PraticeDataset, AiModelsVersion
    from dataset.models import DataSet
    ai_model = AiModels.objects.create(model_name=param.model_name,
                                       create_user=param.create_user,
                                       mode=param.radio)
    ai_model.save()

    dataset = DataSet.objects.filter(id__in=param.dataset)
    model_list = [ModelDataset(dataset=i, ai_model=ai_model) for i in dataset]
    ModelDataset.objects.bulk_create(model_list)

    train = DataSet.objects.filter(id__in=param.test)
    train_list = [PraticeDataset(dataset=i, ai_model=ai_model) for i in train]
    PraticeDataset.objects.bulk_create(train_list)
    ai_model_version = AiModelsVersion.objects.create(model_id=ai_model.id)
    ai_model_version.save()
    if dataset[0].dataset_type == 1:
        get_json_1(dataset, _file="dev", model=ai_model, version=ai_model_version.version)
        get_json_1(train, _file="train", model=ai_model, version=ai_model_version.version)
        sa = {
            "JobName": ai_model.model_name,
            "dataset_root": f"/myjfs/share/aimp_model/{ai_model.id}/{ai_model_version.version}",
            "script": "train_nlp_infcls",
            "model": "uie-nano",
            "callback_url": f"{base_url}/model/model_data/{ai_model.id}/{ai_model_version.version}",
        }
        data = requests.post(url="http://ubt-b450m-02.lan:9000/workload/job", json=sa,
                             headers={"Content-Type": "application/json"})
        data = data.content.decode()
        data = json.loads(data)
        ai_model_version.job_id = data["JobId"]
        ai_model_version.save()
    if dataset[0].dataset_type == 2:
        get_json_2(dataset, _file="dev", model=ai_model, version=ai_model_version.version)
        get_json_2(train, _file="train", model=ai_model, version=ai_model_version.version)
        sa = {
            "JobName": ai_model.model_name,
            "dataset_root": f"/myjfs/share/aimp_model/{ai_model.id}/{ai_model_version.version}",
            "script": "train_nlp_infoex",
            "model": "uie-nano",
            "callback_url": f"{base_url}/model/model_data/{ai_model.id}/{ai_model_version.version}",
        }

        data = requests.post(url="http://ubt-b450m-02.lan:9000/workload/job", json=sa,
                             headers={"Content-Type": "application/json"})
        data = data.content.decode()

        data = json.loads(data)
        ai_model_version.job_id = data["JobId"]
        ai_model_version.save()
    if dataset[0].dataset_type == 4:
        get_json_4(dataset, _file="dev", model=ai_model, version=ai_model_version.version)
        get_json_4(train, _file="train", model=ai_model, version=ai_model_version.version)
        sa = {
            "JobName": ai_model.model_name,
            "dataset_root": f"/myjfs/share/aimp_model/{ai_model.id}/{ai_model_version.version}",
            "script": "train_nlp_intent",
            "model": "uie-nano",
            "callback_url": f"{base_url}/model/model_data/{ai_model.id}/{ai_model_version.version}",
        }
        data = requests.post(url="http://ubt-b450m-02.lan:9000/workload/job", json=sa,
                             headers={"Content-Type": "application/json"})
        data = data.content.decode()
        data = json.loads(data)
        ai_model_version.job_id = data["JobId"]
        ai_model_version.save()
    if dataset[0].dataset_type == 5:
        get_json_5(dataset, _file="val", model=ai_model, version=ai_model_version.version)
        get_json_5(train, _file="train", model=ai_model, version=ai_model_version.version)
        sa = {
            "JobName": ai_model.model_name,
            "dataset_root": f"/myjfs/share/aimp_model/{ai_model.id}/{ai_model_version.version}",
            "script": "train_cls",
            "callback_url": f"{base_url}/model/model_data/{ai_model.id}/{ai_model_version.version}",
        }
        data = requests.post(url="http://ubt-b450m-02.lan:9000/workload/job", json=sa,
                             headers={"Content-Type": "application/json"})
        data = data.content.decode()
        data = json.loads(data)
        ai_model_version.job_id = data["JobId"]
        ai_model_version.save()
    if dataset[0].dataset_type == 6:
        get_json(dataset, "val", ai_model, ai_model_version.version)
        get_json(train, "train", ai_model, ai_model_version.version)
        # """http://ubt-b450m-02.lan:9000/"""
        sa = {
            "JobName": ai_model.model_name,
            "dataset_root": f"/myjfs/share/aimp_model/{ai_model.id}/{ai_model_version.version}",
            "model": "fasterrcnn_resnet50_fpn", "script": "train_det",
            "callback_url": f"{base_url}/model/model_data/{ai_model.id}/{ai_model_version.version}",
            "options": []}
        data = requests.post(url="http://ubt-b450m-02.lan:9000/workload/job", json=sa,
                             headers={"Content-Type": "application/json"})
        data = data.content.decode()
        data = json.loads(data)
        ai_model_version.job_id = data["JobId"]
        ai_model_version.save()
    if dataset[0].dataset_type == 7:
        get_json(dataset, "val", ai_model, version=ai_model_version.version)
        get_json(train, "train", ai_model, version=ai_model_version.version)
        """http://ubt-b450m-02.lan:9000/"""
        sa = {
            "JobName": ai_model.model_name,
            "dataset_root": f"/myjfs/share/aimp_model/{ai_model.id}/{ai_model_version.version}",
            "model": "fasterrcnn_resnet50_fpn", "script": "train_det",
            "callback_url": f"{base_url}/model/model_data/{ai_model.id}/{ai_model_version.version}",
            "options": []}
        data = requests.post(url="http://ubt-b450m-02.lan:9000/workload/job", json=sa,
                             headers={"Content-Type": "application/json"})
        data = data.content.decode()
        data = json.loads(data)
        ai_model_version.job_id = data["JobId"]
        ai_model_version.save()
    # print(sa)
    # data = requests.post(url="http://ubt-b450m-02.lan:9000/workload/job",json=sa,headers={"Content-Type":"application/json"})
    # print(data.status_code)
    # print(data.content.decode())
    # print(sa)
    return {"id": ai_model.id}


@api.put("/aimodel", response_model=ResponseMsg)
def ai_model(param: IdIn = Body()):
    from .models import AiModels
    AiModels.objects.filter(id=param.id).update(status=0)
    return {"msg": "修改成功"}


@api.put("/aimodelexpalin", response_model=ResponseMsg)
def ai_model_expalin(param: ChangeModelExplain = Body()):
    from .models import AiModels
    ai_model = AiModels.objects.filter(id=param.id).first()
    ai_model.explain = param.explain
    ai_model.save()
    return {"msg": "修改成功"}


@api.delete("/aimodel", response_model=ResponseMsg)
def ai_model(dataset_param: IdInList = Body()):
    from .models import AiModels
    AiModels.objects.filter(id__in=dataset_param.id_list).delete()
    return {"msg": "删除成功"}


@api.get("/aimodellist", response_model=ModelDropList)
def aimodellist(param: ModelType = Depends()):
    from .models import AiModels
    # if param.model_type:
    #     dataset_list = DataSet.objects.filter(dataset_type=param.model_type).all()
    #     dataset_id = [i.id for i in dataset_list]
    #     model_dataset = ModelDataset.objects.filter(dataset_id in dataset_list).all()
    #     model_dataset =
    aimodels = AiModels.objects.filter().all()
    return {
        "return_list": [
            {
                "model_name": ai_model.model_name,
                "model_id": ai_model.id
            } for ai_model in aimodels
        ]
    }


class DeployModel(BaseModel):
    model_config: dict


@api.post("/deploy_model")
def deploy_model(param: DeployModel):
    model_config = param.model_config
    print(model_config, type(model_config))
    data = requests.post(url="http://ubt-B450M-02:9000/workload/job", json=model_config,
                         headers={"Content-Type": "application/json"})
    print(data.status_code)
    print(data)
    rep = data.json()
    print(rep)
    return rep


def get_model_status(i):
    if i.aimodelsversion_set.last():
        return i.aimodelsversion_set.last().status
    else:
        return 0

@api.post("/aimodellist", response_model=ModelOutList)
async def model_list(param: SearchIn = Body(),
                     param_page: Paging = Body()):
    from .models import AiModels
    aimodel_list = AiModels.objects.filter(model_name__contains=param.search_data).all()
    count = await aimodel_list.acount()
    aimodel_list = aimodel_list.order_by(
        param_page.order_key if param_page.order == 1 else f"-{param_page.order_key}").all()
    aimodel_list = aimodel_list[
                   param_page.page * param_page.size - param_page.size: param_page.page * param_page.size] if param_page.size > 0 else aimodel_list
    return {
        'count': count,
        "model_list": [
            {
                "key": i.id,
                "id": i.id,
                "model_name": i.model_name,
                "model_type": await sync_to_async(get_dataset)(i, "dataset"),
                "dataset_name": await sync_to_async(get_dataset)(i, "dataset_str"),
                "practice_status": await sync_to_async(get_model_status)(i),
                "practice_version": await sync_to_async(get_version)(i, 1),
                "publish_version": await sync_to_async(get_version)(i, 2),
            }async for i in aimodel_list
        ]
    }


@api.get("/aimodelversion", response_model=ModelVersionList)
def aimodel_version(param: IdIn = Depends()):
    from .models import AiModels
    ai_model = AiModels.objects.filter(id=param.id).first()
    model_name = ai_model.model_name
    return {
        "return_list": [
            {
                "key": i.id,
                "id": i.id,
                "name": f"{model_name}-V{i.version}",
                "status": i.status,
                "is_publish": "是" if i.is_publish else "否",
                "version": f"V{i.version}" if i.version else "-",
            }
            for i in ai_model.aimodelsversion_set.all()
        ]
    }


@api.get("/createmodel", response_model=GetModel)
def create_model(param: IdIn = Depends()):
    from .models import AiModels
    ai_model = AiModels.objects.filter(id=param.id).first()
    return {
        "model_name": ai_model.model_name,
        "dataset_type": ai_model.modeldataset_set.all()[0].dataset.dataset_type,
        "dataset_list": [i.dataset.id for i in ai_model.modeldataset_set.all()],
        "version": f"V{ai_model.aimodelsversion_set.last().version + 1}",
        "test_list": [i.dataset_id for i in ai_model.praticedataset_set.all()],
    }


@api.get("/version")
def version(param: IdIn = Depends()):
    from .models import AiModels
    ai_model = AiModels.objects.filter(id=param.id).first()
    model_type = get_model_dataset_type(ai_model, 1)
    return {
        "return_list": [
            {
                "version": f"V{i.version}",
                "key": i.version,
                "status": i.status,
                "path": i.path
            } for i in ai_model.aimodelsversion_set.all()
        ],
        "model_type": model_type
    }


@api.post("/model_data/{model_id}/{version}", response_model=ResponseMsg)
def model_data(model_id: str, version: str, param: ModelIn = Body()):
    from .models import AiModelsVersion
    if param.detail.step == "finish training":
        ai_model = AiModelsVersion.objects.filter(version=version, model_id=model_id).first()
        _path = f"//ubt-B450M-02@19002/share/aimp_model/{model_id}/{version}/runs/{ai_model.model.model_name}-{ai_model.job_id}"
        if ai_model.model.modeldataset_set.all()[0].dataset.dataset_type < 5:
            _path = f"{_path}/model_best"
        ai_model.path = _path
        ai_model.status = 2
        ai_model.save()
    if param.detail.step == "start training":
        ai_model = AiModelsVersion.objects.filter(version=version, model_id=model_id).first()
        ai_model.epoch_count = param.detail.epochs
        ai_model.save()
    if param.detail.step == "end epoch":
        ai_model = AiModelsVersion.objects.filter(version=version, model_id=model_id).first()
        ai_model.epoch += 1
        if ai_model.epoch == ai_model.epoch_count:
            _path = f"//ubt-B450M-02@19002/share/aimp_model/{model_id}/{version}/runs/{ai_model.model.model_name}-{ai_model.job_id}"
            if ai_model.model.modeldataset_set.all()[0].dataset.dataset_type < 5:
                ai_model.path = f"{_path}/model_best"
            ai_model.status = 2
        ai_model.save()
    return {"msg": "成功"}


@api.post("/model_type/{model_id}/{version}", response_model=ResponseMsg)
def model_data(model_id: str, version: str, param: ModelIn = Body()):
    from .models import AiModelsVersion
    logger.info(f"model_id:{model_id},version:{version},param:{param}")
    _aimp_static_path = env_dict.get("AIMP_DIR") if env_dict.get(
        "AIMP_DIR") else f"{str(pathlib.Path.home())}/.kubao/aimp"
    if _aimp_static_path.endswith("\\"):
        _aimp_static_path = _aimp_static_path[0:-1]
    if _aimp_static_path.endswith("\\"):
        _aimp_static_path = _aimp_static_path[0:-1]
    if _aimp_static_path.endswith("/"):
        _aimp_static_path = _aimp_static_path[0:-1]
    os.makedirs(_aimp_static_path,exist_ok=True)
    if param.detail.step == "finish training":
        ai_model = AiModelsVersion.objects.filter(version=version, model_id=model_id).first()
        _path = f"{_aimp_static_path}/mode/{model_id}/{version}/mode/model_best"
        ai_model.path = _path
        ai_model.status = 2
        ai_model.save()
    if param.detail.step == "start training":
        ai_model = AiModelsVersion.objects.filter(version=version, model_id=model_id).first()
        ai_model.epoch_count = param.detail.epochs
        ai_model.save()
    if param.detail.step == "end epoch":
        ai_model = AiModelsVersion.objects.filter(version=version, model_id=model_id).first()
        ai_model.epoch += 1
        if ai_model.epoch == ai_model.epoch_count:
            if ai_model.test_type in [2, 4]:
                ai_model.path = f"{_aimp_static_path}/mode/{model_id}/{version}/mode"
            else:
                ai_model.path = f"{_aimp_static_path}/mode/{model_id}/{version}/mode/model_best"
            ai_model.status = 2
        # print(env_dict.get(f"AIDP_FILE_URL_{ai_model.id}"),f"AIDP_FILE_URL_{ai_model.id}")
        if env_dict.get(f"AIDP_FILE_URL_{ai_model.id}"):
            data = requests.post(env_dict.get(f"AIDP_FILE_URL_{ai_model.id}"),json={"status":ai_model.status,"epoch":ai_model.epoch,"epoch_count":ai_model.epoch_count})
            if data.status_code>=400:
                logger.info(f'url:{env_dict.get(f"AIDP_FILE_URL_{ai_model.id}")},code:{data.status_code}')
        ai_model.save()
    return {"msg": "成功"}


@api.get("/getmodel")
def get_model():
    from .models import ModelDataset
    from dataset.models import DataSet
    dataset = DataSet.objects.filter(dataset_type=4).all()
    ai_model = ModelDataset.objects.filter(dataset__in=dataset).all()
    ai_model = set([i.ai_model for i in ai_model])
    return sorted([{
        "id": i.id,
        "name": i.model_name,
        "versions": [
            {
                "version": i.version,
                "path": i.path
            } for i in i.aimodelsversion_set.all()
        ]
    } for i in ai_model], key=lambda x: x["id"])


@api.get("/test")
def test(projceid=Query()):
    # # with open("./file/raw_data/使用数据.json", "r", encoding="utf-8") as f:
    # #     data_list = f.read()
    # with open("./file/raw_data/结果数据.json", "r", encoding="utf-8") as f:
    #     return_data_list = f.read()
    # # data_list = json.loads(data_list)
    # return_data_list=json.loads(return_data_list)
    #
    # return_data = []
    print(projceid)
    # for i, j in zip(return_data_list, return_data_list):
    #     if j:
    #         return_data.append({ 'result': j})
    # print(len(return_data))
    return {'file_path': 123}
    # 补充试用部分


def try_out_model(job):
    x = requests.post('http://ubt-B450M-02:9000/workload/job', json=job).json()
    idx = x['JobId']
    while True:
        output = ""
        x = requests.post(f'http://ubt-B450M-02:9000/workload/job/{idx}').json()
        sleep(0.5)
        state = x['JobState']
        if state in ['COMPLETED', 'FAILED']:
            output = x['StdOut'] if state == 'COMPLETED' else x['StdErr']
            output = base64.urlsafe_b64decode(output)  # bytes
            output = output.decode('utf-8')  # for text output
            break
    return output


def get_model_dataset_type(ai_model_instance, _type):
    if _type == 1:
        return ai_model_instance.modeldataset_set.first().dataset.dataset_type
    if _type == 2:
        return ai_model_instance.model.modeldataset_set.first().dataset.dataset_type


def get_all_model(model_version_insetance):
    return [i.dataset_id for i in model_version_insetance.model.modeldataset_set.all()]


def get_tag_name(tag_dataset):
    return tag_dataset.tag_id.tag_name


# def get_tag_list()
@api.post("/tryout")  # ,response_model=TryOut)
async def try_out(requests: Request, file: UploadFile):
    from .models import AiModelsVersion
    from dataset.models import DatasetTag
    contents = await file.read()
    file_name = file.filename
    _path = "//ubt-B450M-02@19002/share/aimp_model"
    param = dict(requests.headers)
    if not os.path.exists(f"{_path}/{param['model_id']}/try"):
        os.mkdir(f"{_path}/{param['model_id']}/try")
    with open(f"{_path}/{param['model_id']}/try/{file_name}", "wb+") as f:
        f.write(contents)
    if not os.path.exists("file/try"):
        os.mkdir("file/try")
    if not os.path.exists(f"file/try/{param['model_id']}"):
        os.mkdir(f"file/try/{param['model_id']}")
    with open(f"file/try/{param['model_id']}/{file_name}", "wb+") as f:
        f.write(contents)
    ai_model_train = await AiModelsVersion.objects.filter(model_id=param['model_id'], version=param['version']).afirst()
    if await sync_to_async(get_model_dataset_type)(ai_model_train, 2) == 5:
        job = {
            'modfunc': 'mtl.cls',
            'args': [f"{_path}/{param['model_id']}/try/{file_name}"],
            "options": {"model": f"{ai_model_train.path}/model.pt"}
        }
        data = try_out_model(job)
        data = json.loads(data)
        values = list(data.values())[0]
        dataset = await sync_to_async(get_all_model)(ai_model_train)
        dataset_tags = DatasetTag.objects.filter(data_id_id__in=dataset).all()
        return_tag = {}
        return_tag_id = []
        async for i in dataset_tags:

            if i.tag_id_id not in return_tag:
                return_tag[i.tag_id_id] = {
                    "color": i.color,
                    "id": i.id,
                    "tag_id": i.tag_id_id,
                    "tag_name": await sync_to_async(get_tag_name)(i)
                }
                return_tag_id.append(i.tag_id_id)
        return_tag_id.sort()
        img = cv2.imread(f"file/try/{param['model_id']}/{file_name}")
        width = img.shape[1]
        height = img.shape[0]
        tags = list(filter(lambda x: values[x] > 0, range(0, len(values))))
        tags = [{"tag_name": return_tag[return_tag_id[x]]["tag_name"],
                 "tag_id": return_tag[return_tag_id[x]]["tag_id"],
                 "id": return_tag[return_tag_id[x]]["id"],
                 "color": return_tag[return_tag_id[x]]["color"],
                 "precent": round(values[x] * 100, 2)} for x in tags]
        tags = sorted(tags, key=lambda x: x["precent"], reverse=True)
        image = {
            "path": f"{app_url}/file/try/{param['model_id']}/{file_name}",
            "width": width,
            "height": height,
            "tags": tags
        }
        return image
    #     value = list(data.values())[0]
    #     value_index = value.index(max(value))
    #     dataset = await sync_to_async(get_all_model)(ai_model_train)
    #     dataset_tags = DatasetTag.objects.filter(data_id_id__in = dataset).all()
    #
    #     return_tag = {}
    #     return_tag_id = []
    #     async for i in dataset_tags:
    #         print(i)
    #         if i.tag_id_id not in return_tag:
    #             return_tag[i.tag_id_id]={
    #                 "color":i.color,
    #                 "id":i.id,
    #                 "tag_id":i.tag_id_id,
    #                 "tag_name":await sync_to_async(get_tag_name)(i)
    #             }
    #             return_tag_id.append(i.tag_id_id)
    #     return_tag_id.sort()
    #     tags = [return_tag[return_tag_id[i]] for i in range(0,len(value))]
    #     return_data = return_tag[return_tag_id[value_index]]
    #     return_data["path"]=f"{app_url}/file/try/{param['model_id']}/{file_name}"
    #     img = cv2.imread(f"file/try/{param['model_id']}/{file_name}")
    #
    #     width = img.shape[1]
    #     height = img.shape[0]
    #     return_data["width"]=width
    #     return_data["height"]=height
    #     # return_data = {
    #     #     "width": width,
    #     #     "height": height,
    #     #     # "path": file_path,
    #     #     "color": "red",
    #     #     "id": 1,
    #     #     "tag_id": 2,
    #     #     "tag_name": "张三",
    #     # }
    # #     file_path = "/file/95/src=http___hbimg.b0.upaiyun.com_c6d807246794208aeff2ba6da548442d2ba5e9de57b2-163xHB_fw658&refer=http___hbimg.b0.upaiyun.jpg"
    # #     img = cv2.imread(f"./static/{file_path}")
    # #     width = img.shape[1]
    # #     height = img.shape[0]
    # #     file_path = f"http://127.0.0.1:8000{file_path}"
    #     print(return_data,999999999999999999999999)
    #     print(tags,53453486396)
    #     return_data["tags"]=tags[0]
    #     print(return_data)
    #     return return_data
    if await sync_to_async(get_model_dataset_type)(ai_model_train, 2) == 6:
        job = {
            'modfunc': 'mtl.layout',
            'args': [f"{_path}/{param['model_id']}/try/{file_name}"],
            "options": {"model": f"{ai_model_train.path}/model.pt"}
        }
        data = try_out_model(job)
        data = json.loads(data)
        data_value = list(data.values())[0]

        boxes = data_value["boxes"]
        label = data_value["labels"]
        scores = data_value["scores"]
        dataset = await sync_to_async(get_all_model)(ai_model_train)
        _tag = await DatasetTag.objects.filter(tag_id_id=label[0], data_id_id__in=dataset).afirst()
        score = scores[0]
        img = cv2.imread(f"static/dataset_try/{param['model_id']}/{file_name}")
        width = img.shape[1] if img.shape[1] <= 400 else 400
        height = img.shape[0]

        _path = f"""M {boxes[0][0]} {boxes[0][1]} L {boxes[0][2]} {boxes[0][1]}
                 L {boxes[0][2]} {boxes[0][3]}   L {boxes[0][0]} {boxes[0][2]}  Z
            """
        return {
            "width": width,
            "height": height,
            "path": f"{app_url}/try/{param['model_id']}/{file_name}",
            "tag_name": await sync_to_async(get_tag_name)(_tag),
            "tag_id": int(label[0]),
            "color": _tag.color,
            "probability": score,
            "svg_path": _path
        }

        # data = json.loads(data)
        # value = list(data.values())[0]
        # value_index = value.index(max(value))


@api.post("/tryouttext")
def try_out_text(param: Intext = Body()):
    from .models import AiModelsVersion
    from dataset.models import DatasetTag
    # 获取Model版本
    ai_model = AiModelsVersion.objects.filter(model_id=param.id, version=param.version).first()
    dataset = [i.dataset_id for i in ai_model.model.modeldataset_set.all()]
    dataset_tags = DatasetTag.objects.filter(data_id_id__in=dataset).all()
    return_tag = {}
    return_tag_id = []
    return_tag_name = {}
    for i in dataset_tags:
        if i.tag_id_id not in return_tag:
            return_tag[i.tag_id_id] = {
                "color": i.color,
                "id": i.id,
                "tag_id": i.tag_id_id,
                "tag_name": get_tag_name(i)
            }
            return_tag_name[get_tag_name(i)] = {
                "color": i.color,
                "id": i.id,
                "tag_id": i.tag_id_id,
                "tag_name": get_tag_name(i)
            }
            return_tag_id.append(i.tag_id_id)
    if param.dataset_type == 1:
        schema = [f'{prompt_prefix}[{",".join([return_tag[i]["tag_name"] for i in return_tag])}]']
    else:
        schema = [return_tag[i]["tag_name"] for i in return_tag]

    # task = Taskflow('information_extraction', schema=schema,
    #                 task_path=ai_model.path)
    # return_data = task([param.text])

    task = Taskflow('information_extraction', schema=schema,
                    task_path=ai_model.path)
    return_data = task([param.text])

    # ____text = "十分钟之后查一下有没有去北京的高铁"
    # return_data = [{'目的地': [{'text': '北京', 'start': 12, 'end': 14, 'probability': 0.40610737},{'text': '北京', 'start': 6, 'end': 15, 'probability': 0.40610737}]}]
    if param.dataset_type == 1:
        if not return_data[0]:
            return_data = {"text": "未分类成功", "probability": "分类失败"}
        else:
            return_data = list(return_data[0].values())[0][0]
            return_data["probability"] = f'{round(float(return_data["probability"]) * 100, 2)}%'
        return return_data
    elif param.dataset_type == 2:
        text_list = re.findall(r".{2,60}", param.text)
        tag_list = {}
        text_pos = {i: 0 for i in range(0, len(param.text))}
        line_pos = {}

        for item in return_data:
            for tag_name, value in item.items():
                for _tag in value:
                    begin_line = _tag["start"] // 60
                    end_line = _tag["end"] // 60
                    if begin_line == end_line:
                        max_pos = max([text_pos[i] for i in range(_tag["start"], _tag["end"])])
                        if not tag_list.get(begin_line, []):
                            tag_list[begin_line] = []
                        tag_list[begin_line].append({
                            "x1": character_classify(param.text[begin_line * 60:_tag["start"]]),
                            "x2": character_classify(param.text[end_line * 60:_tag["end"]]),
                            "color": return_tag_name[tag_name]["color"],
                            "tx": character_classify(param.text[begin_line * 60:_tag["start"]]) + 10,
                            "cx": character_classify(param.text[begin_line * 60:_tag["start"]]) + 5,
                            "y1": 15 + max_pos * 30,
                            "y2": 15 + max_pos * 30,
                            "cy": 15 + max_pos * 30 + 15,
                            "ty": 15 + max_pos * 30 + 20,
                            "text": tag_name
                        })
                        line_pos[begin_line] = max([line_pos.get(begin_line, 0), max_pos + 1])
                        for i in range(_tag["start"], _tag["end"]):
                            text_pos[i] = max_pos + 1
                    else:
                        # 开始位置
                        if not tag_list.get(begin_line, []):
                            tag_list[begin_line] = []
                        max_pos = max([text_pos[i] for i in range(_tag["start"], begin_line * 60)])
                        tag_list[begin_line].append({
                            "x1": character_classify(param.text[begin_line * 60:_tag["start"]]),
                            "x2": 60 * 14,
                            "color": return_tag_name[tag_name]["color"],
                            "tx": character_classify(param.text[begin_line * 60:_tag["start"]]) + 10,
                            "cx": character_classify(param.text[begin_line * 60:_tag["start"]]) + 5,
                            "y1": 15 + max_pos * 30,
                            "y2": 15 + max_pos * 30,
                            "cy": 15 + max_pos * 30 + 15,
                            "ty": 15 + max_pos * 30 + 20,
                            "text": tag_name
                        })
                        line_pos[begin_line] = max([line_pos.get(begin_line, 0), max_pos + 1])
                        max_pos = max([text_pos[i] for i in range(_tag["start"], begin_line * 60)])

                        if not tag_list.get(end_line, []):
                            tag_list[end_line] = []
                        tag_list[end_line].append({
                            "x1": 0,
                            "x2": character_classify(param.text[end_line * 60:_tag["end"]]),
                            "color": return_tag_name[tag_name]["color"],
                            "tx": 10,
                            "cx": 15,
                            "y1": 15 + max_pos * 30,
                            "y2": 15 + max_pos * 30,
                            "cy": 15 + max_pos * 30 + 15,
                            "ty": 15 + max_pos * 30 + 20,
                            "text": tag_name
                        })
                        line_pos[begin_line] = max([line_pos.get(begin_line, 0), max_pos + 1])
                        max_pos = max([text_pos[i] for i in range(begin_line * 60, _tag["end"])])
                        for i in range(_tag["start"], _tag["end"]):
                            line_pos[i] = max_pos + 1
        return_data = []
        for i in range(0, len(text_list)):
            line_begin = sum(line_pos[z] for z in range(0, i)) * 40 + i * 25
            for j in range(0, len(tag_list[i])):
                tag_list[i][j]["y1"] += line_begin + 10
                tag_list[i][j]["y2"] += line_begin + 10
                tag_list[i][j]["cy"] += line_begin + 10
                tag_list[i][j]["ty"] += line_begin + 10
            return_data.append({
                "text": {
                    "text": text_list[i],
                    "color": "black",
                    "x": 2,
                    "y": line_begin + 15
                },
                "tags": tag_list[i]
            })
        return {"return_data": return_data}


@api.post("/tryouttext4")
def tryouttext_4(param: Intext = Body()):
    from .models import AiModelsVersion
    from dataset.models import TagIntention
    # 获取Model版本
    ai_model = AiModelsVersion.objects.filter(model_id=param.id, version=param.version).first()
    dataset = [i.dataset_id for i in ai_model.model.modeldataset_set.all()]
    dataset_tags = TagIntention.objects.filter(data_id_id__in=dataset).all()

    # 返回值
    slots_list = []
    intention_list = []
    for i in dataset_tags:
        intention_list.append(i.tag_id.tag_name)
        for j in i.tagslot_set.all():
            if j.slot not in intention_list:
                slots_list.append(j.slot)
    schema = f'{prompt_prefix4}[{",".join(intention_list)}]'
    task = Taskflow('intent_detection', schema={schema: slots_list},
                    task_path=ai_model.path)
    # return_data = task([param.text])
    # schema = [f'{prompt_prefix}[{",".join([return_tag[i]["tag_name"] for i in return_tag])}]']
    return_data = task(param.text)
    # return_data = [{'意图识别[旅行,歌曲]': [{'text': '歌曲', 'probability': 0.4194415211677551, 'relations': {'目的地': [{'text': '青岛', 'start': 9, 'end': 11, 'probability': 0.5085137}]}}]}]
    text_list = re.findall(r".{2,60}", param.text)
    tag_list = {}
    text_pos = {i: 0 for i in range(0, len(param.text))}
    line_pos = {}
    if return_data[0]:
        return_data = return_data[0]
    item = list(return_data.values())[0][0]
    for tag_name, value in item['relations'].items():
        for _tag in value:
            begin_line = _tag["start"] // 60
            end_line = _tag["end"] // 60
            if begin_line == end_line:
                max_pos = max([text_pos[i] for i in range(_tag["start"], _tag["end"])])
                if not tag_list.get(begin_line, []):
                    tag_list[begin_line] = []
                tag_list[begin_line].append({
                    "x1": character_classify(param.text[begin_line * 60:_tag["start"]]),
                    "x2": character_classify(param.text[end_line * 60:_tag["end"]]),
                    "color": "black",
                    "tx": character_classify(param.text[begin_line * 60:_tag["start"]]) + 10,
                    "cx": character_classify(param.text[begin_line * 60:_tag["start"]]) + 5,
                    "y1": 15 + max_pos * 30,
                    "y2": 15 + max_pos * 30,
                    "cy": 15 + max_pos * 30 + 15,
                    "ty": 15 + max_pos * 30 + 20,
                    "text": tag_name
                })
                line_pos[begin_line] = max([line_pos.get(begin_line, 0), max_pos + 1])
                for i in range(_tag["start"], _tag["end"]):
                    text_pos[i] = max_pos + 1
            else:
                # 开始位置
                if not tag_list.get(begin_line, []):
                    tag_list[begin_line] = []
                max_pos = max([text_pos[i] for i in range(_tag["start"], begin_line * 60)])
                tag_list[begin_line].append({
                    "x1": character_classify(param.text[begin_line * 60:_tag["start"]]),
                    "x2": 60 * 14,
                    "color": "black",
                    "tx": character_classify(param.text[begin_line * 60:_tag["start"]]) + 10,
                    "cx": character_classify(param.text[begin_line * 60:_tag["start"]]) + 5,
                    "y1": 15 + max_pos * 30,
                    "y2": 15 + max_pos * 30,
                    "cy": 15 + max_pos * 30 + 15,
                    "ty": 15 + max_pos * 30 + 20,
                    "text": tag_name
                })
                line_pos[begin_line] = max([line_pos.get(begin_line, 0), max_pos + 1])
                max_pos = max([text_pos[i] for i in range(_tag["start"], begin_line * 60)])

                if not tag_list.get(end_line, []):
                    tag_list[end_line] = []
                tag_list[end_line].append({
                    "x1": 0,
                    "x2": character_classify(param.text[end_line * 60:_tag["end"]]),
                    "color": "balck",
                    "tx": 10,
                    "cx": 15,
                    "y1": 15 + max_pos * 30,
                    "y2": 15 + max_pos * 30,
                    "cy": 15 + max_pos * 30 + 15,
                    "ty": 15 + max_pos * 30 + 20,
                    "text": tag_name
                })
                line_pos[begin_line] = max([line_pos.get(begin_line, 0), max_pos + 1])
                max_pos = max([text_pos[i] for i in range(begin_line * 60, _tag["end"])])
                for i in range(_tag["start"], _tag["end"]):
                    line_pos[i] = max_pos + 1
        return_data = []
        for i in range(0, len(text_list)):
            line_begin = sum(line_pos[z] for z in range(0, i)) * 40 + i * 25
            for j in range(0, len(tag_list[i])):
                tag_list[i][j]["y1"] += line_begin + 10
                tag_list[i][j]["y2"] += line_begin + 10
                tag_list[i][j]["cy"] += line_begin + 10
                tag_list[i][j]["ty"] += line_begin + 10
            return_data.append({
                "text": {
                    "text": text_list[i],
                    "color": "black",
                    "x": 2,
                    "y": line_begin + 15
                },
                "tags": tag_list[i]
            })
    return {
        "return_data": return_data,
        "text": item["text"],
        "probability": f"{round(float(item['probability']) * 100, 2)}%"
    }


def get_user_auth(reqeust: Request):
    global env_dict
    auth_type = env_dict.get("AIMP_AUTH_TYPE")
    if auth_type:
        return []
    header = dict(reqeust.headers)
    _url = env_dict.get("AIDP_URL")
    if header.get("Cookie"):
        header = {"Cookie": header.get("Cookie","")}
    # elif header.get("cookie"):
    #     header = {"Cookie": header.get("cookie","")}
    else:
        header = {
            "Cookie": "kubao_remember_token=1|2abb1957e7879bd9388b2b87224ff2d9dc56026801605a4b62e73b89bec2585314ccd2b55042b79a8517194fe81c418b614a6a22ae272479311d5faa0a0c75fd; Expires=Mon, 11-Jan-2044 09:11:10 GMT; Path=/"}
    try:
        _header = env_dict.get("AIMP_AUTH_HEADER","")
        _date = env_dict.get("AIMP_AUTH_DATE","")
        date = str(datetime.date.today())
        if json.dumps(_header) == header and date == _date:
            _content = env_dict.get("AIMP_AUTH_CONTENT")
        else:
            reqeust = requests.get(url=_url, headers=header)
            _content = reqeust.content.decode()
            env_dict["AIMP_AUTH_HEADER"] = _header
            env_dict["AIMP_AUTH_DATE"] = _date
            env_dict["AIMP_AUTH_CONTENT"] = _content
    except ValueError as e:
        env_dict["AIMP_AUTH_CONTENT"] = json.dumps({"code": 401, "msg": "权限验证失败"})
        logger.error(f"权限url:{_url}请求报错，问题是:{e}")
        return {"code": 420, "msg": "request请求报错"}
    try:
        json_object = json.loads(_content)
        return json_object
    except ValueError as e:
        return {"code": 401, "msg": "权限验证失败"}


@api.get("/getaimodel")
def get_model(user_auth=Depends(get_user_auth), param: ModelIdIn = Depends()):
    if type(user_auth) == dict:
        return {"code": 401, "msg": "权限验证失败"}
    for i in user_auth:
        if i["id"] in [7, 16]:
            break
    else:
        if user_auth:
            return {"code": 401, "msg": "权限验证失败"}

    from .models import AiModels
    ai_model = AiModels.objects.filter(id=param.model_id).first()
    if ai_model:
        if ai_model.aimodelsversion_set.all():
            return {
                "model_id": ai_model.id,
                "model_name": ai_model.model_name,
                "verison_list": [
                    {
                        "version_id": i.id,
                        "version_name": i.name,
                        "train_status": f"{i.status}",
                        "publish_status": f"{i.is_publish}",
                        "postscript": ""
                    } for i in ai_model.aimodelsversion_set.all()
                ]
            }
        else:
            return {
                "model_id": ai_model.id,
                "model_name": ai_model.model_nme
            }
    else:
        return {}


# @api.post("/dataset")
# @logger.catch
# def ope_dataset(param: DatasetIn = Body()):
#     from .models import AiModels, PraticeDataset, ModelDataset
#     from dataset.models import Tag, DataSet, DatasetTag
#     _path = param.dataset_path
#     with open(_path, "r", encoding="utf-8") as f:
#         rawimage = f.read()
#     tag = Tag.objects.filter(tag_name__in=param.tags).all()
#     tag_list = [i.tag_name for i in tag]
#     create_tag = set(param.tags) - set(tag_list)
#     tag_list = [Tag(tag_name=i) for i in create_tag]
#     if tag_list:
#         Tag.objects.bulk_create(tag_list)  # 创建一组标签
#     tag_list = Tag.objects.filter(tag_name__in=param.tags).all()
#     tags_dict = {i.tag_name: i.id for i in tag_list}
#     color = "#E3890B"
#     # 创建标签的流程
#
#     # rawimage = rawimage.
#     rawimage = json.loads(rawimage)
#     # text_list = rawimage["word"]
#     # bbox_list = rawimage["bbox_list"]
#     rawimage = rawimage["image_list"]
#     random.shuffle(rawimage)
#
#     if param.model_id is None:
#         # 创建训练集
#         dataset_train = DataSet.objects.create(dataset_name=f"{param.model_name}的训练集", dataset_type=8)
#         dataset_train.save()
#
#         # 训练集链接tag
#         tag_id_list = [DatasetTag(data_id_id=dataset_train.id, color=color,
#                                   shoutcut_key="", tag_id_id=i.id) for i in tag_list]
#
#         DatasetTag.objects.bulk_create(tag_id_list)
#
#         # dataset_tag_list = DatasetTag.objects.filter(data_id=dataset_train.id,)
#
#         # 创建测试集
#         dataset_test = DataSet.objects.create(dataset_name=f"{param.model_name}的测试集", dataset_type=8)
#         dataset_test.save()
#
#         # 测试集链接tag
#         tag_id_list = [DatasetTag(data_id_id=dataset_test.id, color=color,
#                                   shoutcut_key="", tag_id_id=i.id) for i in tag_list]
#         if tag_id_list:
#             DatasetTag.objects.bulk_create(tag_id_list)
#
#         # 创建模型
#         ai_model = AiModels.objects.create(model_name=param.model_name)
#         ai_model.save()
#
#         # 模型和数据集连接
#         model_dataset = ModelDataset.objects.create(dataset_id=dataset_train.id, ai_model_id=ai_model.id)
#         model_dataset.save()
#
#         # 模型和测试集连接
#         pracrice_model = PraticeDataset.objects.create(dataset_id=dataset_test.id, ai_model_id=ai_model.id)
#         pracrice_model.save()
#
#
#     else:
#         ai_model = AiModels.objects.filter(id=param.model_id).first()
#         dataset_train = ai_model.modeldataset_set.first()
#         # ai_model = AiModels.objects.filter(id=param.model_id).first()
#         dataset_test = ai_model.praticedataset_set.first()
#     change_data(rawimage[0:int(len(rawimage) * 0.8)] if (len(rawimage) > 1) else rawimage, dataset_train, tags_dict)
#     change_data(rawimage[int(len(rawimage) * 0.8):] if (len(rawimage) > 1) else rawimage, dataset_test, tags_dict)
#     return {"model_id": ai_model.id, "code": 1, "msg": "插入成功"}


def get_only_data(_path,_id_list):
    if os.path.exists(_path):
        with open(_path, "r", encoding="utf-8") as f:
            _data=f.readlines()
        _write_data=[]
        for i in _data:
            j = json.loads(i.strip())
            if j.get("id") not in _id_list:
                _write_data.append(i)
        os.remove(_path)
        with open(_path, "a", encoding="utf-8") as f:
            for z in _write_data:
                f.write(z)


def check_data(data):
    train_list=[]
    err_id_list=[]
    for i in data:
        try:
            with Image.open(i["image_path"]) as img:
                width = img.width
                height = img.height
                i["width"] = width
                i["height"]= height
            train_list.append(i)
        except Exception as e:
            if isinstance(e, OSError):
                err_id_list.append(i["image_path"])
            else:
                err_id_list.append(i["image_path"])
    return train_list,err_id_list


@api.post("/train")
@logger.catch(reraise=True, exclude=HTTPException)
def get_train(b_task: BackgroundTasks, user_auth=Depends(get_user_auth), param: TrainIn = Body()):
    if type(user_auth) == dict:
        return {"code": 401, "msg": "权限验证失败"}
    for i in user_auth:
        if int(param.test_type) in [1, 3]:
            if i["id"] == 7:
                break
        elif int(param.test_type) in [2, 4]:
            if i["id"] == 16:
                break
    else:
        if user_auth:
            return {"code": 401, "msg": "权限验证失败"}
    import json
    from .models import AiModels, PraticeDataset, ModelDataset, AiModelsVersion
    from dataset.models import Tag, DataSet, DatasetTag,RawFilesImage

    _path = param.dataset_path
    with open(_path, "r", encoding="utf-8") as f:
        rawimage = f.read()
    rawimage = json.loads(rawimage)

    # 训练集，测试集位置操作
    rawimage = rawimage.get("image_list", [])
    if not rawimage:
        raise HTTPException(404, detail="没有数据！")

    rawimage,error_id_list=check_data(rawimage)
    if not rawimage:
        logger.info(f"错误图片列表:{error_id_list}")
        raise HTTPException(400, detail="数据错误！")


    # 标签入库操作
    if param.tags:
        if param.file_tags:
            _tag = set(param.tags) | set(param.file_tags)
        else:
            _tag = set(param.tags)
    else:
        _tag = set(param.file_tags)
    # _tag = set(param.tags) if set(param.tags) else set()|set(param.file_tags) if set(param.tags) else set()
    _tag = list(_tag)
    tag = Tag.objects.filter(tag_name__in=_tag).all()
    tag_list = [i.tag_name for i in tag]
    create_tag = set(_tag) - set(tag_list)
    tag_list = [Tag(tag_name=i) for i in create_tag]
    if tag_list:
        Tag.objects.bulk_create(tag_list)  # 创建一组标签
    tag_list = Tag.objects.filter(tag_name__in=_tag).all()
    tags_dict = {i.tag_name: i.id for i in tag_list}
    color = "#E3890B"

    filebookname_list = list({i["fileBookName"] for i in rawimage}) if int(param.test_type) < 3 else list(
        {i["volume_id"] for i in rawimage})
    random.shuffle(filebookname_list)
    train_filebookname = filebookname_list[0:int(len(filebookname_list) * 0.8)] if len(
        filebookname_list) > 1 else filebookname_list
    dev_filebookname = filebookname_list[int(len(filebookname_list) * 0.8):] if len(
        filebookname_list) > 1 else filebookname_list
    # 创建标签的流程

    train_image = []
    dev_image = []
    _pre_os = os.getcwd()
    _pre_os = _pre_os.replace("\\", "/")
    for i in rawimage:
        if int(param.test_type) < 3:
            if i["fileBookName"] in train_filebookname:
                train_image.append(i)
            if i["fileBookName"] in dev_filebookname:
                dev_image.append(i)
        else:
            if i["volume_id"] in train_filebookname:
                train_image.append(i)
            if i["volume_id"] in dev_filebookname:
                dev_image.append(i)
    global env_dict
    _aimp_static_path = env_dict.get("AIMP_DIR") if env_dict.get("AIMP_DIR") else f"{str(pathlib.Path.home())}/.kubao/aimp"
    if _aimp_static_path.endswith("\\"):
        _aimp_static_path = _aimp_static_path[0:-1]
    if _aimp_static_path.endswith("\\"):
        _aimp_static_path = _aimp_static_path[0:-1]
    if _aimp_static_path.endswith("/"):
        _aimp_static_path = _aimp_static_path[0:-1]
    os.makedirs(_aimp_static_path,exist_ok=True)
    max_seq_len = env_dict.get("MAX_SEQ_LENGTH",512)
    if param.model_id is None:
        # 创建训练集
        dataset_train = DataSet.objects.create(dataset_name=f"{param.model_name}的训练集", dataset_type=8)
        dataset_train.save()

        # 训练集链接tag
        tag_id_list = [DatasetTag(data_id_id=dataset_train.id, color=color,
                                  shoutcut_key="", tag_id_id=i.id) for i in tag_list]

        DatasetTag.objects.bulk_create(tag_id_list)

        # 创建测试集
        dataset_test = DataSet.objects.create(dataset_name=f"{param.model_name}的测试集", dataset_type=8)
        dataset_test.save()

        # 测试集链接tag
        tag_id_list = [DatasetTag(data_id_id=dataset_test.id, color=color,
                                  shoutcut_key="", tag_id_id=i.id) for i in tag_list]
        if tag_id_list:
            DatasetTag.objects.bulk_create(tag_id_list)

        # 创建模型
        ai_model = AiModels.objects.create(model_name=param.model_name)
        ai_model.save()

        # 模型和数据集连接
        model_dataset = ModelDataset.objects.create(dataset_id=dataset_train.id, ai_model_id=ai_model.id)
        model_dataset.save()

        # 模型和测试集连接
        pracrice_model = PraticeDataset.objects.create(dataset_id=dataset_test.id, ai_model_id=ai_model.id)
        pracrice_model.save()
        if int(param.test_type) in [1, 2]:
            change_data(train_image, dataset_train, tags_dict, _aimp_static_path)
            change_data(dev_image, dataset_test, tags_dict, _aimp_static_path)
        else:

            change_data_by_handwork(train_image, dataset_train, tags_dict, _aimp_static_path,error_id_list)
            change_data_by_handwork(dev_image, dataset_test, tags_dict, _aimp_static_path,error_id_list)
        practice = AiModelsVersion.objects.create(name=f"{ai_model.model_name}_V1",
                                                  test_type=param.test_type, version=1,
                                                  max_seq_len=max_seq_len,
                                                  model_id=ai_model.id,
                                                  epoch_count=env_dict.get("EPOCH_KBIE",50) if int(param.test_type) not in [1,3]
                                                  else env_dict.get("EPOCH_KBIEX",15))
        practice.save()
        os.makedirs(f"{_aimp_static_path}/mode/{ai_model.id}/{practice.version}", exist_ok=True)
        # create_path(["mode",ai_model.id,f"{practice.version}"])
        if int(param.test_type) in [1, 2]:
            write_file(f"{_aimp_static_path}/mode/{ai_model.id}/train.jsonl", train_image)
            write_file(f"{_aimp_static_path}/mode/{ai_model.id}/dev.jsonl", dev_image)
        else:
            write_file_by_handwork(f"{_aimp_static_path}/mode/{ai_model.id}/train.jsonl", train_image)
            write_file_by_handwork(f"{_aimp_static_path}/mode/{ai_model.id}/dev.jsonl", dev_image)
        # b_task.add_task(create_mode, f"mode/{ai_model.id}", practice.version, conf, practice.id, param.callback_url)
        if int(param.test_type) in [2, 4]:
            os.makedirs(f"{_aimp_static_path}/file/dataset/{dataset_train.id}", exist_ok=True)
            os.makedirs(f"{_aimp_static_path}/file/dataset/{dataset_test.id}", exist_ok=True)
            b_task.add_task(create_mode_uiex, f"{_aimp_static_path}/mode/{ai_model.id}", practice.version,
                            practice.id, param.callback_url,
                            f"{_aimp_static_path}/file/dataset/{dataset_train.id}",
                            f"{_aimp_static_path}/file/dataset/{dataset_test.id}", int(param.test_type),param.status_url)
        else:
            b_task.add_task(create_mode, f"{_aimp_static_path}/mode/{ai_model.id}", practice.version,
                             practice.id, param.callback_url, int(param.test_type),
                            f"{_aimp_static_path}/file/dataset/{dataset_train.id}",
                            f"{_aimp_static_path}/file/dataset/{dataset_test.id}",param.status_url)
        logger.info(
            f"新增训练模型id:{practice.model_id},版本:V{practice.version},入参:{dict(param)},错误图片列表:{error_id_list}")
        return {"model_id": ai_model.id, "code": 1, "msg": "创建成功","error_id_list":error_id_list}
    else:
        rawimage_id_list=list(map(lambda x:x["id"] ,list(filter(lambda x:x.get("id"),rawimage))))
        RawFilesImage.objects.filter(aidp_id__in=rawimage_id_list).delete()
        ai_model = AiModels.objects.filter(id=param.model_id).first()
        dataset_train = ai_model.modeldataset_set.first()
        dataset_test = ai_model.praticedataset_set.first()
        RawFilesImage.objects.filter(aidp_id__in=rawimage_id_list,data_id_id__in=[dataset_train.id,dataset_test.id]).delete()
        get_only_data(f"{_aimp_static_path}/mode/{ai_model.id}/train.jsonl", rawimage_id_list)
        get_only_data(f"{_aimp_static_path}/mode/{ai_model.id}/dev.jsonl", rawimage_id_list)
        version = ai_model.aimodelsversion_set.last().version if ai_model.aimodelsversion_set.all() else 0

        if int(param.test_type) in [1, 2]:
            change_data(train_image, dataset_train.dataset, tags_dict, _aimp_static_path)
            change_data(dev_image, dataset_test.dataset, tags_dict, _aimp_static_path)
        else:
            change_data_by_handwork(train_image, dataset_train.dataset, tags_dict, _aimp_static_path,error_id_list)
            change_data_by_handwork(dev_image, dataset_test.dataset, tags_dict, _aimp_static_path,error_id_list)
        practice = AiModelsVersion.objects.create(name=f"{ai_model.model_name}_V{version + 1}",
                                                  test_type=param.test_type, version=version + 1,
                                                  model_id=ai_model.id,
                                                  max_seq_len=max_seq_len,
                                                  epoch_count=env_dict.get("EPOCH_KBIE",50) if int(param.test_type) in [1,3] else env_dict.get("EPOCH_KBIEX",15))
        practice.save()
        # 获取所有的图片
        os.makedirs(f"{_aimp_static_path}/mode/{ai_model.id}/{version + 1}", exist_ok=True)
        if int(param.test_type) in [1, 2]:
            write_file(f"{_aimp_static_path}/mode/{ai_model.id}/train.jsonl", train_image)
            write_file(f"{_aimp_static_path}/mode/{ai_model.id}/dev.jsonl", dev_image)
        else:
            write_file_by_handwork(f"{_aimp_static_path}/mode/{ai_model.id}/train.jsonl", train_image)
            write_file_by_handwork(f"{_aimp_static_path}/mode/{ai_model.id}/dev.jsonl", dev_image)
        # b_task.add_task(create_mode, f"mode/{ai_model.id}", version + 1, conf, practice.id, param.callback_url)
        if int(param.test_type) in [2, 4]:
            b_task.add_task(create_mode_uiex, f"{_aimp_static_path}/mode/{ai_model.id}", version + 1,  practice.id,
                            param.callback_url,
                            f"{_aimp_static_path}/file/dataset/{dataset_train.dataset_id}",
                            f"{_aimp_static_path}/file/dataset/{dataset_test.dataset_id}", int(param.test_type),
                            param.status_url)
        else:
            b_task.add_task(create_mode, f"{_aimp_static_path}/mode/{ai_model.id}", version + 1,  practice.id,
                            param.callback_url, int(param.test_type),
                            f"{_aimp_static_path}/file/dataset/{dataset_train.dataset_id}",f"{_aimp_static_path}/file/dataset/{dataset_test.dataset_id}",param.status_url)
            logger.info(f"新增训练模型id:{practice.model_id},版本:V{practice.version},入参:{dict(param)},错误图片列表:{error_id_list}")
        return {"model_id": ai_model.id, "code": 2, "msg": "添加成功","error_id_list":error_id_list}


@functools.lru_cache(int(os.environ.get('_MODEL_MAX_CACHESIZE', '16')))
def get_model_by_version(param: InferenceVersionIdIn = Body()) -> AiModelsVersionOut:
    from .models import AiModelsVersion
    ai_model_version = AiModelsVersion.objects.filter(id=param.version_id).first()
    return ai_model_version


@functools.lru_cache(int(os.environ.get('_MODEL_MAX_CACHESIZE', '16')))
def get_taskflow(param: InferenceTaskflowIn,
                 ai_model_version:AiModelsVersionOut):
    import os
    os.environ["HF_DATASETS_OFFLINE"] = "1"
    os.environ["TRANSFORMERS_OFFLINE"] = "1"
    tags = param.tags
    # schema = [{'文档包含著录项[否,是]': list(tags)}]
    device = env_dict.get("DEVICE_INFERENCE","cpu")
    batch_size_inference = env_dict.get("BATCH_SIZE_INFERENCE",1)
    _log_path = env_dict.get("AIMP_DIR") if env_dict.get("AIMP_DIR") else os.path.expanduser('~/.kubao/aimp')
    if ai_model_version.test_type == 2:
        _log_path = f"{_log_path}/mode/{ai_model_version.model_id}" \
                    f"/{ai_model_version.version}"
    else:
        _log_path = f"{_log_path}/mode/{ai_model_version.model_id}" \
                    f"/{ai_model_version.version}/mode"
    if not os.path.exists(_log_path):
        os.makedirs(_log_path, exist_ok=True)
    with open(f"{_log_path}/model.log", "a", encoding="utf-8") as f:
        f.write(
            f"{datetime.datetime.now()},batch_size:{batch_size_inference},device:{device},schema1:{list(tags) if tags else ''},schema2:{list(param.file_tags) if param.file_tags else ''},task_path:{ai_model_version.path}\n")
    taskflow = Taskflow('document_record',
                        max_seq_len=ai_model_version.max_seq_len,
                        cls_max_seq_len=ai_model_version.max_seq_len,
                        batch_size=int(batch_size_inference),
                        device=device,
                        # skip_cls = 分类超参
                        schema=list(tags) if tags else list(param.file_tags),
                        task_path=f"{ai_model_version.path}")
    return taskflow, param


async def aget_taskflow(
        param: InferenceTaskflowIn = Body(),
        ai_model_version: AiModelsVersionOut = Depends(get_model_by_version)):
    return get_taskflow(param, ai_model_version)


@api.post("/inference")
@logger.catch
def get_inference(b_task: BackgroundTasks,
                  param: InferenceIn = Body(),
                  taskflow=Depends(aget_taskflow),
                  user_auth=Depends(get_user_auth),
                  ai_model_version=Depends(get_model_by_version)):
    if type(user_auth) == dict:
        return {"code": 401, "msg": "权限验证失败"}
    for i in user_auth:
        if int(ai_model_version.test_type) in [1, 3]:
            if i["id"] == 7:
                break
        elif ai_model_version.test_type in [2, 4]:
            if i["id"] == 16:
                break
    else:
        if user_auth:
            return {"code": 401, "msg": "权限验证失败"}

    with open(param.data_path, "r", encoding="utf-8") as f:
        data_list = f.read()
    data_list = json.loads(data_list)
    b_task.add_task(write_task_flow, taskflow, ai_model_version, data_list, param.callback_url)
    return {'msg': "开始著录"}
    # 补充试用部分

import os
def write_task_flow(taskflow, ai_model_version, data_list, callback_url=None):
    return_data_list = taskflow([''.join(i['word']) for i in data_list])
    return_data = []
    for i, j in zip(data_list, return_data_list):
        if j:
            return_data.append({'image_id': i['image_id'], "fileBookName": i["fileBookName"], 'result': j})
    os.makedirs(f"file/result/{ai_model_version.model_id}/{ai_model_version.version}",exist_ok=True)
    _path = f"file/result/{ai_model_version.model_id}/{ai_model_version.version}/{int(time.time())}.txt"
    with open(_path, "w", encoding="utf-8") as f:
        f.write(json.dumps(return_data, ensure_ascii=False))
    import os
    s = os.getcwd()
    s = s.replace("\\", "/")
    if callback_url:
        requests.get(f"{callback_url}{s}/{_path}")


def write_file(path, raw_images):
    with open(path, "a", encoding="utf-8") as f:
        for raw_image in raw_images:
            raw_image_list = raw_image["image_path"]
            raw_image_list = raw_image_list.replace("\\", "/")
            raw_image_list = raw_image_list.split("/")
            project_name = raw_image_list[-4]
            fragment = [
                {
                    "label": raw_image["mark_list"][mark_pos]["tag"],
                    "id": mark_pos,
                    "fragment": [{
                        "box_id": i["box_id"],
                        "mention": i["text"],
                        "offset_map": i["offset_map"]
                    } for i in raw_image["mark_list"][mark_pos]["param_list"]]
                } for mark_pos in range(0, len(raw_image["mark_list"]))
            ]
            json_list ={
                "word": raw_image["word"],
                "bbox": raw_image["bbox_list"],
                "image": f'{project_name}/{raw_image["fileBookName"]}/{raw_image["image_name"]}',
                "path": raw_image["image_path"],
                "fileBookName": raw_image["fileBookName"],
                "relations": [],
                "entities": fragment}

            if raw_image.get("id"):
                json_list["id"]=raw_image["id"]
            _dict = json.dumps(json_list, ensure_ascii=False)
            f.write(f"{_dict}\n")


def write_file_by_handwork(path, raw_images):
    with open(path, "a", encoding="utf-8") as f:
        for raw_image in raw_images:
            raw_image_list = raw_image["image_path"]
            raw_image_list = raw_image_list.replace("\\", "/")
            raw_image_list = raw_image_list.split("/")
            project_name = raw_image_list[-4]
            fragment = [
                {
                    "label": raw_image["mark_list"][mark_pos]["tag"],
                    "id": mark_pos,
                    "record_type": raw_image["mark_list"][mark_pos]["record_type"],
                    "fragment": [{"mention": _zip_file[0], "bbox": _zip_file[1]} for _zip_file in
                                 list(zip(raw_image["mark_list"][mark_pos]["text"],
                                          raw_image["mark_list"][mark_pos]["bbox"]))]
                } for mark_pos in range(0, len(raw_image["mark_list"]))
            ]
            json_list = {
                "word": raw_image["word"],
                "bbox": raw_image["bbox_list"],
                "image": f'{project_name}/{raw_image["fileBookName"]}/{raw_image["image_name"]}',
                "path": raw_image["image_path"],
                "fileBookName": raw_image["fileBookName"],
                "file_id": raw_image["file_id"],
                "volume_id": raw_image["volume_id"],
                "relations": [],
                "entities": fragment}

            if raw_image.get("id"):
                json_list["id"] = raw_image["id"]
            _dict = json.dumps(json_list, ensure_ascii=False)

            f.write(f"{_dict}\n")


# 原始数据写入数据:
def change_data(rawimage, data, tags_dict, _aimp_static_path):
    from dataset.models import RawFilesImage, FileBox, TextBox, MarkBox
    tags_list = []
    for i in range(0, len(rawimage)):
        text_list = rawimage[i]["word"]
        bbox_list = rawimage[i]["bbox_list"]
        raw_image_list = rawimage[i]["image_path"]
        raw_image_list = raw_image_list.replace("\\", "/")
        raw_image_list = raw_image_list.split("/")
        project_name = raw_image_list[-4]
        if not os.path.exists(
                f"{_aimp_static_path}/file/dataset/{data.id}/{project_name}/{rawimage[i]['fileBookName']}"):
            os.makedirs(f"{_aimp_static_path}/file/dataset/{data.id}/{project_name}/{rawimage[i]['fileBookName']}",
                        exist_ok=True)
        shutil.copyfile(rawimage[i]['image_path'],
                        f"{_aimp_static_path}/file/dataset/{data.id}/{project_name}/{rawimage[i]['fileBookName']}/{rawimage[i]['image_name']}")
        img = Image.open(rawimage[i]['image_path'])
        width = img.width
        height = img.height
        raw_file_image = RawFilesImage(
            path=f"{_aimp_static_path}/file/dataset/{data.id}/{project_name}/{rawimage[i]['fileBookName']}/{rawimage[i]['image_name']}",
            name=rawimage[i]['image_name'],
            image_width=width,raw_status=2,
            image_height=height, image_id=rawimage[i]['image_id'],
            data_id_id=data.id,aidp_id=rawimage[i].get("id",""))
        raw_file_image.save()
        for q in rawimage[i]["mark_list"]:
            for j in range(0, len(q["param_list"])):
                file_box = FileBox(raw_file_id=raw_file_image.id, bbox_id=j, text=text_list[j],
                                   bbox=json.dumps(bbox_list[q["param_list"][j]["box_id"]]))
                file_box.save()
                for z in q["param_list"][j]["offset_map"]:
                    text_box = TextBox.objects.create(bbox_begin=z[0], bbox_end=z[1], bbox_id=file_box.id)
                    text_box.save()
                    tags_list.append(MarkBox(tag_id=tags_dict[q["tag"]], text_id=text_box.id))
    if tags_list:
        MarkBox.objects.bulk_create(tags_list)


# 原始数据写入数据:
def change_data_by_handwork(rawimage, data, tags_dict, _aimp_static_path,err_image_list):
    from dataset.models import RawFilesImage, FileBox, TextBox, MarkBox
    tags_list = []
    for i in range(0, len(rawimage)):

        raw_image_list = rawimage[i]["image_path"]
        raw_image_list = raw_image_list.replace("\\", "/")
        raw_image_list = raw_image_list.split("/")
        project_name = raw_image_list[-4]
        image_path = rawimage[i]['image_path']
        # if not os.path.exists(
        #         f"{_aimp_static_path}/file/dataset/{data.id}/{project_name}/{rawimage[i]['fileBookName']}"):
        os.makedirs(f"{_aimp_static_path}/file/dataset/{data.id}/{project_name}/{rawimage[i]['fileBookName']}",
                        exist_ok=True)
        shutil.copyfile(image_path,
                        f"{_aimp_static_path}/file/dataset/{data.id}/{project_name}/{rawimage[i]['fileBookName']}/{rawimage[i]['image_name']}")

        raw_file_image = RawFilesImage(
            path=f"{_aimp_static_path}/file/dataset/{data.id}/{project_name}/{rawimage[i]['fileBookName']}/{rawimage[i]['image_name']}",
            name=rawimage[i]['image_name'],
            image_width=rawimage[i]["width"], raw_status=2,
            image_height=rawimage[i]["height"], image_id=rawimage[i]['image_id'],
            data_id_id=data.id, aidp_id=rawimage[i].get("id", ""))
        raw_file_image.save()
        for q in rawimage[i]["mark_list"]:
            _list = list(zip(q["bbox"], q["text"]))
            for j in range(0, len(_list)):
                file_box = FileBox(raw_file_id=raw_file_image.id,
                                   bbox_id=j,
                                   text=_list[j][1],
                                   bbox=json.dumps(_list[j][0]))
                file_box.save()
                for z in _list:
                    text_box = TextBox.objects.create(bbox_begin=0, bbox_end=len(z[1]) - 1 if len(z[1]) > 0 else 0,
                                                      bbox_id=file_box.id)
                    text_box.save()
                    tags_list.append(MarkBox(tag_id=tags_dict[q["tag"]], text_id=text_box.id))
    if tags_list:
        MarkBox.objects.bulk_create(tags_list)



async def create_mode(path, version_id,  _id, callback_url, test_type,train_path,dev_path,file_url):
    global env_dict
    os.makedirs(f"{path}/{version_id}", exist_ok=True)
    shutil.copyfile(f"{path}/dev.jsonl", f"{path}/{version_id}/dev.jsonl")
    shutil.copyfile(f"{path}/train.jsonl", f"{path}/{version_id}/train.jsonl")
    from .models import AiModelsVersion
    ai_model_version = await AiModelsVersion.objects.filter(id=_id).afirst()
    batch_size_train = env_dict.get("BATCH_SIZE_TRAIN",8)
    from anyio import run_process
    device = env_dict.get("DEVICE_TRAIN","cuda")

    if file_url:
        env_dict[f"AIDP_FILE_URL_{_id}"] = file_url
    if os.path.exists(f"./document_record_finetune.exe"):
        ope_command = f'document_record_finetune.exe  --uie_model_path=kbnlp/data/pretrained/kbie-nano ' \
                      f'--seed=42 ' \
                      f'--train_path={path}/{version_id}/train.jsonl  --dev_path={path}/{version_id}/dev.jsonl  --max_seq_len=448 ' \
                      f'--num_epoch={env_dict.get("EPOCH_KBIE",50)}  --save_dir={path}/{version_id}/mode --learning_rate=1e-5 --logging_steps=10 ' \
                      f'--batch_size={batch_size_train} --negative_ratio=4  --weight_decay=1e-6  --task_type=document_record  ' \
                      f'--train_image_base_path={train_path}  ' \
                      f'--dev_image_base_path={dev_path} ' \
                      f'--device={device} '
        ope_command += '--data_source=aidp3.0' if test_type < 3 else '--data_source=aidp3.1'
        env = os.environ.copy()
        env['CALLBACK_URL'] = f"{base_url}/model/model_type/{ai_model_version.model_id}/{ai_model_version.version}"

        process = await run_process(ope_command,
                                    check=False, stderr=subprocess.STDOUT,
                                    env=env)
    else:
        env = os.environ.copy()
        env['CALLBACK_URL'] = f"{base_url}/model/model_type/{ai_model_version.model_id}/{ai_model_version.version}"
        python_path = f'kbnlp/scripts/information_extraction{";" if sys.platform == "win32" else ":"}{env.get("PYTHONPATH", "")}'
        uie_model_path = 'kbnlp/data/pretrained/kbie-nano'
        if 'KUBAO_AIMP_DISTPKG' in env and env['KUBAO_AIMP_DISTPKG']:
            dst = env['KUBAO_AIMP_DISTPKG']
            python_path = f'{dst}/kbnlp/scripts/information_extraction:{env.get("PYTHONPATH", "")}'
            uie_model_path = os.path.dirname(dst) + '/../../share/aimp/kbnlp/data/pretrained/kbie-nano'
        if 'PREFIX' in env and env['PREFIX']:
            uie_model_path = f'{env["PREFIX"]}/share/aimp/kbnlp/data/pretrained/kbie-nano'
        env['PYTHONPATH'] = python_path

        process = await run_process([
            sys.executable, '-m',
            'document_record_finetune',
            f'--uie_model_path={uie_model_path}',
            f'--train_path={path}/{version_id}/train.jsonl',
            f'--dev_path={path}/{version_id}/dev.jsonl',
            f"--seed=42",
            f"--batch_size={batch_size_train}",
            '--max_seq_len=512', f'--num_epoch={env_dict.get("EPOCH_KBIE",50)}', f'--save_dir={path}/{version_id}/mode',
            '--learning_rate=1e-5', '--logging_steps=10', '--shuffle',
            '--negative_ratio=4', '--weight_decay=1e-6', '--task_type=document_record',
            f'--device={device}',
            f"--train_image_base_path={train_path}",
            f"--dev_image_base_path={dev_path}",
            "--data_source=aidp3.0" if test_type < 3 else '--data_source=aidp3.1'
        ]
            , check=False, stderr=subprocess.STDOUT,
            env=env)  # stdout=wf, stderr=wf)
        print(process.returncode,8888888888888888)
    if process.returncode == 0:
        if env_dict.get(f"AIDP_FILE_URL_{_id}"):
            del env_dict[f"AIDP_FILE_URL_{_id}"]
        await sync_to_async(update_epoch)(ai_model_version.id, path, callback_url)
    else:
        await sync_to_async(update_epoch)(ai_model_version.id)
        if file_url:
            data = await sync_to_async(requests.post)(file_url, json={"status": 3, "epoch": ai_model_version.epoch, "epoch_count": ai_model_version.epoch_count})
            if env_dict.get(f"AIDP_FILE_URL_{_id}"):
                del env_dict[f"AIDP_FILE_URL_{_id}"]
            if data.status_code > 400:
                logger.info(f"案卷url为{file_url}, 返回的状态码{data.status_code}")
        try:
            sys.stdout.write(process.stdout.decode(sys.getdefaultencoding()))
        except:
            sys.stdout.write("启动失败")
    os.makedirs(f"{path}/{version_id}/mode", exist_ok=True)
    with open(f"{path}/{version_id}/mode/model.log", 'wb') as f:
        f.write(process.stdout)
    # sys.stdout.write(process.stdout.decode(sys.getdefaultencoding()))


import sys
async def create_mode_uiex(path, version_id,  _id, callback_url, train_path, dev_path, test_type,file_url):
    global env_dict
    os.makedirs(f"{path}/{version_id}", exist_ok=True)
    shutil.copyfile(f"{path}/dev.jsonl", f"{path}/{version_id}/dev.jsonl")
    shutil.copyfile(f"{path}/train.jsonl", f"{path}/{version_id}/train.jsonl")
    epoch_count = env_dict.get("EPOCH_KBIEX",15)
    from .models import AiModelsVersion
    ai_model_version = await AiModelsVersion.objects.filter(id=_id).afirst()
    batch_size_train_uiex = env_dict.get("BATCH_SIZE_TRAIN_UIEX",1)
    max_seq_len = env_dict.get("MAX_SEQ_LENGTH",512)
    # path = f"../../{path}"
    from anyio import run_process
    if file_url:
        env_dict[f"AIDP_FILE_URL_{_id}"]=file_url
    if os.path.exists(f"./kbiex.exe"):
        ope_command = f'kbiex.exe ' \
        f'--logging_steps=10  ' \
        f'--save_strategy=epoch  ' \
        f'--evaluation_strategy=epoch  ' \
        f'--seed=42  ' \
        f'--model_name_or_path=kbnlp/data/pretrained/kbie-x-base  ' \
        f'--output_dir={path}/{version_id}/mode  ' \
        f'--train_path={path}/{version_id}/train.jsonl  ' \
        f'--dev_path={path}/{version_id}/dev.jsonl  ' \
        f'--max_seq_len={max_seq_len} ' \
        f'--per_device_train_batch_size={batch_size_train_uiex}  ' \
        f'--per_device_eval_batch_size={batch_size_train_uiex}  ' \
        f'--num_train_epochs={epoch_count}  ' \
        f'--learning_rate=1e-5  ' \
        f'--label_names=start_positions,end_positions  ' \
        f'--do_train  ' \
        f'--do_eval  ' \
        f'--load_best_model_at_end  ' \
        f'--overwrite_output_dir  ' \
        f'--metric_for_best_model=eval_f1  ' \
        f'--save_total_limit=1  ' \
        f'--task_type=document_record  ' \
        f'--train_image_base_path={train_path}  ' \
        f'--dev_image_base_path={dev_path} '
        ope_command += '--data_source=aidp3.0' if test_type < 3 else '--data_source=aidp3.1'

        env = os.environ.copy()
        env['CALLBACK_URL'] = f"{base_url}/model/model_type/{ai_model_version.model_id}/{ai_model_version.version}"

        process = await run_process(ope_command,
                                    check=False, stderr=subprocess.STDOUT,
                                    env=env)
    else:
        env = os.environ.copy()
        env['CALLBACK_URL'] = f"{base_url}/model/model_type/{ai_model_version.model_id}/{ai_model_version.version}"
        python_path = f'kbnlp/scripts/information_extraction{";" if sys.platform == "win32" else ":"}{env.get("PYTHONPATH", "")}'
        uie_model_path = 'kbnlp/data/pretrained/kbie-x-base'
        if 'KUBAO_AIMP_DISTPKG' in env and env['KUBAO_AIMP_DISTPKG']:
            dst = env['KUBAO_AIMP_DISTPKG']
            python_path = f'{dst}/kbnlp/scripts/information_extraction:{env.get("PYTHONPATH", "")}'
            uie_model_path = os.path.dirname(dst) + '/../../share/aimp/kbnlp/data/pretrained/kbie-x-base'
        if 'PREFIX' in env and env['PREFIX']:
            uie_model_path = f'{env["PREFIX"]}/share/aimp/kbnlp/data/pretrained/kbie-x-base'
        env['PYTHONPATH'] = python_path

        process = await run_process([
            sys.executable, '-m',
            'my_uiex_finetune',
            "--logging_steps=10",
            "--save_strategy=epoch",
            "--evaluation_strategy=epoch",
            "--seed=42",
            f'--model_name_or_path={uie_model_path}',
            f"--output_dir={path}/{version_id}/mode",
            f'--train_path={path}/{version_id}/train.jsonl',
            f'--dev_path={path}/{version_id}/dev.jsonl',
            "--max_seq_len=512",
            f"--per_device_train_batch_size={batch_size_train_uiex}",
            f"--per_device_eval_batch_size={batch_size_train_uiex}",
            f"--num_train_epochs={epoch_count}",
            "--learning_rate=1e-5",
            '--task_type=document_record',
            f"--label_names=start_positions,end_positions",
            "--do_train",
            "--do_eval",
            "--overwrite_output_dir",
            "--metric_for_best_model=eval_f1",
            "--save_total_limit=1",
            "--load_best_model_at_end",
            f"--train_image_base_path={train_path}",
            f"--dev_image_base_path={dev_path}",
            "--data_source=aidp3.0" if test_type < 3 else '--data_source=aidp3.1'
        ]
            , check=False, stderr=subprocess.STDOUT,
            env=env)  # stdout=wf, stderr=wf)
    if process.returncode == 0:
        if env_dict.get(f"AIDP_FILE_URL_{_id}"):
            del env_dict[f"AIDP_FILE_URL_{_id}"]
        await sync_to_async(update_epoch)(ai_model_version.id, path, callback_url)
    else:
        await sync_to_async(update_epoch)(ai_model_version.id)
        if file_url:
            data = await sync_to_async(requests.post)(file_url, json={"status": 3, "epoch": ai_model_version.epoch, "epoch_count": ai_model_version.epoch_count})
            if env_dict.get(f"AIDP_FILE_URL_{_id}"):
                del env_dict[f"AIDP_FILE_URL_{_id}"]
            if data.status_code>400:
                logger.info(f"案卷url为{file_url}, 返回的状态码{data.status_code}")
        try:
            sys.stdout.write(process.stdout.decode(sys.getdefaultencoding()))
        except Exception as e:
            sys.stdout.write("启动失败")
    os.makedirs(f"{path}/{version_id}/mode", exist_ok=True)
    with open(f"{path}/{version_id}/model.log", 'wb') as f:
        f.write(process.stdout)


def update_epoch(verson_id, _path=None, callback_url=None):
    from .models import AiModelsVersion
    ai_model_version = AiModelsVersion.objects.filter(id=verson_id).first()
    if _path:
        ai_model_version.status = 2
        if ai_model_version.test_type in [2, 4]:
            ai_model_version.path = f"{_path}/{ai_model_version.version}/mode"
        else:
            ai_model_version.path = f"{_path}/{ai_model_version.version}/mode/model_best"
        if callback_url:
            requests.get(callback_url)
    else:
        ai_model_version.status = 3
    ai_model_version.save()


def create_path(file_list):
    _file = ""
    for i in file_list:
        _file = f"{_file}/{i}"
        if not os.path.exists(_file[1:]):
            os.mkdir(_file[1:])


@api.get("/versionstatus")
@logger.catch
def Version_status(user_auth=Depends(get_user_auth), param: VersionStatus = Depends()):
    if type(user_auth) == dict:
        return {"code": 401, "msg": "权限验证失败"}
    for i in user_auth:
        if i["id"] in [7, 16]:
            break
    else:
        if user_auth:
            return {"code": 401, "msg": "权限验证失败"}
    from .models import AiModelsVersion
    if param.version_id:
        aimodel_version = AiModelsVersion.objects.filter(id=param.version_id)
    else:
        aimodel_version = AiModelsVersion.objects.filter(model_id=param.model_id)
    if aimodel_version:
        aimodel_version = aimodel_version.last()
        return {"epoch": aimodel_version.epoch, "count": aimodel_version.epoch_count}
    else:
        return {}


@api.post("/inferencewithoutfile")
@logger.catch
def get_inference_without_file(b_task: BackgroundTasks,
                               # param_tag: InferenceTaskflowIn = Body(),
                               param: InferenceIn = Body(),
                               user_auth=Depends(get_user_auth),
                               taskflow=Depends(aget_taskflow),
                               ai_model_version=Depends(get_model_by_version)):
    if type(user_auth) == dict:
        return {"code": 401, "msg": "权限验证失败"}
    for i in user_auth:
        if int(ai_model_version.test_type) in [1,3]:
            if i["id"] == 7:
                break
        elif int(ai_model_version.test_type) in [2,4]:
            if i["id"] == 16:
                break
    else:
        if user_auth:
            return {"code": 401, "msg": "权限验证失败"}

    # taskflow.set_schema(param_tag.tags)
    with open(param.data_path, "r", encoding="utf-8") as f:
        data_list = f.read()
    data_list = json.loads(data_list)
    if int(ai_model_version.test_type) in [1, 2]:
        b_task.add_task(inference_callback, taskflow[0], data_list, param.callback_url, ai_model_version)
    elif int(ai_model_version.test_type) in [3, 4]:
        b_task.add_task(inference_callback_handwork, taskflow[0], data_list, param.callback_url, param.file_url,
                        ai_model_version, taskflow[1])
    return {'msg': "开始推理"}
    # 补充试用部分


def inference_callback(taskflow, data_list, callback_url, ai_model_version):
    filebookname_list = list({i["fileBookName"] for i in data_list})
    filebookname_dict = {}
    for j in data_list:
        if not filebookname_dict.get(j["fileBookName"], []):
            filebookname_dict[j["fileBookName"]] = []
        filebookname_dict[j["fileBookName"]].append(j)
    for z in range(0, len(filebookname_list)):
        data_list = filebookname_dict[filebookname_list[z]]
        if ai_model_version.test_type == 2:
            return_data_list = taskflow(data_list)
        else:
            return_data_list = taskflow([''.join(i['word']) for i in data_list])
        return_data = []
        _url = f"{callback_url}&fileBookName={filebookname_list[z]}&theEnd="
        for i, j in zip(data_list, return_data_list):
            if j:
                return_data.append({'image_id': i['image_id'], "fileBookName": i["fileBookName"], 'result': j})
        if z == len(filebookname_list) - 1:
            _url = f"{_url}1"
        requests.post(_url, json=return_data)


def inference_callback_handwork(taskflow, begin_data_list, callback_url, file_url, ai_model_version, param_tag):
    volume_id_list = list({i["volume_id"] for i in begin_data_list})
    volume_id_dict = {}
    volume_file_dict={}
    aimp_ocr = env_dict.get("USE_AIMP_OCR",0)
    for j in begin_data_list:
        if not volume_id_dict.get(j["volume_id"], []):
            volume_id_dict[j["volume_id"]] = []
        if not volume_file_dict.get(j["volume_id"], set()):
            volume_file_dict[j["volume_id"]]=set()
        j["image"] = j["image_path"]
        if aimp_ocr and int(aimp_ocr) == 1:
            if j.get("bbox"):
                del j["bbox"]
            if j.get("word"):
                del j["word"]
        volume_file_dict[j["volume_id"]]=volume_file_dict.get(j["volume_id"], set())|{j["file_id"]}
        volume_id_dict[j["volume_id"]].append(j)
    for z in range(0, len(volume_id_list)):
        data_list = volume_id_dict[volume_id_list[z]]
        if param_tag.file_tags:
            taskflow.set_schema(list(param_tag.file_tags))
            file_id_dict = {}
            for filebook in volume_id_dict[volume_id_list[z]]:
                if not file_id_dict.get(filebook["file_id"], []):
                    file_id_dict[filebook["file_id"]] = []
                file_id_dict[filebook["file_id"]].append(filebook)
            file_book = list(volume_file_dict[volume_id_list[z]])
            for file_z in range(0,len(file_book)):
                data_list = file_id_dict[file_book[file_z]]
                print(data_list)
                return_data_list = taskflow(data_list)
                print(return_data_list)
                # if ai_model_version.test_type == 4:
                #     return_data_list = taskflow(data_list)
                # else:
                #     return_data_list = taskflow([''.join(i['word']) for i in data_list])
                return_data = []
                if str(file_book[file_z]) == "0":
                    continue
                _url = f"{file_url}&file_id={file_book[file_z]}&theEnd="
                for i, j in zip(data_list, return_data_list):
                    if j:
                        return_data.append(
                            {'image_id': i['image_id'], "fileBookName": i["fileBookName"], "file_id": i["file_id"],
                             "volume_id": i["volume_id"], 'result': j})
                data = requests.post(_url, json=return_data)
                if data.status_code >= 400 or os.getenv("IS_TRAIN_VERSION"):
                    logger.info(f"案件url为{_url}, 返回的状态码{data.status_code}")
        if param_tag.tags:
            data_list = volume_id_dict[volume_id_list[z]]
            taskflow.set_schema(list(param_tag.tags))
            return_data_list = taskflow(data_list)
            # if ai_model_version.test_type == 4:
            #     return_data_list = taskflow(data_list)
            # else:
            #     return_data_list = taskflow([''.join(i['word']) for i in data_list])
            return_data = []

            _url = f"{callback_url}&volume_id={volume_id_list[z]}&theEnd="
            for i, j in zip(data_list, return_data_list):
                if j:
                    return_data.append(
                        {'image_id': i['image_id'], "fileBookName": i["fileBookName"], "file_id": i["file_id"],
                         "volume_id": i["volume_id"], 'result': j})
            if z == len(volume_id_list) - 1:
                _url = f"{_url}1"
            data = requests.post(_url, json=return_data)
            if data.status_code >= 400 or os.getenv("IS_TRAIN_VERSION"):
                logger.info(f"案卷url为{_url}, 返回的状态码{data.status_code}")
    else:
        if param_tag.file_tags:
            _url=f"{file_url}&file_id=0&theEnd=1"
            data = requests.post(_url, json=[])
            if data.status_code >= 400 or os.getenv("IS_TRAIN_VERSION"):
                logger.info(f"案件url为{_url}, 返回的状态码{data.status_code}")


# 补充试用部分
@api.post("/test1")
def test1(request: Request, param=Body()):
    print(dict(request.headers))
    print(param,777777777)
    return {"msg": "请求成功"}


@api.post("/test3")
def test3(request: Request, param=Body()):
    print(dict(request.headers))
    print(param)
    return {"msg": "请求成功"}

@api.get("/test2")
def test2():
    url = env_dict.get("AIDP_URL")
    headers = {
        "cookie": "kubao_remember_token=1|2abb1957e7879bd9388b2b87224ff2d9dc56026801605a4b62e73b89bec2585314ccd2b55042b79a8517194fe81c418b614a6a22ae272479311d5faa0a0c75fd; Expires=Thur, 23-Dec-2024 09:11:10 GMT; Path=/"}
    data = requests.get(url, headers=headers)
    print(data.content.decode())
    return 123


"""document_record_finetune.exe  --uie_model_path=kbnlp/data/pretrained/uie-nano --train_path=mode/279/1/train.jsonl  --dev_path=mode/279/1/dev.jsonl  --max_seq_len=448 --num_epoch=50  --save_dir=mode/279/1/mode --learning_rate=1e-5 --logging_steps=10 --negative_ratio=4  --weight_decay=1e-6  --task_type=document_record"""




