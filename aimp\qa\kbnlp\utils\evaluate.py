class Measure:
    def __init__(self):
        # 所有词的个数，也就是句子的长度，含ROOT
        self.countAllWords = 0
        # 依存关系和正确支配的词都正确
        self.countWordsAndDep = 0
        # 正确支配的词
        self.countWords = 0
        # # 非根正确支配词
        # self.countWordsAndDepNotRoot = 0
        # # 非根总节点
        # self.countAllWordsNotRoot = 0
        # 句子
        self.countSentence = 0
        # 正确根节点
        self.countWordsAndDepRoot = 0
        # 整个依存关系正确
        self.countAllDependency = 0
        self.countAllWordsAndDep = 0

    def __repr__(self):
        return "UAS:%-4.2f%% LAS:%-4.2f%% RA:%-4.2f%% CM:%-4.2f%% CM':%-4.2f%%" % \
               (self.getUAS() * 100, self.getLAS() * 100, self.getRA() * 100, self.getCM() * 100, self.getCMS() * 100)

    # 无标记依存正确率
    def getUAS(self):
        return self.countWords / self.countAllWords

    # 带标记正确依存率
    def getLAS(self):
        return self.countWordsAndDep / self.countAllWords

    # 根正确率
    def getRA(self):
        return self.countWordsAndDepRoot / self.countSentence

    # 完全匹配率
    def getCM(self):
        return self.countAllDependency / self.countSentence

    # 改进的CM指标
    def getCMS(self):
        return self.countAllWordsAndDep / self.countSentence

    @staticmethod
    def is_punct(dep, punct_dep='punct'):
        return dep == punct_dep

    # 统计正确的依赖词数，和正确的依赖词和依赖关系数
    def updateScore(self, dependencyWordsRef, dependencyRef, dependencyWordsPre, dependencyPre):
        """

        :param dependencyWordsRef: 参考依存词
        :param dependencyRef: 参考依存关系
        :param dependencyWordsPre: 预测依存词
        :param dependencyPre: 预测依存关系
        :return:
        """

        dependencyCount = 0
        wordsAndDepCount = 0
        punctuation = 0
        for i in range(len(dependencyWordsRef)):
            if Measure.is_punct(dependencyRef[i]):
                punctuation += 1
            if dependencyWordsPre[i] == dependencyWordsRef[i]:
                dependencyCount += 1
                if not Measure.is_punct(dependencyRef[i]):
                    self.countWords += 1
                if dependencyPre[i] == dependencyRef[i]:
                    wordsAndDepCount += 1
                    if not Measure.is_punct(dependencyRef[i]):
                        self.countWordsAndDep += 1
                        if dependencyWordsRef[i] == len(dependencyWordsRef) + 1:
                            self.countWordsAndDepRoot += 1

        self.countSentence += 1
        self.countAllWords += len(dependencyPre) - punctuation

        if dependencyCount == len(dependencyWordsRef):
            self.countAllDependency += 1
        if wordsAndDepCount == len(dependencyWordsRef):
            self.countAllWordsAndDep += 1
