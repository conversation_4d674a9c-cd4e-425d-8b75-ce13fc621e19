import itertools
import jsonlines
from dataclasses import dataclass
from typing import Optional

import pyarrow as pa

import datasets
from datasets.table import table_cast
from kbnlp.scripts.information_extraction.my_convert import DoccanoConvertorForCloseDomain

logger = datasets.utils.logging.get_logger(__name__)


if datasets.config.PYARROW_VERSION.major >= 7:

    def pa_table_from_pylist(mapping):
        return pa.Table.from_pylist(mapping)

else:

    def pa_table_from_pylist(mapping):
        # Copied from: https://github.com/apache/arrow/blob/master/python/pyarrow/table.pxi#L5193
        arrays = []
        names = []
        if mapping:
            names = list(mapping[0].keys())
        for n in names:
            v = [row[n] if n in row else None for row in mapping]
            arrays.append(v)
        return pa.Table.from_arrays(arrays, names)


@dataclass
class DoccanoConvertorForCloseDomainConfig(datasets.BuilderConfig):
    """BuilderConfig for JSON."""
    # DoccanoConvertorForCloseDomain参数, 注意必须指定类型提示，否则不能认出该字段。
    is_train: bool = True
    task_type: str = 'ext'
    anno_type: str = 'image'
    # 以下参数从datasets库中json.py文件中拷贝过来。
    features: Optional[datasets.Features] = None
    use_threads: bool = True  # deprecated
    block_size: Optional[int] = None  # deprecated
    chunksize: int = 10 << 20  # 10MB
    newlines_in_values: Optional[bool] = None


class DoccanoConvertorForCloseDomainBuilder(datasets.ArrowBasedBuilder):
    BUILDER_CONFIG_CLASS = DoccanoConvertorForCloseDomainConfig

    def _info(self):
        if self.config.block_size is not None:
            logger.warning("The JSONLines loader parameter `block_size` is not supported currently.")
            self.config.chunksize = self.config.block_size
        if self.config.use_threads is not True:
            logger.warning(
                "The JSONLines loader parameter `use_threads` is deprecated and doesn't have any effect anymore."
            )
        if self.config.newlines_in_values is not None:
            raise ValueError("The JSONLines loader parameter `newlines_in_values` is no longer supported")
        return datasets.DatasetInfo(features=self.config.features)

    def _split_generators(self, dl_manager):
        """We handle string, list and dicts in datafiles"""
        if not self.config.data_files:
            raise ValueError(f"At least one data file must be specified, but got data_files={self.config.data_files}")
        data_files = dl_manager.download_and_extract(self.config.data_files)
        if isinstance(data_files, (str, list, tuple)):
            files = data_files
            if isinstance(files, str):
                files = [files]
            files = [dl_manager.iter_files(file) for file in files]
            return [datasets.SplitGenerator(name=datasets.Split.TRAIN, gen_kwargs={"files": files})]
        splits = []
        for split_name, files in data_files.items():
            if isinstance(files, str):
                files = [files]
            files = [dl_manager.iter_files(file) for file in files]
            splits.append(datasets.SplitGenerator(name=split_name, gen_kwargs={"files": files}))
        return splits

    def _cast_table(self, pa_table: pa.Table) -> pa.Table:
        if self.config.features is not None:
            # more expensive cast to support nested structures with keys in a different order
            # allows str <-> int/float or str to Audio for example
            pa_table = table_cast(pa_table, self.config.features.arrow_schema)
        return pa_table

    def _generate_tables(self, files):
        for file_idx, file in enumerate(itertools.chain.from_iterable(files)):
            convertor = DoccanoConvertorForCloseDomain(file,
                                                       splits=[1., 0, 0] if self.config.is_train else [0, 1., 0],
                                                       task_type=self.config.task_type,
                                                       is_shuffle=False,
                                                       seed=None,
                                                       anno_type=self.config.anno_type,
                                                       verbose=False,
                                                       )
            train_ds, dev_ds, _ = convertor.convert()
            dataset = train_ds if self.config.is_train else dev_ds
            dataset = {k: [d[k] for d in dataset] for k in dataset[0].keys()}
            pa_table = pa.Table.from_pydict(mapping=dataset)
            yield file_idx, self._cast_table(pa_table)


