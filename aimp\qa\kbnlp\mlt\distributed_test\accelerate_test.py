import argparse
import os
import random
import shutil
import time
import warnings
import random

import torch
import torchvision
from torch.autograd import Variable
import torch.utils.data.dataloader as Data

import torch.nn as nn
import torch.nn.parallel
import torch.backends.cudnn as cudnn
import torch.distributed as dist
import torch.optim
import torch.multiprocessing as mp
import torch.utils.data
import torch.utils.data.distributed
import torchvision.transforms as transforms
import torchvision.datasets as datasets
import torchvision.models as models
from accelerate import Accelerator, DistributedType
from time import time

from qa.kbnlp.mlt.dataset import BaseDataset
from torch.utils.data.dataloader import Dataset, DataLoader
from qa.kbnlp.mlt.data_transform import TransformList


parser = argparse.ArgumentParser(description='PyTorch ImageNet Training')
parser.add_argument("--fp16", action="store_true", help="If passed, will use FP16 training.")
parser.add_argument("--cpu", action="store_true", help="If passed, will train on the CPU.")
args = parser.parse_args()

args.seed = 0

random.seed(args.seed)
torch.manual_seed(args.seed)
cudnn.deterministic = True


train_data = torchvision.datasets.MNIST(
    './mnist', train=True, transform=torchvision.transforms.ToTensor(), download=True
)
test_data = torchvision.datasets.MNIST(
    './mnist', train=False, transform=torchvision.transforms.ToTensor()
)


testX, testY = test_data.data, test_data.targets


def build_sample(dataX, dataY):
    examples = []
    for i, (x, y) in enumerate(zip(dataX, dataY)):
        examples.append({'original_input': x})
        examples[-1]['_id'] = i
        examples[-1]['target'] = y
    return examples


def to_tensor(sample):
    sample['original_input'] = torch.tensor(sample['original_input'], dtype=torch.float).unsqueeze(0)
    sample['target'] = torch.tensor(sample['target'], dtype=torch.long)
    return sample


class DemoDataset(BaseDataset):
    def __init__(self, data, transform_list: TransformList = None):
        super(DemoDataset, self).__init__(data, transform_list)


dataset = DemoDataset(build_sample(testX, testY), TransformList(to_tensor))
train_data = dataset
test_data = dataset


accelerator = Accelerator(fp16=args.fp16, cpu=args.cpu)


train_loader = Data.DataLoader(dataset=train_data, shuffle=True, batch_size=64)
test_loader = Data.DataLoader(dataset=test_data, batch_size=64)


class Net(torch.nn.Module):
    def __init__(self):
        super(Net, self).__init__()
        self.conv1 = torch.nn.Sequential(
            torch.nn.Conv2d(1, 32, 3, 1, 1),
            torch.nn.ReLU(),
            torch.nn.MaxPool2d(2))
        self.conv2 = torch.nn.Sequential(
            torch.nn.Conv2d(32, 64, 3, 1, 1),
            torch.nn.ReLU(),
            torch.nn.MaxPool2d(2)
        )
        self.conv3 = torch.nn.Sequential(
            torch.nn.Conv2d(64, 64, 3, 1, 1),
            torch.nn.ReLU(),
            torch.nn.MaxPool2d(2)
        )
        self.dense = torch.nn.Sequential(
            torch.nn.Linear(64 * 3 * 3, 128),
            torch.nn.ReLU(),
            torch.nn.Linear(128, 10)
        )

    def forward(self, x):
        conv1_out = self.conv1(x)
        conv2_out = self.conv2(conv1_out)
        conv3_out = self.conv3(conv2_out)
        res = conv3_out.view(conv3_out.size(0), -1)
        out = self.dense(res)
        return out


model = Net()

optimizer = torch.optim.Adam(model.parameters(), lr=2e-3)
loss_func = torch.nn.CrossEntropyLoss()
cudnn.benchmark = True

model, optimizer, train_loader, test_loader = accelerator.prepare(model, optimizer, train_loader, test_loader)

time_cost = 0.
for epoch in range(10):
    start = time()
    model.train()
    if args.local_rank == 0:
        print('epoch {}'.format(epoch + 1))
    # training-----------------------------
    train_loss = 0.
    train_acc = 0.
    # train_sampler.set_epoch(epoch)
    # test_sampler.set_epoch(epoch)
    # for batch_x, batch_y in test_loader:
    for batch in test_loader:
        batch_x, batch_y = batch['original_input'], batch['target']
        b_size = batch_x.size(0)
        batch_x, batch_y = Variable(batch_x), Variable(batch_y)
        out = model(batch_x)
        loss = loss_func(out, batch_y)
        with torch.no_grad():
            pred = torch.max(out, 1)[1]
            correct = (pred == batch_y).sum().float()
            # reduce_loss = reduce_mean(loss, args.nprocs)
            # reduce_acc = reduce_mean(correct, args.nprocs)
            # train_loss += reduce_loss.item()
            # train_acc += reduce_acc.item()
        optimizer.zero_grad()
        accelerator.backward()
        optimizer.step()

    if args.local_rank == 0:
        print('Train Loss: {:.6f}, Acc: {:.6f}'.format(train_loss / (len(
            train_loader)), train_acc * args.nprocs / (len(train_data))))

    # evaluation--------------------------------
    model.eval()
    eval_loss = 0.
    eval_acc = 0.
    with torch.no_grad():
        model.eval()
        # for batch_x, batch_y in test_loader:
        for batch in test_loader:
            batch_x, batch_y = batch['original_input'], batch['target']
            batch_x, batch_y = Variable(batch_x).cuda(args.local_rank, non_blocking=True), Variable(batch_y).cuda(
                args.local_rank, non_blocking=True)
            out = model(batch_x)
            loss = loss_func(out, batch_y)
            # eval_loss += loss.item()
            pred = torch.max(out, 1)[1]
            correct = (pred == batch_y).sum().float()

            # reduce_loss = reduce_mean(loss, args.nprocs)
            # reduce_acc = reduce_mean(correct, args.nprocs)
            # eval_loss += reduce_loss.item()
            # eval_acc += reduce_acc.item()

    if args.local_rank == 0:
        print('Test Loss: {:.6f}, Acc: {:.6f}'.format(eval_loss / len(
            test_loader), eval_acc * args.nprocs / len(test_data)))
    end = time()
    cost = torch.tensor(end - start, dtype=torch.float, device=args.local_rank)

    # cost = reduce_mean(cost, args.nprocs)
    time_cost += cost
if args.local_rank == 0:
    print('time: %f' % time_cost)
