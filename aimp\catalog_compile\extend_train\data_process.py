# encoding=utf-8
import os


class Data:

    def __init__(self, path, text, label, id):
        self.path = path
        self.text = text
        self.label = label
        self.id = id
        self.pred_label = ''
        self.pred_id = 0
        self.is_biaoti = False


class DataProcess:

    def __init__(self, app_user_dir):
        self.first = set() # 首页类别
        self.more = set() # 超过5页类别
        self.less = set() # 少于5页的类别
        self.labe2datas = dict()
        self.label2id = dict()
        self.id2label = dict()
        with open(os.path.join(app_user_dir, 'suffix.dat'), 'r', encoding='utf-8') as f:
            for line in f:
                if '\n' == line:
                    continue
                id, label = line.replace('\n', '').split('=-')
                self.label2id[label] = id
                self.id2label[int(id)] = label

    def process(self, path):
        for dir_path, dir_names, file_names in os.walk(path):
            label2num = dict()
            is_first = True
            for file in file_names:
                label = file.split('-')[1]
                id = self.label2id[label]
                if is_first:
                    self.first.add(label)
                    is_first = False
                full_path = os.path.join(dir_path, file)
                text = ''
                with open(full_path, 'r', encoding='utf-8') as f:
                    for line in f:
                        if '\n' == line:
                            continue
                        text += line.replace('\n', '') + ' '
                data = Data(full_path, text, label, id)
                if 'biaoti' in file:
                    data.is_biaoti = True
                data_list = []
                if label in self.labe2datas:
                    data_list = self.labe2datas[label]
                data_list.append(data)
                self.labe2datas[label] = data_list
                if label in self.first:
                    continue
                count = 1
                if label in label2num:
                    count = label2num[label] + 1
                label2num[label] = count
            for key in label2num:
                if label2num[key] > 5:
                    self.more.add(key)
                if label2num[key] <= 4:
                    self.less.add(key)
        inter = self.more.intersection(self.less)
        for key in inter:
            self.more.remove(key)
        inter = self.more.intersection(self.first)
        for key in inter: # 去掉首页类
            self.more.remove(key)
        inter = self.less.intersection(self.first)
        for key in inter: # 去掉首页类
            self.less.remove(key)
