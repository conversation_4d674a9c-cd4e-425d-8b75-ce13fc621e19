import json
import os
import random
import re
import string
import threading
import time
import uuid
from pathlib import Path
from tugraph import GraphDatabase
from .wkld_job_ex_clt import example
from syntactic_analysis.parsing import parse_result
from common.logger import logger


model_path = str(Path(__file__).parent.parent.parent.parent / "static/config/rnn_standard_layer_2_bi_True_demo.parser")


class TuGraphData:

    _instance = None  # 保存单例对象
    _lock = threading.RLock()  # 锁
    init_flag = False

    def __new__(cls, *args, **kwargs):

        if cls._instance:  # 如果已经有单例了就不再去抢锁，避免IO等待
            cls.init_flag = True
            return cls._instance

        with cls._lock:  # 使用with语法，方便抢锁释放锁
            if not cls._instance:
                cls._instance = super().__new__(cls)
            else:
                cls.init_flag = True

            return cls._instance

    def __init__(self, tugraph_db: GraphDatabase):
        if not TuGraphData.init_flag:
            self.gdb = tugraph_db
            self.labels = self.label_txt()

    @staticmethod
    def get_img(dir_path):
        """
        获取图片路径
        :param dir_path: 目录路径
        :return:
        """
        img_dict = dict()
        img_list = []
        img_type_list = {'jpg', 'bmp', 'png', 'jpeg', 'rgb', 'tif'}
        search_file_name_re_exp = ".*_\d+\.(.*)"
        if dir_path and os.path.exists(dir_path):
            for root, dirs, files in os.walk(dir_path):
                for item in files:
                    match = re.match(search_file_name_re_exp, item, re.IGNORECASE)
                    if match and match.group(1) in img_type_list:
                        file_path = f'{root}/{match.group()}'
                        origin_name = re.sub("_\d+", "", match.group())
                        origin_path = f'{root}/{origin_name}'
                        if origin_path not in img_dict:
                            img_dict[origin_path] = [file_path]
                            img_list.append(file_path)
                        else:
                            temp_item = img_dict[origin_path]
                            temp_item.append(file_path)
                            img_dict[origin_path] = temp_item
                            img_list.append(file_path)
                    else:
                        if any(item.lower().endswith(ext) for ext in img_type_list):
                            file_path = f'{root}/{item}'
                            if file_path not in img_dict:
                                img_dict[file_path] = []
                                img_list.append(file_path)

        return img_dict, img_list

    @staticmethod
    def label_txt():
        config_path = Path(__file__).parent.parent.parent.parent / "static/config/la_dp09.txt"
        la_dp_path = str(config_path)
        with open(la_dp_path, "r", encoding="utf-8") as f:
            content = f.read()
        labels = content.split()

        return labels

    def word_config(self, word_relation, fs_id, graph_name):
        """
        词
        :param word_relation:
        :param fs_id:
        :return:
        """
        for index, item in enumerate(word_relation[0]):
            word_list = list(set(item))
            query_data = self.gdb.cypher.query({
                "graph": f"{graph_name}",
                "script": f"MATCH (n:word) WHERE n.id IN {word_list} RETURN n, n.id"
            })
            result = query_data.get("result")
            # exist_word = {w[1]: int(re.search(r"\[(\d+)]", w[1]).group(1)) for w in result} if result else {}
            exist_word = {w[1]: json.loads(w[0])["identity"] for w in result}
            if len(word_list) >= len(exist_word):
                new_word = list(set(word_list).difference(set(list(exist_word.keys()))))
            else:
                new_word = word_list

            if new_word:
                new_word_id = self.gdb.nodes.create({
                    "label": "word",
                    "fields": ["id", "lang"],
                    "values": [[item, "zh-CN"] for item in new_word]
                })
                new_word_dict = {w: new_word_id[n] for n, w in enumerate(new_word)}
                exist_word.update(new_word_dict)

            # WordOccurrence创建
            wo_id = self.gdb.nodes.create({
                "label": "WordOccurrence",
                "fields": ["id", "i"],
                "values": [[f"{str(uuid.uuid4())}{i}{str(int(round(time.time() * 1000000)))}", str(i)] for i in range(1, len(word_list) + 1)]
            })

            # 建立fs-->WordOccurrence的边
            img_edge = [{"source": fs_id, "destination": int(wi), "values": []} for wi in wo_id]
            rs = self.gdb.relationship.create({
                "label": "NLP_HAS",
                "fields": [],
                "edge": img_edge
            })

            # 建立WordOccurrence-->word的边
            img_edge = [{"source": wi, "destination": exist_word[word], "values": []} for wi, word in
                        zip(wo_id, word_list)]
            logger.info(img_edge)
            rs = self.gdb.relationship.create({
                "label": "NLP_IS",
                "fields": [],
                "edge": img_edge
            })

            # 建立WordOccurrence-->WordOccurrence的边
            img_edge = [{"source": wo_id[word_list.index(word)],
                         "destination": wo_id[word_list.index(item[word_relation[1][index][n] - 1])] if
                         word_relation[1][index][n] != 0 else fs_id,
                         "values": [word_relation[2][index][n]]} for n, word in enumerate(item)]
            rs = self.gdb.relationship.create({
                "label": "NLP_DEP",
                "fields": ["deprel"],
                "edge": img_edge
            })

    def sync_tugraph(self, item_data, img_dir_path, graph_name):
        """
        同步tugraph
        """
        logger.info(f"\n______item_data_______\n{item_data}")
        dh = item_data.get('dh')  # 档号
        img_dir = item_data.get('wjlj', '')  # 文件路径
        dir_path = os.path.join(img_dir_path, img_dir).replace("\\", "/")
        logger.info(f"*****************{dh} start sync******************")
        if not os.path.exists(dir_path):
            logger.error(f"{dh} --> No picture")
            return

        # 获取item
        query_data = self.gdb.cypher.query({
            "graph": f"{graph_name}",
            "script": f"MATCH (n:item) WHERE n.dh='{dh}' RETURN id(n)"
        })
        item_ret = query_data.get("result")
        if not item_ret:
            try:
                # create item node
                item_ret = self.gdb.nodes.create({
                    "label": "item",
                    "fields": list(item_data.keys()),
                    "values": [list(item_data.values())]
                })
            except Exception as e:
                logger.error(e)
            item_id = item_ret[0]  # 条目v_id
        else:
            item_id = item_ret[0][0]  # 条目v_id

        # 图片路径列表
        img_dict, img_list = self.get_img(dir_path)
        jobs = [
            {'modfunc': 'mtl.layout', 'args': img_list},
            {'modfunc': 'mtl.ocr', 'args': img_list}
        ]
        logger.info(f"{dh} start layout and ocr...")
        ret = example(jobs)
        layout_result = ret.get("layout")
        ocr_result = ret.get("ocr")

        logger.info(f"{dh} start sync tugraph...")
        for origin_path, split_list in img_dict.items():
            query_data = self.gdb.cypher.query({
                "graph": f"{graph_name}",
                "script": f"MATCH (i:Img) WHERE i.path='{origin_path}' RETURN i"
            })
            result = query_data.get("result")
            if result:
                continue
            img_values = [origin_path]  # 每张图片的数据
            if split_list:  # 存在分割的图片
                img_id_list = []
                img_in_database = False
                for item_file in split_list:
                    box_values = []  # 图片中box数据
                    title_list = []  # 图片中的标题
                    side_note = []  # 图片中的边注
                    #
                    box_id_list = []
                    fs_id_list = []
                    try:
                        spilt_num = re.search("_\d+", item_file).group()
                        # 版面分析
                        layout_dic = layout_result.get(item_file)
                        for box, label, score in zip(layout_dic['boxes'], layout_dic['labels'],
                                                     layout_dic['scores']):
                            x1, y1, x2, y2 = [int(item) for item in box]
                            label = self.labels[label]
                            unique_key = f"{str(uuid.uuid4())}{spilt_num}{str(int(round(time.time() * 1000000)))}"
                            box_values.append([unique_key, label, str(int(score)), str(x1), str(y1), str(x2), str(y2)])

                        # 全文ocr识别
                        content = ocr_result.get(item_file)
                        # FakeSentence入库
                        fs_id_list = self.gdb.nodes.create({
                            "label": "FakeSentence",
                            "fields": ["id", "raw"],
                            "values": [[f"{str(uuid.uuid4())}{content[4:8]}{str(int(round(time.time() * 1000000)))}", content if len(content) < 1300 else f"{content[:1300]}..."]]
                        })
                        logger.info(f"{item_file} 开始语法依存解析......")
                        # 语法依存解析
                        word_relation = parse_result(model_path, content)
                        self.word_config(word_relation, fs_id_list[0], graph_name)

                        if not img_in_database:
                            img_values.extend(['、'.join(title_list), '、'.join(side_note)])
                            # 图片入库
                            img_id_list = self.gdb.nodes.create({
                                "label": "Img",
                                "fields": ["path", "title", "side_note"],
                                "values": [img_values]
                            })
                            img_in_database = True

                        # box入库
                        box_id_list = self.gdb.nodes.create({
                            "label": "Box",
                            "fields": ["id", "label", "score", "x1", "y1", "x2", "y2"],
                            "values": box_values
                        })

                        # 建立图片-->条目的边
                        img_edge = [{"source": img_id_list[0], "destination": item_id, "values": []}]
                        rs = self.gdb.relationship.create({
                            "label": "IMG_IN_ITEM",
                            "fields": [],
                            "edge": img_edge
                        })

                        # 建立图片-->box的边
                        img_edge = [{"source": img_id_list[0], "destination": box_id, "values": []} for box_id in
                                    box_id_list]
                        rs = self.gdb.relationship.create({
                            "label": "IMG_HAS_BOX",
                            "fields": [],
                            "edge": img_edge
                        })

                        # 建立FakeSentence-->Img的边
                        img_edge = [{"source": fs_id, "destination": img_id_list[0], "values": []} for fs_id in
                                    fs_id_list]
                        rs = self.gdb.relationship.create({
                            "label": "FS_IN_IMG",
                            "fields": [],
                            "edge": img_edge
                        })
                    except Exception as e:
                        import traceback
                        logger.error(f"{dh} --- {item_file} error: {traceback.format_exc()}")
                        nodes_list = fs_id_list + box_id_list + img_id_list
                        for new_id in nodes_list:
                            try:
                                ret = self.gdb.nodes.delete(new_id)
                            except Exception as e:
                                logger.error(f"{dh} --- {item_file} rollback error: {e}")
                                continue
            else:  # 不存在分割的图片
                box_values = []  # 图片中box数据
                title_list = []  # 图片中的标题
                side_note = []  # 图片中的边注
                #
                img_id_list = []
                box_id_list = []
                fs_id_list = []
                try:
                    # 版面分析
                    layout_dic = layout_result.get(origin_path)
                    for box, label, score in zip(layout_dic['boxes'], layout_dic['labels'],
                                                 layout_dic['scores']):
                        x1, y1, x2, y2 = [int(item) for item in box]
                        label = self.labels[label]
                        unique_key = f"{str(uuid.uuid4())}{''.join(random.choices(string.ascii_uppercase + str(score) + string.digits, k=20))}{str(int(round(time.time() * 1000000)))}"
                        box_values.append([unique_key, label, str(int(score)), str(x1), str(y1), str(x2), str(y2)])

                    # 全文ocr识别
                    content = ocr_result.get(origin_path)
                    # FakeSentence入库
                    fs_id_list = self.gdb.nodes.create({
                        "label": "FakeSentence",
                        "fields": ["id", "raw"],
                        "values": [[f"{str(uuid.uuid4())}{content[4:8]}{str(int(round(time.time() * 1000000)))}", content if len(content) < 1300 else f"{content[:1300]}..."]]
                    })

                    logger.debug(f"{origin_path} 开始语法依存解析......")
                    # 语法依存解析
                    word_relation = parse_result(model_path, content)
                    self.word_config(word_relation, fs_id_list[0], graph_name)

                    img_values.extend(['、'.join(title_list), '、'.join(side_note)])
                    # 图片入库
                    img_id_list = self.gdb.nodes.create({
                        "label": "Img",
                        "fields": ["path", "title", "side_note"],
                        "values": [img_values]
                    })

                    # box入库
                    box_id_list = self.gdb.nodes.create({
                        "label": "Box",
                        "fields": ["id", "label", "score", "x1", "y1", "x2", "y2"],
                        "values": box_values
                    })

                    # 建立图片-->条目的边
                    img_edge = [{"source": img_id_list[0], "destination": item_id, "values": []}]
                    rs = self.gdb.relationship.create({
                        "label": "IMG_IN_ITEM",
                        "fields": [],
                        "edge": img_edge
                    })

                    # 建立图片-->box的边
                    img_edge = [{"source": img_id_list[0], "destination": box_id, "values": []} for box_id in
                                box_id_list]
                    rs = self.gdb.relationship.create({
                        "label": "IMG_HAS_BOX",
                        "fields": [],
                        "edge": img_edge
                    })

                    # 建立FakeSentence-->Img的边
                    img_edge = [{"source": fs_id, "destination": img_id_list[0], "values": []} for fs_id in
                                fs_id_list]
                    rs = self.gdb.relationship.create({
                        "label": "FS_IN_IMG",
                        "fields": [],
                        "edge": img_edge
                    })
                except Exception as e:
                    import traceback
                    logger.error(f"{dh} --- {origin_path} error: {traceback.format_exc()}")
                    nodes_list = fs_id_list + box_id_list + img_id_list
                    for new_id in nodes_list:
                        try:
                            ret = self.gdb.nodes.delete(new_id)
                        except Exception as e:
                            logger.error(f"{dh} --- {origin_path} rollback error: {e}")
                            continue

        logger.info(f"{dh} --- sync finished......")
        return dh




