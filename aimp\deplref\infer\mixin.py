import os
from copy import copy
import torch
from functools import lru_cache
from anyio import to_thread, Semaphore, CapacityLimiter

class BatchInferFnMixin:
    collect_fn = torch.stack

    @classmethod
    @lru_cache(1)
    def cap(cls):
        return CapacityLimiter(int(os.environ.get('THREADS_PER_TASK', '4')))

    def get_model(self, model_pt_path, use_gpu=True, gpu_id=None):
        new = copy(self)
        new.initialize(model_pt_path, use_gpu, gpu_id)
        return new

    async def batch_infer_fn(self, files):
        batch_size = getattr(self, 'max_batch_size', 1)
        fs = [to_thread.run_sync(self.preprocess, f, limiter=self.cap()) for f in files]
        rs = []
        l = len(files)
        for i in range(0, l, batch_size):
            xs = []; es = []
            for j in range(i, min(i + batch_size, l)):
                try:
                    xs.append(await fs[j])
                except Exception as e:
                    xs.append(self.example_data)
                    es.append((j, e))
                finally:
                    fs[j] = None
            xs = self.collect_fn(xs)
            result = self.inference(xs); del xs
            result = self.postprocess(result)
            rs.extend(result); del result
            for j, e in es:
                rs[j] = e
        return rs
