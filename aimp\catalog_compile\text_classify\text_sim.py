# encoding=utf8
import numpy as np
import math
from numpy.linalg import norm
from collections import defaultdict
from pyemd import emd


# @profile
def bm25(q, t):
    k = 1.5
    b = 0.75
    score = 0.0
    sq = 0.0
    for qt in q:
        if qt in t:
            score += q[qt] * t[qt] * (k + 1) / (t[qt] + k * (1 - b + b * len(t) / 10))
        sq += q[qt]
    if sq > 0:
        s = score / sq
        if s > 0.2:
            s = 0.2 + (s - 0.2) * 0.6
        return s
    else:
        return -1


# @profile
def ctr(q, t):
    s1 = 0.0
    s2 = 0.0
    for qt in q:
        s1 += q[qt]
        if qt in t:
            s2 += t[qt]
    if s1 == 0:
        return 0.0
    else:
        s = s2 / s1
        if s > 0.5:
            s = 0.5 + (s - 0.5) * 0.5
        return s


# @profile
def cqr(q, t):
    s1 = 0.0
    s2 = 0.0
    for qt in t:
        s1 += t[qt]
        if qt in q:
            s2 += q[qt]
    if s1 == 0:
        return 0.0
    else:
        s = s2 / s1
        if len(q) - len(t) > 0:
            s /= math.sqrt(1 + (len(q) - len(t)))
        if s > 0.5:
            s = 0.5 + (s - 0.5) * 0.4
    return s


# @profile
def wmd(s1, s2, w2v):
    score = math.sqrt(_wmdistance(w2v, s1, s2)) * 0.5
    if score == float('inf'):
        score = 2.0
    return score


# @profile
def se_sim(qv, tw, w2v):
    fv = np.zeros([300]).astype('float32')
    for term in tw:
        if term in w2v:
            fv += np.array(w2v[term])
    if len(tw) > 0:
        fv /= float(len(tw))
    s = np.dot(qv, fv)/(norm(qv)*norm(fv))
    return s.item()


# @profile
def query_len_penalty(q):
    try:
        score = math.log(len(q), 4) - 1.5
        if score > 1.0:
            score = 1.0
    except:
        score = -1.0
    return score


# @profile
def title_len_penalty(t):
    try:
        score = math.log(len(t) + 2, 4) - 1.3
        if score > 1.0:
            score = 1.0
    except:
        score = -1.0
    return score


# score=2.4 | attach_score=1.5 
def get_score(qts, tts, qws, tws, w2v, score=2.4):
    return bm25(qws, tws) * 2 + ctr(qws, tws) + cqr(qws, tws) - wmd(qts, tts, w2v) + se_sim(_q2v(w2v, qws), tts, w2v) + query_len_penalty(qts) + title_len_penalty(tts) - score


# @profile
# NOTE current hotspot
def _wmdistance(w2v, s1, s2):
    s1 = [token for token in s1 if token in w2v]
    s2 = [token for token in s2 if token in w2v]
    if not s1 or not s2:
        return float('inf')

    i2w = list(set(s1+s2))
    w2i = { w:i for i, w in enumerate(i2w) }
    vocab_len = len(i2w)

    if vocab_len == 1:
        return 0.0

    ds1 = set(s1)
    ds2 = set(s2)
    
    w2v = [w2v[w] for w in i2w] # w2v[i2w]
    w2vnorm = norm(w2v, axis=-1, keepdims=True)
    w2v /= np.where(w2vnorm != 0, w2vnorm, 1.0)

    dist = np.zeros((vocab_len, vocab_len), dtype=np.double)
    for i, t1 in enumerate(i2w):
        if t1 not in ds1:
            continue
        v1 = w2v[w2i[t1]]

        for j, t2 in enumerate(i2w):
            if t2 not in ds2 or dist[i, j] != 0.0:
                continue
            v2 = w2v[w2i[t2]]
            dist[i, j] = dist[j, i] = norm(v1-v2)

    if abs(np.sum(dist)) < 1e-8:
        return float('inf')

    def nbow(doc):
        d = np.zeros(vocab_len, dtype=np.double)
        nbow = _doc2bow(w2i, doc)
        doc_len = len(doc)
        for idx, freq in nbow:
            d[idx] = freq / float(doc_len)
        return d

    d1 = nbow(s1)
    d2 = nbow(s2)
    # Compute WMD.
    return emd(d1, d2, dist)


# @profile
def _doc2bow(w2i, doc):
    counter = defaultdict(int)
    for w in doc:
        counter[w2i[w]] += 1
    return counter.items()


# @profile
def _q2v(w2v, tws):
    v = np.zeros([300], dtype='float32')
    count = 0
    for k in tws:
        for ch in list(k):
            if ch in w2v:
                count += 1
                v += w2v[ch]
    if count > 0:
        v /= float(count)
    return v


if __name__ == '__main__':
    qts = ['a','b']
    tts = ['b','c']
    qws = {'a': 1.0, 'b': 1.0}
    tws = {'b': 1.0, 'c': 1.0}
    from catalog_compile.common.nlp_res import get_w2v
    w2v = get_w2v()
    from timeit import Timer
    timer = Timer('get_score(qts, tts, qws, tws, w2v)', globals=globals())
    c, t = timer.autorange()
    c, t = timer.autorange()
    print('time {}s/it, {} loops'.format(t/c, c))
    print(get_score(qts, tts, qws, tws, w2v))
