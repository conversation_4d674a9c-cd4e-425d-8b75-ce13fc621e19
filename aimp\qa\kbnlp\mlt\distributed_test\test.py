import torchvision
import torch
from qa.kbnlp.mlt.dataset import BaseDataset
from torch.utils.data.dataloader import Dataset, DataLoader
from qa.kbnlp.mlt.data_transform import TransformList
from torch.nn.parallel import DistributedDataParallel as DDP

train_data = torchvision.datasets.MNIST(
    './mnist', train=True, transform=torchvision.transforms.ToTensor(), download=True
)
test_data = torchvision.datasets.MNIST(
    './mnist', train=False, transform=torchvision.transforms.ToTensor()
)
testX, testY = test_data.data, test_data.targets


def build_sample(dataX, dataY):
    examples = []
    for i, (x, y) in enumerate(zip(dataX, dataY)):
        examples.append({'original_input': x})
        examples[-1]['_id'] = i
        examples[-1]['target'] = y
    return examples


def to_tensor(sample):
    sample['original_input'] = torch.tensor(sample['original_input'], dtype=torch.float)
    sample['target'] = torch.tensor(sample['target'], dtype=torch.float)
    return sample


class DemoDataset(BaseDataset):
    def __init__(self, data, transform_list: TransformList = None):
        super(DemoDataset, self).__init__(data, transform_list)


dataset = DemoDataset(build_sample(testX, testY), TransformList(to_tensor))

dataloader = DataLoader(dataset, batch_size=64)
print('finish.')
