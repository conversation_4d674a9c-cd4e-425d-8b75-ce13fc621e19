def merge_dict(d: dict, overwrite=False, inplace=False, **kwargs):
    """Merging the provided dict with other kvs

    Args:
      d:
      kwargs:
      d: dict:
      overwrite:  (Default value = False)
      inplace:  (Default value = False)
      **kwargs:

    Returns:


    """
    nd = dict([(k, v) for k, v in d.items()] + [(k, v) for k, v in kwargs.items() if overwrite or k not in d])
    if inplace:
        d.update(nd)
        return d
    return nd


def merge_locals_kwargs(locals: dict, kwargs: dict = None, excludes=('self', 'kwargs', '__class__')):
    if not kwargs:
        kwargs = dict()
    return merge_dict(dict((k, v) for k, v in locals.items()
                           if k not in excludes), **kwargs)


def ids_to_tags(tag_ids, lens, vocab):
    predict_tags = []
    vocab = vocab.idx_to_token
    for b, l in zip(tag_ids, lens):
        predict_tags.append([])
        for i in b[:l]:
            predict_tags[-1].append(vocab[i])
    return predict_tags
