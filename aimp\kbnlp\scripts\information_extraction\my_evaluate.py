# # Copyright (c) 2022 PaddlePaddle Authors. All Rights Reserved.
# #
# # Licensed under the Apache License, Version 2.0 (the "License");
# # you may not use this file except in compliance with the License.
# # You may obtain a copy of the License at
# #
# #     http://www.apache.org/licenses/LICENSE-2.0
# #
# # Unless required by applicable law or agreed to in writing, software
# # distributed under the License is distributed on an "AS IS" BASIS,
# # WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# # See the License for the specific language governing permissions and
# # limitations under the License.
#
# import argparse
# import re
# from functools import partial
# import jsonlines
# from collections import defaultdict
# import torch
# from torch.utils.data import DataLoader
# from copy import copy
# from transformers import AutoTokenizer
# from kbnlp.scripts.information_extraction.metric import SpanEvaluator, VerboseSpanEvaluator
# from kbnlp.scripts.information_extraction.utils import dbc2sbc
# from kbnlp.taskflow import Taskflow
# from log import logger
#
# from kbnlp.taskflow.models.information_extraction.information_extraction_model import UIE, AutoPromptUIE, AutoPromptUIEConfig
#
#
# def evaluate(model, metric, data_loader, device):
#     """
#     Given a dataset, it evals model and computes the metric.
#     Args:
#         model(obj:`paddle.nn.Layer`): A model to classify texts.
#         metric(obj:`paddle.metric.Metric`): The evaluation metric.
#         data_loader(obj:`paddle.io.DataLoader`): The dataset loader which generates batches.
#         device:
#     """
#     model.eval()
#     metric.reset()
#     with torch.no_grad():
#         for batch in data_loader:
#             start_ids = batch.pop('start_ids')
#             end_ids = batch.pop('end_ids')
#             prompt = batch.pop('prompt')
#             span2label = batch.pop('span2label')
#             offset_mapping = batch.pop('offset_mapping')
#             batch = dict((k, v.to(device) if isinstance(v, torch.Tensor) else v) for k, v in batch.items())
#             inputs = [batch['input_ids'], batch['attention_mask'], batch['token_type_ids'], batch['position_ids']]
#             if 'auto_prompt_ids' in batch:
#                 inputs.append(batch['auto_prompt_ids'])
#             res = model(*inputs)
#             start_prob, end_prob = res['start_prob'], res['end_prob']
#             metric.compute(
#                 start_prob.to('cpu'), end_prob.to('cpu'), start_ids, end_ids, prompt, span2label, offset_mapping.tolist())
#             # metric.update(num_correct, num_infer, num_label)
#         [precision, recall, f1_score, macro_p, macro_r, macro_f1,
#          macro_f1_2, micro_p, micro_r, micro_f1, matrix, labels] = metric.accumulate()
#         model.train()
#     return precision, recall, f1_score, macro_p, macro_r, macro_f1, macro_f1_2, micro_p, micro_r, micro_f1, matrix, labels
#
#
# def unify_prompt_name(prompt):
#     # The classification labels are shuffled during finetuning, so they need
#     # to be unified during evaluation.
#     if re.search(r'\[.*?\]$', prompt):
#         prompt_prefix = prompt[:prompt.find("[", 1)]
#         cls_options = re.search(r'\[.*?\]$', prompt).group()[1:-1].split(",")
#         cls_options = sorted(list(set(cls_options)))
#         cls_options = ",".join(cls_options)
#         prompt = prompt_prefix + "[" + cls_options + "]"
#         return prompt
#     return prompt
#
#
# # def get_infer_model(model, tokenizer, data, device='cpu'):
# #     model.eval()
# #     dataset = UIEDataset(data)
# #     loader = DataLoader(dataset,
# #                         collate_fn=partial(model.collate, tokenizer=tokenizer, max_seq_len=args.max_seq_len),
# #                         batch_size=1)
# #     with torch.no_grad():
# #         for b in loader:
# #             b.pop('start_ids', None)
# #             b.pop('end_ids', None)
# #             b = dict((k, v.to(device) if isinstance(v, torch.Tensor) else v) for k, v in b.items())
# #             torch._C._jit_set_profiling_mode(False)
# #             inputs = [b['input_ids'], b['attention_mask'], b['token_type_ids'], b['position_ids']]
# #             if 'auto_prompt_ids' in b:
# #                 inputs.append(b['auto_prompt_ids'])
# #             model = torch.jit.trace(model.to(device), inputs, strict=False)
# #             logger.info('trace finished.')
# #             break
# #     return model
#
#
# def do_eval():
#     task = Taskflow('information_extraction', task_path=args.task_path)
#
#     with jsonlines.open(file=args.test_path, mode='r') as jr:
#         dev_dataset = []
#         for d in jr:
#             if 'content' in d:
#                 d['text'] = d.pop('content')
#             # if args.normal:
#             #     if 'word' in d:
#             #         d['word'] = [dbc2sbc(w) for w in d['word']]
#             #     if 'text' in d:
#             #         d['text'] = dbc2sbc(d['text'])
#             #     d['prompt'] = dbc2sbc(d['prompt'])
#             dev_dataset.append(d)
#     entity_predict_inputs = []
#     uni_set = set()
#     entity_schema = set()
#     for exam in dev_dataset:
#         exam = copy(exam)
#         is_unique = False
#         if 'image' in exam:
#             if exam['image'] not in uni_set:
#                 uni_set.add(exam['image'])
#                 is_unique = True
#             else:
#                 continue
#         else:
#             if exam['text'] not in uni_set:
#                 uni_set.add(exam['text'])
#                 is_unique = True
#         prompt = dbc2sbc(exam['prompt'])
#         if '的' not in prompt:
#             entity_schema.add(prompt)
#         else:
#             print('Not Implement.')
#             pass
#         if is_unique:
#             # information_extraction内部强制调用dbc2sbc
#             exam = {k: v for k, v in exam.items() if
#                     k in ['text', 'word', 'bbox', 'image', 'image_width', 'image_height']}
#             entity_predict_inputs.append(exam)
#     entity_schema = sorted(list(entity_schema))
#     task.set_schema(entity_schema)
#     entity_res = task(entity_predict_inputs)
#     for input_, res in zip(entity_predict_inputs, entity_res):
#
#
#
#     # collate = model.collate  # trace之后得到的ScriptModule没有collate属性。# TODO：把collate放到Module外边。
#
#     def collate(examples, tokenizer, max_seq_len):
#         tokenizer_outputs = model.collate(examples, tokenizer=tokenizer, max_seq_len=max_seq_len, pop_offset_mapping=False)
#         tokenizer_outputs['prompt'] = [exam['prompt'] for exam in examples]
#         tokenizer_outputs['span2label'] = [exam['span2label'] for exam in examples]
#         assert len(tokenizer_outputs['prompt']) == len(tokenizer_outputs['input_ids'])
#         return tokenizer_outputs
#     # if args.trace:
#     #     model = get_infer_model(model, tokenizer, dev_dataset[:1], device=args.device)
#     class_dict = {}
#     if args.debug:
#         for data in dev_dataset:
#             class_name = unify_prompt_name(data['prompt']
#                                            if '的' not in data['prompt'] else '的' + data['prompt'].rsplit('的')[-1])
#             # Only positive examples are evaluated in debug mode
#             if len(data['result_list']) != 0:
#                 class_dict.setdefault(class_name, []).append(data)
#     else:
#         class_dict["all_classes"] = dev_dataset
#     logger.info('start eval.')
#     from torch.utils.data.dataset import Dataset
#     from typing import List, Dict
#
#     class UIEDataset(Dataset):
#
#         def __init__(self, data: List[Dict]):
#             super().__init__()
#             self.data = data
#
#         def __getitem__(self, index):
#             return self.data[index]
#
#         def __len__(self):
#             return len(self.data)
#     for key in sorted(list(class_dict.keys()))[:]:
#         dev_dataset = UIEDataset(class_dict[key])
#         dev_loader = DataLoader(dev_dataset,
#                                 collate_fn=partial(collate, tokenizer=tokenizer, max_seq_len=args.max_seq_len),
#                                 batch_size=args.batch_size)
#
#         metric = VerboseSpanEvaluator()
#         precision, recall, f1, macro_p, macro_r, macro_f1, macro_f1_2, micro_p, micro_r, micro_f1, matrix, labels = evaluate(model, metric, dev_loader, args.device)
#         logger.info("-----------------------------")
#         logger.info("Class Name: %s" % key)
#         print('labels:', [-1] + list(sorted(metric.labels)))
#         # logger.info("Evaluation P: %.3f | R: %.3f | F1: %.3f. " %
#         #             (precision * 100, recall * 100, f1 * 100))
#         print('precision:', precision, 'recall:', recall, 'f1:', f1, 'macro_p:', macro_p, 'macro_r:', macro_r,
#               'macro_f1:', macro_f1, 'macro_f1_2:', macro_f1_2, 'micro_p:', micro_p, 'micro_r:', micro_r, 'micro_f1:',
#               micro_f1, 'rate:', matrix, labels, sep='\n')
#     logger.info('eval finished.')
#
#
# if __name__ == "__main__":
#     # yapf: disable
#     parser = argparse.ArgumentParser()
#
#     parser.add_argument("--task_path", type=str, default=None, help="The path of saved model that you want to load.")
#     parser.add_argument("--test_path", type=str, default=None, help="The path of test set.")
#     parser.add_argument("--test_image_base_path", type=str, default='', help="表示存放dev图片的基目录，评估kb-ie-x模型时有效，此时数据中有image字段，本字段和image字段拼接代表完整的图片路径。")
#     parser.add_argument("--device", default='cuda', type=str)
#     parser.add_argument("--batch_size", type=int, default=16, help="Batch size per GPU/CPU for training.")
#     parser.add_argument("--max_seq_len", type=int, default=512, help="The maximum total input sequence length after tokenization.")
#     # parser.add_argument("--normal", action='store_true', help="调用dbc2sbc方法，将半角转换为全角字符。")
#     # parser.add_argument("--debug", action='store_true', help="Precision, recall and F1 score are calculated for each class separately if this option is enabled.")
#
#     args = parser.parse_args()
#     # yapf: enable
#
#     do_eval()
