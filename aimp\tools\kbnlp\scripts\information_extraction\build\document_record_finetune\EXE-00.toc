('G:\\kubao-aimp\\aimp\\tools\\kbnlp\\scripts\\information_extraction\\build\\document_record_finetune\\document_record_finetune.exe',
 True,
 False,
 True,
 'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PyInstaller\\bootloader\\images\\icon-console.ico',
 None,
 False,
 False,
 '<?xml version="1.0" encoding="UTF-8" standalone="yes"?><assembly xmlns="urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0"><assemblyIdentity type="win32" name="document_record_finetune" processorArchitecture="amd64" version="*******"/><trustInfo xmlns="urn:schemas-microsoft-com:asm.v3"><security><requestedPrivileges><requestedExecutionLevel level="asInvoker" uiAccess="false"/></requestedPrivileges></security></trustInfo><dependency><dependentAssembly><assemblyIdentity type="win32" name="Microsoft.Windows.Common-Controls" language="*" processorArchitecture="*" version="*******" publicKeyToken="6595b64144ccf1df"/></dependentAssembly></dependency><compatibility xmlns="urn:schemas-microsoft-com:compatibility.v1"><application><supportedOS Id="{e2011457-1546-43c5-a5fe-008deee3d3f0}"/><supportedOS Id="{35138b9a-5d96-4fbd-8e2d-a2440225f93a}"/><supportedOS Id="{4a2f28e3-53b9-4441-ba9c-d69d4a4a6e38}"/><supportedOS Id="{1f676c76-80e1-4239-95bb-83d0f6d0da78}"/><supportedOS Id="{8e0f7a12-bfb3-4fe8-b9a5-48fd50a15a9a}"/></application></compatibility><application xmlns="urn:schemas-microsoft-com:asm.v3"><windowsSettings><longPathAware xmlns="http://schemas.microsoft.com/SMI/2016/WindowsSettings">true</longPathAware></windowsSettings></application></assembly>',
 True,
 True,
 False,
 None,
 None,
 None,
 'G:\\kubao-aimp\\aimp\\tools\\kbnlp\\scripts\\information_extraction\\build\\document_record_finetune\\document_record_finetune.pkg',
 [('PYZ-00.pyz',
   'G:\\kubao-aimp\\aimp\\tools\\kbnlp\\scripts\\information_extraction\\build\\document_record_finetune\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'G:\\kubao-aimp\\aimp\\tools\\kbnlp\\scripts\\information_extraction\\build\\document_record_finetune\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'G:\\kubao-aimp\\aimp\\tools\\kbnlp\\scripts\\information_extraction\\build\\document_record_finetune\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'G:\\kubao-aimp\\aimp\\tools\\kbnlp\\scripts\\information_extraction\\build\\document_record_finetune\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'G:\\kubao-aimp\\aimp\\tools\\kbnlp\\scripts\\information_extraction\\build\\document_record_finetune\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'G:\\kubao-aimp\\aimp\\tools\\kbnlp\\scripts\\information_extraction\\build\\document_record_finetune\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_win32comgenpy',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_win32comgenpy.py',
   'PYSOURCE'),
  ('pyi_rth_pywintypes',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks\\rthooks\\pyi_rth_pywintypes.py',
   'PYSOURCE'),
  ('pyi_rth_pythoncom',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks\\rthooks\\pyi_rth_pythoncom.py',
   'PYSOURCE'),
  ('document_record_finetune',
   'G:\\kubao-aimp\\aimp\\tools\\kbnlp\\scripts\\information_extraction\\document_record_finetune.py',
   'PYSOURCE')],
 [],
 False,
 False,
 1670890819,
 [('run.exe',
   'C:\\ProgramData\\Anaconda3\\envs\\py3.9_torch\\lib\\site-packages\\PyInstaller\\bootloader\\Windows-64bit-intel\\run.exe',
   'EXECUTABLE')])
