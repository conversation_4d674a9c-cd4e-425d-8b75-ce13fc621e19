# encoding=utf-8
import os
import logging
import copy
import torch, torchvision
from catalog_compile.text_classify.util import is_contain_chinese
import shutil
from catalog_compile.text_classify.index import Index
from catalog_compile.common.nlp_res import get_w2v
from catalog_compile.extend_train.attach_reg import cross_validate
from catalog_compile.extend_train.data_process import DataProcess
from catalog_compile.ocr_process import single_image_layout_ocr
from PIL import Image
import cv2
import numpy as np


log = logging.getLogger(__name__)
_APP_USER_DIR = os.path.expanduser('~/.kubao/smarttext/user')
_APP_MODEL_DIR = os.path.expanduser('~/.kubao/smarttext/model')
_MODEL_FOLDER = '/nonexistent/model/'
# suffix_path = os.path.join(const.APP_USER_DIR, "suffix.dat")
# order_path = os.path.join(const.APP_USER_DIR, "order.txt")
# data_dir_path = os.path.join(get_base_dir(), 'init')
# model_path = os.path.join(MODEL_FOLDER, 'index')


class OrderItem:

    def __init__(self, label, prefix):
        self.label = label
        self.prefix = prefix


def get_all_category(path, conf_product_one=4):
    category = dict()
    ones = set()
    one_num = conf_product_one # int(get_config_str('product', 'one'))
    for dir_path, dir_names, file_names in os.walk(path):
        label2num = dict()
        for file in file_names:
            try:
                label = file.split('-')[1]
                num = int(file.split('-')[2][:file.split('-')[2].index('.')])
                label_count = 1
                if label in label2num:
                    label_count = label2num[label] + 1
                label2num[label] = label_count
                if num >= 2:
                    continue
                count = 1
                if label in category:
                    count = category[label] + 1
                category[label] = count
            except:
                log.exception('get all category error')
        for key in label2num:
            if label2num[key] > one_num:
                ones.add(key)
    return sorted(category.items(), key=lambda d: d[1], reverse=True), ones


def create_suffix(category, suffix_path):
    f = None
    try:
        if os.path.exists(suffix_path):
            os.rename(suffix_path, suffix_path.replace('.dat', '') + '1.dat')
        f = open(suffix_path, 'a+', encoding='utf-8')
        f.writelines('0=-内页\n')
        count = 1
        for item in category:
            f.writelines(str(count) + '=-' + item[0] + '\n')
            count += 1
    except:
        log.exception('create suffix error')
        if os.path.exists(suffix_path):
            os.remove(suffix_path)
        if os.path.exists(suffix_path.replace('.dat', '') + '1.dat'):
            os.rename(suffix_path.replace('.dat', '') + '1.dat', suffix_path)
    finally:
        if f is not None:
            f.close()
        if os.path.exists(suffix_path.replace('.dat', '') + '1.dat'):
            os.remove(suffix_path.replace('.dat', '') + '1.dat')


def create_order(category, path, order_path):
    datas = []
    top_100_label = []
    length = len(category)
    if length > 100:
        length = 100
    f = None
    try:
        if os.path.exists(order_path):
            os.rename(order_path, order_path.replace('.txt', '') + '1.txt')
        for i in range(length):
            top_100_label.append(category[i][0])
        for dir_path, dir_names, file_names in os.walk(path):
            data = []
            file_names.sort()
            for file in file_names:
                try:
                    label = file.split('-')[1]
                    if label in top_100_label and label not in data:
                        data.append(label)
                except:
                    log.exception('create order data error')
            if len(data) > 0:
                datas.append(data)
        order = dict()
        for data in datas:
            for i in range(len(data)-1, -1, -1):
                prefix = set()
                label = data[i]
                if label in order:
                    prefix = order[label].prefix
                for j in range(i):
                    prefix.add(data[j])
                order[label] = OrderItem(label, prefix)
        while 1 == 1:
            flag = True
            for key in order:
                order_item = order[key]
                prefix = order_item.prefix
                prefix_update = copy.copy(prefix)
                for label in prefix:
                    prefix_update = prefix_update.union(order[label].prefix)
                if key in prefix_update:
                    prefix_update.remove(key)
                order[key] = OrderItem(key, prefix_update)
                if prefix != prefix_update:
                    flag = False
            if flag:
                break
        f = open(order_path, 'a+', encoding='utf-8')
        id = 1
        for i in range(1, 101):
            flag = False
            for key in order:
                if len(order[key].prefix) == i-1:
                    f.writelines(str(id) + '=-' + key + '\n')
                    flag = True
            if flag:
                id += 1
    except:
        log.exception('create order error')
        if f is not None:
            f.close()
        if os.path.exists(order_path):
            os.remove(order_path)
        if os.path.exists(order_path.replace('.txt', '') + '1.txt'):
            os.rename(order_path.replace('.txt', '') + '1.txt', order_path)
    finally:
        if f is not None:
            f.close()
        if os.path.exists(order_path.replace('.txt', '') + '1.txt'):
            os.remove(order_path.replace('.txt', '') + '1.txt')


def get_files_num(path):
    count = 0
    for dir_path, dir_names, file_names in os.walk(path):
        count = len(dir_names)
        break
    return count


def ocr_prase(CatalogInitializer, ones, suffix_path, data_dir_path):
    # task_id = CatalogInitializer.task_id
    path = CatalogInitializer.input_path
    count = 0
    try:
        layout = CatalogInitializer.tool_layout
        ocr = CatalogInitializer.tool_ocr
        label2id = dict()
        with open(suffix_path, 'r', encoding='utf-8') as f:
            for line in f:
                if '\n' == line:
                    continue
                id, label = line.replace('\n', '').split('=-')
                label2id[label] = id
        total = get_files_num(path)
        # data = {
        #     'param': {
        #         'path': path,
        #         'train_num': count,
        #         'todo_num': total - count
        #     }
        # }
        # _API_CLIENT.post_init_train(task_id, data)
        statobj = CatalogInitializer.statobj
        statobj['total'] = total
        for dir_path, dir_names, file_names in os.walk(path):
            dir = None
            for file in file_names:
                if CatalogInitializer.stopped:
                    log.info('ocr prase stopped')
                    return
                dir = dir_path
                f = None
                f1 = None
                full_path = os.path.join(dir_path, file)
                is_del = True
                try:
                    label = file.split('-')[1]
                    num = int(file.split('-')[2][:file.split('-')[2].index('.')])
                    id = label2id[label]
                    # CatalogInitializer.wait()
                    with Image.open(full_path, 'r') as f:
                        f = cv2.cvtColor(np.asarray(f.convert('RGB')), cv2.COLOR_RGB2BGR)
                    device_tensor = torch.as_tensor(f, device=ocr.device)[
                            None,:].permute([0,3,1,2]).float()
                    f = None
                    is_success, text, biaoti_flag = single_image_layout_ocr(device_tensor, ocr, layout, 10)
                    # CatalogInitializer.wait()
                    text = text.replace('\n', '').replace('\t', ' ')
                    if len(text) <= 1 or not is_contain_chinese(text):
                        continue
                    if biaoti_flag: # 标题
                        attach_dest = os.path.join(data_dir_path, 'attach', os.path.basename(dir_path))
                        if not os.path.exists(attach_dest):
                            os.makedirs(attach_dest)
                        f1 = open(os.path.join(attach_dest, file.replace('.jpg', '') + '_biaot.txt'), 'a+',
                                  encoding='utf-8')
                        f1.writelines(text + '\n')
                        if label in ones and num >= 2:
                            continue
                        biaoti_dir = os.path.join(data_dir_path, 'biaoti_tmp')
                        if not os.path.exists(biaoti_dir):
                            os.makedirs(biaoti_dir)
                        f = open(os.path.join(biaoti_dir, str(id) + '.txt'), 'a+', encoding='utf-8')
                        f.writelines(text + '\t' + str(id) + '\n')
                    else: # 全文
                        attach_dest = os.path.join(data_dir_path, 'attach', os.path.basename(dir_path))
                        if not os.path.exists(attach_dest):
                            os.makedirs(attach_dest)
                        f1 = open(os.path.join(attach_dest, file.replace('.jpg', '.txt')), 'a+', encoding='utf-8')
                        f1.writelines(text + '\n')
                        if label in ones and num >= 2:
                            continue
                        quanwen_dir = os.path.join(data_dir_path, 'quanwen_tmp')
                        if not os.path.exists(quanwen_dir):
                            os.makedirs(quanwen_dir)
                        f = open(os.path.join(quanwen_dir, str(id) + '.txt'), 'a+', encoding='utf-8')
                        f.writelines(text + '\t' + str(id) + '\n')
                except:
                    is_del = False
                    log.exception('ocr error ' + dir_path + ', '+ file)
                finally:
                    if f is not None:
                        f.close()
                    if f1 is not None:
                        f1.close()
                    if is_del:
                        os.remove(full_path)
            if dir is not None:
                count += 1
                if count > total:
                    count = total
                if len(os.listdir(dir)) == 0:
                    shutil.rmtree(dir)
                # data = {
                #     'param': {
                #         'path': path,
                #         'train_num': count,
                #         'todo_num': total - count
                #     }
                # }
                # _API_CLIENT.post_init_train(task_id, data)
                statobj['processed'] += 1
    except:
        log.exception('ocr parse error')
    finally:
        if os.path.exists(path) and len(os.listdir(path)) == 0:
            shutil.rmtree(path)


def duplicate(data_dir_path):
    is_success = True
    biaoti_data = dict()
    for dir_path, dir_names, file_names in os.walk(os.path.join(data_dir_path, 'biaoti_tmp')):
        for file in file_names:
            full_path = os.path.join(dir_path, file)
            text = set()
            with open(full_path, 'r', encoding='utf-8') as f:
                for line in f:
                    if '\n' == line:
                        continue
                    text.add(line.replace('\n', ''))
            biaoti_data[file] = text
    quanwen_data = dict()
    for dir_path, dir_names, file_names in os.walk(os.path.join(data_dir_path, 'quanwen_tmp')):
        for file in file_names:
            full_path = os.path.join(dir_path, file)
            text = set()
            with open(full_path, 'r', encoding='utf-8') as f:
                for line in f:
                    if '\n' == line:
                        continue
                    text.add(line.replace('\n', ''))
            quanwen_data[file] = text
    for key in biaoti_data:
        f = None
        try:
            biaoti_dir = os.path.join(data_dir_path, 'biaoti')
            if not os.path.exists(biaoti_dir):
                os.makedirs(biaoti_dir)
            f = open(os.path.join(biaoti_dir, key), 'a+', encoding='utf-8')
            for text in biaoti_data[key]:
                f.writelines(text + '\n')
        except:
            log.exception('write biaoti data error')
            is_success = False
        finally:
            if f is not None:
                f.close()
    for key in quanwen_data:
        f = None
        try:
            quanwen_dir = os.path.join(data_dir_path, 'quanwen')
            if not os.path.exists(quanwen_dir):
                os.makedirs(quanwen_dir)
            f = open(os.path.join(quanwen_dir, key), 'a+', encoding='utf-8')
            for text in quanwen_data[key]:
                f.writelines(text + '\n')
        except:
            log.exception('write quanwen data error')
            is_success = False
        finally:
            if f is not None:
                f.close()
    return is_success


def generate_train_data(CatalogInitializer, ones, suffix_path, data_dir_path):
    ocr_prase(CatalogInitializer, ones, suffix_path, data_dir_path)
    is_success = True
    if os.path.exists(os.path.join(data_dir_path, 'biaoti_tmp')) or os.path.exists(os.path.join(data_dir_path, 'quanwen_tmp')):
        is_success = duplicate(data_dir_path)
    if is_success:
        if os.path.exists(os.path.join(data_dir_path, 'biaoti_tmp')):
            shutil.rmtree(os.path.join(data_dir_path, 'biaoti_tmp'))
        if os.path.exists(os.path.join(data_dir_path, 'quanwen_tmp')):
            shutil.rmtree(os.path.join(data_dir_path, 'quanwen_tmp'))
    else:
        if os.path.exists(os.path.join(data_dir_path, 'biaoti')):
            shutil.rmtree(os.path.join(data_dir_path, 'biaoti'))
        if os.path.exists(os.path.join(data_dir_path, 'quanwen')):
            shutil.rmtree(os.path.join(data_dir_path, 'quanwen'))


def extract_rule(CatalogInitializer):
    try:
        assert CatalogInitializer.model_name
        model_name = CatalogInitializer.model_name
        #    model_name = ''
        if hasattr(CatalogInitializer, 'app_user_dir'):
            app_user_dir = CatalogInitializer.app_user_dir
        else:
            CatalogInitializer.app_user_dir = app_user_dir = _APP_USER_DIR
        app_user_dir = os.path.join(app_user_dir, model_name)
        if not os.path.exists(app_user_dir):
            os.makedirs(app_user_dir)
        if hasattr(CatalogInitializer, 'model_dir'):
            model_dir = CatalogInitializer.model_dir
        else:
            model_dir = _APP_MODEL_DIR
            CatalogInitializer.model_dir = model_dir
        assert CatalogInitializer.var_data_dir
        var_data_dir = CatalogInitializer.var_data_dir
        #    CatalogInitializer.data_base_dir = data_base_dir = get_base_dir()
        suffix_path = os.path.join(app_user_dir, "suffix.dat")
        order_path = os.path.join(app_user_dir, "order.txt")
        data_dir_path = os.path.join(var_data_dir, 'init', model_name)
        model_path = os.path.join(model_dir, 'index', model_name)
        attach_path = os.path.join(app_user_dir, 'attach.txt')

        if hasattr(CatalogInitializer, 'statobj'):
            statobj = CatalogInitializer.statobj
        else:
            statobj = CatalogInitializer.statobj = dict(stage=0, stages=0, processed=0, total=0)
        statobj['stages'] += 7
        category, ones = get_all_category(CatalogInitializer.input_path)
        statobj['stage'] += 1
        if not os.path.exists(suffix_path):
            create_suffix(category, suffix_path)
        statobj['stage'] += 1
        if not os.path.exists(order_path):
            create_order(category, CatalogInitializer.input_path, order_path)
        statobj['stage'] += 1
        if os.path.exists(CatalogInitializer.input_path):
            generate_train_data(CatalogInitializer, ones, suffix_path, data_dir_path)
        statobj['stage'] += 1
        if not os.path.exists(model_path):
            os.makedirs(model_path)
        data = DataProcess(app_user_dir)
        data.process(os.path.join(data_dir_path, 'attach'))
        statobj['stage'] += 1
        w2v = get_w2v(CatalogInitializer.w2v_path)
        cross_validate(data.labe2datas, data.id2label, w2v, data.more, data.less, attach_path)
        statobj['stage'] += 1
        index = Index(w2v, os.path.join(model_path, 'index.nn'), 2)
        index.build_annoy_index(data_dir_path, CatalogInitializer)
        statobj['stage'] += 1
    except:
        log.exception('rule engine error')
    finally:
        pass
        #_API_CLIENT.post_init_train(CatalogInitializer.task_id, {
        #    'status': 3
        #})
