# Copyright (c) 2022 PaddlePaddle Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.


from qa.kbnlp.task import Task
from .information_extraction import UIETask, AutoPromptUIETask
from typing import Optional
import torch

TASKS = {
    'information_extraction': {
        "pretrained": {
            "uie-base": {
                "task_class": UIETask,
            },
            "uie-tiny": {
                "task_class": UIETask,
            },
            "uie-nano": {
                "task_class": UIETask,
            },
            "uie-medical-base": {
                "task_class": UIETask,
            },
            "auto-uie-nano": {
                "task_class": AutoPrompt<PERSON><PERSON><PERSON>,
            },
            "auto-uie-tiny": {
                "task_class": AutoPrompt<PERSON>ETask,
            },
            "auto-uie-base": {
                "task_class": AutoPromptUI<PERSON>ask,
            },
        },
        "default": {
            "model": "uie-tiny"
        }
    }
}


class Taskflow:
    """
    The Taskflow is the end2end inferface that could convert the raw text to model result, and decode the model result to task result. The main functions as follows:
        1) Convert the raw text to task result.
        2) Convert the model to the inference model.
        3) Offer the usage and help message.
    Args:
        task (str): The task name for the Taskflow, and get the task class from the name.
        model (str, optional): The model name in the task, if set None, will use the default model.
        device (str, optional): The device id for the gpu or cpu, the defalut value is 'cpu'.
        task_path (str, optional): 指定加载模型的存放路径。
        kwargs (dict, optional): Additional keyword arguments passed along to the specific task.

    """

    def __init__(self, task, model=None, device: Optional[str] = 'cpu', **kwargs):
        try:
            assert task in TASKS, "The task name:{} is not in Taskflow list, please check your task name.".format(
                task)
            self.task = task
            tag = "pretrained"
            ind_tag = "model"
            self.model = model

            if self.model is not None:
                assert self.model in set(TASKS[task][tag].keys(
                )), "The {} name:{} is not in task:[{}]".format(tag, model, task)
            else:
                self.model = TASKS[task]['default'][ind_tag]
            # Set the device for the task
            if device == 'cpu' or device is None:
                device = 'cpu'
            elif isinstance(device, str) and device.startswith('cuda'):
                device = device
            else:
                raise ValueError('device 值设置错误。')
            # Update the task config to kwargs
            config_kwargs = dict(TASKS[self.task][tag][self.model])
            task_class = config_kwargs.pop('task_class')
            kwargs.update(config_kwargs)
            self.task_instance: Task = task_class(
                model=self.model,
                device=device,
                **kwargs)
            # task_list = TASKS.keys()
            # Taskflow.task_list = task_list
        except Exception as e:
            import traceback
            print(traceback.format_exc())

    def __call__(self, inputs, **kwargs):
        """
        The main work function in the taskflow.
        inputs:task的输入。
        """
        results = self.task_instance(inputs, **kwargs)
        return results

    # def help(self):
    #     """
    #     Return the task usage message.
    #     """
    #     return self.task_instance.help()

    def to(self, device):
        self.task_instance.to(device)

    @property
    def device(self):
        return self.task_instance.device

    def task_path(self):
        """
        Return the path of current task
        """
        return self.task_instance._task_path

    @staticmethod
    def tasks():
        """
        Return the available task list.
        """
        task_list = list(TASKS.keys())
        return task_list

    # def from_segments(self, *inputs):
    #     results = self.task_instance.from_segments(inputs)
    #     return results

    # def interactive_mode(self, max_turn):
    #     with self.task_instance.interactive_mode(max_turn):
    #         while True:
    #             human = input("[Human]:").strip()
    #             if human.lower() == "exit":
    #                 exit()
    #             robot = self.task_instance(human)[0]
    #             print("[Bot]:%s" % robot)

    def set_schema(self, schema):
        assert self.task_instance.model in [
            "uie-base", "uie-tiny", "uie-medical-base", "uie-nano", "auto-uie-nano"
        ], 'This method can only used for the task with uie model.'
        self.task_instance.set_schema(schema)


def get_env_device():
    """
    Return the device name of running enviroment.
    """
    if torch.cuda.is_available():
        return 'cuda'
    return 'cpu'
