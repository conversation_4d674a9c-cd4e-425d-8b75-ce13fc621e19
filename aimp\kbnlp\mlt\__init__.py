# import sys
# import os
# import re
# BASE_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
# sys.path.append(BASE_DIR)
# from kbnlp.mlt.multi_task_learning import MultiTaskLearning as Mlt
# from typing import List, Optional, Union
# import torch
# DEVICE = 'cuda' if torch.cuda.is_available() else 'cpu'
# model_path = BASE_DIR + '/kbnlp/mlt/data/pretrained/electra_small_tok_pos_ner_dep_rte'
#
#
# def cut_sentence(para):
#     para = re.sub('([。！？\?])([^”’])', r"\1\n\2", para)  # 单字符断句符
#     para = re.sub('(\.{6})([^”’])', r"\1\n\2", para)  # 英文省略号
#     para = re.sub('(\…{2})([^”’])', r"\1\n\2", para)  # 中文省略号
#     para = re.sub('([。！？\?][”’])([^，。！？\?])', r'\1\n\2', para)
#     # 如果双引号前有终止符，那么双引号才是句子的终点，把分句符\n放到双引号后，注意前面的几句都小心保留了双引号
#     para = para.rstrip()  # 段尾如果有多余的\n就去掉它
#     # 很多规则中会考虑分号;，但是这里我把它忽略不计，破折号、英文双引号等同样忽略，需要的再做些简单调整即可。
#     return [sen for sen in para.split("\n") if sen.strip() !='']
#
# # def cut_sentence(document: str, flag: str = "all", limit: int = 510) -> List[str]:
# #     """
# #
# #     Args:
# #         document:
# #         flag: Type:str, "all" 中英文标点分句，"zh" 中文标点分句，"en" 英文标点分句
# #         limit: 默认单句最大长度为510个字符
# #
# #     Returns: Type:list
# #
# #     """
# #     sent_list = []
# #     try:
# #         if flag == "zh":
# #             document = re.sub('(?P<quotation_mark>([。？！…](?![”’"\'])))', r'\g<quotation_mark>\n', document)  # 单字符断句符
# #             document = re.sub('(?P<quotation_mark>([。？！]|…{1,2})[”’"\'])', r'\g<quotation_mark>\n', document)  # 特殊引号
# #         elif flag == "en":
# #             document = re.sub('(?P<quotation_mark>([.?!](?![”’"\'])))', r'\g<quotation_mark>\n', document)  # 英文单字符断句符
# #             document = re.sub('(?P<quotation_mark>([?!.]["\']))', r'\g<quotation_mark>\n', document)  # 特殊引号
# #         else:
# #             document = re.sub('(?P<quotation_mark>([。？！….?!](?![”’"\'])))', r'\g<quotation_mark>\n', document)  # 单字符断句符
# #             document = re.sub('(?P<quotation_mark>(([。？！.!?]|…{1,2})[”’"\']))', r'\g<quotation_mark>\n',
# #                               document)  # 特殊引号
# #
# #         sent_list_ori = document.splitlines()
# #         for sent in sent_list_ori:
# #             sent = sent.strip()
# #             if not sent:
# #                 continue
# #             else:
# #                 while len(sent) > limit:
# #                     tokenizer_paddlenlp_ernie_layoutx_base_uncased.jsonl = sent[0:limit]
# #                     sent_list.append(tokenizer_paddlenlp_ernie_layoutx_base_uncased.jsonl)
# #                     sent = sent[limit:]
# #                 sent_list.append(sent)
# #     except:
# #         sent_list.clear()
# #         sent_list.append(document)
# #     return sent_list
#
#
# class MltParser:
#     def __init__(self, save_dir=model_path, device=DEVICE):
#         self.mlt = Mlt()
#         self.mlt.load(save_dir=save_dir, device=device)
#         self.device = device
#
#     def set_device(self, device):
#         self.mlt.pretrained.to(device)
#         self.device = device
#
#     def task_name(self):
#         return list(self.mlt.tasks.keys())
#
#     def __call__(self,
#                  data: Union[List[str], List[List[str]]],
#                  batch_size=40,
#                  tasks: Optional[Union[str, List[str]]] = None,  # 任务名称。
#                  skip_tasks: Optional[Union[str, List[str]]] = None, **kwargs):
#         """
#             模型的可以执行的任务可以通过模型名称来判断,或者task_name方法返回任务名。
#         :param data: 不允许传入str,如果只解析一个句子，请用列表包装。如果传入List[str],列表中每个str代表一个句子，此时参数is_split_into_words无效；如果为空，则返回空的结果。
#         如果传入List[List[str]],每个List[str]代表一个句子，此时当is_split_into_words为True，则表示每个str是一个token,不用再对句子分词，
#         如果is_split_into_words为False，则表示每个str代表一个group，句子仍需要分词，在分词时不同的group的字符不会被放到同一个token中。
#         :param batch_size: 预测批量的大小，批量过大可能会报显存异常异常。
#         :param tasks: 指定要做的任务列表。默认为None，即做模型包含的所有任务。
#         :param skip_tasks: 指定跳过一些任务列表。注意不要跳过分词任务，除非提供的句子是已经提前好词，且指定is_split_into_words参数为True。
#         :param kwargs: 其它特殊功能参数，详细可查看MultiTaskLearning的call方法。
#         :return: 返回一个字典，key是任务名，value是对应任务的所有data的预测结果列表。
#         """
#         return self.mlt(data, batch_size=batch_size, tasks=tasks, skip_tasks=skip_tasks, device=self.device, **kwargs)
#
