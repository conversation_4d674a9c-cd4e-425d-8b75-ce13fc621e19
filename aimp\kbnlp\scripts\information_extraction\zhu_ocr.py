from typing import <PERSON>ple
import os
import torch
# torch.backends.cuda.matmul.allow_tf32 = True
# torch.backends.cudnn.allow_tf32 = False
# torch.backends.cudnn.deterministic = True
import torch.nn as nn
import torch.nn.functional as F
import kornia
import inspect
from PIL import Image, ImageFont
import cv2, numpy as np


# import pyclipper


# __all__ = ['OCR_DEVICE', 'OCRSystem']


# OCR_DEVICE = None
# _accelerator_string = None
# if 'KUBAOAI_ACCELERATOR' in os.environ:
#     _accelerator_string = os.environ['KUBAOAI_ACCELERATOR']
# if 'KUBAOAI_VISION_ACCELERATOR' in os.environ:
#     _accelerator_string = os.environ['KUBAOAI_VISION_ACCLERATOR']
# if _accelerator_string is None and torch.cuda.is_available():
#     _accelerator_string = 'cuda'
# else:
#     _accelerator_string = 'cpu'
# OCR_DEVICE = torch.device(_accelerator_string)


class OCRSystem(nn.Module):

    def __init__(self,
                 det_model_path: str = None,
                 det_max_side_len: float = 960, det_db_thresh: float = 0.3, det_db_box_thresh: float = 0.5,
                 det_db_unclip_ratio: float = 2.0, det_db_min_size: int = 3, det_per_img: int = 1000,
                 rec_model_path: str = None, rec_char_dict_path: str = None,
                 rec_image_shape: str = '3,32,320', rec_char_type: str = 'ch', rec_batch_num: int = 30,  # 如果每行的字比较多可以使用rec_image_shape='3,32,640'
                 rec_max_text_length: int = 25, rec_use_space_char: bool = True,
                 cls_model_path: str = None, cls_vertical_model_path: str = None,
                 cls_image_shape: str = '3,48,192', cls_batch_num: int = 30, cls_thresh: float = 0.9,
                 device='cpu' if not torch.cuda.is_available() else 'cuda',
                 **kwargs):
        super().__init__()
        frame = inspect.currentframe()
        for k in inspect.getargs(frame.f_code).args:
            if k != 'self':
                kwargs[k] = frame.f_locals[k]
        self.hparams = kwargs
        for k in self.hparams.keys():
            setattr(self, k, self.hparams[k])
        self._MODELS = {}
        self._text_detector = self.get_or_load(self.det_model_path)
        self._text_classifier = self.get_or_load(self.cls_model_path)
        self._text_vertical = self.get_or_load(self.cls_vertical_model_path)
        self._text_recognizer = self.get_or_load(self.rec_model_path)
        self._chars, self._chardict = self.chars_and_char_dict(
            self.rec_char_dict_path, self.rec_use_space_char)

    def get_or_load(self, path):
        if path is not None:
            if path not in self._MODELS:
                self._MODELS[path] = torch.jit.load(path, map_location=self.device)
            return self._MODELS[path]

    @staticmethod
    def chars_and_char_dict(rec_char_dict_path, rec_use_space_char=True):
        chars, chardict = [], {}
        if rec_char_dict_path is not None:
            with open(rec_char_dict_path, 'r', encoding='utf-8') as f:
                chars.extend([l.strip('\r\n') for l in f])
            if rec_use_space_char:
                chars.append(' ')
            chars.append('\x00')
            chardict = {j: i for i, j in enumerate(chars)}
        return chars, chardict

    # @profile
    def text_detect(self, img: torch.Tensor, yielding=lambda: None):
        max_side_len = self.det_max_side_len
        h, w = img.shape[-2:]
        if h == 0 or w == 0:
            return torch.empty(0, 4, 2, dtype=torch.float32), torch.empty(0, 6, dtype=torch.float32)
        if max(h, w) > max_side_len:
            if h > w:
                h, w = max_side_len, round(max_side_len / h * w)
            else:
                h, w = round(max_side_len / w * h), max_side_len
            h, w = int(h), int(w)
            x = F.interpolate(img.to(torch.float32), [h, w], mode='bilinear', align_corners=False)
        else:
            x = img.to(torch.float32)
        hscale, wscale = img.shape[-2] / h, img.shape[-1] / w
        rscale = np.sqrt(hscale * wscale)
        x = x / 255
        dtype, device = x.dtype, x.device
        mean = torch.as_tensor([0.485, 0.456, 0.406], dtype=dtype, device=device)
        std = torch.as_tensor([0.229, 0.224, 0.225], dtype=dtype, device=device)
        x = (x - mean[:, None, None]) / std[:, None, None]
        pad_h = 0 if h % 32 == 0 else 32 - h % 32
        pad_w = 0 if w % 32 == 0 else 32 - w % 32
        ### pad at top-left gives nice results for up-right pages.
        # x = F.pad(x, [0, pad_w, 0, pad_h])
        # maps = self._text_detector(x)[:,:,:h,:w]
        x = F.pad(x, [pad_w, 0, pad_h, 0])
        maps = self._text_detector(x)[:, :, pad_h:, pad_w:]
        # pad = [pad_w-pad_w//2, pad_w//2, pad_h-pad_h//2, pad_h//2]
        # x = F.pad(x, pad)
        # maps = self._text_detector(x)[:,:,pad[2]:pad[2]+h,pad[0]:pad[0]+w]
        maps = (maps * 255).round().to(torch.uint8)[0, 0]
        yielding()
        maps = maps.cpu().numpy()
        # cv2.imwrite('{}.png'.format(_dbg_i), maps)
        mask = cv2.threshold(maps, self.det_db_thresh * 255, 255, cv2.THRESH_BINARY)[1]
        mask = cv2.dilate(mask, np.ones([2, 2]))  # np.array([[1,1],[1,1]]))
        outs = cv2.findContours(mask, cv2.RETR_LIST, cv2.CHAIN_APPROX_SIMPLE)
        if len(outs) == 3:
            contours = outs[1]
        elif len(outs) == 2:
            contours = outs[0]
        contours = contours[:self.det_per_img]
        boxes, rects = [], []
        sec = 0
        for i, contour in enumerate(contours):
            rect = cv2.minAreaRect(contour)
            if min(rect[1]) < self.det_db_min_size:
                continue
            points = cv2.boxPoints(rect)
            xywh = cv2.boundingRect(points)
            map_ = maps[xywh[1]:xywh[1] + xywh[3], xywh[0]:xywh[0] + xywh[2]]
            mask_ = np.zeros(map_.shape, dtype=np.uint8)
            # points_ = points.copy()
            # points_[:] -= xyxy[:2]
            # cv2.fillPoly(mask_, points_.reshape(1,-1,2).round().astype(np.int32), 1)
            cv2.fillPoly(mask_, points.reshape(1, -1, 2).astype(np.int32), 1, offset=[-xywh[0], -xywh[1]])
            score = cv2.mean(map_, mask_)[0] / 255
            if score < self.det_db_box_thresh:
                continue
            distance = (rect[1][0] * rect[1][1]) * self.det_db_unclip_ratio / ((rect[1][0] + rect[1][1]) * 2)
            # offset = pyclipper.PyclipperOffset()
            # offset.AddPath(points, pyclipper.JT_ROUND, pyclipper.ET_CLOSEDPOLYGON)
            # contour_ = np.array(offset.Execute(distance)).reshape(-1,1,2)
            # rect_ = cv2.minAreaRect(contour_)
            rect_ = (rect[0], (rect[1][0] + distance * 2, rect[1][1] + distance * 2), rect[2])
            if min(rect_[1]) < self.det_db_min_size + 2:
                continue
            if rect_[1][0] > rect_[1][1]:
                rect_ = (rect_[0], (rect_[1][1], rect_[1][0]), 90 + rect_[2])
            else:
                if rect_[1][1] < 1.5 * rect_[1][0]:
                    if rect_[2] < 45:
                        rect_ = (rect_[0], (rect_[1][1], rect_[1][0]), 90 + rect_[2])
                else:
                    if rect_[2] == 0:
                        rect_ = (rect_[0], rect_[1], 180)
            rect_ = ((rect_[0][0] * wscale, rect_[0][1] * hscale),
                     (rect_[1][0] * rscale, rect_[1][1] * rscale), rect_[2])
            points_ = cv2.boxPoints(rect_)
            boxes.append(points_)
            rects.append([rect_[0][0], rect_[0][1], rect_[1][0], rect_[1][1], rect_[2], score])
            # box中心x,box中心y,高，宽（90度时是水平摆放文字，180度时是垂直摆放的文字），角度，文本框检测的分数。
            # print(rect_, rect, distance, score)
            # print(points_.round().astype(np.int16).tolist())
        boxes = np.asarray(boxes, dtype=np.float32).reshape(-1, 4, 2)
        # boxes[:,:,0] *= wscale
        # boxes[:,:,1] *= hscale
        rects = np.asarray(rects, dtype=np.float32).reshape(-1, 6)
        u = self._argsort_boxes(boxes)
        return boxes[u], rects[u]

    @staticmethod
    def _argsort_boxes(boxes):
        e = np.rec.array(boxes.min(1), dtype=[('x', np.float32), ('y', np.float32)]).reshape(-1)
        u = np.argsort(e, 0, order=['y', 'x'])
        for i in range(len(u) - 1):
            j, k = u[i], u[i + 1]
            l, m = e[j], e[k]
            if abs(m[1] - l[1]) < 10 and m[0] < l[0]:
                u[i], u[i + 1] = k, j
        return u

    # @profile
    def _resample_to(self, img: torch.Tensor, boxes: np.ndarray, rects: np.ndarray, dsize: Tuple[int, int]):
        points_src = torch.as_tensor(boxes)
        points_des = torch.empty_like(points_src)
        rects = torch.as_tensor(rects)
        h, w = dsize
        des_ws = torch.minimum(rects[:, 3:4] / rects[:, 2:3] * h, torch.as_tensor(w))
        points_des[:, :2, 1] = 0.  # y of p0x1 = 0
        points_des[:, 2:, 1] = h  # y of p2p3 = h
        points_des[:, [0, 3], 0] = 0.  # x of p0p3 = 0
        points_des[:, [1, 2], 0] = des_ws
        m = kornia.geometry.get_perspective_transform(points_src, points_des)
        m_ = m.to(self.device)
        patch = kornia.geometry.warp_perspective(img.expand(m_.size()[0], -1, -1, -1), m_, [h, w],
                                                 padding_mode='border')  # TIME 0.01 B=75
        des_ws = des_ws.ceil().to(torch.int)
        return patch, des_ws

    def text_cls(self, img: torch.Tensor, boxes: np.ndarray, rects: np.ndarray):
        dsize = [int(i) for i in self.cls_image_shape.split(',')[1:]]
        patch, des_ws = self._resample_to(img, boxes, rects, dsize)
        # pcopy = patch
        patch = (patch / 255. - 0.5) / 0.5
        for i in range(len(des_ws)):
            des_w = des_ws[i]
            patch[i, :, :, des_w:] = 0
        res = self._text_classifier(patch)  # probs, cls
        # cls = res[1]
        # for i in range(len(des_ws)):
        #     des_w, ci = des_ws[i], cls[i]
        #     cv2.imwrite('P7_{:04d}_{:d}.jpg'.format(i, ci), pcopy[i,:,:,:des_w].cpu().permute(1,2,0).numpy())
        return res

    # @profile
    def text_rec_logit(self, img: torch.Tensor, boxes: np.ndarray, rects: np.ndarray):
        dsize = [int(i) for i in self.rec_image_shape.split(',')[1:]]
        patch, des_ws = self._resample_to(img, boxes, rects, dsize)
        # pcopy = patch
        patch = (patch / 255. - 0.5) / 0.5
        for i in range(len(des_ws)):
            des_w = des_ws[i]
            patch[i, :, :, des_w:] = 0
        res = self._text_recognizer(patch)  # TxBxW
        # for i in range(len(des_ws)):
        #     des_w = des_ws[i]
        #     cv2.imwrite('T7_{:04d}.jpg'.format(i), pcopy[i,:,:,:des_w].cpu().permute(1,2,0).numpy())
        return res

    # generate classic text recognization result
    # @profile
    def text_rec_classic(self, img: torch.Tensor, boxes: np.ndarray, rects: np.ndarray,
                         yielding=lambda: None):
        rec = self.text_rec_logit(img, boxes, rects)
        rec = torch.softmax(rec, -1)
        rv, rid = torch.max(rec, -1)
        yielding()
        rv = rv.cpu();
        rid = rid.cpu()
        blank = rec.shape[-1] - 1
        rec_res = []
        for i in range(rid.shape[1]):
            ri = rid[:, i]
            ib = ri != blank
            ri = torch.unique_consecutive(ri)  # , return_counts=True)
            ri = ri[ri != blank]
            rvi = rv[ib, i]
            score = rvi.mean().nan_to_num()
            rec_res.append([''.join([self._chars[j] for j in ri]), score.item()])
        return rec_res

    # def text_rec_1(self, img: torch.Tensor, boxes: np.ndarray, rects: np.ndarray):
    #     rec = self.text_rec_logit(img, boxes, rects)
    #     rv, rid = torch.max(rec, -1)
    #     rid = rid.cpu()
    #     rec_res = []
    #     for i in range(rid.shape[1]):
    #         ri = rid[:, i]
    #         rec_res.append([''.join([self._chars[j] for j in ri]), 0.0])
    #     return rec_res

    def text_rec_1(self, img: torch.Tensor, boxes: np.ndarray, rects: np.ndarray):
        rec = self.text_rec_logit(img, boxes, rects)
        rec = torch.softmax(rec, -1)
        rv, rid = torch.max(rec, -1)
        rv = rv.cpu().numpy()
        rid = rid.cpu().numpy()
        rec_res = []
        total_step = rid.shape[0]
        blank = rec.shape[-1] - 1
        scale = int(self.rec_image_shape.split(',')[2]) // 32  # 标准的宽高比
        max_predict_size = int(self.rec_image_shape.split(',')[2]) // 4  # 没4个像素做一次预测
        for i, box in enumerate(boxes):
            # if rects[i][3] / rects[i][2] < 10:
            #     per_step_width = rects[i][2] * 10 / 80
            # else:
            #     per_step_width = rects[i][3] / 80
            if rects[i][3] / rects[i][2] < scale:
                per_step_width = rects[i][2] * scale / max_predict_size
            else:
                per_step_width = rects[i][3] / max_predict_size
            x_steps = []
            _, counts = torch.unique_consecutive(torch.tensor(rid[:, i]), return_counts=True)
            for count in counts.tolist():
                x_steps.extend([per_step_width * (len(x_steps) + 1 + len(x_steps) + count - 1 + 1) / 2] * count)
            x_steps = np.array(x_steps)
            x_steps -= (per_step_width / 2)
            dot1 = np.array([val if val > 0 else 0. for val in ((box[0] + box[3]) / 2).tolist()])
            dot2 = np.array([val if val > 0 else 0. for val in ((box[1] + box[2]) / 2).tolist()])
            hypotenuse = np.linalg.norm(dot1 - dot2)
            if hypotenuse == 0:
                hypotenuse += 1
            opposite = np.abs(dot2[1] - dot1[1])
            bottom = np.abs(dot2[0] - dot1[0])
            sin = opposite / hypotenuse * np.sign(dot2[1] - dot1[1])
            cos = bottom / hypotenuse * np.sign(dot2[0] - dot1[0])
            off_xs = x_steps * cos
            off_ys = x_steps * sin
            coord = np.array([(int(dot1[0] + off_x), int(dot1[1] + off_y)) for off_x, off_y in zip(off_xs, off_ys)])
            selection = np.ones(total_step, dtype=bool)
            selection[1:] = rid[:, i][1:] != rid[:, i][:-1]
            selection &= rid[:, i] != blank  # 6624代表空字符串
            if np.any(selection):
                char_list, coord_list, conf_list = list(zip(*[
                    (self._chars[text_id], coord_x, conf)
                    for text_id, coord_x, conf in zip(rid[:, i][selection], coord[selection].tolist(), rv[:, i])
                ]))
            else:
                char_list = []
                coord_list = []
                conf_list = [0]
            rec_res.append(
                [box.astype(np.int32).tolist(), (''.join(char_list), float(np.mean(conf_list)), list(coord_list))])
        return rec_res

    def ocr(self, images):
        """
            每个张图片的结果形如：[[box1, (text1, score1, char_coords1)], [box2, (text2, score2, char_coords2)], ... ]。最终结果是所有图片结果组合成的list。
        [box_i, (text_i, score_i, char_coords_i)]代表该图片的第i个文本框的结果。
        其中box记录了四个顶点的坐标，text是该文本框识别出的文字，score是识别出该文字的分数，char_coords是每个字的质心的坐标。
        例如：
            box: [[886, 176], [1638, 176], [1638, 253], [886, 253]]
            text: '不动产登记申请审批表'
            score: 0.9997788667678833
            char_coords: [[919, 214], [996, 214], [1063, 214], [1139, 214], [1216, 214], [1292, 214], [1369, 214], [1446, 214], [1513, 214], [1589, 214]]
        :param image: image path
        :return:
        """
        if isinstance(images, str) or isinstance(images, Image.Image) or isinstance(images, np.ndarray):
            images = [images]
        res = []
        for image in images:
            with torch.inference_mode():
                # img = cv2.imread(image, cv2.IMREAD_COLOR)
                # img = cv2.imdecode(np.fromfile(image, dtype=np.uint8), cv2.IMREAD_COLOR)
                if isinstance(image, str):
                    img = np.asarray(Image.open(image).convert("RGB"))[:, :, [2, 1, 0]]  # 转化为BGR
                elif isinstance(image, Image.Image):
                    img = np.asarray(image.convert("RGB"))[:, :, [2, 1, 0]]  # 转化为BGR
                elif isinstance(image, np.ndarray):
                    img = image
                else:
                    raise ValueError()
                # rot = cv2.getRotationMatrix2D([img.shape[1]/2,img.shape[0]/2], p*1.0, 1.0)
                # img = cv2.warpAffine(img, rot, [img.shape[1],img.shape[0]])
                img_ = img

                img_ = torch.as_tensor(img_, device=self.device)[None, :].permute([0, 3, 1, 2]).to(torch.float32)

                boxes, rects = self.text_detect(img_)
                if len(boxes) < 1:
                    res.append([])
                    continue
                # cv2.polylines(img, boxes.round().astype(np.int32), 1, (255, 0, 0))
                # cv2.imwrite(wname, img, [cv2.IMWRITE_JPEG_QUALITY, 60])
                ## cls = ocr.text_cls(img_, boxes, rects)
                # rec_res = ocr.text_rec_classic(img_, boxes, rects)
                rec_res = self.text_rec_1(img_, boxes, rects)
                res.append(rec_res)
        return res


def degree(vec1, vec2):
    """
    计算二维空间中两向量之间的夹角，
    Args:
        vec1: 向量1
        vec2: 向量2

    Returns:[0, 180]

    """
    dot_product = np.dot(vec1, vec2)
    arc_cos = np.arccos(dot_product / (np.linalg.norm(vec1) * np.linalg.norm(vec2)))
    angle = np.degrees(arc_cos)
    return angle


def draw_char_position(images, to_path=None, use_customized_pp_ocr=False, margin_patient=18., other_word=None,
                       other_bbox=None,
                       show_word=True,
                       show_other_word=True,
                       size=20,
                       line_length=26,
                       line_width=3,
                       shrink_width=0,
                       shrink_height=0,
                       ):
    """

    Args:
        images: 图片地址列表
        to_path: 画出字符位置、文本框后的图片的存放路径
        use_customized_pp_ocr: 是否使用定制的可生成字符坐标的pp ocr
        margin_patient: 文本框y轴相差小于或等于该值的文本框，被判定为同一行文本。
        other_word: 其他文本框对应的文字。
        other_bbox: 其他任意任意指定的文本框。如果该字段为None,则other_word、show_other_word都失效。
        show_word: 该字段表示是否展示图片的ocr结果到每个文本框上。默认为True，该文本框所在行号和该行的文本框序号以及该文本框的ocr结果；
            否则只展示该文本框所在行号和该行的文本框序号。
        show_other_word: 该字段表示是否展示other_word到other_bbox之上。默认为True,将word展示到指定的other box之上，否则不展示。不影响other box的显示。
        size: 控制打印文字font字体size。
        line_length: 画字坐标的线长
        line_width: 画字坐标的线宽
        shrink_width: 缩小ocr检测出的文本框宽度。固定质心，在左右均缩小该值。
        shrink_height: 缩小ocr检测出的文本框高度。固定质心，在上下均缩小该值。
    Returns:

    """
    from kbnlp.scripts.information_extraction.utils import ocr_sort_draw
    if isinstance(images, str):
        images = [images]
    torch._C._jit_set_profiling_mode(False)  # 需要在最开始处执行
    if not use_customized_pp_ocr:
        ocr = OCRSystem(det_model_path='dtm/001.pt',
                        cls_model_path='dtm/003.pt', cls_vertical_model_path='dtm/006.pt',
                        rec_model_path='dtm/tp1t7.pt',
                        rec_image_shape='3,32,640',  # 文本框文字过于筹码需要设置为'3,32,640'
                        # rec_model_path=r'C:\Users\<USER>\Desktop\forwang\handwrite\002R.pt',  # 手写体+打印体。
                        # rec_model_path=r'C:\Users\<USER>\Desktop\forwang\chocr_server_rec.pt', # 百度官方打印体大模型。
                        rec_char_dict_path='dtm/ppocr_keys_v1.txt',
                        # rec_char_dict_path=r'C:\Users\<USER>\Desktop\forwang\handwrite\002R_keys_v1', # 手写体+打印体模型词表。
                        )  # , det_max_side_len=1152)
    else:
        from kbnlp.scripts.information_extraction.pp_ocr_customized import PaddleOCRCustomized
        import paddle
        ocr = PaddleOCRCustomized(show_log=False, lang='ch', use_gpu=True if paddle.get_device().startswith('gpu') else False)
    img_list = []
    font = ImageFont.truetype(r'D:\workspace\pycharm\layoutLMv3\simfang.ttf', size=size)
    for img_path in images:
        img = ocr_sort_draw(img_path, ocr_system=ocr, margin_patient=margin_patient,
                            other_word=other_word, other_bbox=other_bbox, show_word=show_word,
                            show_other_word=show_other_word, offset=size, font=font,
                            line_length=line_length, line_width=line_width,
                            shrink_width=shrink_width,
                            shrink_height=shrink_height,
                            )
        img_list.append(img)
    if to_path:
        for img, img_path in zip(img_list, images):
            name = os.path.basename(img_path)
            prefix, suffix = name.rsplit('.', maxsplit=1)
            img_path = os.path.join(to_path, prefix + '_char_position.' + suffix)
            if not os.path.exists(to_path):
                os.makedirs(to_path)
            img.save(img_path)
    return img_list


def FLD(img0, img_name='', length=200, draw_flag=False):
    if len(img0.shape) == 3:
        img = cv2.cvtColor(img0, cv2.COLOR_BGR2GRAY)
    else:
        img = np.asarray(img0)
    # 创建一个FLD对象，筛选长度200以上的直线
    fld = cv2.ximgproc.createFastLineDetector(length_threshold=length)
    # 执行检测结果
    try:
        dlines = fld.detect(img)
    except BaseException as e:
        dlines = np.array([[]])  # 多线程导致某些图像FLD报错
    if draw_flag:
        # draw = np.ones(shape=img.shape, dtype=np.uint8) * 255
        draw = img.copy()
        draw = cv2.add(draw, 200)
        draw = fld.drawSegments(draw, dlines).astype(np.uint8)
        print('draw.shape', draw.shape)
        cv2.imencode('.jpg', draw)[1].tofile('D://FLD_'+str(img_name))
    return dlines


if __name__ == '__main__':
    path = r"C:\Users\<USER>\Desktop\英文.png"
    draw_char_position([path],
                       to_path=r'C:\Users\<USER>\Desktop\zhu_ocr', use_customized_pp_ocr=False, shrink_height=2, line_length=5, size=12)
    # draw_char_position([path],
    #                    to_path=r'C:\Users\<USER>\Desktop\pp_ocr', use_customized_pp_ocr=True, shrink_height=2)
    # path = r"D:\workspace\pycharm\nlp_project\kbnlp\raw_data\tax\images"
    # draw_char_position([os.path.join(path, name) for name in os.listdir(path)],
    #                    to_path=r'C:\Users\<USER>\Desktop\zhu_ocr')
    # draw_char_position([os.path.join(path, name) for name in os.listdir(path)], use_customized_pp_ocr=True,
    #                    to_path=r'C:\Users\<USER>\Desktop\pp_ocr')
    # draw_char_position(r'C:\Users\<USER>\Desktop\202212\数豹2022.12月缴费通知+发票_4M.pdf_page_1.jpg',
    #                    to_path=r'C:\Users\<USER>\Desktop\pp', use_customized_pp_ocr=True, margin_patient=18, size=12,
    #                    line_length=10, line_width=1)

"""
if __name__ == '__main__':
    cv2.setNumThreads(0)
    torch.set_num_threads(1)
    torch._C._jit_set_profiling_mode(False)
    ocr = OCRSystem(det_model_path='dtm/001.pt',
        cls_model_path='dtm/003.pt', cls_vertical_model_path='dtm/006.pt',
        rec_model_path='dtm/tp1t7.pt',
        rec_char_dict_path='dtm/ppocr_keys_v1.txt') #, det_max_side_len=1152)
    # fname = '0001.jpg'
    fname = 'b51.jpg'
    # fname = 'M002_1.jpg'
    # fname = 'ID1.jpg'
    # imc = 7
    imc = 8
    # for i in range(1, imc+1):
    #     global _dbg_i
    #     _dbg_i = f'0815np/{i}'
    #     fname = f'tp1t7/{i}.png'
    #     fname = f'0815np/{i}.jpg'
    #     print(fname)

    s = 0
    with torch.inference_mode(): #, torch.autocast('cuda'):
      for p in [i*10 for i in range(1)]:
        wname = ('r{:03d}_'.format(p)+fname)
        # wname = ('{}.msk.jpg'.format(fname))
        print(wname)
        img = cv2.imread(fname, cv2.IMREAD_COLOR)
        # rot = cv2.getRotationMatrix2D([img.shape[1]/2,img.shape[0]/2], p*1.0, 1.0)
        # img = cv2.warpAffine(img, rot, [img.shape[1],img.shape[0]])
        img_ = img
        t = time()
        img_ = torch.as_tensor(img_, device=OCR_DEVICE)[None,:].permute([0,3,1,2]).to(torch.float32)
        boxes, rects = ocr.text_detect(img_)
        if len(boxes) < 1:
            continue
        cv2.polylines(img, boxes.round().astype(np.int32), 1, (255,0,0))
        cv2.imwrite(wname, img, [cv2.IMWRITE_JPEG_QUALITY, 60])
        ## cls = ocr.text_cls(img_, boxes, rects)
        # rec_res = ocr.text_rec_classic(img_, boxes, rects)
        rec_res = ocr.text_rec_1(img_, boxes, rects)
        print(rec_res)
        i = 1
        print(rec_res[i], boxes[i], rects[i], sep='\n')
        if rects[i][3] / rects[i][2] < 10:
            each = rects[i][2] * 10 / 80
        else:
            each = rects[i][3] / 80
        print('each', each)
        txt = rec_res[i][0]
        for j in range(80):
            if txt[j] != '\0':
                print(txt[j], j*each)
        print('length', rects[i][3])
        exit(0)
        s += time() - t
    from sys import stderr
    print(s, file=stderr)
"""
