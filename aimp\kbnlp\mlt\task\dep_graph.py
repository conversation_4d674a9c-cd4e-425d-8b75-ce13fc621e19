import math
from typing import Dict
from functools import partial
import torch
from torch import nn
import torch.nn.functional as F
from torch.utils.data.dataloader import DataLoader, Dataset
from torch.nn.utils.rnn import pad_sequence

from kbnlp.mlt.util import merge_locals_kwargs, merge_dict
from kbnlp.mlt.task import Task
from kbnlp.mlt.data_transform import VocabDict
from kbnlp.mlt.layers.decoder.linear_decoder import LinearCRFDecoder
from kbnlp.mlt.layers.mlp import MLP
from kbnlp.mlt.layers.biaffine import Biaffine
from kbnlp.mlt.layers.decoder.grte_decoder import GRTEDecoder
from kbnlp.mlt.common.vocab import Vocab
from kbnlp.mlt.util.io import CoNLLReader
from kbnlp.mlt.data_transform import TransformList
# from kbnlp.mlt.task.standard import Configure, StaticOracle
from kbnlp.mlt.dataset import BaseDataset, SortingSamplerBuilder
from kbnlp.mlt.common.metric import DependencyMetric
from kbnlp.mlt.util.tensor_util import lengths_to_mask
from kbnlp.mlt.util import ids_to_tags


def read_CoNLL_data(path, check_projective=True, mode='left'):  # mode可以是'left',或者'self',或者'right'。self表示句子中心用自己的索引表示，left表示用0表示,right表示用len+1表示。
    data = []
    for words, heads, tags, labels in CoNLLReader(path, check_projective=check_projective):
        heads_ = []
        for idx, h in enumerate(heads):
            if h == len(heads) + 1:
                if mode == 'left':  # CoNLLReader方法的句子中心词用len+1表示。
                    heads_.append(0)
                elif mode == 'self':
                    heads_.append(idx + 1)
                else:
                    heads_.append(h)
            else:
                heads_.append(h)
        if words:
            data.append((words, heads_, tags, labels))
    return data


class BiaffineDecoder(nn.Module):
    def __init__(self, input_size, hidden_arc, hidden_rel, num_rel, dropout=0.2):
        super(BiaffineDecoder, self).__init__()
        self.arc_hidden = MLP([input_size, hidden_arc * 2], output_dropout=dropout, output_activation=nn.Mish)
        # self.arc_hidden_t = MLP([input_size, hidden_arc], output_dropout=dropout, output_activation=nn.Mish)
        self.rel_hidden = MLP([input_size, hidden_rel * 2], output_dropout=dropout, output_activation=nn.Mish)
        # self.rel_hidden_t = MLP([input_size, hidden_rel], output_dropout=dropout, output_activation=nn.Mish)
        self.arc_biaffine = Biaffine(in1_features=hidden_arc, in2_features=hidden_arc, out_features=1)
        self.rel_biaffine = Biaffine(in1_features=hidden_rel, in2_features=hidden_rel, out_features=num_rel)

    def forward(self, embed):
        arc_hidden_h, arc_hidden_t = torch.chunk(self.arc_hidden(embed), chunks=2, dim=-1)
        rel_hidden_h, rel_hidden_t = torch.chunk(self.rel_hidden(embed), chunks=2, dim=-1)
        score_arc = self.arc_biaffine(arc_hidden_h, arc_hidden_t).squeeze(-1)  # b*len*len
        score_rel = self.rel_biaffine(rel_hidden_h, rel_hidden_t)  # b*len*len*num_rel
        return score_arc, score_rel


class DepDataset(BaseDataset):
    def __init__(self, data, transform_list: TransformList = None):
        super(DepDataset, self).__init__(data, transform_list)


class DepBiaffine(Task):
    def __init__(self,
                 trn=None,
                 dev=None,
                 tst=None,
                 batch_size=None,
                 batch_max_tokens=None,
                 eval_batch_size=None,
                 eval_batch_max_tokens=None,
                 # dependencies: str = None,
                 lr=None,
                 separate_optimizer=False,
                 cls_is_bos=False,
                 sep_is_eos=False,
                 use_grte_decoder=False,  # 以下是grte参数
                 method=1,
                 num_label=1,  # num_label等于2或者1，如果为2，label为0，表示wi不以某个关系依存与wj。为1相反
                 rounds=1,
                 extra_layer_normal=False,
                 num_attention_heads=4,
                 attention_probs_dropout_prob=0.1,
                 hidden_dropout_prob=0.1,
                 max_position_embeddings=512,
                 is_decoder=False,
                 layer_norm_eps=1e-12,
                 intermediate_size=1024,
                 hidden_act='gelu',
                 **kwargs,
                 ):
        super(DepBiaffine, self).__init__(
            **merge_locals_kwargs(locals(), kwargs, excludes=['self', 'kwargs', '__class__']))
        self.use_grte_decoder = use_grte_decoder
        self.num_label = num_label
        self.vocabs = VocabDict()  # 至少需要一个tag的vocab

    def build_model(self, encoder_size, task_name, **kwargs):
        num_rel = len(self.vocabs['%s_tag' % task_name])
        if not self.use_grte_decoder:
            return BiaffineDecoder(encoder_size, hidden_arc=128, hidden_rel=512, num_rel=num_rel, dropout=0.2)
        else:
            return GRTEDecoder(num_p=num_rel, hidden_size=encoder_size,
                               **self.config)

    def build_sample(self, data, mode='train', **kwargs):
        examples = []
        if mode == 'train' or mode == 'eval':
            for i, d in enumerate(data):
                ws, hs, ls = None, None, None
                if len(d) == 4:
                    ws, hs, _, ls = d
                else:
                    ws = d
                examples.append({'original_input': ws})
                examples[-1]['_id'] = i
                if isinstance(ws, list):
                    examples[-1]['token'] = ws
                    if hs and ls:
                        examples[-1]['head'] = hs
                        examples[-1]['relation'] = ls
        elif mode == 'predict':
            for i, d in enumerate(data):
                assert isinstance(d, list)
                examples.append({'original_input': d})
                examples[-1]['_id'] = i
                examples[-1]['token'] = d
        return examples

    def build_transformList(self, mode, task_name, **kwargs):
        if mode == 'train' or mode == 'eval':
            transform_list = TransformList()
            if self.use_grte_decoder:
                def add_cls(sample, src_name):
                    sample[src_name] = [sample[src_name][0]] + sample[src_name]
                    return sample
                add_cls_head_transform = partial(add_cls, src_name='head')  # 给cls添加一个虚拟的head
                add_cls_rel_transform = partial(add_cls, src_name='relation')  # 给cls添加一个虚拟的relation
                transform_list.extend([add_cls_head_transform, add_cls_rel_transform])

            def rename(sample, src_name, tar_name):
                sample[tar_name] = sample.pop(src_name)
                return sample
            rename_transform = partial(rename, src_name='relation', tar_name='%s_tag' % task_name)
            transform_list.extend([rename_transform, self.vocabs])
            return transform_list
        elif mode == 'predict':
            return TransformList()
        else:
            raise ValueError("mode必须在'train', 'eval', 'predict' 中取值。")

    def build_dataset(self, data, transform_list, **kwargs):
        return DepDataset(data, transform_list)

    def build_dataloader(self, dataset, batch_size=None, batch_max_tokens=None, shuffle=False, generator=None, gradient_accumulation=1,
                         use_effective_tokens=False, **kwargs):
        return DataLoader(dataset=dataset,
                          batch_sampler=SortingSamplerBuilder.build([data['subtoken_len'] for data in dataset],
                                                                    batch_size, batch_max_tokens, use_effective_tokens,
                                                                    shuffle, generator, gradient_accumulation),
                          collate_fn=partial(DepDataset.collate_fn, padding_value=0, extra_pad_keys=['head']))

    def build_criterion(self, reduction='mean', **kwargs):
        return torch.nn.CrossEntropyLoss(reduction=reduction)

    def build_optimizer(self, decoder, **kwargs):
        raise NotImplementedError('该任务没有实现独立的优化器。')

    def compute_loss(self, task_name, batch, out, mask, criterion):
        """

        :param y:
        :param out:
        :param mask: token length mask
        :param criterion:
        :return:
        """
        if not self.use_grte_decoder:
            mask = mask.clone()
            score_arc, score_rel = out  # (b, len+1, len+1), (b, len+1, len+1, num_rel)
            score_arc = score_arc.masked_fill(~mask.unsqueeze(1), float('-inf'))
            score_rel = score_rel.masked_fill(~mask.unsqueeze(1).unsqueeze(-1), float('-inf'))

            rel_tag_ids_flat = (batch['%s_tag_ids' % task_name])[mask[:, 1:]]  # (b^,)，b^为批量样本的原始句子长度累加和（不包括CLS）。
            head_flat = batch['head'][mask[:, 1:]]  # (b^,)，b^为批量样本的原始句子长度累加和。
            mask[:, 0] = False
            score_arc_flat = score_arc[mask]  # (b^, len+1)
            score_rel_flat = score_rel[mask]  # (b^, len+1, num_rel)
            if self.config['method'] == 1:
                arc_loss = criterion(score_arc_flat, head_flat)
                rel_loss = criterion(score_rel_flat[torch.arange(head_flat.size(0)), head_flat], rel_tag_ids_flat)
                auxiliary_loss1 = criterion(torch.max(score_rel_flat, dim=-1).values, head_flat)
                auxiliary_loss2 = criterion(torch.sum(score_rel_flat, dim=-1), head_flat)
                return arc_loss + rel_loss + auxiliary_loss1 + auxiliary_loss2
            elif self.config['method'] == 2:
                arc_loss = criterion(score_arc_flat, head_flat)
                rel_loss = criterion(score_rel_flat[torch.arange(head_flat.size(0)), head_flat], rel_tag_ids_flat)
                return arc_loss + rel_loss
            elif self.config['method'] == 3:

                max_len, num_rel = score_rel.size(1), score_rel.size(3)
                # offset = (torch.arange(max_len) * len(self.vocabs['%s_tag' % task_name])).to(batch['head'].device)
                offset = batch['head'] * num_rel
                head_flat = (batch['%s_tag_ids' % task_name] + offset)[mask[:, 1:]]
                try:
                    loss = criterion(score_rel_flat.view(-1, max_len * num_rel), head_flat)
                except IndexError as e:
                    print(score_rel_flat.view(-1, max_len * num_rel), head_flat, batch['head'], max_len, num_rel,
                          sep='\n')
                    print(e)
                    raise e
                return loss
        else:
            '''
            mask1 = lengths_to_mask(batch['token_len'] - 1)
            mask2 = mask1.clone()
            # score的size是(b, l+1, l+1, num_relation, 2)，l表示原始句子的长度加1（多了cls），每个relation对应有关和无关两种情况。
            score = out[:, : -1, : -1].masked_fill(~mask1.unsqueeze(1).unsqueeze(-1).unsqueeze(-1), float('-inf'))
            mask1[:, 0] = False
            label = torch.zeros_like(score[:, :, :, :, 0], dtype=torch.long)  # (b, l+1, l+1, num_relation)
            b, lens = label.size(0), label.size(1)
            # 如果第i个样本的w_j的中心词是w_k并且关系是r，score[i, j, k, r, 1]对应的标签值值为1。其他都是0。
            label[torch.arange(b).unsqueeze(-1), torch.arange(lens), batch['head'], batch['%s_tag_ids' % task_name]] = 1
            mask3 = mask1.unsqueeze(-1).masked_fill(~mask2.unsqueeze(1), False)
            loss = criterion(score[mask3].view(-1, 2), label[mask3].view(-1)) * mask3.sum() / b  # 使用mask剔除padding的单词。
            #return loss
            '''
            mask1 = lengths_to_mask(batch['token_len'] - 1)
            # (b, l+1, l+1, num_rel)
            if out.size(-1) == 2:
                score2 = (out[:, :-1, :-1, :, 1] - out[:, :-1, :-1, :, 0]).masked_fill(
                    ~mask1.unsqueeze(1).unsqueeze(-1), float('-inf'))  # (b, l+1, l+1, num_relation)
            elif out.size(-1) == 1:
                score2 = out[:, :-1, :-1, :].squeeze(-1).masked_fill(~mask1.unsqueeze(1).unsqueeze(-1), float(
                    '-inf'))  # (b, l+1, l+1, num_relation)
            else:
                raise ValueError

            mask1[:, 0] = False
            rel_tag_ids_flat = (batch['%s_tag_ids' % task_name])[mask1]  # (b^,)，b^为批量样本的原始句子长度累加和（不包括CLS）。
            head_flat = batch['head'][mask1]  # (b^,)，b^为批量样本的原始句子长度累加和。head记录了cls的虚拟的中心词。
            rel_flat_score2 = score2[mask1]  # b^, l+1, num_rel
            b = mask1.size(0)
            rel_loss = criterion(rel_flat_score2[torch.arange(head_flat.size(0)), head_flat], rel_tag_ids_flat)
            arc_loss = criterion(torch.max(rel_flat_score2, dim=-1).values, head_flat)
            auxiliary_loss = criterion(torch.sum(rel_flat_score2, dim=-1), head_flat)
            total_loss = rel_loss + arc_loss + auxiliary_loss
            return total_loss
            #return sum(total_loss[:-1])*0.2 + total_loss[-1]
            #return sum(total_loss[:-1])*1.0 + total_loss[-1]

    def build_metric(self):
        return DependencyMetric()

    def update_metric(self, prediction, batch, metric, task_name):
        max_len = batch['token_len'].max() - 2
        token_mask = torch.arange(max_len) < (batch['token_len'] - 2).unsqueeze(-1).to('cpu')
        vocab = self.vocabs['%s_tag' % task_name]
        metric(pad_sequence([torch.tensor(heads) for heads in prediction['head']], batch_first=True),
               pad_sequence([torch.tensor([vocab(r) for r in rel]) for rel in prediction['relation']],
                            batch_first=True),
               (batch['head'][:, 0 if not self.use_grte_decoder else 1:]).cpu(),  # 如果使用了grte_decoder，会给CLS添加一个虚拟的head和relation。
               (batch['%s_tag_ids' % task_name][:, 0 if not self.use_grte_decoder else 1:]).cpu(),
               token_mask)

    def feed_batch(self, mode, h: torch.FloatTensor, batch: Dict, output_dict: Dict, decoder: torch.nn.Module, task_name):
        if not self.use_grte_decoder:
            output_dict[task_name] = {}
            h = h[:, :-1, :]
            mask = lengths_to_mask(batch['token_len'] - 1)
            score_arc, score_rel = decoder(h)
            output_dict[task_name]['output'] = (score_arc, score_rel)
            output_dict[task_name]['mask'] = mask
        else:
            output_dict[task_name] = {}
            mask = lengths_to_mask(batch['token_len'])  # 这里包含了sep。
            mask_token_ids = torch.zeros_like(mask, dtype=torch.int)
            mask_token_ids[mask] = 1
            score = decoder(h, mask_token_ids)  # 将所有层的结构都返回，每一层的score的size是(b, l+2, l+2, num_relation, 2), 每个relation对应有关和无关两种情况。
            output_dict[task_name]['output'] = score
            output_dict[task_name]['mask'] = mask

    def decode_output(self, mode, output_dict, batch, model, task_name, **kwargs):
        output_dict_task = output_dict[task_name]
        if not self.use_grte_decoder:
            score_arc, score_rel = output_dict_task['output']
            mask = output_dict_task['mask'].clone()
            score_arc = score_arc.masked_fill(~mask.unsqueeze(1), float('-inf'))
            score_rel = score_rel.masked_fill(~mask.unsqueeze(1).unsqueeze(-1), float('-inf'))
            mask[:, 0] = False
            # score_arc_flat = score_arc[mask]  # (b^, len+1)
            # score_rel_flat = score_rel[mask]  # (b^, len+1, num_rel)
            if self.config['method'] == 1:
                score_arc += torch.max(score_rel, dim=-1).values
                score_arc += torch.sum(score_rel, dim=-1)

                pred_head = torch.argmax(score_arc, dim=-1)  # (b, len+1), pred_head[i, 0]表示对于第i个句子，CLS的中心词，也就是没有意义。
                lens = mask.sum(1)  # 原始句子真实长度
                bad = [not is_tree(seq[1:l + 1], proj=True) for l, seq in zip(lens.tolist(), pred_head.tolist())]
                if any(bad):
                    pred_head[bad] = eisner(score_arc[bad], mask[bad])

                pred_head_flat = pred_head[mask]
                score_rel_flat = score_rel[mask]  # (b^, len+1, num_rel)
                pred_rel_tag_ids_flat = torch.argmax(
                    score_rel_flat[torch.arange(pred_head_flat.size(0)), pred_head_flat],
                    dim=-1)
                # score_arc_flat = score_arc[mask]  # (b^, len+1)
                # score_rel_flat = score_rel[mask]  # (b^, len+1, num_rel)
                # score_arc_flat += torch.max(score_rel_flat, dim=-1).values
                # score_arc_flat += torch.sum(score_rel_flat, dim=-1)
                # pred_head_flat = torch.argmax(score_arc_flat, dim=-1)
                # pred_rel_tag_ids_flat = torch.argmax(score_rel_flat[torch.arange(pred_head_flat.size(0)), pred_head_flat],
                #                                      dim=-1)
            elif self.config['method'] == 2:
                pred_head = torch.argmax(score_arc, dim=-1)  # (b, len+1), pred_head[i, 0]表示对于第i个句子，CLS的中心词，也就是没有意义。
                lens = mask.sum(1)  # 原始句子真实长度
                bad = [not is_tree(seq[1:l + 1], proj=True) for l, seq in zip(lens.tolist(), pred_head.tolist())]
                if any(bad):
                    pred_head[bad] = eisner(score_arc[bad], mask[bad])

                pred_head_flat = pred_head[mask]
                score_rel_flat = score_rel[mask]  # (b^, len+1, num_rel)
                pred_rel_tag_ids_flat = torch.argmax(
                    score_rel_flat[torch.arange(pred_head_flat.size(0)), pred_head_flat],
                    dim=-1)
                # score_arc_flat = score_arc[mask]  # (b^, len+1)
                # score_rel_flat = score_rel[mask]  # (b^, len+1, num_rel)
                # pred_head_flat = torch.argmax(score_arc_flat, dim=-1)
                # pred_rel_tag_ids_flat = torch.argmax(score_rel_flat[torch.arange(pred_head_flat.size(0)), pred_head_flat],
                #                                      dim=-1)
            elif self.config['method'] == 3:
                score_rel_flat = score_rel[mask]  # (b^, len+1, num_rel)
                max_len, num_rel = score_rel_flat.size(1), score_rel_flat.size(2)
                head_rel = torch.argmax(score_rel_flat.view(-1, max_len * num_rel), dim=-1)
                pred_head_flat = torch.div(head_rel, num_rel, rounding_mode='floor')
                pred_rel_tag_ids_flat = head_rel % num_rel

            split_size_or_sections = list((batch['token_len'] - 2).cpu().numpy())
            vocab = self.vocabs['%s_tag' % task_name]
            output_dict_task['prediction'] = {
                'head': [list(hs.numpy()) for hs in torch.split(pred_head_flat.cpu(), split_size_or_sections)],
                'relation':
                    [[vocab.get_token(r) for r in rs] for rs in
                     torch.split(pred_rel_tag_ids_flat, split_size_or_sections)]}
        else:
            out = output_dict_task['output'] + output_dict_task['output']
            mask = lengths_to_mask(batch['token_len'] - 1)
            # score的size是(b, l+1, l+1, num_relation, 1)，+l表示原始句子的长度加1（多了cls）。
            score = (out[:, : -1, : -1]).masked_fill(
                ~mask.unsqueeze(1).unsqueeze(-1).unsqueeze(-1), float('-inf'))
            mask[:, 0] = False

            if score.size(-1) == 2:
                rel_score = score[:, :, :, :, 1] - score[:, :, :, :, 0]
            elif score.size(-1) == 1:
                rel_score = score.squeeze(-1)

            arc_score = torch.max(rel_score, dim=-1).values  # (b, l+1, l+1)
            arc_score += torch.sum(rel_score, dim=-1)
            #
            #arc_score = torch.sum(rel_score, dim=-1)

            pred_head = torch.argmax(arc_score, dim=-1)

            lens = mask.sum(1)  # 原始句子真实长度
            
            bad = [not is_tree(seq[1:l + 1], proj=True) for l, seq in zip(lens.tolist(), pred_head.tolist())]  # 耗时1s左右。
            if any(bad):
                pred_head[bad] = eisner(arc_score[bad], mask[bad])
                # max_len = torch.max(torch.sum(mask[bad], dim=-1) + 1)
                # pred_head[bad, : max_len] = eisner2(arc_score[bad], mask[bad])
            pred_head_flat = pred_head[mask]
            score_rel_flat = rel_score[mask]  # (b^, len+1, num_rel)
            pred_rel_tag_ids_flat = torch.argmax(score_rel_flat[torch.arange(pred_head_flat.size(0)), pred_head_flat],
                                                 dim=-1)
            split_size_or_sections = (batch['token_len'] - 2).cpu().tolist()
            vocab = self.vocabs['%s_tag' % task_name]
            output_dict_task['prediction'] = {
                'head': [hs.tolist() for hs in torch.split(pred_head_flat.cpu(), split_size_or_sections)],
                'relation':
                    [[vocab.get_token(r) for r in rs] for rs in
                     torch.split(pred_rel_tag_ids_flat.cpu(), split_size_or_sections)]}

    def prediction_to_result(self, pred, vocab, batch, output_dict, task_name, **kwargs):
        if not self.use_grte_decoder:
            return [{'head': [0 if h == idx + 1 else h for idx, h in enumerate(hs)], 'relation': rels} for hs, rels in
                    zip(pred['head'], pred['relation'])]
        else:
            return [{'head': hs, 'relation': rels} for hs, rels in
                    zip(pred['head'], pred['relation'])]
        
    def post_transform(self, task_name, batch, output_dict, device, **kwargs):
        output_dict.pop(task_name)


def eisner(scores, mask):
    r"""
    符合投射性的一阶esiner算法。

    References:
        - Ryan McDonald, Koby Crammer and Fernando Pereira. 2005.
          `Online Large-Margin Training of Dependency Parsers`_.

    Args:
        scores (~torch.Tensor): ``[batch_size, seq_len, seq_len]``.这里的seq_len表示原始句子加上虚根的长度。
            依存词-中心词对的分数。第0维代表batch，第1维代表依存词索引，第2维代表中心词索引。
            scores[i, j, k]表示第i个样本的的wj的中心词是wk的分数。
            scores[i, 0, :]代表第i个样本的虚根的中心词的分数，这是无意义的。
        mask (~torch.BoolTensor): ``[batch_size, seq_len]``.这里的seq_len表示原始句子加上虚根的长度。
            seq_len mask，其中mask[:, 0]必须是False，0代表虚构。padding的位置上也必须为False，该参数主要是为了计算出原始句子的真实长度。

    Returns:
        ~torch.Tensor:
            返回一个[batch_size, seq_len]的张量，其中res[:, 0]无意义。

    .. _Online Large-Margin Training of Dependency Parsers:
        https://www.aclweb.org/anthology/P05-1012/
    """
    lens = mask.sum(1)
    batch_size, seq_len, _ = scores.shape
    scores = scores.permute(2, 1, 0)
    s_i = torch.full_like(scores, float('-inf'))
    s_c = torch.full_like(scores, float('-inf'))
    p_i = scores.new_zeros(seq_len, seq_len, batch_size).long()
    p_c = scores.new_zeros(seq_len, seq_len, batch_size).long()
    s_c.diagonal().fill_(0)

    for w in range(1, seq_len):
        n = seq_len - w
        starts = p_i.new_tensor(range(n)).unsqueeze(0)
        # ilr = C(i->r) + C(j->r+1)
        ilr = stripe(s_c, n, w) + stripe(s_c, n, w, (w, 1))
        # [batch_size, n, w]
        il = ir = ilr.permute(2, 0, 1)
        # I(j->i) = max(C(i->r) + C(j->r+1) + s(j->i)), i <= r < j
        il_span, il_path = il.max(-1)
        s_i.diagonal(-w).copy_(il_span + scores.diagonal(-w))
        p_i.diagonal(-w).copy_(il_path + starts)
        # I(i->j) = max(C(i->r) + C(j->r+1) + s(i->j)), i <= r < j
        ir_span, ir_path = ir.max(-1)
        s_i.diagonal(w).copy_(ir_span + scores.diagonal(w))
        p_i.diagonal(w).copy_(ir_path + starts)

        # C(j->i) = max(C(r->i) + I(j->r)), i <= r < j
        cl = stripe(s_c, n, w, (0, 0), 0) + stripe(s_i, n, w, (w, 0))
        cl_span, cl_path = cl.permute(2, 0, 1).max(-1)
        s_c.diagonal(-w).copy_(cl_span)
        p_c.diagonal(-w).copy_(cl_path + starts)
        # C(i->j) = max(I(i->r) + C(r->j)), i < r <= j
        cr = stripe(s_i, n, w, (0, 1)) + stripe(s_c, n, w, (1, w), 0)
        cr_span, cr_path = cr.permute(2, 0, 1).max(-1)
        s_c.diagonal(w).copy_(cr_span)
        s_c[0, w][lens.ne(w)] = float('-inf')
        p_c.diagonal(w).copy_(cr_path + starts + 1)

    def backtrack(p_i, p_c, heads, i, j, complete):
        if i == j:
            return
        if complete:
            r = p_c[i, j]
            backtrack(p_i, p_c, heads, i, r, False)
            backtrack(p_i, p_c, heads, r, j, True)
        else:
            r, heads[j] = p_i[i, j], i
            i, j = sorted((i, j))
            backtrack(p_i, p_c, heads, i, r, True)
            backtrack(p_i, p_c, heads, j, r + 1, True)

    preds = []
    p_c = p_c.permute(2, 0, 1).cpu()
    p_i = p_i.permute(2, 0, 1).cpu()
    for i, length in enumerate(lens.tolist()):
        heads = p_c.new_zeros(length + 1, dtype=torch.long)
        backtrack(p_i[i], p_c[i], heads, 0, length, True)
        preds.append(heads.to(mask.device))

    return pad(preds, total_length=seq_len).to(mask.device)


def pad(tensors, padding_value=0, total_length=None):
    size = [len(tensors)] + [max(tensor.size(i) for tensor in tensors)
                             for i in range(len(tensors[0].size()))]
    if total_length is not None:
        assert total_length >= size[1]
        size[1] = total_length
    out_tensor = tensors[0].data.new(*size).fill_(padding_value)
    for i, tensor in enumerate(tensors):
        out_tensor[i][[slice(0, i) for i in tensor.size()]] = tensor
    return out_tensor


def stripe(x, n, w, offset=(0, 0), dim=1):
    """r'''Returns packagea diagonal stripe of the tensor.

    Args:
      x: Tensor
      n: int
      w: int
      offset: tuple (Default value = (0)
      dim: int (Default value = 1)
      Example:
      0):

    Returns:

    >>> x = torch.arange(25).view(5, 5)
    >>> x
    tensor([[ 0,  1,  2,  3,  4],
            [ 5,  6,  7,  8,  9],
            [10, 11, 12, 13, 14],
            [15, 16, 17, 18, 19],
            [20, 21, 22, 23, 24]])
    >>> stripe(x, 2, 3, (1, 1))
    tensor([[ 6,  7,  8],
            [12, 13, 14]])
    >>> stripe(x, 2, 3, dim=0)
    tensor([[ 0,  5, 10],
            [ 6, 11, 16]])
    """
    x, seq_len = x.contiguous(), x.size(1)
    stride, numel = list(x.stride()), x[0, 0].numel()
    stride[0] = (seq_len + 1) * numel
    stride[1] = (1 if dim == 1 else seq_len) * numel
    return x.as_strided(size=(n, w, *x.shape[2:]),
                        stride=stride,
                        storage_offset=(offset[0] * seq_len + offset[1]) * numel)


def is_tree(sequence, proj=False, multi_root=False):
    r"""
    根据依存弧判断是否是一可合法的依存树。

    Args:
        sequence (list[int]):
            中心词索引列表。句子的核心的中心词设置为0.句子中单词的索引从1开始，root用0表示。
        proj (bool):
            If ``True``, 需要符合投射性. Default: ``False``.
        multi_root (bool):
            If ``False``, 需要保证有且只有一个root；否则必需有1个及以上个root。 Default: ``False``.
    """

    if proj and not is_projective(sequence):
        return False
    n_roots = sum(head == 0 for head in sequence)
    if n_roots == 0:
        return False
    if not multi_root and n_roots > 1:
        return False
    if any(i == head for i, head in enumerate(sequence, 1)):
        return False
    return next(tarjan(sequence), None) is None


def is_projective(sequence):
    """
    判断sequence对应的依存树是否符合投射性。该方法不会判断依存树的完整性。该方法不会确保一定无环，如某单词的中心词被设置为自己（[2, -1, 3]）。
    :param sequence: 中心词索引的列表。句子的核心的head的索引用0表示，句子中的单词索引从1开始计数。对于没有计算出中心词的单词对应的索引列表中值应该为-1。
    :return: 如果符合投射性，则返回True。如果传入的sequence列表只是局部树（有些值为-1），则只判断该局部树。
    """
    pairs = [(h, d) for d, h in enumerate(sequence, 1) if h >= 0]
    for i, (hi, di) in enumerate(pairs):
        for hj, dj in pairs[i + 1:]:
            (li, ri), (lj, rj) = sorted([hi, di]), sorted([hj, dj])
            if li <= hj <= ri and hi == dj:
                return False
            if lj <= hi <= rj and hj == di:
                return False
            if (li < lj < ri or li < rj < ri) and (li - lj) * (ri - rj) > 0:
                return False
    return True


def tarjan(sequence):
    r"""
    Tarjan algorithm 用来寻找有向图图的强连通分量(SCCs)。o(n)时间复杂度。
    调用该方法得到一个生成器。
    Args:
        sequence (list):
            List of head indices.

    Yields:
        调用next(tarjan(sequence), None)如果有SCC则返回一个，否则返回None.
    """

    sequence = [-1] + sequence
    # record the search order, i.e., the timestep
    dfn = [-1] * len(sequence)
    # record the the smallest timestep in packagea SCC
    low = [-1] * len(sequence)
    # push the visited into the stack
    stack, onstack = [], [False] * len(sequence)

    def connect(i, timestep):
        dfn[i] = low[i] = timestep[0]
        timestep[0] += 1
        stack.append(i)
        onstack[i] = True

        for j, head in enumerate(sequence):
            if head != i:
                continue
            if dfn[j] == -1:
                yield from connect(j, timestep)
                low[i] = min(low[i], low[j])
            elif onstack[j]:
                low[i] = min(low[i], dfn[j])

        # packagea SCC is completed
        if low[i] == dfn[i]:
            cycle = [stack.pop()]
            while cycle[-1] != i:
                onstack[cycle[-1]] = False
                cycle.append(stack.pop())
            onstack[i] = False
            # ignore the self-loop
            if len(cycle) > 1:
                yield cycle

    timestep = [0]
    for i in range(len(sequence)):
        if dfn[i] == -1:
            yield from connect(i, timestep)


def eisner2(scores, mask) -> torch.Tensor:
    lens = mask.sum(1)
    batch_size, seq_len, _ = scores.shape
    # [batch_size, w, n]
    scores = scores.permute(2, 1, 0)
    s_i = torch.full_like(scores, float('-inf'))
    s_c = torch.full_like(scores, float('-inf'))
    p_i = scores.new_zeros(seq_len, seq_len, batch_size).long()
    p_c = scores.new_zeros(seq_len, seq_len, batch_size).long()
    s_c.diagonal().fill_(0)

    for w in range(1, seq_len):
        n = seq_len - w
        starts = p_i.new_tensor(range(n)).unsqueeze(0)
        # ilr = C(i->r) + C(j->r+1)
        ilr = stripe(s_c, n, w) + stripe(s_c, n, w, (w, 1))
        # [batch_size, n, w]
        ilr = ilr.permute(2, 0, 1)
        il = ilr + scores.diagonal(-w).unsqueeze(-1)
        # I(j->i) = max(C(i->r) + C(j->r+1) + s(j->i)), i <= r < j
        il_span, il_path = il.max(-1)
        s_i.diagonal(-w).copy_(il_span)
        p_i.diagonal(-w).copy_(il_path + starts)
        ir = ilr + scores.diagonal(w).unsqueeze(-1)
        # I(i->j) = max(C(i->r) + C(j->r+1) + s(i->j)), i <= r < j
        ir_span, ir_path = ir.max(-1)
        s_i.diagonal(w).copy_(ir_span)
        p_i.diagonal(w).copy_(ir_path + starts)

        # C(j->i) = max(C(r->i) + I(j->r)), i <= r < j
        cl = stripe(s_c, n, w, (0, 0), 0) + stripe(s_i, n, w, (w, 0))
        cl_span, cl_path = cl.permute(2, 0, 1).max(-1)
        s_c.diagonal(-w).copy_(cl_span)
        p_c.diagonal(-w).copy_(cl_path + starts)
        # C(i->j) = max(I(i->r) + C(r->j)), i < r <= j
        cr = stripe(s_i, n, w, (0, 1)) + stripe(s_c, n, w, (1, w), 0)
        cr_span, cr_path = cr.permute(2, 0, 1).max(-1)
        s_c.diagonal(w).copy_(cr_span)
        s_c[0, w][lens.ne(w)] = float('-inf')
        p_c.diagonal(w).copy_(cr_path + starts + 1)

    predicts = []
    p_c = p_c.permute(2, 0, 1).cpu()
    p_i = p_i.permute(2, 0, 1).cpu()
    for i, length in enumerate(lens.tolist()):
        heads = p_c.new_ones(length + 1, dtype=torch.long)
        backtrack2(p_i[i], p_c[i], heads, 0, length, True)
        predicts.append(heads)

    return pad_sequence(predicts, True).to(scores.device)


def backtrack2(p_i, p_c, heads, i, j, complete):
    if i == j:
        return
    if complete:
        r = p_c[i, j]
        backtrack2(p_i, p_c, heads, i, r, False)
        backtrack2(p_i, p_c, heads, r, j, True)
    else:
        r, heads[j] = p_i[i, j], i
        i, j = sorted((i, j))
        backtrack2(p_i, p_c, heads, i, r, True)
        backtrack2(p_i, p_c, heads, j, r + 1, True)


def stripe2(x, n, w, offset=(0, 0), dim=1):
    r'''Returns a diagonal stripe of the tensor.
    Parameters:
        x (Tensor): the input tensor with 2 or more dims.
        n (int): the length of the stripe.
        w (int): the width of the stripe.
        offset (tuple): the offset of the first two dims.
        dim (int): 0 if returns a horizontal stripe; 1 else.
    Example::
    >>> x = torch.arange(25).view(5, 5)
    >>> x
    tensor([[ 0,  1,  2,  3,  4],
            [ 5,  6,  7,  8,  9],
            [10, 11, 12, 13, 14],
            [15, 16, 17, 18, 19],
            [20, 21, 22, 23, 24]])
    >>> stripe(x, 2, 3, (1, 1))
    tensor([[ 6,  7,  8],
            [12, 13, 14]])
    >>> stripe(x, 2, 3, dim=0)
    tensor([[ 0,  5, 10],
            [ 6, 11, 16]])
    '''
    x, seq_len = x.contiguous(), x.size(1)
    stride, numel = list(x.stride()), x[0, 0].numel()
    stride[0] = (seq_len + 1) * numel
    stride[1] = (1 if dim == 1 else seq_len) * numel
    return x.as_strided(size=(n, w, *x.shape[2:]),
                        stride=stride,
                        storage_offset=(offset[0] * seq_len + offset[1]) * numel)



if __name__ == '__main__':
    pass
