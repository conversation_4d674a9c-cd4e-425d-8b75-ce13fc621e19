from typing import Optional, Union, List
import torch, torchvision
from torch.nn import <PERSON><PERSON><PERSON>
from myocr import OCRSystem


@torch.inference_mode()
def single_image_layout_ocr(device_tensor: torch.Tensor, ocr: OCRSystem,
        layout: Optional[Module]=None,
        reserve_pred_numbers: Union[int, List[int], torch.Tensor] = None):
    n = reserve_pred_numbers
    device = device_tensor.device
    if layout is not None and n is not None:
        if isinstance(n, list):
            n = torch.as_tensor(n, device=device)
        det = layout([device_tensor[0]])[1][0]
        if isinstance(n, int):
            boxes = (det['scores']>=0.5)*(det['labels']==n)
        else:
            boxes = (det['scores']>=0.5)*(det['labels'][:,None]==n[None,:]).any(-1)
        boxes = det['boxes'][boxes].int()
    else:
        boxes = torch.zeros([0,4], device=device)
    flag = boxes.size(0) > 0
    if flag:
        mask = torch.zeros_like(device_tensor)
        for i in boxes:
            mask[:,:,i[1]:i[3],i[0]:i[2]] = 1.
        device_tensor *= mask
    boxes, rects = ocr.text_detect(device_tensor)
    if len(boxes) < 1:
        return False, '', flag
    rec_res = ocr.text_rec_classic(device_tensor, boxes, rects)
    return True, '\n'.join([i[0] for i in rec_res]), flag

if __name__ == '__main__':
    from importlib_resources import files
    dtm = files('dtm')
    x = str(dtm/'../0001.jpg')
    x = torchvision.io.read_image(x)[None,[2,1,0],:].float()
    layout = torch.jit.load(dtm/'la_dp09.pt', map_location='cpu')
    ocr = OCRSystem(det_model_path=dtm/'001.pt', rec_image_shape='3,32,320',
        cls_model_path=dtm/'003.pt', cls_vertical_model_path=dtm/'006.pt',
        rec_model_path=dtm/'tp1t7.pt', device='cpu',
        rec_char_dict_path=dtm/'ppocr_keys_v1.txt') 
    rec_res = single_image_layout_ocr(x, ocr, layout, 10)
    print(rec_res)
