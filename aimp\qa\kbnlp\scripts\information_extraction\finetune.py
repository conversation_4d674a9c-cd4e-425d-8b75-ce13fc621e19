# Copyright (c) 2022 PaddlePaddle Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import argparse
import time
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))
# print(sys.path)
import shutil
from functools import partial
import jsonlines

import numpy as np
import torch
from torch.utils.data import Dataset, DataLoader
from torch.optim import AdamW
from torch.nn.parallel import DistributedDataParallel
from transformers import AutoTokenizer, AutoConfig
from accelerate import Accelerator
from Image.scripts.information_extraction.metric import SpanEvaluator

from qa.kbnlp.task.models.information_extraction_model import UIE
from qa.kbnlp.pretrained.ernie.configuration_ernie import ErnieConfig
from qa.kbnlp.task.information_extraction import UIEDataset
from qa.kbnlp.scripts.information_extraction.evaluate import evaluate
from aqa.kbnlp.scripts.information_extraction.doccano import convert_example_to_prompt_format
from qa.kbnlp.scripts.information_extraction.utils import dbc2sbc


# TODO:1,关系的提示符转化为id；2，数据的特殊字符的转换。3,BCELoss。
def do_train():
    accelerator = Accelerator(cpu=True if args.device == 'cpu' else False)
    accelerator.print(args)
    device = accelerator.device
    print('process id: %d, current device: %s' % (accelerator.process_index, device))

    tokenizer = AutoTokenizer.from_pretrained(args.tokenizer if args.tokenizer else args.uie_model_path)
    assert not (args.uie_model_path and args.checkpoint), "不能同时从uie模型、checkpoint中初始化模型。"
    if args.train_path_processed:
        print("从'train_path_processed'中加载数据， 'fewshot','train_top','negative_ratio'参数无效。")
        with jsonlines.open(args.train_path_processed, mode='r') as jr:
            train_dataset = []
            for d in jr:  # 兼容以前的数据，以后同意用text。
                if 'content' in d:
                    d['text'] = d.pop('content')
                train_dataset.append(d)

            if args.shuffle:
                ids = np.random.permutation(len(train_dataset))
                train_dataset = [train_dataset[i] for i in ids]
    elif args.train_path:
        train_dataset, _, _ = convert_example_to_prompt_format(args.train_path,
                                                               # save_dir=args.save_dir,
                                                               fewshot=args.fewshot,
                                                               top=args.train_top,
                                                               negative_ratio=args.negative_ratio,
                                                               splits=[1., 0., 0.],
                                                               # task_type='ext',
                                                               # options=['正向', '负向'],
                                                               # prompt_prefix='情感倾向',
                                                               shuffle=args.shuffle,
                                                               seed=args.seed,
                                                               # separator='##',
                                                               )
    else:
        raise ValueError("必须提供'train_path_processed'和'train_path'中的一个。")

    if args.dev_path_processed:
        print("从'dev_path_processed'中加载数据， 'negative_ratio','dev_top'参数无效。")
        with jsonlines.open(args.dev_path_processed, mode='r') as jr:
            dev_dataset = []
            for d in jr:
                if 'content' in d:
                    d['text'] = d.pop('content')
                dev_dataset.append(d)
    elif args.dev_path:
        _, dev_dataset, _ = convert_example_to_prompt_format(
                                                args.dev_path,
                                                save_dir=None,
                                                top=args.dev_top,
                                                negative_ratio=args.negative_ratio,
                                                splits=[0., 1.0, 0.],
                                                # task_type='ext',
                                                # options=['正向', '负向'],
                                                # prompt_prefix='情感倾向',
                                                shuffle=True,
                                                seed=args.seed,
                                                # separator='##',
                                                return_prompt=False,)
    else:
        dev_dataset = []
        print('no eval dataset.')
    if args.uie_model_path:
        model = UIE.from_pretrained(args.uie_model_path).to(device)
    elif args.checkpoint:
        model = UIE.from_pretrained(args.checkpoint).to(device)
    else:
        accelerator.print("Without 'uie_model_path' and 'checkpoint', random init the AutoPromptUIE model params. This is not recommended.")
        model = UIE(ErnieConfig()).to(device)

    accelerator.print('train_dataset: ', len(train_dataset))
    accelerator.print('dev_dataset: ', len(dev_dataset))

    def normalize(data):
        data['text'] = dbc2sbc(data['text'])
        return data

    if args.normal:
        train_dataset = [normalize(d) for d in train_dataset]
        dev_dataset = [normalize(d) for d in dev_dataset]

    train_dataset = UIEDataset(train_dataset)
    train_loader = DataLoader(train_dataset, batch_size=args.batch_size,
                              collate_fn=partial(model.collate, tokenizer=tokenizer, max_seq_len=args.max_seq_len))

    dev_dataset = UIEDataset(dev_dataset)
    dev_loader = DataLoader(dev_dataset, batch_size=args.batch_size,
                            collate_fn=partial(model.collate, tokenizer=tokenizer, max_seq_len=args.max_seq_len))
    optimizer = AdamW(params=model.parameters(), lr=args.learning_rate, weight_decay=args.weight_decay)
    model, optimizer, train_loader = accelerator.prepare(model, optimizer, train_loader)
    criterion = torch.nn.BCELoss()
    metric = SpanEvaluator()

    checkpoint_name_list = []
    global_step = 0
    best_epoch = 0
    best_f1 = 0
    best_precision, best_recall = 0, 0
    start_time = time.time()
    model.train()
    for epoch in range(1, args.num_epochs + 1):
        loss_list = []
        time_temp = time.time()
        for batch in train_loader:
            optimizer.zero_grad()
            batch = dict((k, v.to(device) if isinstance(v, torch.Tensor) else v) for k, v in batch.items())
            start_ids, end_ids = batch.pop('start_ids'), batch.pop('end_ids')
            start_prob, end_prob = model(**batch)[:2]
            loss_start = criterion(start_prob, start_ids)
            loss_end = criterion(end_prob, end_ids)
            loss = (loss_start + loss_end) / 2.0
            # loss.backward()
            accelerator.backward(loss)
            optimizer.step()

            loss_list.append(float(loss))
            global_step += 1
            if global_step % args.logging_steps == 0:
                time_diff = time.time() - time_temp
                # TODO: 不要sum，直接累加
                loss_avg = sum(loss_list) / len(loss_list)
                accelerator.print(
                    "global step %d, epoch: %d, loss: %.5f, speed: %.2f step/s" % (global_step, epoch, loss_avg,
                                                                                   args.logging_steps / time_diff))
                time_temp = time.time()

            # if args.save_checkpoint and len(dev_dataset) > 0 and global_step % args.valid_steps == 0:
        if accelerator.is_local_main_process and args.save_checkpoint and len(dev_dataset) > 0:
            checkpoint_name = "model_%d" % global_step
            checkpoint_name_list.append(checkpoint_name)
            if len(checkpoint_name_list) > args.max_checkpoint_num:
                checkpoint_to_delete = checkpoint_name_list.pop(0)
                delete(args.save_dir, checkpoint_to_delete)
            save(args.save_dir, checkpoint_name, model, tokenizer)
            precision, recall, f1 = evaluate(model, metric, dev_loader, device=device)
            accelerator.print("Evaluation precision: %.5f, recall: %.5f, F1: %.5f" % (precision, recall, f1))
            if f1 > best_f1:
                accelerator.print(f"********** best F1 performence has been updated: {best_f1:.5f} --> {f1:.5f}")
                best_epoch, best_precision, best_recall, best_f1 = epoch, precision, recall, f1
                save(args.save_dir, 'model_best', model, tokenizer)
    if accelerator.is_local_main_process:
        save(args.save_dir, checkpoint_name='model', model=model, tokenizer=tokenizer)
    accelerator.print("Best F1: %.5f, the epoch is %d, precision is %.5f, recall is %.5f."
                      % (best_f1, best_epoch, best_precision, best_recall))
    accelerator.print('Finish. Train eval time cost: %.1fs.' % (time.time() - start_time))


def delete(save_dir, checkpoint_name):
    dir_to_delete = os.path.join(save_dir, checkpoint_name)
    if os.path.exists(dir_to_delete):
        shutil.rmtree(dir_to_delete)


def save(save_dir, checkpoint_name='model', model=None, tokenizer=None):
    save_dir = os.path.join(save_dir, checkpoint_name)
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)
    model_to_save = model
    if isinstance(model, DistributedDataParallel):
        model_to_save = model.module
    model_to_save.save_pretrained(save_dir)
    tokenizer.save_pretrained(save_dir)


if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument("--train_path", default=None, type=str, help="The path of train set.")
    parser.add_argument("--train_path_processed", default=None, type=str, help="经过doccano.py处理的结果，免去中间步骤.")
    parser.add_argument("--train_top", default=0, type=int, help="Select top k of train set to train.")
    parser.add_argument("--fewshot", default=0, type=int, help='读取数据时保证的few-shot number,达到该值后,不在读取数据。该参数仅对训练集有效。')
    parser.add_argument("--negative_ratio", default=3, type=int,
                        help="Used only for the extraction task, the ratio of positive and negative samples, number of "
                             "negtive samples = negative_ratio * number of positive samples.该参数仅对训练集有效，验证集全负采样 ")
    # parser.add_argument("--splits", default=[0.8, 0.2, 0.], type=float, nargs="*",
    #                     help="The ratio of samples in datasets. [0.6, 0.4, 0.] means 60% samples used for training, "
    #                          "40% for evaluation and 0% for test.该参数长度必须为0或者3.如果为3，则dev_path必须为空。目前测试集上必须是0.")
    parser.add_argument("--dev_path", default=None, type=str, help="The path of dev set.")
    parser.add_argument("--dev_path_processed", default=None, type=str, help="经过doccano.py处理的结果，免去中间步骤.")
    parser.add_argument("--dev_top", default=0, type=int, help="Select top k of dev set to evaluate.")

    parser.add_argument("--save_dir", default='./checkpoint', type=str,
                        help="The output directory where the model checkpoints will be written. ")

    parser.add_argument("--num_epochs", default=100, type=int, help="Total number of training epochs to perform.")
    parser.add_argument("--batch_size", default=8, type=int, help="Batch size per GPU/CPU for training.")
    parser.add_argument("--max_seq_len", default=512, type=int, help="The maximum input sequence length, "
                        "Sequences longer than this will be split automatically.")
    parser.add_argument("--learning_rate", default=1e-5, type=float, help="The initial learning rate for AdamW.")
    parser.add_argument("--weight_decay", default=0., type=float, help="The weight_decay for AdamW.")
    parser.add_argument('--device', default="cuda",
                        help="Select which device to train model, defaults to cuda 0.")
    parser.add_argument("--logging_steps", default=10, type=int, help="The interval steps to logging.")

    parser.add_argument("--uie_model_path", default=None, help='Select the pretrained model.')
    parser.add_argument("--tokenizer", default=None, type=str,
                        help='tokenizer的路径，默认不填写与uie_model_path or checkpoint存放在同一路径下。')

    parser.add_argument("--checkpoint", default=None, type=str,
                        help="该参数与uie_model_path参数互斥。当使用本参数时，type_auto_prompt_size和auto_prompt_len无效。")
    parser.add_argument("--save_checkpoint", action='store_true', help='是否保存checkpoint，使用该参数表示保存。')
    parser.add_argument("--max_checkpoint_num", default=5, type=int, help="最多能保存的checkpoint, 设置过大会比较耗费磁盘存储空间。")
    parser.add_argument("--shuffle", action='store_true',
                        help="Whether to shuffle the dataset, defaults to True.")
    parser.add_argument("--seed", default=1000, type=int, help="Random seed for initialization")
    parser.add_argument("--normal", action='store_true', help="全角转半角。")

    args = parser.parse_args()
    do_train()
    print('finish.')
