import jsonlines
import re

error_name = ['李雾',
              '李霁',
              '宝山',
              '贠宝山',
              '候有昌昌',
              '候有昌',
              '粗金龙',
              '俎金龙',
              '陈金山吴',
              '陈金山',
              '蒋成寿宇',
              '蒋成寿',
              '赵卿',
              '赵倬卿',
              '刘皮秋',
              '刘庋秋',
              '李臣',
              '李荩臣',
              '刘依尾',
              '刘依黾',
              '心龙',
              '郇心龙']


def exam(path):
    entity_type2values = dict()
    with jsonlines.open(path) as jr:
        examples = list(jr)
    for i, exam in enumerate(examples):
        text = exam['text']
        for e in exam['entities']:
            entity_text = text[e['start_offset']: e['end_offset']]
            # e['text'] = entity_text
            entity_type2values.setdefault(e['label'], []).append(entity_text)

    print(len(examples))
    print(sorted(entity_type2values.keys()))
    # ['入伍日期', '入党日期', '入团日期', '出生日期', '别名', '哪里人', '姓名', '性别', '战争战役', '战功', '政治面貌', '次数', '牺牲日期', '职务', '部队']
    for k, v in entity_type2values.items():
        print(k, len(v), sorted(set(v)))

    p1 = re.compile('\d{4}年|\d{4}年\d{1,2}月|\d{4}年\d{1,2}月\d{1,2}日')
    print([date for date in set(entity_type2values['出生日期']) if re.fullmatch(p1, date) is None])
    print([date for date in set(entity_type2values['入团日期']) if re.fullmatch(p1, date) is None])
    print([date for date in set(entity_type2values['入党日期']) if re.fullmatch(p1, date) is None])
    print([date for date in set(entity_type2values['牺牲日期']) if re.fullmatch(p1, date) is None])
    p2 = re.compile('[\u4e00-\u9fa5]+')
    print([xm for xm in set(entity_type2values['姓名']) if re.fullmatch(p2, xm) is None])
    print([xm for xm in set(entity_type2values['别名']) if re.fullmatch(p2, xm) is None])
    print([zw for zw in set(entity_type2values['职务']) if re.fullmatch(p2, zw) is None])
    print([battle for battle in set(entity_type2values['战争战役']) if re.fullmatch(p2, battle) is None])
    print([where for where in set(entity_type2values['哪里人']) if re.fullmatch(p2, where) is None])

    print('*' * 100)
    entity_type2values = dict()

    for exam in examples:
        text = exam['text']
        for e in exam['entities']:
            entity_text = text[e['start_offset']: e['end_offset']]
            if e['label'] in ['出生日期', '入团日期', '入党日期', '牺牲日期']:
                entity_text = entity_text.replace('.', '')
                entity_text = entity_text.replace('。', '')
                entity_text = entity_text.replace('：', '')
                entity_text = entity_text.replace(' ', '')
                if entity_text == '1927年农历6月29日':
                    entity_text = '1927年6月29日'
                if entity_text == '1952年1019日':
                    entity_text = '1952年10月19日'
                elif entity_text == '1953年3月10':
                    entity_text = '1953年3月10日'
                elif entity_text == '1931年春':
                    entity_text = '1931年'
                elif entity_text == '1952年秋':
                    entity_text = '1952年'
                elif entity_text == '1951年底':
                    entity_text = '1951年'
                elif entity_text == '10月' or entity_text == '10月21日':
                    pass
            if e['label'] == '姓名':
                entity_text = entity_text.replace('·', '')
                if entity_text == '再隆华':
                    entity_text = '冉隆华'
                if entity_text == '陈\u3000琦':
                    entity_text = '陈琦'
                elif entity_text == '刘，荣':
                    entity_text = '刘荣'
                for error_n, true_n in zip(error_name[0::2], error_name[1::2]):
                    if entity_text == error_n:
                        entity_text = true_n
            if e['label'] == '战功':
                if entity_text in ['一', '二', '三', '四']:
                    entity_text += '等功'
                if entity_text == '二等':
                    entity_text += '功'
                if entity_text == '3等功':
                    entity_text = '三等功'
            if e['label'] == '哪里人':
                if '吻山县' in entity_text:
                    entity_text = entity_text.replace('吻山县', '砀山县')
            if e['label'] == '职务':
                if entity_text == '班长,':
                    entity_text = '班长'
                if entity_text == '战土':
                    entity_text = '战士'
                if entity_text == '营长、团参谋长':
                    entity_text = '团参谋长'
                if entity_text == '主，任':
                    entity_text = '主任'
            if e['label'] == '部队':
                entity_text = entity_text.replace('帅', '师')
                entity_text = entity_text.replace('师师', '师')
            if e['label'] == '次数':
                if not entity_text.endswith('次'):
                    entity_text += '次'
                if entity_text == '两次':
                    entity_text = entity_text.replace('两', '2')
                if entity_text == '一次':
                    entity_text = entity_text.replace('一', '1')

            if e['label'] == '战争战役':
                if entity_text == '抗美援朝中':
                    entity_text = '抗美援朝'
                if entity_text == '抗美援朝':
                    entity_text = '抗美援朝战争'
            e['text'] = entity_text
            entity_type2values.setdefault(e['label'], []).append(e['text'])

    print([date for date in set(entity_type2values['出生日期']) if re.fullmatch(p1, date) is None])
    print([date for date in set(entity_type2values['入团日期']) if re.fullmatch(p1, date) is None])
    print([date for date in set(entity_type2values['入党日期']) if re.fullmatch(p1, date) is None])
    print([date for date in set(entity_type2values['牺牲日期']) if re.fullmatch(p1, date) is None])

    print([xm for xm in set(entity_type2values['姓名']) if re.fullmatch(p2, xm) is None])
    print([xm for xm in set(entity_type2values['别名']) if re.fullmatch(p2, xm) is None])
    print([zw for zw in set(entity_type2values['职务']) if re.fullmatch(p2, zw) is None])
    print([battle for battle in set(entity_type2values['战争战役']) if re.fullmatch(p2, battle) is None])
    print([where for where in set(entity_type2values['哪里人']) if re.fullmatch(p2, where) is None])
    return entity_type2values, examples


feats_sorted = {'特等功': 0, '五大功': 0, '一等功': 1, '三大功': 1, '四大功': 1, '集体一等功': 2, '二等功': 3, '一大功': 3,
                '二大功': 3, '三小功': 3, '双大功': 3, '大功': 3, '集体二等功': 4, '三等功': 5, '一小功': 5, '二小功': 5,
                '小功': 5}
order2feat = {0: '特等功', 1: '一等功', 2: '集体一等功', 3: '二等功', 4: '集体二等功', 5: '三等功'}
p = re.compile('\d')


def sort_key(feat):
    try:
        if feat.endswith('多次'):
            feat = feat[:-2]
        elif feat.endswith('次'):
            feat = feat[:re.search(p, feat).span()[0]]
    except Exception as e:
        print('#' * 100)
        print(feat)
        raise e
    if feat in feats_sorted:
        return feats_sorted[feat]
    else:
        return len(feats_sorted)


# ['入伍日期', '入党日期', '入团日期', '出生日期', '别名', '哪里人', '姓名', '性别', '战争战役', '战功', '政治面貌', '次数', '牺牲日期', '职务', '部队']
def save_to_xlsx(examples):
    import openpyxl
    from openpyxl import Workbook
    wb = Workbook()
    ws = wb.active
    ws.title = '人物'
    column_names = ['ID', 'name', 'feat', 'gender', 'birthplace', 'birthday', 'join_army_day', 'sacrifice_day', 'politics_status', 'battle', 'troop', 'duty']
    column_index = [chr(ord('A') + i) + '1' for i in range(len(column_names))]  # A1~M1
    for idx, name in zip(column_index, column_names):
        ws[idx] = name
    count = 0
    count_null = 0
    feat_other = []
    saved_exam = []
    for i, exam in enumerate(examples):
        d = dict([(label, '') for label in ['入伍日期', '入党日期', '入团日期', '出生日期', '别名', '哪里人', '姓名', '性别', '战争战役', '战功', '政治面貌', '次数', '牺牲日期', '职务', '部队']])
        d['战功备注'] = []
        d['战功'] = []
        for e in exam['entities']:
            entity_text = e['text']
            entiti_label = e['label']

            # 别名、次数也可有多个值，但该字段不需要。
            if entiti_label == '战功':
                d['战功'].append(entity_text)
                for rel in exam['relations']:
                    if rel['from_id'] == e['id']:
                        for e2 in exam['entities']:
                            if e2['id'] == rel['to_id']:
                                d['战功备注'].append(entity_text + e2['text'])
                                break
                        break
                else:
                    d['战功备注'].append(entity_text)

            else:
                d[entiti_label] = entity_text

        if d['姓名'] == '孙生禄':
            print()

        # d['战功备注'] = sorted(d['战功备注'], key=sort_key)
        # feat_ = d['战功备注'][0] if d['战功备注'] else ''
        # if feat_.endswith('多次'):
        #     feat_ = feat_[:-2]
        # elif feat_.endswith('次'):
        #     feat_ = feat_[:re.search(p, feat_).span()[0]]
        # d['战功备注'] = '，'.join(d['战功备注'])
        d['战功'] = sorted(d['战功'], key=sort_key)
        d['战功'] = '，'.join(d['战功'])
        # d['战功'] = feat_[:re.search(p, feat_).span()[0]] if feat_.endswith('次') else feat_
        # d['战功'] = order2feat[feats_sorted[d['战功']]] if d['战功'] in feats_sorted else d['战功']


        if d['战功备注']:
            d['战功备注'] += '。'
        yx_story = '，'.join([t for t in [d['性别'], d['哪里人'] + '人' if d['哪里人'] else '', d['部队'] + d['职务'],
                                         d['牺牲日期'] + '在' + d['战争战役'] + '中牺牲' if d['战争战役'] else
                                         (d['牺牲日期'] + '牺牲' if d['牺牲日期'] else '')] if t])
        if yx_story:
            yx_story += '。'
        # ['name', 'feat', 'gender', 'birthplace', 'birthday', 'join_army_day', 'sacrifice_day', 'politics_status',
        #  'battle', 'troop', 'duty']
        row = [d['姓名'], d['战功'], d['性别'], d['哪里人'], d['出生日期'], d['入伍日期'], d['牺牲日期'],
               d['政治面貌'], d['战争战役'], d['部队'], d['职务']]
        if i < 153:  # 特级英雄
            saved_exam.append(row)
            if any([ke in d['战功'] for ke in feats_sorted.keys()]):
                count += 1
            elif d['战功'] == '':
                count_null += 1
            else:
                feat_other.append(d['战功'])
        else:
            if any([ke in d['战功'] for ke in feats_sorted.keys()]):
                count += 1
                saved_exam.append(row)
            elif d['战功'] == '':
                count_null += 1
            else:
                feat_other.append(d['战功'])

    with jsonlines.open(r'D:\workspace\pycharm\nlp_project\yml6kbqa\data\二等功及以上英雄著录.jsonl') as jr:
        data_dict = [d for d in jr]
    maps = {'ID': 'ID',
             'name': '姓名',
             'feat': '功绩',
             'gender': '性别',
             'birthplace': '籍贯',
             'birthday': '出生日期',
             'join_army_day': '参军时间',
             'sacrifice_day': '牺牲时间',
             'politics_status': '政治面貌',
             'battle': '参加的战役名称',
             'troop': '所属军团',
             'duty': '职务'}
    for j, (row_, data) in enumerate(zip(saved_exam, data_dict)):
        row_.insert(0, str(j + 1))
        assert row_[1] == data['姓名']
        for idx, v in enumerate(row_):
            if not v:
                row_[idx] = data[maps[column_names[idx]]]
        ws.append(row_)
    wb.save('二等功及以上英雄_图库文件.xlsx')

    print('count:', count, 'count_null:', count_null)
    print(set(feat_other), len(feat_other))


entity_type2values1, examples = exam(path='data/low_confident_verified.jsonl')

entity_type2values2, examples2 = exam(path='data/烈士英明录第一卷修正版.jsonl')

not_exist = []
exist = []
for exam in reversed(examples):
    for e in exam['entities']:
        if e['label'] == '姓名':
            name = e['text']
            if name not in entity_type2values2['姓名']:
                not_exist.append(name)
                examples2.append(exam)
            else:
                exist.append(name)
            break
print(exist)
print(not_exist)

save_to_xlsx(examples2)
print()
