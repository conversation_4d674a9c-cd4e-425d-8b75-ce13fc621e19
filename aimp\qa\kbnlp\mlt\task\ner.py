from typing import Dict, List
from functools import partial

import torch
from torch import nn
from torch.utils.data.dataloader import DataLoader

from qa.kbnlp.mlt.task import Task
from qa.kbnlp.mlt.dataset import BaseDataset, SortingSamplerBuilder, pad_data
from qa.kbnlp.mlt.data_transform import TransformList, VocabDict
from qa.kbnlp.mlt.common.metric import F1
from qa.kbnlp.mlt.util import merge_locals_kwargs, ids_to_tags
from qa.kbnlp.mlt.layers.relative_transformer import RelativeTransformer
from qa.kbnlp.mlt.util.tensor_util import lengths_to_mask


#TODO: 该任务predict时收到batch_size影响。
class TransformerNer(nn.Module):
    def __init__(self, input_size, num_layer, hidden_size, num_heads, num_labels, dropout, after_norm=True,
                 position_embed_max_len=1024):
        super(TransformerNer, self).__init__()
        self.relative_transformer = RelativeTransformer(input_size, num_layer, hidden_size, num_heads, dropout,
                                                        after_norm, position_embed_max_len)
        self.out = nn.Linear(input_size, num_labels)

    def forward(self, embed, mask):
        embed = self.relative_transformer(embed, mask)
        score = self.out(embed)
        return score[:, 1:-1, :]


def read_ner_tsv_data(path, sep=None):
    data = []
    with open(path, 'r', encoding='utf-8') as fr:
        ws, ts = [], []
        for line in fr:
            line = line.strip().split(sep)[:2]
            if len(line) == 2:
                ws.append(line[0])
                ts.append(line[1])
            elif len(line) == 0:
                data.append((ws, ts))
                ws, ts = [], []
            else:
                raise ValueError('仅支持csv格式，前两列分别是单词和ner标注。')
        if ws and ts:
            data.append((ws, ts))
    return data


def get_k_fewshot_data(datas, shuffle=False, k=10):
    """
    Args:
    Returns: 返回k-few-shot的数据。
    """
    import numpy as np
    if k <= 0:
        return datas
    res = []
    labels = set()
    for ws, ls in datas:
        for w, l in zip(ws, ls):
            if l.startswith('S') or l.startswith('B'):
                labels.add(l)

    label_count = dict((l, 0) for l in labels)
    if shuffle:
        idx = np.random.permutation(len(datas))
        datas = [datas[i] for i in idx]
    for d in datas:
        temp = {}
        all_overflow = True
        ws, ls = d
        for w, l in zip(ws, ls):
            if l.startswith('B') or l.startswith('S'):
                if l in temp:
                    temp[l] += 1
                else:
                    temp[l] = 1
                    if label_count[l] < k:
                        all_overflow = False
        if not all_overflow:
            for l, c in temp.items():
                label_count[l] += temp[l]
            res.append(d)
    if shuffle:
        idx = np.random.permutation(len(res))
        res = [res[i] for i in idx]
    return res


class NerDataset(BaseDataset):
    def __init__(self, data: List[Dict], transform_list: TransformList = None):
        super().__init__(data, transform_list)
#
# input_size, num_layer, hidden_size, num_heads, num_labels, dropout, after_norm=True, position_embed_max_len=1024


class TeNer(Task):
    def __init__(self,
                 trn=None,
                 dev=None,
                 tst=None,
                 num_layer=2,
                 hidden_size=512,  # transformer中前馈神经网络的隐藏层大小。
                 num_heads=4,
                 drop_transformer=0.2,  # transformer中attention和前馈神经网络的drop大小。
                 after_norm=True,
                 position_embed_max_len=1024,
                 batch_size=None,
                 batch_max_tokens=None,
                 eval_batch_size=None,
                 eval_batch_max_tokens=None,
                 # dependencies: str = None,
                 lr=None,
                 separate_optimizer=False,
                 cls_is_bos=False,
                 sep_is_eos=False,
                 **kwargs,
                 ):
        super(TeNer, self).__init__(**merge_locals_kwargs(locals(), kwargs, excludes=['self', 'kwargs', '__class__']))
        self.vocabs = VocabDict()  # 至少需要一个tag的vocab
        self.num_layer = num_layer
        self.hidden_size = hidden_size
        self.num_heads = num_heads
        self.drop_transformer = drop_transformer
        self.after_norm = after_norm
        self.position_embed_max_len = position_embed_max_len

    def build_model(self, encoder_size, task_name, **kwargs):
        return TransformerNer(encoder_size, self.num_layer, self.hidden_size, self.num_heads,
                              len(self.vocabs['%s_tag' % task_name]),
                              self.drop_transformer, self.after_norm, self.position_embed_max_len)

    def build_sample(self, data):
        examples = []
        for i, d in enumerate(data):
            ws, ts = None, None
            if len(d) == 2:
                ws, ts = d
            else:
                ws = d
            examples.append({'original_input': ws})
            examples[-1]['_id'] = i
            if isinstance(ws, list):
                examples[-1]['token'] = ws
                if ts:
                    examples[-1]['ner_tag'] = ts
        return examples

    def build_transformList(self, mode, task_name, **kwargs):
        if mode == 'train' or mode == 'eval':
            def rename(sample, src_name, tar_name):
                sample[tar_name] = sample.pop(src_name)
                return sample

            rename_transform = partial(rename, src_name='ner_tag', tar_name='%s_tag' % task_name)
            return TransformList(rename_transform, self.vocabs)
        else:
            return TransformList()

    def build_dataset(self, data, transform_list, **kwargs):
        return NerDataset(data, transform_list=transform_list)

    def build_dataloader(self, dataset, batch_size=None, batch_max_tokens=None, shuffle=False, generator=None, gradient_accumulation=1,
                         use_effective_tokens=False, **kwargs):
        return DataLoader(dataset=dataset,
                          batch_sampler=SortingSamplerBuilder.build([data['subtoken_len'] for data in dataset],
                                                                    batch_size, batch_max_tokens, use_effective_tokens,
                                                                    shuffle, generator, gradient_accumulation),
                          collate_fn=partial(NerDataset.collate_fn, padding_value=0))

    def build_criterion(self, reduction='mean', **kwargs):
        return torch.nn.CrossEntropyLoss(reduction=reduction)

    def build_optimizer(self, decoder, **kwargs):
        pass

    def compute_loss(self, task_name, batch, out, mask, criterion):
        y = batch['%s_tag_ids' % task_name]
        loss = criterion(out[mask], y[mask])
        return loss

    def build_metric(self):
        return F1()

    def update_metric(self, prediction, batch, metric, task_name):
        for p, g in zip(prediction, tag_to_span(batch['%s_tag' % task_name])):
            pred = set(p)
            gold = set(g)
            metric(pred, gold)

    def feed_batch(self, mode: str, h: torch.FloatTensor, batch: Dict, output_dict: Dict, decoder: torch.nn.Module,
                   task_name):
        mask = lengths_to_mask(batch['token_len'] - 2)  # 在最后decoder时使用
        logits = decoder(h, mask=lengths_to_mask(batch['token_len']))
        output_dict[task_name] = {
            'output': logits,
            'mask': mask
        }

    def decode_output(self, mode, output_dict, batch, model, task_name, **kwargs):
        output_per_task = output_dict[task_name]
        output = output_per_task['output']
        mask = output_per_task['mask']
        predict_tag_ids = torch.argmax(output, dim=-1)
        predict_tags = ids_to_tags(predict_tag_ids.tolist(), mask.sum(1).tolist(), self.vocabs['%s_tag' % task_name])
        entity_span = tag_to_span(predict_tags)
        output_per_task['prediction'] = entity_span

    def prediction_to_result(self, pred, vocab, batch, output_dict, task_name, **kwargs):
        return spans_to_tokens(pred, batch['token'])

    def post_transform(self, task_name, batch, output_dict, device, **kwargs):
        output_dict.pop(task_name)


def spans_to_tokens(spans, tokens):
    entity_batch = []
    for entity_span_per_sent, tokens_per_sent in zip(spans, tokens):
        # entity = [(tokens_per_sent[begin: end], e_type, begin, end) for e_type, begin, end in entity_span_per_sent]
        entity = [{'entity': ''.join(tokens_per_sent[begin: end]), 'type': e_type, 'start': begin + 1, 'end': end + 1}
                  for e_type, begin, end in
                  entity_span_per_sent]  # 第一个单词索引记作1.
        entity_batch.append(entity)
    return entity_batch


def tag_to_span(batch_tags, merge_types=None):
    """
    :param batch_tags:
    :param merge_types: 默认为None。对于预先计算出的实体列表，进一步合并。对于merge_types指定某些实体种类，如果预先计算的实体列表中一些实体连接在一起（需要在句子中完全连接），且实体种类也相同则将他们合并。
    :return: 返回一个三元组列表，(实体类型，start, end)。end不包括在内。
    """
    spans = []
    for tags in batch_tags:
        entities = get_entities(tags)
        if merge_types and len(entities) > 1:
            merged_entities = []
            begin = 0
            for i in range(1, len(entities)):
                if entities[begin][0] != entities[i][0] or entities[i - 1][2] != entities[i][1] \
                        or entities[i][0] not in merge_types:
                    merged_entities.append((entities[begin][0], entities[begin][1], entities[i - 1][2]))
                    begin = i
            merged_entities.append((entities[begin][0], entities[begin][1], entities[-1][2]))
            entities = merged_entities
        spans.append(entities)
    return spans


def get_entities(seq, suffix=False):
    """Gets entities from BMOES或者BIOES序列。

    Args:
      seq(list): sequence of labels.
      suffix:  (Default value = False)

    Returns:
      list: list of (chunk_type, chunk_start, chunk_end).末尾不包括。
    """
    # # for nested list
    # if any(isinstance(s, list) for s in seq):
    #     seq = [item for sublist in seq for item in sublist + ['O']]

    prev_tag = 'O'
    prev_type = ''
    begin_offset = 0

    chunks = []
    for i, chunk in enumerate(seq + ['O']):  # 防止最后一个begin后面没有出现end的情况。
        if suffix:
            tag = chunk[-1]
            type_ = chunk[:-2]
        else:
            tag = chunk[0]
            type_ = chunk[2:]

        if end_of_chunk(prev_tag, tag, prev_type, type_):
            chunks.append((prev_type, begin_offset, i))
        if start_of_chunk(prev_tag, tag, prev_type, type_):
            begin_offset = i
        prev_tag = tag
        prev_type = type_
    return chunks


def end_of_chunk(prev_tag, tag, prev_type, type_):
    """检查是否前一个token是不是一个实体的结尾。BMOES或者BIOES均可以处理。

    Args:
      prev_tag: previous chunk tag.
      tag: current chunk tag.
      prev_type: previous type.
      type_: current type.

    Returns:
      chunk_end: boolean.

    """
    chunk_end = False

    if prev_tag == 'E' or prev_tag == 'S':
        chunk_end = True
    if (prev_tag == 'B' or prev_tag == 'I' or prev_tag == 'M') and (tag == 'B' or tag == 'O' or tag == 'S'):
        chunk_end = True

    if prev_tag != 'O' and prev_tag != '.' and prev_type != type_:
        chunk_end = True

    return chunk_end


def start_of_chunk(prev_tag, tag, prev_type, type_):
    """检查当前单词是不是一个实体的开始。BMOES或者BIOES均可以处理。

    Args:
      prev_tag: previous chunk tag.
      tag: current chunk tag.
      prev_type: previous type.
      type_: current type.

    Returns:
      chunk_start: boolean.

    """
    chunk_start = False

    if tag == 'B' or tag == 'S':
        chunk_start = True

    if (prev_tag == 'E' or prev_tag == 'S' or prev_tag == 'O') and (tag == 'E' or tag == 'I' or tag == 'M'):
        chunk_start = True

    if tag != 'O' and tag != '.' and prev_type != type_:
        chunk_start = True
    return chunk_start
