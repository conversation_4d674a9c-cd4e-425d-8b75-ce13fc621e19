# coding:utf-8
# Copyright (c) 2022  PaddlePaddle Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License"
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import base64
import json
import os
import re
import warnings
from copy import copy, deepcopy
from typing import Dict, List
from functools import partial
from PIL import Image
import numpy as np
import torch
from torch.utils.data import Dataset, DataLoader
from transformers import AutoTokenizer

# from ..layers import GlobalPointerForEntityExtraction, GPLinkerForRelationExtraction
from .models.information_extraction.information_extraction_model import UIE, UIEX  # UIEM

from .utils import get_bool_ids_greater_than, get_span
from .task import Task
from .utils import SchemaTree, dbc2sbc, get_id_and_prob  # gp_decode, DataCollatorGP
from kbnlp.scripts.information_extraction.temp_doc_parser import DocParser
from kbnlp.scripts.information_extraction.my_utils import sort_boxes
usage = r"""
         """

MODEL_MAP = {"UIE": UIE, "UIEX": UIEX}  # , "UIEM": UIEM

WORD_LIST_JOINER = ''


class UIETask(Task):
    """
    Universal Information Extraction Task.
    Args:
        task(string): The name of taskflow.
        model(string): The model name in the taskflow.
        kwargs (dict, optional): Additional keyword arguments passed along to the specific taskflow.
    """

    def __init__(self, task, model, schema=None, device='cpu', **kwargs):
        super().__init__(task=task, model=model, device=device, **kwargs)
        self._block_size = kwargs.get('block_size', 1000)
        self._doc_stride = 0  # TODO:添加支持
        self._position_prob = kwargs.get("position_prob", 0.5)
        self._layout_analysis = kwargs.get("layout_analysis", False)
        self._ocr_lang = kwargs.get("ocr_lang", "ch")
        self._schema_lang = kwargs.get("schema_lang", "ch")
        self._expand_to_a4_size = False if self._custom_model else True

        with open(os.path.join(self._task_path, "config.json")) as f:
            self._init_class = json.load(f)["architectures"][0]

        self._is_en = True if model in ["uie-base-en"] or self._schema_lang == "en" else False

        if self._init_class in ["UIEX"]:
            self._summary_token_num = 4  # [CLS] prompt [SEP] [SEP] text [SEP] for UIE-X
        else:
            self._summary_token_num = 3  # [CLS] prompt [SEP] text [SEP]

        self._usage = usage
        self._doc_parser = None
        self._schema_tree = None
        if schema is not None:
            self.set_schema(schema)

    def set_schema(self, schema):
        if isinstance(schema, dict) or isinstance(schema, str):
            schema = [schema]
        self._schema_tree = self._build_tree(schema)

    def _construct_model(self):
        """
        Construct the inference model for the predictor.
        """
        model_instance = MODEL_MAP[self._init_class].from_pretrained(self._task_path)
        self._model = model_instance
        if hasattr(self._model, 'conjunct'):
            self.conjunct = self._model.conjunct
        else:
            self.conjunct = '的'
        self._model.eval()

    def _construct_tokenizer(self):
        """
        Construct the tokenizer for the predictor.
        """
        self._tokenizer = AutoTokenizer.from_pretrained(self._task_path)

    def _preprocess(self, inputs, **kwargs):
        """
        Transform the raw text to the model inputs, two steps involved:
           1) Transform the raw text to token ids.
           2) Generate the other model inputs from the raw text and token ids.
        """
        inputs = inputs[0]
        inputs = self._check_input_text(inputs)
        inputs = self._parse_inputs(inputs)
        outputs = {}
        outputs["text"] = inputs
        return outputs

    def _check_input_text(self, inputs):
        if isinstance(inputs, dict) or isinstance(inputs, str):
            inputs = [inputs]
        if isinstance(inputs, list):
            input_list = []
            for i in range(len(inputs)):
                example = copy(inputs[i])
                if isinstance(inputs[i], str):
                    example = {'text': inputs[i]}
                if not isinstance(example, dict):
                    raise ValueError('无效输入格式。inputs必须是str或者dict或者一个list，如为list，则该list中的每个元素是str或者dict。')
                elif not ('text' in example or 'word' in example or 'image' in example):
                    raise ValueError("无效输入格式，当inputs是dict或者list[dict]时，每个字典中至少有'text'、'word'、'image'中的一个字段。")

                if 'bbox' in example and 'word' not in example:
                    raise ValueError('当bbox列表存在时必须提供相应的word列表。')
                if 'word' not in example and 'text' not in example:  # 此前已经判定过word、text、image至少存在一个,所以此时image必在字典中。
                    if isinstance(example['image'], str):  # ocr结果中可能有空列表，所以放在前面检查。
                        if os.path.isfile(example['image']):
                            if not self._doc_parser:
                                self._doc_parser = DocParser(ocr_lang=self._ocr_lang,
                                                             layout_analysis=self._layout_analysis, device=self.device)
                            parse_res = self._doc_parser.parse({"doc": example["image"]},
                                                               expand_to_a4_size=self._expand_to_a4_size)
                            bbox = []
                            word = []
                            offset_x, offset_y = parse_res["offset_x"], parse_res["offset_x"]
                            for segment in parse_res["layout"]:
                                org_box = segment[0]  # bbox before expand to A4 size
                                box = [
                                    org_box[0] + offset_x,
                                    org_box[1] + offset_y,
                                    org_box[2] + offset_x,
                                    org_box[3] + offset_y,
                                ]
                                if segment[1].strip():
                                    word.append(segment[1])
                                    bbox.append(box)

                            indices, bbox, _ = sort_boxes(bbox)
                            word = [word[ind] for ind in indices]
                            example['word'] = word
                            example['bbox'] = bbox
                            example['text'] = WORD_LIST_JOINER.join(word)
                        else:
                            raise ValueError('image字段存放图片的绝对路径。')
                    else:
                        NotImplementedError("仅支持传送图片绝对路径。")

                if 'word' in example:
                    word = example['word']
                    if (not isinstance(word, list)) or (word and not isinstance(word[0], str)):  # 空列表不报错
                        raise ValueError(f"word必须是一个字符串列表。word：{word}。")

                    if 'bbox' in example:
                        bbox = example['bbox']
                        if (not (isinstance(bbox, list) and len(word) == len(bbox)) or
                                (bbox and not (isinstance(bbox[0], list) and
                                 len(bbox[0]) == 4 and isinstance(bbox[0][0], (int, float))))):  # 空列表不报错
                            raise ValueError(f"bbox必须是和word同长的列表, 每个box都是长为4的整数列表。word：{word},bbox：{bbox}。")
                        word, bbox = [], []
                        for w, box in zip(example['word'], example['bbox']):
                            if w.strip():
                                word.append(w.strip())
                                bbox.append(box)
                        indices, bbox, _ = sort_boxes(bbox)
                        word = [word[ind] for ind in indices]
                        example['word'] = word
                        example['bbox'] = bbox
                    else:
                        word = [w.strip() for w in word if w.strip()]
                        example['word'] = word
                    text = WORD_LIST_JOINER.join(example['word'])
                    if 'text' in example:
                        if example['text'] != text:
                            warnings.warn(f"text和word字段发生冲突，丢弃原text字段。text:{example['text']},"
                                          f"'{WORD_LIST_JOINER}'.join(word):{text}。")
                            example['text'] = text
                    else:
                        example['text'] = text
                    if len(word) == 0:
                        warnings.warn(f'第{i}个样本在去除空词后，word列表为空列表。')
                        example['word'] = ['']
                        if 'bbox' in example:
                            example.pop('bbox')
                        example['text'] = ''
                elif 'text' in example:
                    if not isinstance(example["text"], str):
                        raise TypeError(
                            "Invalid inputs, the 'text' field should be string in the dict. but type of {} found!".format(
                                type(example["text"])
                            )
                        )
                    if len(example['text']) == 0:
                        warnings.warn(f'text字段是空字符串。')
                    example['word'] = [example['text']]
                if 'word' in example:
                    example['word'] = list(example['word'])
                if 'bbox' in example:
                    example['bbox'] = list(example['bbox'])
                input_list.append(example)
        else:
            raise ValueError('无效输入格式。inputs必须是str或者dict或者list，如为list，则该list中的每个元素是str或者dict。')
        # 执行完毕，保证word和text字段共存，且text与WORD_LIST_JOINER连接word的值相等。
        return input_list

    def _single_stage_predict(self, examples):
        short_examples, input_map = self._auto_splitter(examples)
        if self._init_class in ["UIEX"]:
            dataset = UIEXDataset(short_examples, self._tokenizer)
        else:
            dataset = UIEDataset(short_examples, self._tokenizer)
        dataloader = DataLoader(dataset, batch_size=self._batch_size, collate_fn=partial(dataset.collate,
                                                                                         device=self._device))

        sentence_ids = []
        probs = []
        for batch in dataloader:
            if self._init_class in ["UIEX"]:
                input_ids, token_type_ids, position_ids, attention_mask, bbox, image = batch['input_ids'], batch['token_type_ids'], batch['position_ids'], \
                         batch['attention_mask'], batch['bbox'], batch['image']
                inputs = (input_ids, token_type_ids, position_ids, attention_mask, bbox, image)
            else:
                inputs = batch['input_ids'], batch['attention_mask'], batch['token_type_ids'], batch['position_ids']

            res = self._model(*inputs, return_dict=True)
            start_prob, end_prob = res['start_prob'], res['end_prob']
            # list(zip([list(zip(text, probs)) for text, probs in (
            #     zip([self._tokenizer.convert_ids_to_tokens(idx) for idx in batch['input_ids']], start_prob.numpy()))],
            #          [list(zip(text, probs)) for text, probs in (
            #              zip([self._tokenizer.convert_ids_to_tokens(idx) for idx in batch['input_ids']],
            #                  end_prob.numpy()))]))
            start_ids_list = get_bool_ids_greater_than(start_prob.cpu().numpy(),
                                                       limit=self._position_prob, return_prob=True)
            end_ids_list = get_bool_ids_greater_than(end_prob.cpu().numpy(),
                                                     limit=self._position_prob, return_prob=True)

            for start_ids, end_ids, offset_map in zip(start_ids_list, end_ids_list, batch['offset_mapping']):
                span_set = get_span(start_ids, end_ids, with_prob=True)
                sentence_id, prob = get_id_and_prob(span_set, offset_map)  # 调试此句时需要注意，offset_map在该函数内部会被修改
                sentence_ids.append(sentence_id)
                probs.append(prob)
        results = self._convert_ids_to_results(short_examples, sentence_ids, probs)
        results = self._auto_joiner(results, input_map)
        return results

    def _auto_joiner(self, short_results, input_mapping):
        concat_results = []
        is_cls_task = False
        for short_result in short_results:
            if short_result == []:
                continue
            elif "start" not in short_result[0].keys() and "end" not in short_result[0].keys():
                is_cls_task = True
                break
            else:
                break
        for k, vs in input_mapping.items():
            if is_cls_task:
                cls_options = {}
                single_results = []
                for v in vs:
                    if len(short_results[v]) == 0:
                        continue
                    if short_results[v][0]["text"] not in cls_options.keys():
                        cls_options[short_results[v][0]["text"]] = [1, short_results[v][0]["probability"]]
                    else:
                        cls_options[short_results[v][0]["text"]][0] += 1
                        cls_options[short_results[v][0]["text"]][1] += short_results[v][0]["probability"]
                if len(cls_options) != 0:
                    cls_res, cls_info = max(cls_options.items(), key=lambda x: x[1])
                    concat_results.append([{"text": cls_res, "probability": cls_info[1] / cls_info[0]}])
                else:
                    concat_results.append([])
            else:
                single_results = []
                for v in vs:
                    single_results.extend(short_results[v])
                concat_results.append(single_results)
        return concat_results

    def _run_model(self, inputs):
        examples = inputs['text']
        results = []
        for i in range(0, len(examples) + self._block_size, self._block_size):
            examples = examples[i: i + self._block_size]
            if examples:
                examples = self._cache(examples)
                results.extend(self._predict(examples))
        inputs["result"] = results
        return inputs

    def _cache(self, examples):
        def box_norm(box, width, height):
            def clip(min_num, num, max_num):
                return min(max(num, min_num), max_num)

            x0, y0, x1, y1 = box
            x0 = clip(0, int((x0 / width) * 1000), 1000)
            y0 = clip(0, int((y0 / height) * 1000), 1000)
            x1 = clip(0, int((x1 / width) * 1000), 1000)
            y1 = clip(0, int((y1 / height) * 1000), 1000)
            if x1 < x0:
                warnings.warn('x0大于x1，文本可能过于倾斜或垂直文本框。')
                x0, x1 = x1, x0
            if y1 < y0:
                warnings.warn('y0大于y1，文本可能过于倾斜。')
                y0, y1 = y1, y0

            return [x0, y0, x1, y1]

        def process(examples):
            """
                把_auto_splitter中需要用到的重复数据准备好。
            Args:
                examples: List[Dict], dict中可能的字段组合，
                [image, text, word, bbox]：
                [image, text, word]：
                [image, text,]：
                [image, word, bbox]：
                [image, word]：
                [image]：

                [text, word, bbox]：
                [text, word]：
                [text,]：
                [word, bbox]：
                [word]:

            Returns:

            """
            if self._init_class in ['UIEX']:
                image_path2features = {}
                mean = (123.675, 116.280, 103.530)
                std = (58.395, 57.120, 57.375)
                text_pair = [d["word"] for d in examples]
                encoded_text_pair = self._tokenizer(text=text_pair,
                                                    add_special_tokens=False,
                                                    is_split_into_words=True,
                                                    return_offsets_mapping=True,
                                                    )

                for d, encoding in zip(examples, encoded_text_pair.encodings):
                    d['text_pair_ids'] = encoding.ids
                    d['text_pair_word_ids'] = encoding.word_ids
                    d['text_pair_offset_mapping'] = []
                    ahead_len = 0
                    if len(encoding.word_ids) > 0:
                        pre_word_id = encoding.word_ids[0]
                        for offset, w_id in zip(encoding.offsets, encoding.word_ids):
                            if w_id != pre_word_id:
                                ahead_len += len(d['word'][pre_word_id])
                            d['text_pair_offset_mapping'].append([offset[0] + ahead_len, offset[1] + ahead_len])
                            pre_word_id = w_id
                    if 'image' in d:
                        if isinstance(d['image'], str):
                            if os.path.isfile(d['image']):
                                file_path = d['image']
                                if file_path not in image_path2features:
                                    img = Image.open(file_path).convert("RGB")
                                    width = img.width
                                    height = img.height
                                    img = np.array(img.resize((224, 224), 1), dtype=np.float32)
                                    mean = np.array(mean, dtype=np.float32)
                                    std = np.array(std, dtype=np.float32)
                                    img -= mean
                                    img /= std
                                    # change to channel fist
                                    img = np.swapaxes(img, 1, 2)
                                    img = np.swapaxes(img, 1, 0)
                                    image_path2features[file_path] = (img, width, height)
                                    img = img
                                else:
                                    img, width, height = image_path2features[file_path]
                            else:
                                raise ValueError('image中需要存放文档图片的绝对路径。')
                        else:
                            raise NotImplementedError("暂时'image'字段只支持图片路径。")
                        d['image'] = img
                        if 'bbox' in d:
                            d['bbox'] = [box_norm(d['bbox'][w_id], width, height)
                                         for w_id in d['text_pair_word_ids']]
                        else:
                            d['bbox'] = None
                    else:
                        d['image'] = None
                        # 当image字段没有提供时，bbox视为无效。因为此时不知道image_width、image_height。
                        # TODO:支持传入image_width、image_height。
                        d['bbox'] = None
                    # 当未传入bbox时，bbox设置为None，交给后续的dataset来处理成全为0的bbox。
            else:
                # 英文可能要用空格，暂时不考虑英文，训练脚本暂时也只支持中文。
                text_pair = [''.join(d["word"]) if 'word' in d else d["text"] for d in examples]
                encoded_text_pair = self._tokenizer(text=text_pair,
                                                    add_special_tokens=False,
                                                    is_split_into_words=False,
                                                    return_offsets_mapping=True,
                                                    )
                for d, encoding in zip(examples, encoded_text_pair.encodings):
                    d['text_pair_ids'] = encoding.ids
                    d['text_pair_word_ids'] = encoding.word_ids
                    d['text_pair_offset_mapping'] = [[offset[0], offset[1]] for offset in encoding.offsets]
            return examples
        examples = process(examples)
        return examples

    def _predict(self, examples):
        return self._multi_stage_predict(examples)

    def _parse_inputs(self, inputs):
        """
        去除'text', 'word', 'bbox', 'image'以外的字段
        Args:
            inputs: List[dict]

        Returns:List[dict]

        """
        keys = ['text', 'word', 'bbox', 'image']
        inputs = [dict((k, v) for k, v in data.items() if k in keys) for data in inputs]
        return inputs

    def _auto_splitter(self, examples):
        """
        Split the raw texts automatically for model inference.
        Args:
            examples:
            max_seq_len (int): 用户设置的最大长度，该值代表text, textpair以及special token的组成的最大子词长度。
        return:
        """
        short_examples = []

        prompt = [d['prompt'] for d in examples]
        encoded_prompt = self._tokenizer(text=prompt,
                                         add_special_tokens=False,
                                         is_split_into_words=False,
                                         return_offsets_mapping=True,
                                         )

        input_map = {}
        idx = 0
        for cnt, (en_text, exam) in enumerate(zip(encoded_prompt.encodings, examples)):
            input_map[cnt] = []
            text_ids = en_text.ids
            text_offset_mapping = [[offset[0], offset[1]] for offset in en_text.offsets]
            text_pair_ids = exam['text_pair_ids']
            max_text_pair_len = self._max_seq_len - len(text_ids) - self._summary_token_num
            if max_text_pair_len <= 0:
                raise ValueError(f"max_seq_len设置过短max_seq_len:{self._max_seq_len},"
                                 f" prompt+specail:{len(text_ids) + self._summary_token_num}，请适当放大该值。")

            doc_stride = max_text_pair_len - 1 if self._doc_stride >= max_text_pair_len else self._doc_stride
            if self._doc_stride < 0:
                doc_stride = 0
            start = 0
            end = max_text_pair_len
            text_pair_spans = [(start, end)]
            while end < len(text_pair_ids):
                start = end - doc_stride
                end = start + max_text_pair_len
                text_pair_spans.append((start, end))
            text_pair_spans[-1] = (text_pair_spans[-1][0], len(text_pair_ids))
            for span in text_pair_spans:
                exam_ = copy(exam)
                exam_['text_ids'] = text_ids
                exam_['text_offset_mapping'] = text_offset_mapping
                exam_['text_pair_ids'] = text_pair_ids
                exam_['text_pair_span'] = span

                short_examples.append(exam_)
                input_map[cnt].append(idx)
                idx += 1
        return short_examples, input_map

    def _multi_stage_predict(self, datas):
        """
        Traversal the schema tree and do multi-stage prediction.
        Args:
            datas:
        Returns:
            list: a list of predictions, where the list's length
                equals to the length of `datas`
        """

        results = [{} for _ in range(len(datas))]
        # Input check to early return
        if len(datas) < 1 or self._schema_tree is None:
            return results
        # Copy to stay `self._schema_tree` unchanged
        schema_list = self._schema_tree.children[:]
        while len(schema_list) > 0:
            node = schema_list.pop(0)
            examples = []
            input_map = {}
            cnt = 0
            idx = 0
            if not node.prefix:
                for one_data in datas:
                    one_data = copy(one_data)
                    one_data['prompt'] = dbc2sbc(node.name)
                    examples.append(one_data)
                    input_map[cnt] = [idx]
                    idx += 1
                    cnt += 1
            else:
                for pre, one_data in zip(node.prefix, datas):
                    if len(pre) == 0:
                        input_map[cnt] = []
                    else:
                        for p in pre:
                            one_data = copy(one_data)
                            if self._is_en:
                                if re.search(r"\[.*?\]$", node.name):
                                    prompt_prefix = node.name[: node.name.find("[", 1)].strip()
                                    cls_options = re.search(r"\[.*?\]$", node.name).group()
                                    # Sentiment classification of xxx [positive, negative]
                                    prompt = prompt_prefix + p + " " + cls_options
                                else:
                                    prompt = node.name + p
                            else:
                                prompt = p + node.name
                            one_data['prompt'] = dbc2sbc(prompt)
                            examples.append(one_data)
                        input_map[cnt] = [i + idx for i in range(len(pre))]
                        idx += len(pre)
                    cnt += 1
            if len(examples) == 0:
                result_list = []
            else:
                with torch.no_grad():
                    result_list = self._single_stage_predict(examples)

            if not node.parent_relations:
                relations = [[] for i in range(len(datas))]
                for k, v in input_map.items():
                    for idx in v:
                        if len(result_list[idx]) == 0:
                            continue
                        if node.name not in results[k].keys():
                            results[k][node.name] = result_list[idx]
                        else:
                            results[k][node.name].extend(result_list[idx])
                    if node.name in results[k].keys():
                        relations[k].extend(results[k][node.name])
            else:
                relations = node.parent_relations
                for k, v in input_map.items():
                    for i in range(len(v)):
                        if len(result_list[v[i]]) == 0:
                            continue
                        if "relations" not in relations[k][i].keys():
                            relations[k][i]["relations"] = {node.name: result_list[v[i]]}
                        elif node.name not in relations[k][i]["relations"].keys():
                            relations[k][i]["relations"][node.name] = result_list[v[i]]
                        else:
                            relations[k][i]["relations"][node.name].extend(result_list[v[i]])
                new_relations = [[] for i in range(len(datas))]
                for i in range(len(relations)):
                    for j in range(len(relations[i])):
                        if "relations" in relations[i][j].keys() and node.name in relations[i][j]["relations"].keys():
                            for k in range(len(relations[i][j]["relations"][node.name])):
                                new_relations[i].append(relations[i][j]["relations"][node.name][k])
                relations = new_relations

            prefix = [[] for _ in range(len(datas))]
            for k, v in input_map.items():
                for idx in v:
                    for i in range(len(result_list[idx])):
                        if self._is_en:
                            prefix[k].append(" of " + result_list[idx][i]["text"])
                        else:
                            prefix[k].append(result_list[idx][i]["text"] + "的")

            for child in node.children:
                child.prefix = prefix
                child.parent_relations = relations
                schema_list.append(child)
        # results = self._add_bbox_info(results, data)
        return results

    # def _add_bbox_info(self, results, data):
    #     def _add_bbox(result, char_boxes):
    #         for vs in result.values():
    #             for v in vs:
    #                 if "start" in v.keys():
    #                     boxes = []
    #                     for i in range(v["start"], v["end"]):
    #                         cur_box = char_boxes[i][1]
    #                         if i == v["start"]:
    #                             box = cur_box
    #                             continue
    #                         _, cur_y1, cur_x2, cur_y2 = cur_box
    #                         if cur_y1 == box[1] and cur_y2 == box[3]:
    #                             box[2] = cur_x2
    #                         else:
    #                             boxes.append(box)
    #                             box = cur_box
    #                     if box:
    #                         boxes.append(box)
    #                     boxes = [[int(b) for b in box] for box in boxes]
    #                     v["bbox"] = boxes
    #                 if v.get("relations"):
    #                     _add_bbox(v["relations"], char_boxes)
    #         return result
    #
    #     new_results = []
    #     for result, one_data in zip(results, data):
    #         if "layout" in one_data.keys():
    #             layout = one_data["layout"]
    #             char_boxes = []
    #             for segment in layout:
    #                 sbox = segment[0]
    #                 text_len = len(segment[1])
    #                 if text_len == 0:
    #                     continue
    #                 if len(segment) == 2 or (len(segment) == 3 and segment[2] != "table"):
    #                     char_w = (sbox[2] - sbox[0]) * 1.0 / text_len
    #                     for i in range(text_len):
    #                         cbox = [sbox[0] + i * char_w, sbox[1], sbox[0] + (i + 1) * char_w, sbox[3]]
    #                         char_boxes.append((segment[1][i], cbox))
    #                 else:
    #                     cell_bbox = [(segment[1][i], sbox) for i in range(text_len)]
    #                     char_boxes.extend(cell_bbox)
    #
    #             result = _add_bbox(result, char_boxes)
    #         new_results.append(result)
    #     return new_results

    def _convert_ids_to_results(self, examples, sentence_ids, probs):
        """
        Convert ids to raw text in a single stage.
        """
        results = []
        for example, sentence_id, prob in zip(examples, sentence_ids, probs):
            if len(sentence_id) == 0:
                results.append([])
                continue
            result_list = []
            text = example["text"]
            prompt = example["prompt"]
            for i in range(len(sentence_id)):
                start, end = sentence_id[i]
                if start < 0 and end >= 0:
                    continue
                if end < 0:
                    start += len(prompt) + 1
                    end += len(prompt) + 1
                    result = {"text": prompt[start:end], "probability": prob[i]}
                    result_list.append(result)
                else:
                    result = {"text": text[start:end], "start": start, "end": end, "probability": prob[i]}
                    result_list.append(result)
            results.append(result_list)
        return results

    @classmethod
    def _build_tree(cls, schema, name="root"):
        """
        Build the schema tree.
        """
        schema_tree = SchemaTree(name)
        for s in schema:
            if isinstance(s, str):
                schema_tree.add_child(SchemaTree(s))
            elif isinstance(s, dict):
                for k, v in s.items():
                    if isinstance(v, str):
                        child = [v]
                    elif isinstance(v, list):
                        child = v
                    else:
                        raise TypeError(
                            "Invalid schema, value for each key:value pairs should be list or string"
                            "but {} received".format(type(v))
                        )
                    schema_tree.add_child(cls._build_tree(child, name=k))
            else:
                raise TypeError("Invalid schema, element should be string or dict, " "but {} received".format(type(s)))
        return schema_tree

    def _postprocess(self, inputs):
        """
        This function will convert the model output to raw text.
        inputs:_run_model方法的输出
        """
        return inputs["result"]


class UIEXDataset(Dataset):
    def __init__(self, examples: List[Dict], tokenizer):
        self.examples = examples
        self.tokenizer = tokenizer
        self.padding_img = np.zeros((3, 224, 224), dtype=np.float32)

    def __getitem__(self, item):
        exam = self.examples[item]
        text_ids = exam['text_ids']
        text_offset_mapping = exam['text_offset_mapping']

        span_l, span_r = exam['text_pair_span']
        short_text_pair_ids = exam['text_pair_ids'][span_l: span_r]
        s_text_pair_offset_mapping = exam['text_pair_offset_mapping'][span_l: span_r]

        sep_index = len(text_ids) + 1
        input_ids = self.tokenizer.build_inputs_with_special_tokens(exam['text_ids'], short_text_pair_ids)
        if 'bbox' in exam and exam['bbox'] is not None:
            bbox = [[0, 0, 0, 0]] * (sep_index + 2) + exam['bbox'][span_l: span_r] + [[1000, 1000, 1000, 1000]]
        else:
            bbox = [[0, 0, 0, 0]] * len(input_ids)
        if 'image' in exam and exam['image'] is not None:
            image = exam['image']
        else:
            image = self.padding_img
        offset_mapping = [[0, 0]] + text_offset_mapping + [[0, 0], [0, 0]] + s_text_pair_offset_mapping + [[0, 0]]
        token_type_ids = [1] * (sep_index + 1) + [0] * (len(input_ids) - sep_index - 1)
        attention_mask = [1] * len(input_ids)
        position_ids = list(range(len(input_ids)))
        ans = {'input_ids': input_ids,
               'attention_mask': attention_mask,
               'bbox': bbox,
               'image': image,
               'token_type_ids': token_type_ids,
               'position_ids': position_ids,
               'offset_mapping': offset_mapping}
        # 把下划线删掉
        ans = {k: ([v[i] for i, idx in enumerate(input_ids) if idx != 6] if k != 'image' else v)
               for k, v in ans.items()}
        return ans

    def __len__(self):
        return len(self.examples)

    def collate(self, batch, device='cpu'):  # List[Dict]
        max_length = max([len(exam['input_ids']) for exam in batch])
        for exam in batch:
            difference = max_length - len(exam['input_ids'])
            if difference > 0:
                exam['input_ids'] += [self.tokenizer.pad_token_id] * difference
                exam['attention_mask'] += [0] * difference
                exam['bbox'] += [[0, 0, 0, 0]] * difference
                exam['token_type_ids'] += [0] * difference
                exam['position_ids'] += [0] * difference
        batch = {key: [exam[key] for exam in batch] for key in batch[0].keys()}
        batch_ = {key: torch.tensor(v, device=device) for key, v in batch.items() if key != 'offset_mapping'}
        batch_['offset_mapping'] = batch['offset_mapping']
        batch = batch_
        return batch


class UIEDataset(Dataset):
    def __init__(self, examples: List[Dict], tokenizer):
        self.examples = examples
        self.tokenizer = tokenizer

    def __getitem__(self, item):
        exam = self.examples[item]
        text_ids = exam['text_ids']
        text_offset_mapping = exam['text_offset_mapping']

        span_l, span_r = exam['text_pair_span']
        short_text_pair_ids = exam['text_pair_ids'][span_l: span_r]
        s_text_pair_offset_mapping = exam['text_pair_offset_mapping'][span_l: span_r]

        sep_index = len(text_ids) + 1
        input_ids = self.tokenizer.build_inputs_with_special_tokens(exam['text_ids'], short_text_pair_ids)

        offset_mapping = [[0, 0]] + text_offset_mapping + [[0, 0]] + s_text_pair_offset_mapping + [[0, 0]]
        offset_mapping = deepcopy(offset_mapping)
        token_type_ids = [0] * (sep_index + 1) + [1] * (len(input_ids) - sep_index - 1)
        attention_mask = [1] * len(input_ids)
        position_ids = list(range(len(input_ids)))

        return {'input_ids': input_ids,
                'attention_mask': attention_mask,
                'token_type_ids': token_type_ids,
                'position_ids': position_ids,
                'offset_mapping': offset_mapping}

    def __len__(self):
        return len(self.examples)

    def collate(self, batch, device='cpu'):  # List[Dict]
        max_length = max([len(exam['input_ids']) for exam in batch])
        for exam in batch:
            difference = max_length - len(exam['input_ids'])
            if difference > 0:
                exam['input_ids'] += [self.tokenizer.pad_token_id] * difference
                exam['attention_mask'] += [0] * difference
                exam['token_type_ids'] += [0] * difference
                exam['position_ids'] += [0] * difference
        batch = {key: [exam[key] for exam in batch] for key in batch[0].keys()}
        batch_ = {key: torch.tensor(v, device=device) for key, v in batch.items() if key != 'offset_mapping'}
        batch_['offset_mapping'] = batch['offset_mapping']
        batch = batch_
        return batch
