# Copyright (c) 2022 PaddlePaddle Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import argparse
import re
from functools import partial
import jsonlines
from collections import defaultdict
import torch
from torch.utils.data import DataLoader
from kbnlp.scripts.information_extraction.utils import dbc2sbc
from transformers import AutoTokenizer
from kbnlp.scripts.information_extraction.metric import SpanEvaluator, VerboseSpanEvaluator
from log import logger
import numpy as np
from kbnlp.taskflow.models.information_extraction.information_extraction_model import UIE, AutoPromptUIE, AutoPromptUIEConfig
p = re.compile('\[[.+,]*.+\]')


# TODO: 添加评估关系的支持。
def evaluate(model, metric, data_loader, device):
    """
    Given a dataset, it evals model and computes the metric.
    Args:
        model(obj:`paddle.nn.Layer`): A model to classify texts.
        metric(obj:`paddle.metric.Metric`): The evaluation metric.
        data_loader(obj:`paddle.io.DataLoader`): The dataset loader which generates batches.
        device:
    """
    model.eval()
    metric.reset()
    with torch.no_grad():
        for batch in data_loader:
            start_ids = batch.pop('start_ids')
            end_ids = batch.pop('end_ids')
            prompt = batch.pop('prompt')
            span2label = batch.pop('span2label')
            offset_mapping = batch.pop('offset_mapping')
            batch = dict((k, v.to(device) if isinstance(v, torch.Tensor) else v) for k, v in batch.items())
            inputs = [batch['input_ids'], batch['attention_mask'], batch['token_type_ids'], batch['position_ids']]
            if 'auto_prompt_ids' in batch:
                inputs.append(batch['auto_prompt_ids'])
            res = model(*inputs)
            start_prob, end_prob = res['start_prob'], res['end_prob']
            metric.compute(
                start_prob.to('cpu'), end_prob.to('cpu'), start_ids, end_ids, prompt, span2label, offset_mapping.tolist())
            # metric.update(num_correct, num_infer, num_label)
        [precision, recall, f1_score, macro_p, macro_r, macro_f1,
         macro_f1_2, micro_p, micro_r, micro_f1, matrix, labels] = metric.accumulate()
        model.train()
    return precision, recall, f1_score, macro_p, macro_r, macro_f1, macro_f1_2, micro_p, micro_r, micro_f1, matrix, labels


def unify_prompt_name(prompt):
    # The classification labels are shuffled during finetuning, so they need
    # to be unified during evaluation.
    if re.search(r'\[.*?\]$', prompt):
        prompt_prefix = prompt[:prompt.find("[", 1)]
        cls_options = re.search(r'\[.*?\]$', prompt).group()[1:-1].split(",")
        cls_options = sorted(list(set(cls_options)))
        cls_options = ",".join(cls_options)
        prompt = prompt_prefix + "[" + cls_options + "]"
        return prompt
    return prompt


# def get_infer_model(model, tokenizer, data, device='cpu'):
#     model.eval()
#     dataset = UIEDataset(data)
#     loader = DataLoader(dataset,
#                         collate_fn=partial(model.collate, tokenizer=tokenizer, max_seq_len=args.max_seq_len),
#                         batch_size=1)
#     with torch.no_grad():
#         for b in loader:
#             b.pop('start_ids', None)
#             b.pop('end_ids', None)
#             b = dict((k, v.to(device) if isinstance(v, torch.Tensor) else v) for k, v in b.items())
#             torch._C._jit_set_profiling_mode(False)
#             inputs = [b['input_ids'], b['attention_mask'], b['token_type_ids'], b['position_ids']]
#             if 'auto_prompt_ids' in b:
#                 inputs.append(b['auto_prompt_ids'])
#             model = torch.jit.trace(model.to(device), inputs, strict=False)
#             logger.info('trace finished.')
#             break
#     return model


def do_eval():
    tokenizer = AutoTokenizer.from_pretrained(args.model_path)
    if args.model_type == 'auto':
        config = AutoPromptUIEConfig.from_pretrained(args.model_path)
        model = AutoPromptUIE(config)
        model.load_state_dict(
            torch.load(args.model_path + '/pytorch_model.bin', map_location=torch.device('cpu')))
        model.to(args.device)
    else:
        model = UIE.from_pretrained(args.model_path).to(args.device)

    text2label = defaultdict(lambda: defaultdict(set))
    with jsonlines.open(file=args.test_path, mode='r') as jr:
        dev_dataset = []
        for d in jr:
            if 'content' in d:
                d['text'] = d.pop('content')
            if args.normal:
                d['text'] = dbc2sbc(d['text'])
                d['prompt'] = dbc2sbc(d['prompt'])
            dev_dataset.append(d)
            if d['result_list']:
                label = d['prompt']
                for span_info in d['result_list']:
                    l, r = span_info['start'], span_info['end'] - 1
                    text2label[d['text']][(l, r)].add(label)
    for d in dev_dataset:
        d['span2label'] = text2label[d['text']]

    # collate = model.collate  # trace之后得到的ScriptModule没有collate属性。# TODO：把collate放到Module外边。

    def collate(examples, tokenizer, max_seq_len):
        tokenizer_outputs = model.collate(examples, tokenizer=tokenizer, max_seq_len=max_seq_len, pop_offset_mapping=False)
        tokenizer_outputs['prompt'] = [exam['prompt'] for exam in examples]
        tokenizer_outputs['span2label'] = [exam['span2label'] for exam in examples]
        assert len(tokenizer_outputs['prompt']) == len(tokenizer_outputs['input_ids'])
        return tokenizer_outputs
    # if args.trace:
    #     model = get_infer_model(model, tokenizer, dev_dataset[:1], device=args.device)
    class_dict = {}

    for data in dev_dataset:
        match = re.search(p, data['prompt'])
        if match:
            class_dict.setdefault('分类', []).append(data)
        # elif '的' in data['prompt']:  # 目的地
        #     class_dict.setdefault('关系', []).append(data)
        else:
            class_dict.setdefault('实体', []).append(data)
    logger.info('start eval.')
    from torch.utils.data.dataset import Dataset
    from typing import List, Dict

    class UIEDataset(Dataset):

        def __init__(self, data: List[Dict]):
            super().__init__()
            self.data = data

        def __getitem__(self, index):
            return self.data[index]

        def __len__(self):
            return len(self.data)
    for key in sorted(list(class_dict.keys()))[:]:
        dev_dataset = UIEDataset(class_dict[key])
        dev_loader = DataLoader(dev_dataset,
                                collate_fn=partial(collate, tokenizer=tokenizer, max_seq_len=args.max_seq_len),
                                batch_size=args.batch_size)

        metric = VerboseSpanEvaluator()
        precision, recall, f1, macro_p, macro_r, macro_f1, macro_f1_2, micro_p, micro_r, micro_f1, matrix, labels = evaluate(model, metric, dev_loader, args.device)
        logger.info("-----------------------------")
        logger.info("Evaluation Type: %s" % key)
        labels = [-1] + list(sorted(metric.labels))
        # print('labels:', labels)
        # print('label2pred_entity_span_sum', [metric.label2pred_entity_span_sum[l] for l in labels])
        # print('label2gold_entity_span_sum', [metric.label2gold_entity_span_sum[l] for l in labels])
        # print('label2dummy_entity_span_sum', [metric.label2dummy_entity_span_sum[l] for l in labels])
        # logger.info("Evaluation P: %.3f | R: %.3f | F1: %.3f. " %
        #             (precision * 100, recall * 100, f1 * 100))
        print('labels:', labels, 'matrix:', matrix,
              *['Class Name: %s' % l + '\n' + info for l, info in
                zip(labels[1:], ['P: %3.3f | R: %3.3f | F: %3.3f.' % (p * 100, r * 100, f * 100)
                                 for p, r, f in zip(precision[1:], recall[1:], f1[1:])])],
              # 'precision: ', np.around(precision * 100, 3),
              # 'recall:', np.around(recall * 100, 3),
              # 'f1:', np.around(f1 * 100, 3),
              'macro_p: %3.3f | macro_r: %3.3f | macro_f1: %3.3f' % (macro_p * 100, macro_r * 100, macro_f1 * 100),
              'macro_f1_2: %.3f' % (macro_f1_2 * 100),
              'micro_p: %.3f | micro_r: %.3f | micro_f1: %.3f' % (micro_p * 100, micro_r * 100, micro_f1 * 100),
              sep='\n')
    logger.info('eval finished.')


if __name__ == "__main__":
    # yapf: disable
    parser = argparse.ArgumentParser()

    parser.add_argument("--model_path", type=str, default=None, help="The path of saved model that you want to load.")
    parser.add_argument("--model_type", choices=['uie', 'auto'], default='uie', help='eval UIE or AutoPromptUIE model.')
    parser.add_argument("--trace", action='store_true', help='暂时不支持。whether trace the model.')
    parser.add_argument("--test_path", type=str, default=None, help="The path of test set.")
    parser.add_argument("--device", default='cuda', type=str)
    parser.add_argument("--batch_size", type=int, default=16, help="Batch size per GPU/CPU for training.")
    parser.add_argument("--max_seq_len", type=int, default=512, help="The maximum total input sequence length after tokenization.")
    parser.add_argument("--normal", action='store_true', help="调用dbc2sbc方法，将半角转换为全角字符。")
    parser.add_argument("--debug", action='store_true', help="Precision, recall and F1 score are calculated for each class separately if this option is enabled.")

    args = parser.parse_args()
    # yapf: enable

    do_eval()
