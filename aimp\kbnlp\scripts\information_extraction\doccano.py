# Copyright (c) 2022 PaddlePaddle Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import time
import argparse
import os
import random
from decimal import Decimal
import numpy as np
import json
import jsonlines
from utils import convert_ext_examples, convert_cls_examples, get_k_fewshot_data


def convert_example_to_prompt_format(file_path=None,
                                     transform=None,  # 对读取的单条数据做处理, callable.
                                     datas=None,
                                     save_dir=None,
                                     fewshot=0,
                                     top=0,
                                     negative_ratio=3,
                                     splits=(1.0, 0., 0.),
                                     task_type='ext',  # ext, cls, intent
                                     options=('正向', '负向'),
                                     prompt_prefix='情感倾向',
                                     shuffle=True,
                                     seed=1000,
                                     separator='##',
                                     **kwargs):
    random.seed(seed)
    np.random.seed(seed)

    tic_time = time.time()
    if not file_path and not datas:
        raise ValueError("Please input the correct path of doccano file or datas.")
    if file_path and datas:
        raise ValueError("can only input one of doccano file or datas.")
    if file_path:
        if not os.path.exists(file_path):
            raise ValueError("Please input the correct path of doccano file.")
        with open(file_path, "r", encoding="utf-8") as f:
            raw_examples = f.readlines()
            raw_examples = [json.loads(exam) for exam in raw_examples]
    else:
        raw_examples = datas

    if save_dir and not os.path.exists(save_dir):
        os.makedirs(save_dir)

    if len(splits) != 0 and len(splits) != 3:
        raise ValueError("Only [] or len(splits)==3 accepted for splits.")

    if len(splits) == 3 and not Decimal(str(splits[0])) + Decimal(str(splits[1])) + Decimal(
            str(splits[2])) == Decimal("1"):
        raise ValueError(
            "Please set correct splits, sum of elements in splits should be equal to 1."
        )

    if transform and callable(transform):
        raw_examples = [transform(exam) for exam in raw_examples]

    if shuffle:
        ids = np.random.permutation(len(raw_examples))
        raw_examples = [raw_examples[i] for i in ids]
    if top > 0:
        raw_examples = raw_examples[:top]
    if fewshot > 0:
        raw_examples = get_k_fewshot_data(raw_examples, save_dir=save_dir, shuffle=shuffle, k=fewshot)
        # print(len(raw_examples))

    def _create_ext_examples(examples,
                             negative_ratio,
                             prompt_prefix="情感倾向",
                             options=["正向", "负向"],
                             separator="##",
                             shuffle=False,
                             is_train=True,
                             ):
        # res = ()
        # schema = dict()
        # prompt_list = set()
        # for exam in examples:
        #     entity_id2label = {}
        #     for entity in exam['entities']:
        #         label = entity['label']
        #         prompt_list.add(label)
        #         if label not in schema:
        #             schema[label] = set()
        #         entity_id2label[entity['id']] = entity['label']
        #
        #     for relation in exam['relations']:
        #         type_ = relation['type']
        #         prompt_list.add('的' + type_)
        #         from_entity = entity_id2label[relation['from_id']]
        #         schema[from_entity].add(type_)
        # if return_prompt:
        #     prompt_list = list(prompt_list)
        #     res = (prompt_list,)
        # if return_schema:
        #     schema = [{k: list(v)} if v else k for k, v in schema.items()]
        #     res += (schema,)
        entities, relations, aspects = convert_ext_examples(
            examples, negative_ratio, prompt_prefix, options, separator,
            is_train)
        examples = entities + relations + aspects
        if shuffle:
            idx = np.random.permutation(len(examples))
            examples = [examples[i] for i in idx]
        return examples

    # TODO:支持分类任务
    def _create_cls_examples(examples, prompt_prefix, options, shuffle=False):
        examples = convert_cls_examples(examples, prompt_prefix, options)
        if shuffle:
            indexes = np.random.permutation(len(examples))
            examples = [examples[i] for i in indexes]
        return examples

    def _save_examples(save_dir, file_name, examples):
        count = 0
        save_path = os.path.join(save_dir, file_name)
        with open(save_path, "w", encoding="utf-8") as f:
            for example in examples:
                f.write(json.dumps(example, ensure_ascii=False) + "\n")
                count += 1
        print("Save %d examples to %s." % (count, save_path))

    if len(splits) == 0:
        if task_type == "ext":
            res = _create_ext_examples(raw_examples, negative_ratio,
                                       prompt_prefix, options,
                                       separator, shuffle)
        else:
            res = _create_cls_examples(raw_examples, prompt_prefix,
                                       options, shuffle)
        if save_dir:
            _save_examples(save_dir, "train.txt", res[0])
    else:
        if shuffle:
            indexes = np.random.permutation(len(raw_examples))
            raw_examples = [raw_examples[i] for i in indexes]

        i1, i2, _ = splits
        p1 = int(len(raw_examples) * i1)
        p2 = int(len(raw_examples) * (i1 + i2))

        if task_type == "ext":
            train_examples = _create_ext_examples(raw_examples[:p1],
                                                  negative_ratio,
                                                  prompt_prefix,
                                                  options, separator,
                                                  shuffle)
            dev_examples = _create_ext_examples(raw_examples[p1:p2],
                                                -1,
                                                prompt_prefix,
                                                options,
                                                separator,
                                                is_train=False)
            test_examples = _create_ext_examples(raw_examples[p2:],
                                                 -1,
                                                 prompt_prefix,
                                                 options,
                                                 separator,
                                                 is_train=False)
        elif task_type == 'cls':  # TODO:支持分类任务
            train_examples = _create_cls_examples(raw_examples[:p1],
                                                  prompt_prefix,
                                                  options,
                                                  shuffle)
            dev_examples = _create_cls_examples(raw_examples[p1:p2],
                                                prompt_prefix,
                                                options)
            test_examples = _create_cls_examples(raw_examples[p2:],
                                                 prompt_prefix,
                                                 options)
        elif task_type == 'cls_ext' or task_type == 'intent' or task_type == 'document_record':
            train_examples = _create_ext_examples(raw_examples[:p1],
                                                  negative_ratio,
                                                  prompt_prefix,
                                                  options, separator,
                                                  shuffle)
            dev_examples = _create_ext_examples(raw_examples[p1:p2],
                                                -1,
                                                prompt_prefix,
                                                options,
                                                separator,
                                                is_train=False)
            test_examples = _create_ext_examples(raw_examples[p2:],
                                                 -1,
                                                 prompt_prefix,
                                                 options,
                                                 separator,
                                                 is_train=False)
            if options:
                train_examples.extend(_create_cls_examples(raw_examples[:p1],
                                                           prompt_prefix,
                                                           options,
                                                           shuffle))
                dev_examples.extend(_create_cls_examples(raw_examples[p1:p2],
                                                         prompt_prefix,
                                                         options))
                test_examples.extend(_create_cls_examples(raw_examples[p2:],
                                                          prompt_prefix,
                                                          options))

        else:
            raise ValueError('task_type参数错误，支持ext、cls、intent。')

        if shuffle:
            idx = np.random.permutation(len(train_examples))
            train_examples = [train_examples[i] for i in idx]
            idx = np.random.permutation(len(dev_examples))
            dev_examples = [dev_examples[i] for i in idx]
            idx = np.random.permutation(len(test_examples))
            test_examples = [test_examples[i] for i in idx]

        if save_dir:
            _save_examples(save_dir, "train.txt", train_examples)
            _save_examples(save_dir, "dev.txt", dev_examples)
            _save_examples(save_dir, "test.txt", test_examples)
        res = train_examples, dev_examples, test_examples
    print('Finished! convert example costs %.2f seconds.' % (time.time() - tic_time))
    return res


if __name__ == "__main__":
    # yapf: disable
    parser = argparse.ArgumentParser()

    parser.add_argument("--file_path", default="../../raw_data/baidu_rte/doccano_ext_demo.jsonl", type=str, help="The jsonline file exported from doccano platform.")
    parser.add_argument("--save_dir", default=None, type=str, help="The path of data that you wanna save, 默认不保存。")
    parser.add_argument("--fewshot", default=0, type=int, help='读取数据时保证的few-shot number,达到该值后,不在读取数据。')
    parser.add_argument("--top", default=0, type=int, help='只读取数据集中前top个数据。')
    parser.add_argument("--negative_ratio", default=3, type=int, help="Used only for the extraction taskflow, the ratio of positive and negative samples, number of negtive samples = negative_ratio * number of positive samples")
    parser.add_argument("--splits", default=[1.0, 0., 0.], type=float, nargs="*", help="The ratio of samples in datasets. [0.6, 0.2, 0.2] means 60% samples used for training, 20% for evaluation and 20% for test.")
    parser.add_argument("--task_type", choices=['ext', 'cls', 'intent'], default="ext", type=str, help="Select taskflow type, ext for the extraction taskflow and cls for the classification taskflow, defaults to ext.")
    parser.add_argument("--options", default=["正向", "负向"], type=str, nargs="+", help="Used only for the classification taskflow, the options for classification")
    parser.add_argument("--prompt_prefix", default="情感倾向", type=str, help="Used only for the classification taskflow, the prompt prefix for classification")
    parser.add_argument("--shuffle", action='store_true', help="Whether to shuffle the labeled dataset, defaults to True.")
    parser.add_argument("--seed", type=int, default=1000, help="Random seed for initialization")
    parser.add_argument("--separator", type=str, default='##', help="Used only for entity/aspect-level classification taskflow, separator for entity label and classification label")
    args = parser.parse_args()
    # yapf: enable

    data_converted = convert_example_to_prompt_format(**args.__dict__)
    if len(data_converted) == 2 or len(data_converted) == 4:
        print(data_converted[-1])


# --file_path D:/workspace/pycharm/nlp_project/ymlkbqa/data/ner/pseudo_question_1_attr_pair.jsonl --save_dir D:/workspace/pycharm/nlp_project/ymlkbqa/data/ner/one_attr_pair/split_trim_underline_fake_relation --negative_ratio 1 --shuffle --splits 0.99 0.01 0.
