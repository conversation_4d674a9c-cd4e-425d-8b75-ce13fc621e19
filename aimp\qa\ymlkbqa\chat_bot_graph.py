#!/usr/bin/env python3
# coding: utf-8
# File: chatbot_graph.py
# Author: lhy<<EMAIL>,https://huangyong.github.io>
# Date: 18-10-4
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from .question_classifier2 import *
from .question_parser import *
# from py2neo import Graph
import requests
import json
import logging
import traceback

isDebug = True if sys.gettrace() else False
print('isDebug: ', isDebug)


def set_logfile_format():
    #构造一个logger实例，设置日志输出格式和记录级别
    # 定义一个logger实例，实例化logging，logger是记录日志的方法；
    logger = logging.getLogger()
    # handler是选择日志的输出地方，FileHandler记录日志到文件
    # logging.FileHandler：日志输出到文件，比如指定文件名，编码格式，日志文件的打开方式
    if not Path("./log").exists():
        Path("./log").mkdir(parents=True, exist_ok=True)
    fh = logging.FileHandler("./log/question_answer.log", encoding="utf-8", mode="a")
    # formater格式化输出日志的信息，比如时间、代码所在文件名、代码行号、日志级别名字、日志信息
    formatter = logging.Formatter("%(asctime)s %(filename)s [line:%(lineno)d] %(levelname)s %(message)s")
    fh.setFormatter(formatter)
    # 对象给logger实例添加hander
    logger.addHandler(fh)
    # 设置日志的记录级别
    logger.setLevel(logging.INFO)
    #logger的类为logging，具体logging类的方法,
    #比如：logging.info();logging.debug();logging.warning();
    #需要返回实例logger，方便其他地方调用
    return logger


class TuGraph:
    def __init__(self, login_url='http://lgraph:7090/login', user='admin', password='73@TuGraph', graph_name='archive'):
        self.login_url = login_url
        data = requests.post(self.login_url, json={'user': user, 'password': password})
        jwt = data.json()['jwt']
        _auth = {'Authorization': f'Bearer {jwt}'}
        self.headers = {'Accept': 'application/json; charset=UTF-8',
                        'Content-Type': 'application/json; charset=UTF-8'}
        self.headers.update(_auth)
        self.cypher_url = f'http://lgraph:7090/cypher'
        self.graph_name = graph_name

    def run(self, sql):
        try:
            query_data = {
                    "graph": self.graph_name,
                    "script": sql,
                }
            query_ret = requests.post(self.cypher_url, json=query_data, headers=self.headers)
            if query_ret.status_code != 200:
                print(query_ret)
            # print(len(query_ret.json()['result']))
            rets = [json.loads(res[0])['properties'] for res in query_ret.json()['result']]
            return rets
        except Exception as e:
            print(e)
            return []


class ChatBotGraph:
    """问答类"""
    def __init__(self):
        self.logger = set_logfile_format()
        self.logger.setLevel(logging.INFO)
        self.intents = {1: {'pattern': '[实体类型][实体实例]的[属性]是[属性值]。', 'display_limit': 2},
                        2: {'pattern': '[属性]是[属性值]的[实体类型]有[实体实例]。', 'display_limit': 2}}
        self.map = {'gender': '性别',
                     'birthday': '出生日期',
                     'birthplace': '籍贯',
                     'join_army_day': '入伍日期',
                     'join_party_day': '入党日期',
                     'sacrifice_day': '牺牲日期',
                     'duty': '职务',
                     'politics_status': '政治面貌',
                     'feat': '战功',
                     'battle': '战役',
                     'name': '姓名',
                     'troop': '部队'}
        self.search_limit = 100
        self.classifier = QuestionClassifier()
        self.parser = QuestionPaser()
        # self.g = Graph("http://192.168.1.229:7474/", auth=('neo4j', 'kubao'))
        self.g = TuGraph(login_url='http://lgraph:7090/login', user='admin', password='73@TuGraph', graph_name='archive')

    def search_tugraph(self, sql_input_output_info):
        results = []
        for info in sql_input_output_info:
            sql = info['cypher']
            intent = info['intent']
            input_fields = info['input_fields']
            output_keys = info['output_keys']
            rets = self.g.run(sql)
            if rets:
                for ret in rets:
                    if intent == 1:  # 查询属性
                        input_fields['ID'] = ret['ID']
                        output_fields = [{'attr': key.split('/')[0], 'attr_value': ret[key.split('/')[-1]]} for key in
                                         output_keys if key != 'ID']
                        for o_f in output_fields:
                            if o_f['attr'] in self.map:
                                o_f['attr'] = self.map[o_f['attr']]
                            if o_f['attr_value'] is None:
                                o_f['attr_value'] = ''
                        results.append({'intent': intent, 'input_fields': input_fields, 'output_fields': output_fields})
                    else:  # 查询实体
                        output_fields = {'entity_name': ret[output_keys[0].split('/')[-1]],
                                         'entity_type': output_keys[0].split('/')[0],
                                         'ID': ret['ID']}
                        input_fields_ = [{'attr': self.map[d['attr']] if d['attr'] in self.map else d['attr'],
                                          'attr_value': d['attr_value']} for d in input_fields]
                        results.append(
                            {'intent': intent, 'input_fields': input_fields_, 'output_fields': output_fields})
            else:
                if intent == 1:
                    pass
                else:
                    input_fields_ = [{'attr': self.map[d['attr']] if d['attr'] in self.map else d['attr'],
                                      'attr_value': d['attr_value']} for d in input_fields]
                    output_fields = {'entity_name': '',
                                     'entity_type': output_keys[0].split('/')[0],
                                     'ID': ''}
                    results.append({'intent': intent, 'input_fields': input_fields_, 'output_fields': output_fields})
        return results

    def search_graph(self, sql_input_output_info):
        res = []
        for d in sql_input_output_info:
            sql = d['cypher']
            intent = d['intent']
            input_fields = d['input_fields']
            output_keys = d['output_keys']
            nodes = self.g.run(sql).data()
            if nodes:
                for node in nodes:
                    if intent == 1:  # 查询属性
                        input_fields['ID'] = node['m']['ID']
                        output_fields = [{'attr': key.split('/')[0], 'attr_value': node['m'][key.split('/')[-1]]} for key in output_keys if key != 'ID']
                        for o_f in output_fields:
                            if o_f['attr'] in self.map:
                                o_f['attr'] = self.map[o_f['attr']]
                            if o_f['attr_value'] is None:
                                o_f['attr_value'] = ''
                        res.append({'intent': intent, 'input_fields': input_fields, 'output_fields': output_fields})
                    else:  # 查询实体
                        output_fields = {'entity_name': node['m'][output_keys[0].split('/')[-1]],
                                         'entity_type': output_keys[0].split('/')[0],
                                         'ID': node['m']['ID']}
                        input_fields_ = [{'attr': self.map[d['attr']] if d['attr'] in self.map else d['attr'],
                                         'attr_value': d['attr_value']} for d in input_fields]
                        res.append({'intent': intent, 'input_fields': input_fields_, 'output_fields': output_fields})
            else:
                if intent == 1:
                    pass
                else:
                    input_fields_ = [{'attr': self.map[d['attr']] if d['attr'] in self.map else d['attr'],
                                      'attr_value': d['attr_value']} for d in input_fields]
                    output_fields = {'entity_name': '',
                                     'entity_type': output_keys[0].split('/')[0],
                                     'ID': ''}
                    res.append({'intent': intent, 'input_fields': input_fields_, 'output_fields': output_fields})
        return res

    def chat_main(self, sent):
        try:
            # answer = '对不起，暂时还不了解该问题. ^_^'
            sqls = self.classifier.classify2(sent)
            # res = self.search_graph(sqls)
            res = self.search_tugraph(sqls)
            # raise ValueError('eee')
            if not isDebug:
                # 0表示没有判断出意图；1表示判断出意图（不确定检测到的结果是否为空）。
                label = 0 if not sqls else 1
                # print('*'*20)
                self.logger.info(sent + ' ' + str(label))
            return res
        except Exception as e:
            if isDebug:
                raise e
            else:
                logging.error(sent + '\n' + ''.join(traceback.format_exception(None, e, e.__traceback__)))
            return []

    def question_answer(self, question: str):
        """
        Args:
            self: ChatBotGraph类实例。
            question: 问句。
        Returns:
            最多返回的列表长度为self.limit
            返回一个以字典为元素的列表。字典一共三个key，value对。
            1，意图一，给定实体类型，查询相应的某几个属性类问句。
            key1为’intent‘，value1是对应的意图类型，1代表意图1；
            key2为’input_fields‘，value2是一个字典。用来记录问句中解析出来的实体类型(entity_type)和实体实例（entity_name）以及该实体在图库中的唯一标识(id)；
            key3是’output_fields‘,value3是一个列表，代表一个符合用户查询条件的属性结果列表。其中每一个元素都是一个字典。记录结果的属性(attr)和属性值(attr_value)对。
            如，问句是：‘宋元德和冯玉山的出生日期和战功？’，返回结果是：
                [
                 {'intent': 1, 'input_fields': {'entity_type': '人物', 'entity_name', '宋元德', 'id': 1001}, 'output_fields': [{'attr': '出生日期', 'attr_value': '1927年'}, {'attr': '战功', 'attr_value':'三等功'}]},
                 {'intent': 1, 'input_fields': {'entity_type': '人物', 'entity_name', '冯玉山', 'id': 1002}, 'output_fields': [{'attr': '出生日期', 'attr_value': '1926年'}, {'attr': '战功', 'attr_value':'二等功'}]},
                 {'intent': 1, 'input_fields': {'entity_type': '人物', 'entity_name', '冯玉山', 'id': 1003}, 'output_fields': [{'attr': '出生日期', 'attr_value': '1927年'}, {'attr': '战功', 'attr_value':'一等功'}]},
                ]


            2，意图二，给定属性值问具有该属性的实体的实例的问句。
            key1为’intent‘，value1是对应的意图类型,2代表意图2；
            key2为’input_fields‘，value2是一个列表，代表从问句中抽取出来的所有属性条件。其中每一个元素都是一个字典。记录属性(attr)和属性值(attr_value)对。
            key3是’output_fields‘,value3是一个字典，其代表符合问句条件的一个结果实体。用来记录查询出来的实体类型(entity_type)和实体实例（entity_name）以及该实体在图库中的唯一标识(id)；
            如，问句是：‘1930年8月出生的安徽英雄’，返回结果是：
            [
             {'intent': 2, 'input_fields': [{'attr': '出生日期', 'attr_value': '1930年'}, {'attr': '籍贯', 'attr_value': '安徽'}], 'output_fields': {'entity_type': '人物', 'entity_name': '张某', 'id': 10086}},
             {'intent': 2, 'input_fields': [{'attr': '出生日期', 'attr_value': '1930年'}, {'attr': '籍贯', 'attr_value': '安徽'}], 'output_fields': {'entity_type': '人物', 'entity_name': '李某', 'id': 10087}}
            ]
            3，不属于以上意图，返回空列表。

        """
        answer = {}
        return answer

    """
        例如，用户定义的实体问题模板： '[实体类型][实体实例]的[属性]是[属性值]。'；属性问题模板：'[属性]是[属性值]的[实体类型]有[实体实例]。'
        即，有intents字典：
        一：现有某用户提出问句：'宋元德和冯玉山的出生日期和战功？'
            1，经过方法question_answer后,返回如下形式：
            [
             {'intent': 1, 'input_fields': {'entity_type': '人物', 'entity_name', '宋元德', 'id': 1001}, 'output_fields': [{'attr': '出生日期', 'attr_value': '1927年'}, {'attr': '战功', 'attr_value':'三等功'}]},
             {'intent': 1, 'input_fields': {'entity_type': '人物', 'entity_name', '冯玉山', 'id': 1002}, 'output_fields': [{'attr': '出生日期', 'attr_value': '1926年'}, {'attr': '战功', 'attr_value':'二等功'}]},
             {'intent': 1, 'input_fields': {'entity_type': '人物', 'entity_name', '冯玉山', 'id': 1003}, 'output_fields': [{'attr': '出生日期', 'attr_value': '1927年'}, {'attr': '战功', 'attr_value':'一等功'}]},
            ]

            2，再将上步结果经过美化，目标得出一下结果：
            人物宋元德的出生日期是1927年，战功是三等功；人物冯玉山的出生日期是1926年，战功是二等功；人物冯玉山的出生日期是1927年，战功是一等功。
            注：intent值为1代表意图1，也就取出模板1。
                pattern为对应的答句模板。
                display_limit为答句中显示上限。
                input_fields是一个字典，有三个值对，其中’entity_type‘代表’实体类型‘，’entity_name‘代表’实体实例‘，’id‘:1001代表该实体的唯一标识，
                如，{'entity_type': '人物', 'entity_name', '宋元德', 'id': 1001}代表宋元德的实体类型是人物，他的唯一标志id是1001。
                output_fields是一个列表，其中每一个元素是一个字典。该字典有两个值对，‘attr’代表实体的一个’属性‘，’attr_value‘代表对应的’属性值‘对。如{'attr': '出生日期', 'attr_value': '1927年'}代表出生日期是1927年。


        二：又有某用户提出问句：'1930年8月出生的安徽英雄？'
            1，经过方法question_answer后,返回如下形式：   
            [
             {'intent': 2, 'input_fields': [{'attr': '出生日期', 'attr_value': '1930年'}, {'attr': '籍贯', 'attr_value': '安徽'}], 'output_fields': {'entity_type': '人物', 'entity_name': '张某', 'id': 10086}},
             {'intent': 2, 'input_fields': [{'attr': '出生日期', 'attr_value': '1930年'}, {'attr': '籍贯', 'attr_value': '安徽'}], 'output_fields': {'entity_type': '人物', 'entity_name': '李某', 'id': 10087}}
            ]
            2，再将上步结果经过美化，目标得出如下结果：
            出生日期是1930年8月，籍贯是安徽的人物有张某、李某。
            注：intent值为2代表意图2，也就取出模板2。
                input_fields是一个列表，其中每一个元素是一个字典。该字典有两个值对，‘attr’代表实体的一个’属性‘，’attr_value‘代表对应的’属性值‘对。如{'attr': '出生日期', 'attr_value': '1927年'}代表出生日期是1927年。
                output_fields是一个字典，有三个值对，其中’entity_type‘代表’实体类型‘，’entity_name‘代表’实体实例‘，’id‘:10086代表该实体的唯一标识，
                如，{'entity_type': '人物', 'entity_name', '张某', 'id': 1001}代表宋元德的实体类型是人物，他的唯一标志id是1001。


    """


if __name__ == '__main__':
    handler = ChatBotGraph()
    while 1:
        question = input('用户:')
        answer = handler.chat_main(question)
        print('回答:', answer)

