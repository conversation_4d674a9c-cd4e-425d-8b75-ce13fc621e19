from abc import ABC, abstractmethod
from typing import List, Dict, Optional
import random

import torch
from torch.utils.data.dataset import Dataset
from torch.utils.data.dataloader import <PERSON><PERSON><PERSON><PERSON>, Sampler

from kbnlp.mlt.data_transform import TransformList
from kbnlp.mlt.util.tensor_util import pad_data
from kbnlp.mlt.common.configurable import AutoConfigurable


class BaseDataset(Dataset, ABC):

    def __init__(self, data: List[Dict], transform_list: TransformList = None):
        super().__init__()
        self.data = data
        if transform_list:
            for d in data:
                transform_list(d)

    def __getitem__(self, index):
        return self.data[index]

    def __len__(self):
        return len(self.data)

    @staticmethod
    def collate_fn(batch, padding_value=0, pad_keys: Optional[List] = None,
                   extra_pad_keys: Optional[List] = None, **kwargs):

        """
        :param batch: 一个字典。
        :param padding_value: 默认为0.
        :param pad_keys: 指定基础的要转化成张量的signature。默认值为None时，将转化以span,ids,len结尾的signature。
        :param extra_pad_keys: 额外指定要转化为张量的signature。默认为None。
        :param kwargs:
        :return: 返回batch对象，将pad_keys和extra_pad_keys指定的signature转化为张量。
        """

        examples = dict()
        for exam in batch:
            for k, v in exam.items():
                if k not in examples:
                    examples[k] = []
                examples[k].append(v)
        keys = set()
        if pad_keys is None:
            pad_keys = [key for key in examples.keys() if
                        key.endswith('ids') or key.endswith('span') or key.endswith('tail') or key.endswith('len')]
        keys.update(pad_keys)
        if extra_pad_keys is not None:
            keys.update(extra_pad_keys)
        for key in keys:
            if key in examples:
                examples[key] = pad_data(examples[key], padding_value, dtype=torch.long)
        return examples


class SortingSampler(Sampler):
    # noinspection PyMissingConstructor
    def __init__(self, lengths: List[int], batch_size=None, batch_max_tokens=None, use_effective_tokens=False,
                 shuffle=False, generator=None) -> None:
        """A sampler which sort samples according to their lengths. It takes packagea continuous chunk of sorted samples to
        make packagea batch.

        Args:
            lengths: Lengths of each sample, usually measured by number of tokens.
            batch_max_tokens: Maximum tokens per batch.
            use_effective_tokens: Whether to 统计pad的单词 when applying the `batch_max_tokens`.
            batch_size: Maximum samples per batch.
            shuffle: ``True`` to shuffle batches and samples in packagea batch.
        """
        # assert any([batch_size, batch_max_tokens]), 'At least one of batch_size and batch_max_tokens is required'
        self.shuffle = shuffle
        self.batch_size = batch_size
        # self.batch_max_tokens = batch_max_tokens
        self.batch_indices = []
        self.generator = generator  # 分布式训练时使用shuffle时，必须提供该参数，且所有进程的generator的种子必须相同。
        self.drop_last = False  # Accelerate包要求BatchSampler必须提供此属性。
        num_tokens = 0
        mini_batch = []
        for i in torch.argsort(torch.tensor(lengths), descending=True).tolist():
            # if batch_max_tokens:
            effective_tokens = lengths[i] if (not mini_batch or not use_effective_tokens) else lengths[mini_batch[0]]
            if (batch_max_tokens is None or num_tokens + effective_tokens <= batch_max_tokens) and (
                    batch_size is None or len(mini_batch) < batch_size):
                mini_batch.append(i)
                num_tokens += effective_tokens
            else:
                if not mini_batch:  # this sequence is longer than  batch_max_tokens
                    mini_batch.append(i)
                    self.batch_indices.append(mini_batch)
                    mini_batch = []
                    num_tokens = 0
                else:
                    self.batch_indices.append(mini_batch)
                    mini_batch = [i]
                    num_tokens = effective_tokens
        if mini_batch:
            self.batch_indices.append(mini_batch)
        # print(len(max(self.batch_indices, key=len)))

    def __iter__(self):
        if self.shuffle:
            generator = self.generator
            if generator is None:
                generator = torch.Generator()
                generator.manual_seed(int(torch.empty((), dtype=torch.int64).random_().item()))
            for i in torch.randperm(len(self), generator=generator).tolist():
                yield self.batch_indices[i]
        else:
            for batch in self.batch_indices:
                yield batch

    def __len__(self) -> int:
        return len(self.batch_indices)


class SamplerBuilder(AutoConfigurable, ABC):
    @staticmethod
    @abstractmethod
    def build(lengths: List[int], batch_size=None, batch_max_tokens=None, shuffle=False, gradient_accumulation=1,
              **kwargs) -> Sampler:
        """Build packagea ``Sampler`` given statistics of samples and other arguments.

        Args:
            lengths: The lengths of samples.
            shuffle: ``True`` to shuffle batches. Note samples in each mini-batch are not necessarily shuffled.
            gradient_accumulation: Number of mini-batches per update step.
            **kwargs: Other arguments to be passed to the constructor of the sampler.
        """
        pass

    @staticmethod
    def scale(batch_size, batch_max_tokens, gradient_accumulation):
        r"""Scale down the ``batch_size`` and ``batch_max_tokens`` to :math:`\frac{1}{\text{gradient_accumulation}}`
        of them respectively.

        Args:
            gradient_accumulation: Number of mini-batches per update step.

        Returns:
            tuple(int,int): batch_size, batch_max_tokens
        """
        if gradient_accumulation:
            if batch_size:
                batch_size //= gradient_accumulation
            if batch_max_tokens:
                batch_max_tokens //= gradient_accumulation
        return batch_size, batch_max_tokens


class SortingSamplerBuilder(SamplerBuilder):
    @staticmethod
    def build(lengths: List[int], batch_size=None, batch_max_tokens=None, use_effective_tokens=False, shuffle=False, generator=None, gradient_accumulation=1,
              **kwargs) -> Sampler:
        batch_size, batch_max_tokens = SortingSamplerBuilder.scale(batch_size, batch_max_tokens, gradient_accumulation)
        return SortingSampler(lengths, batch_size, batch_max_tokens, use_effective_tokens, shuffle, generator)
