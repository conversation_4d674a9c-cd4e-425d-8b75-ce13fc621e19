# Copyright (c) 2022 PaddlePaddle Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.


import six
import abc
import re
from copy import deepcopy
import numpy as np
from collections import defaultdict
DUMMY_LABEL = -1
p = re.compile('\[[.+,]*.+\]')


@six.add_metaclass(abc.ABCMeta)
class Metric(object):
    r"""
    Base class for metric, encapsulates metric logic and APIs
    Usage:

        .. code-block:: text

            m = SomeMetric()
            for prediction, label in ...:
                m.update(prediction, label)
            m.accumulate()

    Advanced usage for :code:`compute`:

    Metric calculation can be accelerated by calculating metric states
    from model outputs and labels by build-in operators not by Python/NumPy
    in :code:`compute`, metric states will be fetched as NumPy array and
    call :code:`update` with states in NumPy format.
    Metric calculated as follows (operations in Model and Metric are
    indicated with curly brackets, while data nodes not):

        .. code-block:: text

                 inputs & labels              || ------------------
                       |                      ||
                    {model}                   ||
                       |                      ||
                outputs & labels              ||
                       |                      ||    tensor data
                {Metric.compute}              ||
                       |                      ||
              metric states(tensor)           ||
                       |                      ||
                {fetch as numpy}              || ------------------
                       |                      ||
              metric states(numpy)            ||    numpy data
                       |                      ||
                {Metric.update}               \/ ------------------

    Examples:

        For :code:`Accuracy` metric, which takes :code:`pred` and :code:`label`
        as inputs, we can calculate the correct prediction matrix between
        :code:`pred` and :code:`label` in :code:`compute`.
        For examples, prediction results contains 10 classes, while :code:`pred`
        shape is [N, 10], :code:`label` shape is [N, 1], N is mini-batch size,
        and we only need to calculate accurary of top-1 and top-5, we could
        calculate the correct prediction matrix of the top-5 scores of the
        prediction of each sample like follows, while the correct prediction
        matrix shape is [N, 5].

          .. code-block:: text

              def compute(pred, label):
                  # sort prediction and slice the top-5 scores
                  pred = paddle.argsort(pred, descending=True)[:, :5]
                  # calculate whether the predictions are correct
                  correct = pred == label
                  return paddle.cast(correct, dtype='float32')

        With the :code:`compute`, we split_trim_underline_fake_relation some calculations to OPs (which
        may run on GPU devices, will be faster), and only fetch 1 tensor with
        shape as [N, 5] instead of 2 tensors with shapes as [N, 10] and [N, 1].
        :code:`update` can be define as follows:

          .. code-block:: text

              def update(self, correct):
                  accs = []
                  for i, k in enumerate(self.topk):
                      num_corrects = correct[:, :k].sum()
                      num_samples = len(correct)
                      accs.append(float(num_corrects) / num_samples)
                      self.total[i] += num_corrects
                      self.count[i] += num_samples
                  return accs
    """

    def __init__(self):
        pass

    @abc.abstractmethod
    def reset(self):
        """
        Reset states and result
        """
        raise NotImplementedError("function 'reset' not implemented in {}.".
                                  format(self.__class__.__name__))

    @abc.abstractmethod
    def update(self, *args):
        """
        Update states for metric

        Inputs of :code:`update` is the outputs of :code:`Metric.compute`,
        if :code:`compute` is not defined, the inputs of :code:`update`
        will be flatten arguments of **output** of mode and **label** from data:
        :code:`update(output1, output2, ..., label1, label2,...)`

        see :code:`Metric.compute`
        """
        raise NotImplementedError("function 'update' not implemented in {}.".
                                  format(self.__class__.__name__))

    @abc.abstractmethod
    def accumulate(self):
        """
        Accumulates statistics, computes and returns the metric value
        """
        raise NotImplementedError(
            "function 'accumulate' not implemented in {}.".format(
                self.__class__.__name__))

    @abc.abstractmethod
    def name(self):
        """
        Returns metric name
        """
        raise NotImplementedError("function 'name' not implemented in {}.".
                                  format(self.__class__.__name__))

    def compute(self, *args):
        """
        This API is advanced usage to accelerate metric calculating, calulations
        from outputs of model to the states which should be updated by Metric can
        be defined here, where Paddle OPs is also supported. Outputs of this API
        will be the inputs of "Metric.update".

        If :code:`compute` is defined, it will be called with **outputs**
        of model and **labels** from data as arguments, all outputs and labels
        will be concatenated and flatten and each filed as a separate argument
        as follows:
        :code:`compute(output1, output2, ..., label1, label2,...)`

        If :code:`compute` is not defined, default behaviour is to pass
        input to output, so output format will be:
        :code:`return output1, output2, ..., label1, label2,...`

        see :code:`Metric.update`
        """
        return args


class VerboseSpanEvaluator(Metric):

    def __init__(self):
        super(VerboseSpanEvaluator, self).__init__()
        self.labels = set()
        self.matrix = defaultdict(lambda: defaultdict(int))
        self.label2gold_entity_span_sum = defaultdict(int)
        self.label2pred_entity_span_sum = defaultdict(int)
        self.label2dummy_entity_span_sum = defaultdict(int)
        self.conjunct = '的'

    def compute(self, start_probs, end_probs, gold_start_ids, gold_end_ids, prompt, span2label, offset_mapping):
        """

        Args:
            start_probs:
            end_probs:
            gold_spans:
            prompt:
            span2label: list, 列表中每个元素记录该文本中所有的span（left、right的二元组，inclusive）到label（一个set）的映射。
        Returns:

        """
        pred_start_ids = get_bool_ids_greater_than(start_probs)
        pred_end_ids = get_bool_ids_greater_than(end_probs)
        gold_start_ids = get_bool_ids_greater_than(gold_start_ids.tolist())
        gold_end_ids = get_bool_ids_greater_than(gold_end_ids.tolist())
        for predict_start_ids, predict_end_ids, label_start_ids, label_end_ids, prompt_, span2l, offset in zip(
                pred_start_ids, pred_end_ids, gold_start_ids, gold_end_ids, prompt, span2label, offset_mapping):
            pred_set = get_span(predict_start_ids, predict_end_ids)
            label_set = get_span(label_start_ids, label_end_ids)
            num_correct = len(pred_set & label_set)
            match = re.search(p, prompt_)
            if not match:
                # if self.conjunct in prompt_: # 目的地
                #     prompt_ = prompt_[prompt_.rfind(self.conjunct):]
                pred_l_of_span = prompt_
                real_l_of_span = prompt_
                self.matrix[pred_l_of_span][real_l_of_span] += num_correct
                false_pred_span_in_text = get_id(pred_set - label_set, offset)
                false_pred_span_in_text = [(l, r - 1) for l, r in false_pred_span_in_text]
                for span in false_pred_span_in_text:
                    real_label = span2l[span] if span in span2l else set()
                    if real_label:
                        for real_l_of_span in real_label:
                            self.matrix[pred_l_of_span][real_l_of_span] += 1
                    else:
                        self.matrix[pred_l_of_span][DUMMY_LABEL] += 1

                self.label2pred_entity_span_sum[prompt_] += len(pred_set)
                self.label2gold_entity_span_sum[prompt_] += len(label_set)
                self.label2dummy_entity_span_sum[prompt_] += len(label_set - pred_set)
                self.labels.add(prompt_)
            else:
                real_labels_span = get_id(label_set, deepcopy(offset))
                pred_labels_span = get_id(pred_set, offset)
                real_labels = set([prompt_[l + len(prompt_) + 1: r + len(prompt_) + 1]
                                   for l, r in real_labels_span if r <= -1])
                pred_labels = set([prompt_[l + len(prompt_) + 1: r + len(prompt_) + 1]
                                   for l, r in pred_labels_span if r <= -1])
                if not real_labels:
                    if pred_labels:
                        for pred_l in pred_labels:
                            self.matrix[pred_l][DUMMY_LABEL] += 1
                    else:
                        self.matrix[DUMMY_LABEL][DUMMY_LABEL] += 1
                elif not pred_labels:
                    for real_l in real_labels:
                        self.matrix[DUMMY_LABEL][real_l] += 1
                else:
                    for pred_l in real_labels & pred_labels:
                        self.matrix[pred_l][pred_l] += 1
                    for pred_l in pred_labels - real_labels:
                        for real_l in real_labels - pred_labels:
                            self.matrix[pred_l][real_l] += 1
                for pred_l in pred_labels:
                    self.label2pred_entity_span_sum[pred_l] += 1
                for real_l in real_labels:
                    self.label2gold_entity_span_sum[real_l] += 1
                    self.labels.add(real_l)
                if not pred_labels:
                    for l in real_labels:
                        self.label2dummy_entity_span_sum[l] += 1

    def accumulate(self):
        """
        This function returns the mean precision, recall and f1 score for all accumulated minibatches.

        Returns:
            tuple: Returns tuple (`precision, recall, f1 score`).
        """
        labels = [DUMMY_LABEL] + list(sorted(self.labels))

        matrix = np.zeros([len(labels), len(labels)])
        for i in range(len(labels)):
            pred_l = labels[i]
            for j in range(len(labels)):
                gold_l = labels[j]
                matrix[i, j] = self.matrix[pred_l][gold_l]
        # matrix[0] = np.array([self.label2gold_entity_span_sum[l] for l in labels]) - np.sum(matrix[1:], axis=0)
        matrix[0] = np.array([self.label2dummy_entity_span_sum[l] for l in labels])
        for i, l in enumerate(labels):
            self.matrix[DUMMY_LABEL][l] = matrix[0][i]
        # matrix[0, 0] = 0
        TP = matrix.diagonal()
        pred_sum = np.array([self.label2pred_entity_span_sum[l] for l in labels])
        gold_sum = np.array([self.label2gold_entity_span_sum[l] for l in labels])
        precision = TP / pred_sum
        recall = TP / gold_sum
        f1 = 2 * precision * recall / (precision + recall)
        precision[(precision != precision) | (np.abs(precision) == np.inf)] = 0.
        recall[(recall != recall) | (np.abs(recall) == np.inf)] = 0.
        f1[(f1 != f1) | (np.abs(f1) == np.inf)] = 0.
        macro_p = np.mean(precision[1:])
        macro_r = np.mean(recall[1:])
        macro_f1 = np.mean(f1[1:])
        macro_f1_2 = 2 * macro_p * macro_r / (macro_p + macro_r) if macro_p + macro_r != 0 else np.array(0.)
        macro_f1_2 = macro_f1_2 if macro_f1_2 == macro_f1_2 and np.abs(macro_f1_2) != np.inf else 0.
        # 微观指标
        pred_sum_micro = np.sum(pred_sum[1:])
        gold_sum_micro = np.sum(gold_sum[1:])
        TP_micro = np.sum(TP[1:])
        # print(TP_micro, pred_sum_micro, gold_sum_micro)
        micro_p = TP_micro / pred_sum_micro
        micro_r = TP_micro / gold_sum_micro
        micro_f1 = 2 * micro_p * micro_r / (micro_p + micro_r)
        micro_p = micro_p if micro_p == micro_p and np.abs(micro_p) != np.inf else 0.
        micro_r = micro_r if micro_r == micro_r and np.abs(micro_r) != np.inf else 0.
        micro_f1 = micro_f1 if micro_f1 == micro_f1 and np.abs(micro_f1) != np.inf else 0.
        #
        rate = matrix / matrix.sum(axis=0).reshape(1, -1)
        rate[(rate != rate) | (np.abs(rate) == np.inf)] = 0.
        # print(matrix.astype(np.int))
        # print([(l, self.label2dummy_entity_span_sum[l]) for l in labels])
        return precision, recall, f1, macro_p, macro_r, macro_f1, macro_f1_2, \
               micro_p, micro_r, micro_f1, matrix.astype(np.int), labels

    def update(self, num_correct_spans, num_infer_spans, num_label_spans):
        pass

    def reset(self):
        """
        Reset function empties the evaluation memory for previous mini-batches.
        """
        self.labels = set()
        self.matrix = defaultdict(lambda: defaultdict(int))
        # self.label2span_count = defaultdict(int)
        # self.label2span_count[DUMMY_LABEL] = 0
        self.label2gold_entity_span_sum = defaultdict(int)
        self.label2pred_entity_span_sum = defaultdict(int)
        self.label2dummy_entity_span_sum = defaultdict(int)

    def name(self):
        """
        Return name of metric instance.
        """
        return "precision", "recall", "f1"


class SpanEvaluator(Metric):
    """
    SpanEvaluator computes the precision, recall and F1-score for span detection.
    """

    def __init__(self):
        super(SpanEvaluator, self).__init__()
        self.num_infer_spans = 0
        self.num_label_spans = 0
        self.num_correct_spans = 0

    def compute(self, start_probs, end_probs, gold_start_ids, gold_end_ids):
        """
        Computes the precision, recall and F1-score for span detection.
        """
        pred_start_ids = get_bool_ids_greater_than(start_probs)
        pred_end_ids = get_bool_ids_greater_than(end_probs)
        gold_start_ids = get_bool_ids_greater_than(gold_start_ids.tolist())
        gold_end_ids = get_bool_ids_greater_than(gold_end_ids.tolist())
        num_correct_spans = 0
        num_infer_spans = 0
        num_label_spans = 0
        for predict_start_ids, predict_end_ids, label_start_ids, label_end_ids in zip(
                pred_start_ids, pred_end_ids, gold_start_ids, gold_end_ids):
            [_correct, _infer,
             _label] = self.eval_span(predict_start_ids, predict_end_ids,
                                      label_start_ids, label_end_ids)
            num_correct_spans += _correct
            num_infer_spans += _infer
            num_label_spans += _label
        return num_correct_spans, num_infer_spans, num_label_spans

    def update(self, num_correct_spans, num_infer_spans, num_label_spans):
        """
        This function takes (num_infer_spans, num_label_spans, num_correct_spans) as input,
        to accumulate and update the corresponding status of the SpanEvaluator object.
        """
        self.num_infer_spans += num_infer_spans
        self.num_label_spans += num_label_spans
        self.num_correct_spans += num_correct_spans

    def eval_span(self, predict_start_ids, predict_end_ids, label_start_ids,
                  label_end_ids):
        """
        evaluate position extraction (start, end)
        return num_correct, num_infer, num_label
        input: [1, 2, 10] [4, 12] [2, 10] [4, 11]
        output: (1, 2, 2)
        """
        pred_set = get_span(predict_start_ids, predict_end_ids)
        label_set = get_span(label_start_ids, label_end_ids)
        num_correct = len(pred_set & label_set)
        num_infer = len(pred_set)
        # For the case of overlapping in the same category,
        # length of label_start_ids and label_end_ids is not equal
        num_label = max(len(label_start_ids), len(label_end_ids))
        return (num_correct, num_infer, num_label)

    def accumulate(self):
        """
        This function returns the mean precision, recall and f1 score for all accumulated minibatches.

        Returns:
            tuple: Returns tuple (`precision, recall, f1 score`).
        """
        precision = float(self.num_correct_spans /
                          self.num_infer_spans) if self.num_infer_spans else 0.
        recall = float(self.num_correct_spans /
                       self.num_label_spans) if self.num_label_spans else 0.
        f1_score = float(2 * precision * recall /
                         (precision + recall)) if self.num_correct_spans else 0.
        return precision, recall, f1_score

    def reset(self):
        """
        Reset function empties the evaluation memory for previous mini-batches.
        """
        self.num_infer_spans = 0
        self.num_label_spans = 0
        self.num_correct_spans = 0

    def name(self):
        """
        Return name of metric instance.
        """
        return "precision", "recall", "f1"


def get_span(start_ids, end_ids, with_prob=False):
    """
    Get span set from position start and end list.

    Args:
        start_ids (List[int]/List[tuple]): The start index list.
        end_ids (List[int]/List[tuple]): The end index list.
        with_prob (bool): If True, each element for start_ids and end_ids is a tuple aslike: (index, probability).
    Returns:
        set: The span set without overlapping, every id can only be used once .
    """
    if with_prob:
        start_ids = sorted(start_ids, key=lambda x: x[0])
        end_ids = sorted(end_ids, key=lambda x: x[0])
    else:
        start_ids = sorted(start_ids)
        end_ids = sorted(end_ids)

    start_pointer = 0
    end_pointer = 0
    len_start = len(start_ids)
    len_end = len(end_ids)
    couple_dict = {}
    while start_pointer < len_start and end_pointer < len_end:
        if with_prob:
            start_id = start_ids[start_pointer][0]
            end_id = end_ids[end_pointer][0]
        else:
            start_id = start_ids[start_pointer]
            end_id = end_ids[end_pointer]

        if start_id == end_id:
            couple_dict[end_ids[end_pointer]] = start_ids[start_pointer]
            start_pointer += 1
            end_pointer += 1
            continue
        if start_id < end_id:
            couple_dict[end_ids[end_pointer]] = start_ids[start_pointer]
            start_pointer += 1
            continue
        if start_id > end_id:
            end_pointer += 1
            continue
    result = [(couple_dict[end], end) for end in couple_dict]
    result = set(result)
    return result


def get_bool_ids_greater_than(probs, limit=0.5, return_prob=False):
    """
    Get idx of the last dimension in probability arrays, which is greater than a limitation.

    Args:
        probs (List[List[float]]): The input probability arrays.
        limit (float): The limitation for probability.
        return_prob (bool): Whether to return the probability
    Returns:
        List[List[int]]: The index of the last dimension meet the conditions.
    """
    probs = np.array(probs)
    dim_len = len(probs.shape)
    if dim_len > 1:
        result = []
        for p in probs:
            result.append(get_bool_ids_greater_than(p, limit, return_prob))
        return result
    else:
        result = []
        for i, p in enumerate(probs):
            if p > limit:
                if return_prob:
                    result.append((i, float(p)))
                else:
                    result.append(i)
        return result


def get_id(span_set, offset_mapping):
    """
    Return text id and probability of predicted spans
    Args:
        span_set (set): set of predicted spans.
        offset_mapping (list[int]): list of pair preserving the
                index of start and end char in original text pair (prompt + text) for each token.
    Returns:
        sentence_id (list[tuple]): index of start and end char in original text.
        prob (list[float]): probabilities of predicted spans.
    """
    prompt_end_token_id = offset_mapping[1:].index([0, 0])
    bias = offset_mapping[prompt_end_token_id][1] + 1
    for idx in range(1, prompt_end_token_id + 1):
        offset_mapping[idx][0] -= bias
        offset_mapping[idx][1] -= bias

    sentence_id = []

    for start, end in span_set:
        start_id = offset_mapping[start][0]
        end_id = offset_mapping[end][1]
        sentence_id.append((start_id, end_id))
    return sentence_id