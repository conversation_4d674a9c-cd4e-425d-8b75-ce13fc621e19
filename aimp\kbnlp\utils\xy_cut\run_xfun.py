import json
import cv2
import numpy as np
from xycut import bbox2points, recursive_xy_cut, vis_polygons_with_index


def load_data(p):
    with open(p, "r", encoding="utf-8") as f:
        data = json.load(f)
    boxes = []
    for it in data["document"]:
        boxes.append(it["box"])
    return np.array(boxes)


if __name__ == "__main__":
    # boxes = load_data("./zh_train_51.json")
    from kbnlp.scripts.information_extraction.zhu_ocr import OCRSystem

    use_customized_pp_ocr = True
    shrink_width = 5
    shrink_height = 5
    # path = r'D:\data\ocr_box_sorted_sample\b137_r1.5.jpg'
    path = r'D:\workspace\pycharm\nlp_project\kbnlp\utils\xy_cut\1462-0001-01061-008-57.jpg'
    if not use_customized_pp_ocr:
        ocr = OCRSystem(det_model_path='../../scripts/information_extraction/dtm/001.pt',
                        cls_model_path='../../scripts/information_extraction/dtm/003.pt',
                        cls_vertical_model_path='../../scripts/information_extraction/dtm/006.pt',
                        rec_model_path='../../scripts/information_extraction/dtm/tp1t7.pt',
                        rec_char_dict_path='../../scripts/information_extraction/dtm/ppocr_keys_v1.txt')
    else:
        from kbnlp.scripts.information_extraction.pp_ocr_customized import PaddleOCRCustomized
        import paddle

        ocr = PaddleOCRCustomized(show_log=False, lang='ch',
                                  use_gpu=True if paddle.get_device().startswith('gpu') else False)
    boxes = [(min(b[0][0], b[1][0], b[2][0], b[3][0]),
              min(b[0][1], b[1][1], b[2][1], b[3][1]),
              max(b[0][0], b[1][0], b[2][0], b[3][0]),
              max(b[0][1], b[1][1], b[2][1], b[3][1])) for b, _ in ocr.ocr(path)[0]]
    boxes = [[box[0] + shrink_width, box[1] + shrink_height, box[2] - shrink_width, box[3] - shrink_height] for box in
             boxes]
    boxes = np.array(boxes, dtype=np.int)

    image = cv2.imread(path)
    result = vis_polygons_with_index(image, [bbox2points(it) for it in boxes])
    path_ = path.rsplit('.')
    path_[0] += '_original'

    cv2.imwrite('.'.join(path_), result)

    res = []
    split_lines = []
    recursive_xy_cut(np.asarray(boxes).astype(int), np.arange(len(boxes)), res, split_lines=split_lines)
    assert len(res) == len(boxes)
    sorted_boxes = boxes[np.array(res)].tolist()

    result = vis_polygons_with_index(image, [bbox2points(it) for it in sorted_boxes])
    for i, line in enumerate(split_lines):
        cv2.line(
            result,
            (line[0], line[1]),
            (line[2], line[3]),
            color=(0, 0, 255),
            thickness=4,
        )
        cx = (line[0] + line[2]) // 2
        cy = (line[1] + line[3]) // 2
        font = cv2.FONT_HERSHEY_SIMPLEX
        cat_size = cv2.getTextSize(str(i+1), font, 0.5, 2)[0]
        cv2.rectangle(
            result,
            (cx - 5 * len(str(i+1)), cy - cat_size[1] - 5 - 2),
            (cx - 5 * len(str(i+1)) + cat_size[0], cy - 5 + 2),
            (0, 0, 0),
            -1,
        )
        cv2.putText(
            result,
            str(i+1),
            (cx - 5 * len(str(i+1)), cy - 5),
            font,
            0.5,
            (255, 255, 255),
            thickness=1,
            lineType=cv2.LINE_AA,
        )
    path_ = path.rsplit('.')
    path_[0] += '_result'
    cv2.imwrite('.'.join(path_), result)
