import openpyxl
from openpyxl import load_workbook

path = r'D:\workspace\pycharm\nlp_project\ymlkbqa\二等功及以上英雄_图库文件.xlsx'


def synthesizer():
    wb = load_workbook(path)
    ws = wb.active
    column_names = []
    for i, row in enumerate(ws.rows):
        if i == 0:
            column_names.extend([col.value for col in row])
        else:
            break

    for i, col in enumerate(ws.columns):
        col_name = column_names[i]
        values = [r.value for r in col]
        assert values[0] == col_name
        unique_instance = set(values[1:])
