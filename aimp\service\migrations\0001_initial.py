# Generated by Django 4.1.1 on 2022-12-16 02:42

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("model", "0001_initial"),
        ("dataset", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Server",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("ip", models.CharField(max_length=32)),
                ("CPU", models.IntegerField(default=None)),
                ("RAM", models.IntegerField(default=None)),
                ("disk", models.IntegerField(default=None)),
                ("disk_count", models.IntegerField(default=None)),
                ("GPU", models.IntegerField(default=None)),
                ("server_status", models.IntegerField(default=None)),
                ("server_name", models.CharField(default="", max_length=32)),
                ("server_type", models.IntegerField(default=1)),
            ],
        ),
        migrations.CreateModel(
            name="Service",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("create_time", models.IntegerField(default=1671158522)),
                ("update_time", models.IntegerField(default=1671158522)),
                ("service_name", models.CharField(max_length=32)),
                ("service_status", models.IntegerField(default=0)),
                ("service_pos", models.CharField(default="", max_length=255)),
                ("model_id", models.IntegerField(default=0)),
                ("unit_type", models.IntegerField(default=0)),
                ("unit_count", models.IntegerField(default=0)),
            ],
            options={"abstract": False,},
        ),
        migrations.CreateModel(
            name="ServiceServer",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "server",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="service.server"
                    ),
                ),
                (
                    "service",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="service.service",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="ServiceModel",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "ai_model",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="model.aimodels"
                    ),
                ),
                (
                    "service",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="service.service",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="ServiceDataset",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "dataset",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="dataset.dataset",
                    ),
                ),
                (
                    "service",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="service.service",
                    ),
                ),
            ],
        ),
    ]
