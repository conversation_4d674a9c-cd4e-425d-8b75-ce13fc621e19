def process_image_tag(self, line, task_type="ext", check=False, **kwargs):
    """
            将aidp导出的数据处理成文档信息抽取脚本（uie、uiex）能处理的doccano格式。如果读取数据已经是文档信息抽取可以处理的doccano格式
        数据,则把数据原样返回；如果该doccano格式数据中没有cls_label,则cls_label被创建为空列表 。

        # 文档标注导出数据格式
        数据导出成jsonlines格式，每行一个样本，图片放在jsonl文件的同级目录下的images/file_name文件夹下。在jsonl文件中，每个样本是一个字典，包含字段：
        'word': List[str], ocr得到的文字列表。
        'bbox': List[List[int, int, int, int]], ocr得到的所有文本框的bbox列表。
        'image': str, 记录jsonl文件的同级images目录到该图片文件的路径信息）,如，某张图片名为'pig1.jgp',其放在images/pig目录下，该本字段记录为'images/pig/pig1.jpg'。
        'entities': 如下示例，id代表该图片中标注的实体id,从0开始，依次递增，label代表实体标签（著录项），fragment记录用户对该著录项的标注结果，
                    该示例中fragment列表中的第一个著录项'住址著录项'对应的标注步骤如下：
                        1，用户首先在box_id为0的文本框中选中‘上海市’，’上海市‘在该文本框中的start、end值为(2, 5);
                        2，然后在该文本框中选中'SOHO东海广场',其在文本框中的start、end值为(8, 16)；
                        3，最后在box_id为3的文本框中选中了'2208室'，其在3号文本框中的start、end值为(7, 12)。
                    '公司名称著录项'著录像类似。
                    例子：
                    [{'fragment': [{'box_id': 0, 'mention': ['上海市', 'SOHO东海广场'], 'offset_map': [(2, 5), (8, 16)]},
                                   {'box_id': 3, 'mention': ['2208室'], 'offset_map': [(7, 12)]}],
                      'id': 0,
                      'label': '住址著录项'},
                     {'fragment': [{'box_id': 5, 'mention': ['北京酷豹（上海数豹）科技'], 'offset_map': [(3, 15)]},
                                   {'box_id': 6, 'mention': ['有限公司'], 'offset_map': [(0, 4)]}],
                      'id': 1,
                      'label': '公司名称著录项'},
                     {'fragment': [{'box_id': 21, 'mention': ['上海市南京西路'], 'offset_map': [(11, 18)]}],
                      'id': 8,
                      'label': '住址著录项'}]
                    注意：如果一个实体类型（著录项）对应不不止一个实体实例（著录结果），如'住址著录项'包括’上海市SOHO东海广场‘、’上海市南京西路‘，
                    则他们需要要在entities中分别占据一项记录。
        relations: 一个空列表（不是None）。目前不做关系抽取。
        #按照目前设计逻辑，从AIDP导出的数据需要做文本图片做分类。所以task_type参数无效。

        文档信息抽取模型所需doccano格式数据：jsonlines文件，每行是一个样本，每个样本都是一个字典。有text、word、bbox、image、entities、
        relations字段。image和aidp格式数据一致。

    """
    example = copy(line)

    if not ('image_width' in example and 'image_height' in example):
        img = Image.open(os.path.join(self.image_base_path,
                                      *re.split(r'\\|/', example['image']))).convert("RGB")
        example['image_width'] = img.width
        example['image_height'] = img.height
    if 'text' not in line and 'word' in line:
        example['text'] = ''.join(line['word'])
    if not line['entities'] and not line['relations']:
        if 'cls_label' not in example:
            example['cls_label'] = ['否']
        return example

    entities = []
    relations = []
    entity_id = 0
    relation_id = 0
    word_lens = [0] + list(accumulate(map(len, line['word'])))
    for entity in line['entities']:
        if 'fragment' not in entity:  # 直接加载doccano格式数据
            entities.append(entity)
        else:
            frags = entity['fragment']
            label = entity['label']
            whole_entity = []
            for frag in frags:
                box_id = frag['box_id']
                frag_entities = []  # 一个fragment中所有的实体片段。
                for mention, offset in zip(frag['mention'], frag['offset_map']):
                    start_offset = word_lens[box_id] + offset[0]
                    end_offset = word_lens[box_id] + offset[1]
                    frag_entities.append({'id': entity_id,
                                          'label': label,
                                          'start_offset': start_offset,
                                          'end_offset': end_offset})
                    entity_id += 1
                whole_entity.extend(frag_entities)
            entities.extend(whole_entity)
            # 暂时不支持不连续实体功能。
            # if len(whole_entity) >= 2:
            #     for e_i in range(len(whole_entity) - 1):
            #         from_id = whole_entity[e_i]['id']
            #         to_id = whole_entity[e_i + 1]['id']
            #         relations.append({'id': relation_id,
            #                           'from_id': from_id,
            #                           'to_id': to_id,
            #                           'type': '后续'})
            #         relation_id += 1
    # 将原本有的关系保留（之后处理不连续实体，或者相邻文本框被分开的实体，处理已有的关系需要重新考虑。）
    for rel in line['relations']:
        relations.append(rel)
    example['entities'] = entities
    example['relations'] = relations  # 之后标注平台支持关系标注后，则需要处理已有的relations。
    # example = deal_next_relation2([example])[0]  # 该方法有待检查
    # 利用图片中包含的著录项做图片标签。（经验证，直接这么使用不太可行。）
    # example['cls_label'] = ['|'.join(sorted(set([e['label'] for e in example['entities']])))] \
    #     if example['entities'] else []
    example['cls_label'] = ['是' if example['entities'] else '否']
    return example