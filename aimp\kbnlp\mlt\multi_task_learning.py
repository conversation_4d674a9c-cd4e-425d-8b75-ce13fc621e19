import os
import itertools
from copy import copy
from typing import Dict, Any, Tuple, List, Union, Iterable, Callable, Optional
from collections import defaultdict, OrderedDict
from kbnlp.mlt.util import merge_dict, merge_locals_kwargs
from kbnlp.mlt.util.tensor_util import pick_tensor_for_each_token
from kbnlp.mlt.data_transform import TransformList, TransformerSequenceTokenizer
from kbnlp.mlt.common.metric import MetricDict
from kbnlp.mlt.common.serializable import SerializableDict
from kbnlp.mlt.common.reflection import classpath_of
from kbnlp.mlt.common.configurable import Configurable
from kbnlp.mlt.layers.encoder.transformer_encoder import ContextualWordEmbedding
from kbnlp.mlt.task import Task
from kbnlp.mlt.task.segment import Segment
import numpy as np
import torch
from torch import nn
from torch.nn import ModuleDict
from torch.utils.data.dataloader import DataLoader
from transformers import optimization, AutoTokenizer, AutoModel, AutoConfig
from accelerate import Accelerator
from torch.nn.parallel import DistributedDataParallel
from torch.cuda.amp import autocast


import time
# os.chdir('D:/workspace/pycharm/fastdependency/kbnlp')


class MultiTaskModel(torch.nn.Module):

    def __init__(self,
                 encoder,
                 decoders: ModuleDict):
        super().__init__()
        self.encoder = encoder
        self.decoders = decoders


class MultiTaskDataLoader(DataLoader):

    def __init__(self, shuffle=True, generator=None, tau: float = 0.8, **dataloaders) -> None:
        # noinspection PyTypeChecker
        super().__init__(None)
        self.tau = tau
        self.shuffle = shuffle
        self.dataloaders: Dict[str, DataLoader] = dataloaders if dataloaders else {}
        self.generator = generator  # 分布式训练必须设置generator

    def __len__(self) -> int:
        if self.dataloaders:
            return sum(self.sizes)
        return 0

    def __iter__(self):
        if len(self) == 0:
            return None, None
        if self.shuffle:
            sampling_weights, total_size = self.sampling_weights
            task_names = list(self.dataloaders.keys())
            iterators = dict((k, itertools.cycle(v)) for k, v in self.dataloaders.items())
            for task_id in torch.multinomial(torch.tensor(sampling_weights), total_size, replacement=True, generator=self.generator).tolist():
                batch = copy(next(iterators[task_names[task_id]]))
                yield task_names[task_id], batch
        else:
            for task_name, dataloader in self.dataloaders.items():
                for batch in dataloader:
                    yield task_name, batch

    @property
    def sampling_weights(self):
        sampling_weights = self.sizes
        total_size = sum(sampling_weights)
        Z = sum(pow(v, self.tau) for v in sampling_weights)
        sampling_weights = [pow(v, self.tau) / Z for v in sampling_weights]
        return sampling_weights, total_size

    @property
    def sizes(self):
        return [len(v) for v in self.dataloaders.values()]

    def info(self):
        info = ''
        for task_name, task_loader in self.dataloaders.items():
            info += 'task_name: %s, data_size: %d, batch_size: %d, total_step: %d' % (
                task_name, len(task_loader.dataset), task_loader.batch_sampler.batch_size, len(task_loader))
            info += '\n'
        return info


class MultiTaskLearning(Configurable):
    def __init__(self, **kwargs):
        self.model: Optional[MultiTaskModel] = None
        self.tasks: Optional[OrderedDict] = None
        self.task_names = None  # 任务事先排好序
        self.vocabs = None
        self._tokenizer = None
        self._common_transform = None
        self.config = SerializableDict(**kwargs)
        self.accelerator: Accelerator = None

    def load(self, save_dir: str, device='cuda:0' if torch.cuda.is_available() else 'cpu', **kwargs):
        """Load from packagea local/remote component.

        Args:
            save_dir: An identifier which can be packagea local path or packagea remote URL or packagea pre-defined string.
            device: The device this component will be moved onto.
            **kwargs: To override some configs.
        """
        self.load_config(save_dir, **kwargs)
        self.config_compatible()
        encoder = self.config.encoder
        self.tasks = dict((k, v) for k, v in self.config.items() if isinstance(v, Task))
        self.tasks = OrderedDict((name, self.tasks[name]) for name in self.config.task_names if name in self.tasks)
        # 需要先load_vocabs再build_model
        self.load_vocabs(save_dir)
        for task in self.tasks.values():
            task.vocabs.lock()
        self._tokenizer = AutoTokenizer.from_pretrained(save_dir + '/pretrained', use_fast=True,
                                                        do_basic_tokenize=True)

        _tokenizer_transform = TransformerSequenceTokenizer(self._tokenizer,
                                                            self.config['input_key'])
        extra_transform = self.config['extra_transform']
        self._common_transform = TransformList(extra_transform, _tokenizer_transform)
        _transformer = AutoModel.from_config(AutoConfig.from_pretrained(save_dir + '/pretrained'))
        self.model = self.build_model(encoder=encoder, tasks=self.tasks, transformer=_transformer,
                                      tokenizer=self._tokenizer)
        self.load_weights(save_dir, **kwargs)
        self.model.to(device)
        self.model.eval()

    def config_compatible(self):
        if 'transform' in self.config:  # 兼容老版本
            self.config.pop('transform')
        if 'input_key' not in self.config:  # 兼容老版本
            self.config['input_key'] = self.config.encoder._input_key
        if 'extra_transform' not in self.config:  # 兼容老版本
            self.config['extra_transform'] = self.config.encoder._extra_tranform  # 之前将extra_transform拼写错误。

    def save(self, save_dir: str, **kwargs):
        """Save this component to packagea directory.

        Args:
            save_dir: The directory to save this component.
            **kwargs: Not used.
        """
        self._model.encoder.transformer.config.save_pretrained(save_dir + '/pretrained')
        self._tokenizer.save_pretrained(save_dir + '/pretrained')
        self.save_config(save_dir)
        self.save_vocabs(save_dir)
        self.save_weights(save_dir)

    #TODO: 添加从断点中恢复训练

    def load_from_checkpoint(self):
        pass

    def save_checkpoint(self):
        pass

    def load_config(self, save_dir, filename='config.json', **kwargs):
        """Load config from packagea directory.
        Args:
            save_dir: The directory to load config.
            filename: A file name for config.
            **kwargs: K-V pairs to override config.
        """
        # save_dir = get_resource(save_dir)
        self.config.load_json(os.path.join(save_dir, filename))
        self.config.update(kwargs)  # overwrite config loaded from disk
        for k, v in self.config.items():
            if isinstance(v, dict) and 'classpath' in v:
                self.config[k] = Configurable.from_config(v)
        self.on_config_ready(**self.config, save_dir=save_dir)

    def save_config(self, save_dir, filename='config.json'):
        """Save config into packagea directory.

        Args:
            save_dir: The directory to save config.
            filename: A file name for config.
        """
        self._savable_config.save_json(os.path.join(save_dir, filename))

    def load_vocabs(self, save_dir, filename='vocabs.json'):
        for task_name, task in self.tasks.items():
            task.load_vocabs(save_dir, f'{task_name}_{filename}')

    def save_vocabs(self, save_dir, filename='vocabs.json'):
        for task_name, task in self.tasks.items():
            task.save_vocabs(save_dir, f'{task_name}_{filename}')

    def save_weights(self, save_dir, filename='model.pt', trainable_only=True, **kwargs):
        """Save pretrained weights to packagea directory.

        Args:
            save_dir: The directory to save weights into.
            filename: A file name for weights.
            trainable_only: ``True`` to only save trainable weights. Useful when the pretrained contains lots of static
                embeddings.
            **kwargs: Not used for now.
        """
        model = self._model
        state_dict = model.state_dict()
        # if trainable_only:
        #     trainable_names = set(n for n, p in pretrained.named_parameters() if p.requires_grad)
        #     state_dict = dict((n, p) for n, p in state_dict.items() if n in trainable_names)
        torch.save(state_dict, os.path.join(save_dir, filename))

    def load_weights(self, save_dir, filename='model.pt', **kwargs):
        """Load weights from packagea directory.

        Args:
            save_dir: The directory to load weights from.
            filename: A file name for weights.
            **kwargs: Not used.
        """
        filename = os.path.join(save_dir, filename)
        self._model.load_state_dict(torch.load(filename, map_location='cpu'), strict=False)

    def _capture_config(self, locals_: Dict,
                        exclude=(
                                'device',
                                'trn_data', 'dev_data', 'save_dir', 'kwargs', 'self', 'logger', 'verbose',
                                'dev_batch_size', '__class__', 'devices', 'eval_trn')):
        """Save arguments to config

        Args:
          locals_: Dict:
          exclude:  (Default value = ('trn_data')
          'dev_data':
          'save_dir':
          'kwargs':
          'self':
          'logger':
          'verbose':
          'dev_batch_size':
          '__class__':
          'devices'):

        Returns:


        """
        if 'kwargs' in locals_:
            locals_.update(locals_['kwargs'])
        locals_ = dict((k, v) for k, v in locals_.items() if k not in exclude and not k.startswith('_'))
        self.config.update(locals_)
        return self.config

    @property
    def _savable_config(self):
        def convert(k, v):
            if not isinstance(v, SerializableDict) and hasattr(v, 'config'):
                v = v.config
            elif isinstance(v, (set, tuple)):
                v = list(v)
            if isinstance(v, dict):
                v = dict(convert(_k, _v) for _k, _v in v.items())
            return k, v

        config = SerializableDict(
            convert(k, v) for k, v in sorted(self.config.items()))
        config.update({
            'classpath': classpath_of(self),
        })
        return config

    def on_config_ready(self, **kwargs):
        pass

    @property
    def _model(self):
        if self.model is None:
            return None
        if isinstance(self.model, DistributedDataParallel):
            return self.model.module
        elif isinstance(self.model, nn.Module):
            return self.model
        else:
            return None

    def build_model(self, encoder: ContextualWordEmbedding, tasks, transformer, tokenizer, **kwargs) -> torch.nn.Module:
        """
        :param encoder:
        :param tasks:
        :param transformer:
        :param tokenizer:
        :param kwargs:
        :return:
        """
        transformer_module = encoder.module(transformer, tokenizer)
        encoder_size = transformer_module.hidden_size
        # scalar_mixes = torch.nn.ModuleDict()
        decoders = torch.nn.ModuleDict()
        # use_raw_hidden_states = dict()
        for task_name, task in tasks.items():
            decoder = task.build_model(encoder_size, task_name)
            assert decoder, f'Please implement `build_model` of {type(task)} to return packagea decoder.'
            decoders[task_name] = decoder
        self.model = MultiTaskModel(transformer_module, decoders)
        return self.model

    def build_dataset(self, data, **kwargs):
        pass

    def build_vocab(self):
        for name, task in self.tasks.items():
            task.build_vocab(name)

    def build_dataloader(self, mode='train', datas=None, tau: float = 0.8, shuffle=False, generator=None,
                         gradient_accumulation=1, use_effective_tokens=False, **kwargs):
        """
        mode可以为train或者eval。predict的时候不用该方法创建dataloader。
        """
        if mode != 'train' and mode != 'eval':
            raise ValueError('mode只可以是train或者eval。predict的时候不用该方法创建dataloader。')
        mlt_loader_generator = None
        if generator and shuffle:
            mlt_loader_generator = torch.Generator()
            mlt_loader_generator.manual_seed(int(torch.empty((), dtype=torch.int64).random_(generator=generator).item()))
        dataloader = MultiTaskDataLoader(shuffle=shuffle, generator=mlt_loader_generator, tau=tau)
        for task_name, task in self.tasks.items():
            #  模拟训练分词的数据
            if task_name in datas:
                if datas[task_name]:
                    examples = task.build_sample(datas[task_name])
                    transform_list = TransformList(self._common_transform)
                    transform_list.extend(task.build_transformList(mode=mode, task_name=task_name))
                    dataset = task.build_dataset(examples, transform_list=transform_list)
                    if mode != 'train':
                        for vocab in task.vocabs.values():
                            task.vocabs.lock()  # mode不是train的情况下要把vocab锁上。
                            if vocab.unk_token is None:  # 为了处理训练集中极少样本情况下，评估集中可能有些label可能没有出现的情况。
                                vocab.set_unk_as_safe_unk()

                    batch_size, batch_max_tokens = (task.batch_size, task.batch_max_token) if mode == 'train' else (task.eval_batch_size, task.eval_batch_max_tokens)
                    generator_task = None
                    if generator and shuffle:
                        generator_task = torch.Generator()
                        generator_task.manual_seed(int(torch.empty((), dtype=torch.int64).random_(generator=generator).item()))
                    task_loader = task.build_dataloader(dataset,
                                                        batch_size=batch_size,
                                                        batch_max_tokens=batch_max_tokens,
                                                        shuffle=shuffle, generator=generator_task,
                                                        gradient_accumulation=gradient_accumulation,
                                                        use_effective_tokens=use_effective_tokens)
                    dataloader.dataloaders[task_name] = task_loader
        return dataloader

    def build_criterion(self, **kwargs):
        return dict((k, v.build_criterion(**kwargs)) for k, v in self.tasks.items())

    def build_optimizer(self,
                        dataloader,
                        epochs,
                        adam_epsilon=1e-8,
                        weight_decay=0.0,
                        warmup_steps=0.1,
                        lr=1e-3,
                        encoder_lr=5e-5,
                        gradient_accumulation=1,
                        **kwargs):
        model = self._model
        encoder = model.encoder

        encoder_parameters = list(encoder.parameters())
        parameter_groups: List[Dict[str, Any]] = []

        decoders = model.decoders
        decoder_optimizers = dict()
        for k, task in self.tasks.items():
            decoder: torch.nn.Module = decoders[k]
            decoder_parameters = list(decoder.parameters())
            if task.separate_optimizer:
                decoder_optimizers[k] = task.build_optimizer(decoder=decoder, **kwargs)
            else:
                task_lr = task.lr or lr
                parameter_groups.append({"params": decoder_parameters, 'lr': task_lr})
        parameter_groups.append({"params": encoder_parameters, 'lr': encoder_lr})
        no_decay = ['bias', 'LayerNorm.bias', 'LayerNorm.weight']
        no_decay_parameters = set()
        for n, p in model.named_parameters():
            if any(nd in n for nd in no_decay):
                no_decay_parameters.add(p)
        no_decay_by_lr = defaultdict(list)
        for group in parameter_groups:
            _lr = group['lr']
            ps = group['params']
            group['params'] = decay_parameters = []
            group['weight_decay'] = weight_decay
            for p in ps:
                if p in no_decay_parameters:
                    no_decay_by_lr[_lr].append(p)
                else:
                    decay_parameters.append(p)
        for _lr, ps in no_decay_by_lr.items():
            parameter_groups.append({"params": ps, 'lr': _lr, 'weight_decay': 0.0})
        # noinspection PyTypeChecker
        encoder_optimizer = optimization.AdamW(
            parameter_groups,
            lr=lr,
            weight_decay=weight_decay,
            eps=adam_epsilon,
        )
        return encoder_optimizer, decoder_optimizers

    def compute_loss(self,
                     task_name,
                     batch,
                     output: Union[torch.Tensor, Dict[str, torch.Tensor], Iterable[torch.Tensor], Any],
                     task_mask: torch.Tensor,
                     criterion: Callable,
                     task: Task) -> torch.FloatTensor:
        return task.compute_loss(task_name, batch, output, task_mask, criterion)

    def build_metric(self, **kwargs):
        metric_dict = MetricDict()
        for key, task in self.tasks.items():
            if task.dev:
                metric = task.build_metric(**kwargs)
                metric_dict[key] = metric
        return metric_dict

    def reset_metric(self):
        pass

    def update_metrics(self, batch, output_dict, metric_dict, task_name):
        if task_name not in metric_dict:
            metric_dict[task_name] = self.tasks[task_name].build_metric()
        prediction = output_dict[task_name]['prediction']
        metric = metric_dict[task_name]
        self.tasks[task_name].update_metric(prediction, batch, metric, task_name)

    def report_metrics(self, total_loss, total_count, time_cost, metric_dict: Optional[MetricDict]):

        info = 'time:%.1fs.' % time_cost
        for task_name in self.tasks.keys():
            info += f'{task_name}: '
            info += f'{repr(metric_dict[task_name])}' if task_name in metric_dict else ''
            info += ', loss:%.4f.' % (total_loss[task_name] / total_count[task_name]) if task_name in total_loss and task_name in total_count else ''
            info += '   '
        return info

    def print(self, *args, sep=' ', end='\n', file=None):
        if self.accelerator is None:
            print(*args, sep=sep, end=end, file=file)
        else:
            self.accelerator.print(*args, sep=sep, end=end, file=file)

    def fit(self,
            _transformer,
            _tokenizer,
            encoder: ContextualWordEmbedding,
            extra_transform,
            tasks: Dict[str, Task],
            save_dir,
            epochs,
            patience=0.5,
            lr=1e-3,
            encoder_lr=5e-5,
            adam_epsilon=1e-8,
            weight_decay=0.0,
            warmup_steps=0.1,
            shuffle=True,
            gradient_accumulation=1,
            use_effective_tokens=False,
            grad_norm=1.0,
            encoder_grad_norm=None,
            decoder_grad_norm=None,
            tau: float = 0.8,
            input_key='original_input',
            # prune: Callable = None,
            eval_trn=True,
            prefetch=None,
            tasks_need_custom_eval=None,
            _device_placeholder=False,
            cache=False,
            device='cpu',
            logger=None,
            seed=Optional[int],  # 分布式训练必须提供一个长整型整数。
            **kwargs):

        self.accelerator = Accelerator(device_placement=False)
        device = self.accelerator.device
        self.print('num_processes:', self.accelerator.num_processes)
        generator = None
        if seed:
            generator = torch.Generator()
            generator.manual_seed(seed)
        else:
            raise ValueError('分布式训练必须提供该参数')
        task_names = list(tasks.keys())
        locals_ = locals()
        locals_.pop('tasks')
        for name, task in tasks.items():
            locals_[name] = task
        self._capture_config(locals_)
        self.tasks = tasks
        self.build_vocab()
        self._tokenizer = _tokenizer
        _tokenizer_transform = TransformerSequenceTokenizer(self._tokenizer, input_key)
        self._common_transform = TransformList(extra_transform, _tokenizer_transform)
        train_dataloaders = self.build_dataloader(mode='train',
                                                  datas={name: task.trn for name, task in self.tasks.items()},
                                                  shuffle=shuffle, generator=generator,
                                                  gradient_accumulation=gradient_accumulation,
                                                  use_effective_tokens=use_effective_tokens)
        dev_dataloaders = self.build_dataloader(mode='eval',
                                                datas={name: task.dev for name, task in self.tasks.items()},
                                                shuffle=False,
                                                gradient_accumulation=1,
                                                use_effective_tokens=use_effective_tokens)

        # 训练时需要在build_dataloader后执行build_model，因为需要在build_dataloader中动态的创建label的词表。
        self.build_model(encoder, self.tasks, _transformer, self._tokenizer)
        self.model.to(device)  # 当device_placement指定为False时，accelerate要求在创建优化器前将model放到device上。否则不能在tpu上运行。
        # print(self._model.decoders['tok'].classifier.weight)
        self.print('decoders:')
        self.print(self.model.decoders)
        criterions = self.build_criterion()
        encoder_optimizer, decoder_optimizers = self.build_optimizer(dataloader=train_dataloaders,
                                                                                        epochs=epochs,
                                                                                        adam_epsilon=adam_epsilon,
                                                                                        weight_decay=weight_decay,
                                                                                        warmup_steps=warmup_steps,
                                                                                        lr=lr,
                                                                                        encoder_lr=encoder_lr,
                                                                                        gradient_accumulation=gradient_accumulation
                                                                                        )
        # decoder_optimizers目前为空。不单独为子任务设立优化器。
        prepares = self.accelerator.prepare(self.model, *list(train_dataloaders.dataloaders.values()), encoder_optimizer)
        self.model = prepares[0]
        # print(self._model.decoders['tok'].classifier.weight)
        for task_names, loader in zip(train_dataloaders.dataloaders.keys(), prepares[1:-1]):
            train_dataloaders.dataloaders[task_names] = loader
        self.print('train: ', train_dataloaders.info())
        self.print('eval: ', dev_dataloaders.info())
        encoder_optimizer = prepares[-1]

        # Accelerator要求计算dataloader的长度放在，prepare方法之后。因为当gpu数量超过1个时，dataloader经过prepare之后会缩短。
        num_training_steps = len(train_dataloaders) * epochs // gradient_accumulation
        encoder_scheduler = optimization.get_linear_schedule_with_warmup(encoder_optimizer,
                                                                         num_training_steps * warmup_steps,
                                                                         num_training_steps)
        with open('output.txt', 'a', encoding='utf-8') as fw:
            for epo_id in range(1, epochs + 1):
                info = ''
                if epo_id == 19:
                    pass
                info += 'epoch:%d,' % epo_id + self.fit_dataloader(**merge_dict(self.config,
                                                                                train_dataloaders=train_dataloaders,
                                                                                encoder_optimizer=encoder_optimizer,
                                                                                encoder_scheduler=encoder_scheduler,
                                                                                decoder_optimizers=decoder_optimizers,
                                                                                criterions=criterions,
                                                                                device=device,
                                                                                ))
                info += '\n'
                self.accelerator.wait_for_everyone()
                if save_dir and self.accelerator.is_local_main_process:
                    self.save(save_dir + '/save_load_test_epoch_%d' % epo_id)
                if self.accelerator.is_local_main_process:
                    info += 'epoch:%d,' % epo_id + self.eval_dataloader(dev_dataloaders=dev_dataloaders, criterions=criterions, device=device)
                    fw.write(info + '\n')
                self.print(info)
        self.accelerator.wait_for_everyone()
        # if save_dir and self.accelerator.is_local_main_process:
        #     self.save(save_dir)

    def fit_dataloader(self,
                       train_dataloaders,
                       encoder_optimizer,
                       encoder_scheduler,
                       decoder_optimizers,
                       criterions,
                       patience=0.5,
                       gradient_accumulation=1,
                       device='cpu',
                       grad_norm=1.0,
                       encoder_grad_norm=None,
                       decoder_grad_norm=None,
                       **kwargs):
        self.model.train()
        total_loss = dict((task_name, 0.) for task_name in self.tasks.keys())
        total_count = dict((task_name, 1e-10) for task_name in self.tasks.keys())
        time_start = time.time()
        for task_name, batch in train_dataloaders:
            for signature, value in batch.items():
                if isinstance(value, torch.Tensor):
                    batch[signature] = value.to(device)
            decoder_optimizer = decoder_optimizers.get(task_name, None)
            if decoder_optimizer:
                raise NotImplementedError
            output_dict, batch = self.feed_batch('train', batch, task_name, output_dict=dict())
            loss = self.compute_loss(task_name,
                                     batch,
                                     output_dict[task_name]['output'],
                                     output_dict[task_name]['mask'],
                                     criterions[task_name],
                                     self.tasks[task_name])

            # loss.backward()
            self.accelerator.wait_for_everyone()
            self.accelerator.backward(loss)
            total_loss[task_name] += loss.item() * len(batch['original_input'])
            total_count[task_name] += len(batch['original_input'])

            # torch.nn.utils.clip_grad_norm_(filter(lambda p: p.requires_grad, self.pretrained.parameters()), grad_norm)
            self.accelerator.clip_grad_norm_(filter(lambda p: p.requires_grad, self.model.parameters()), grad_norm)
            encoder_optimizer.step()
            encoder_scheduler.step()
            encoder_optimizer.zero_grad()
            if decoder_optimizer:
                raise NotImplementedError
            # self.decode_output(mode='train', output_dict=output_dict, batch=batch, task_name=task_name)
            # 不对训练集计算指标，对于有些teach force的模型不太方便计算metric。
            # self.update_metrics(batch, output_dict, metric_dict, task_name)
        time_end = time.time()
        info = self.report_metrics(total_loss, total_count, time_end - time_start, metric_dict=MetricDict())
        return info

    def eval(self, task_dev_dict: Dict, use_effective_tokens=False, device='cpu'):  # k-v表示任务名和要评估的数据.
        '''
        利用训练好的模型评估指定任务性能。
        '''
        task_dev_dict = OrderedDict(
            (name, task_dev_dict[name]) for name in self.tasks.keys() if name in task_dev_dict.keys())
        dev_dataloaders = self.build_dataloader(mode='eval',
                                                datas=task_dev_dict,
                                                shuffle=False, gradient_accumulation=1,
                                                use_effective_tokens=use_effective_tokens)
        self.print('eval: ', dev_dataloaders.info())
        criterions = OrderedDict((name, self.tasks[name].build_criterion()) for name in task_dev_dict.keys())
        info = self.eval_dataloader(dev_dataloaders, criterions, device)
        return info

    def eval_dataloader(self,
                        dev_dataloaders,
                        criterions,
                        device='cpu',
                        **kwargs):
        if len(dev_dataloaders) == 0:
            return '无评估数据。'
        with torch.no_grad():
            self.model.eval()
            total_loss = dict((task_name, 0.) for task_name in self.tasks.keys())
            total_count = dict((task_name, 1e-10) for task_name in self.tasks.keys())
            time_start = time.time()
            metric_dict = MetricDict()
            for task_name, batch in dev_dataloaders:
                for signature, value in batch.items():
                    if isinstance(value, torch.Tensor):
                        batch[signature] = value.to(device)
                output_dict, batch = self.feed_batch('eval', batch, task_name, output_dict=dict())
                dev_loss = self.compute_loss(task_name,
                                             batch,
                                             output_dict[task_name]['output'],
                                             output_dict[task_name]['mask'],
                                             criterions[task_name],
                                             self.tasks[task_name])
                total_loss[task_name] += dev_loss.item() * len(batch['original_input'])
                total_count[task_name] += len(batch['original_input'])
                self.decode_output(mode='eval', output_dict=output_dict, batch=batch, task_name=task_name)
                self.update_metrics(batch, output_dict, metric_dict, task_name)
            time_end = time.time()
            info = self.report_metrics(total_loss, total_count, time_end - time_start, metric_dict)
        return info

    def feed_batch(self,
                   mode: str,
                   batch: Dict[str, Any],
                   task_name,
                   output_dict=None) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """
        :param mode: 模型的运行方式，'trn'表示训练；'eval'表示评估；'predict'表示预测。
        :param batch:
        :param task_name:
        :param output_dict:
        :return:
        """
        h = self._encode(batch, task_name, output_dict)
        task = self.tasks[task_name]
        task.feed_batch(mode,
                        h,
                        batch=batch,
                        output_dict=output_dict,
                        decoder=self._model.decoders[task_name],
                        task_name=task_name)
        return output_dict, batch

    def decode_output(self, mode, output_dict, batch, task_name=None):
        self.tasks[task_name].decode_output(mode,
                                            output_dict,
                                            batch,
                                            self._model.decoders[task_name],
                                            task_name=task_name)

    def post_transform(self, task_name, batch, output_dict, device, **kwargs):
        self.tasks[task_name].post_transform(task_name, batch, output_dict, device, **kwargs)

    def merge_subwords_hidden(self, batch, output_dict):
        assert 'token_tail' in batch, '第一个任务不是分词任务，必须提供单词列表，用来生成token_span。'
        output_dict['hidden'] = pick_tensor_for_each_token(output_dict['hidden'], batch['token_tail'], average_subwords=True)
        return output_dict['hidden']

    def _encode(self, batch, task_name, output_dict=None):
        model = self._model
        if 'hidden' in output_dict:
            hidden = output_dict['hidden']
        else:
            hidden = model.encoder(input_ids=batch['subtoken_input_ids'])  # 这里没有传入subtoken mask矩阵，内部通过ne(0)来自动创建mask。
            output_dict['hidden'] = hidden
            if not isinstance(self.tasks[task_name], Segment):
                hidden = self.merge_subwords_hidden(batch, output_dict)
        return hidden

    def predict_task(self, task_name, task, batch, output_dict, **kwargs):
        output_dict, batch = self.feed_batch('predict', batch, task_name, output_dict)
        #if not task_name == 'dep':
        self.decode_output(mode='predict', output_dict=output_dict, batch=batch, task_name=task_name)
        output_dict[task_name]['result'] = task.prediction_to_result(output_dict[task_name]['prediction'], None, batch, output_dict, task_name=task_name, **kwargs)
        return output_dict

    def __call__(self,
                 data: Union[List[str], List[List[str]]],
                 batch_size=32,
                 is_split_into_words=False,
                 batch_max_tokens=None,
                 use_effective_tokens=False,
                 device='cpu',
                 tasks: Optional[Union[str, List[str]]] = None,  # 任务名称。
                 skip_tasks: Optional[Union[str, List[str]]] = None,
                 **kwargs
                 ):
        """
        :param data: 为了防止用户混淆，不允许传入str,如果只解析一个句子，请用列表包装。如果传入List[str],列表中每个str代表一个句子，此时参数is_split_into_words无效；如果为空，则返回空的结果。
        如果传入List[List[str]],每个List[str]代表一个句子，此时当is_split_into_words为True，则表示每个str是一个token,不用再对句子分词，
        如果is_split_into_words为False，则表示每个str代表一个group，句子仍需要分词，在分词时不同的group的字符不会被放到同一个token中。
        :param batch_size:  批量预测的size。
        :param is_split_into_words: 如果为True，表示传入的data已经提前做好分词，这要求传入的data必须是List[List[str]]。
        否则表示传入的数据还没有分词，传入的data可以是List[str]，也可以是List[List[str]]，前者str代表一个句子，后者内层的List[str]代表一个被用户划分group的句子，在分词时不同组的字符不会被识别成一个token。
        :param batch_max_tokens: 默认不用，该值用来限制一个batch的词条数，同时设置batch_size和该值可能会使得真实batch_size是变化的。
        :param use_effective_tokens: 默认False，表示统计batch中词条数目时不考虑pad的单词，反之表示将pad词也统计进去。当batch_max_tokens为None时该值无意义。
        :param device: 在cpu或者gpu上运行。
        :param tasks: 指定要做的任务。默认为None，即做模型包含的所有任务。
        :param skip_tasks: 跳过的任务。
        :param kwargs: return_group_info，返回组信息（每一组中有哪些单词组成）,仅当is_split_into_words有效且为False时可用，其它情况无效。
        :return:  返回一个字典，key是任务名，value是对应任务的所有data的预测结果。
        """

        return self.predict(**merge_locals_kwargs(locals(), kwargs))

    def predict(self,
                data: Union[List[str], List[List[str]]],
                batch_size=16,
                is_split_into_words=False,
                batch_max_tokens=None,
                use_effective_tokens=False,
                device='cpu',
                tasks: Optional[Union[str, List[str]]] = None,  # 任务名称。
                skip_tasks: Optional[Union[str, List[str]]] = None,
                **kwargs
                ):
        """
        :param data: 为了防止用户混淆，不允许传入str,如果只解析一个句子，请用列表包装。如果传入List[str],列表中每个str代表一个句子，此时参数is_split_into_words无效；如果为空，则返回空的结果。
        如果传入List[List[str]],每个List[str]代表一个句子，此时当is_split_into_words为True，则表示每个str是一个token,不用再对句子分词，
        如果is_split_into_words为False，则表示每个str代表一个group，句子仍需要分词，在分词时不同的group的字符不会被放到同一个token中。
        :param batch_size:  批量预测的size。
        :param is_split_into_words: 如果为True，表示传入的data已经提前做好分词，传入的data必须是List[List[str]]时该参数才会生效。
        否则表示传入的数据还没有分词，传入的data可以是List[str]，也可以是List[List[str]]，前者str代表一个句子，后者内层的List[str]代表一个被用户划分group的句子，在分词时不同组的字符不会被识别成一个token。
        :param batch_max_tokens: 默认不用，该值用来限制一个batch的词条数，同时设置batch_size和该值可能会使得真实batch_size是变化的。
        :param use_effective_tokens: 默认False，表示统计batch中词条数目时不考虑pad的单词，反之表示将pad词也统计进去。当batch_max_tokens为None时该值无意义。
        :param device: 在cpu或者gpu上运行。
        :param tasks: 指定要做的任务。
        :param skip_tasks: 跳过的任务。
        :param kwargs:
        :return:  返回一个字典，key是任务名，value是对应任务的所有data的预测结果列表。
        """
        results = defaultdict(list)
        if isinstance(tasks, str):
            tasks = list(tasks)
        if tasks is None:
            tasks = OrderedDict((k, v) for k, v in self.tasks.items())
        else:
            assert isinstance(tasks, list) and len(tasks) > 0
            tasks = OrderedDict((name, aTask) for name, aTask in self.tasks.items() if name in tasks)

        if isinstance(skip_tasks, str):
            skip_tasks = list(skip_tasks)
        if isinstance(skip_tasks, list):
            for name in skip_tasks:
                if name in tasks:
                    tasks.pop(name)
        else:
            assert skip_tasks is None, 'skip_tasks表示需要跳过的任务名称。'

        if not (isinstance(data, List) and (
                isinstance(data[0], str) or (isinstance(data[0], list) and isinstance(data[0][0], str)))):
            raise ValueError('参数data只能是List[str]，或者List[List[str]]，详细信息请查看注释。')
        if not data or not data[0]:
            return results
        ordered_task = OrderedDict(tasks)
        first_task_name = list(ordered_task.keys())[0]

        tok_task_name = None
        if isinstance(data[0], list) and is_split_into_words:  # 需要考虑参数is_split_into_words
            if isinstance(ordered_task[first_task_name], Segment):
                ordered_task.pop(first_task_name)
                tok_task_name = first_task_name
        else:
            is_split_into_words = False
            # TODO:实现：如果需要做分词，但是指定的任务不包含分词任务，则将分词任务指定为第一个任务。
            assert isinstance(ordered_task[first_task_name], Segment), '因为提供的data中句子没有提前做分词，所有需要第一个任务是分词任务。'
        if not ordered_task:
            return results
        with torch.no_grad():
            self.model.eval()
            first_task_name = list(ordered_task.keys())[0]
            first_task: Task = ordered_task[first_task_name]
            transform_list = TransformList(self._common_transform)
            transform_list.extend(first_task.build_transformList(mode='predict', task_name=first_task_name))
            data = first_task.build_sample(data)
            dataset = first_task.build_dataset(data, transform_list=transform_list)
            task_loader = first_task.build_dataloader(dataset, batch_size=batch_size, batch_max_tokens=batch_max_tokens,
                                                      shuffle=False, use_effective_tokens=use_effective_tokens)

            ids = []
            for batch in task_loader:
              #with autocast():

                output_dict = {}
                ids.extend(batch['_id'])
                for signature, value in batch.items():
                    if isinstance(value, torch.Tensor):
                        batch[signature] = value.to(device)
                if not isinstance(first_task, Segment) and 'token' in batch:  # 如果提供了已经分好词的句子。
                    results[tok_task_name if tok_task_name else 'token'].extend(batch['token'])  # 保证分词的结果也是排好序的，方便后面重新恢复正常顺序。
                for task_name, task in ordered_task.items():
                    output_dict = self.predict_task(task_name, task, batch, output_dict, **kwargs)
                    results[task_name].extend(output_dict[task_name]['result'])
                    self.post_transform(task_name, batch, output_dict, device, **self.config)
        return dict(
            zip(results.keys(), [list(d) for d in zip(*(sorted(zip(*results.values(), ids), key=lambda x: x[-1])))]))


if __name__ == '__main__':
    pass
    import transformers
    transformers.ElectraModel
