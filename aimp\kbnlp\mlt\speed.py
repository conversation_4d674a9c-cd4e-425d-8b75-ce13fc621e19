from kbnlp.mlt.multi_task_learning import MultiTaskLearning
from kbnlp.mlt.data_transform import TransformList, TransformerSequenceTokenizer
from kbnlp.mlt.task.segment import Segment, read_cws_tsv_data
import torch
from time import time
DEVICE = 'cuda:0'
mlt = MultiTaskLearning()
mlt.load(save_dir='data/models/electra_small_tok_pos_ner_dep_rte_dist', device=DEVICE)

_CTB8_HOME = r'C:/Users/<USER>/AppData/Roaming/hanlp'  #  r'/home/<USER>/.hanlp'
CTB8_CWS_DEV = _CTB8_HOME + '/thirdparty/wakespace.lib.wfu.edu/bitstream/handle/10339/39379/LDC2013T21/data/tasks/cws/dev.txt'
data = [''.join(d) for d in read_cws_tsv_data(CTB8_CWS_DEV)[:]]
s = time()
tok = mlt.tasks['tok']
transform_list = TransformList(mlt._common_transform)
transform_list.extend(mlt.tasks['tok'].build_transformList(mode='predict', task_name='tok'))
data = tok.build_sample(data)
dataset = tok.build_dataset(data, transform_list=transform_list)
loader = tok.build_dataloader(dataset, batch_size=32, batch_max_tokens=510, shuffle=False, use_effective_tokens=False)
print('len:%d, batch_size:%d. ' % (len(loader.dataset), loader.batch_sampler.batch_size), end='')
with torch.no_grad():
    mlt.model.eval()
    for batch in loader:
        for signature, value in batch.items():
            if isinstance(value, torch.Tensor):
                batch[signature] = value.to(DEVICE)
        output_dict = {}
        
        h = mlt._encode(batch, task_name='tok', output_dict=output_dict)
        tok.feed_batch('predict',
                        h,
                        batch=batch,
                        output_dict=output_dict,
                        decoder=mlt._model.decoders['tok'],
                        task_name='tok')
        
        mlt.decode_output(mode='predict', output_dict=output_dict, batch=batch, task_name='tok')
        output_dict['tok']['result'] = tok.prediction_to_result(output_dict['tok']['prediction'], None, batch, output_dict, task_name='tok')
        
        mlt.post_transform('tok', batch, output_dict, DEVICE)
        
e = time()
print('time:%fs.' % (e - s))
