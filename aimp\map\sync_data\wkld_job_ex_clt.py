import json

import requests
from time import sleep
import base64


def example(jobs):
    ids = dict()
    for j in jobs:
        x = requests.post('http://ubt-B450M-02:9000/workload/job', json=j).json()
        ids[x['JobId']] = j
    result = {
        "layout": None,
        "ocr": None
    }
    while ids:
        print('sleep 0.5')
        sleep(0.5)
        todel = set()
        for i in ids:
            x = requests.post(f'http://ubt-B450M-02:9000/workload/job/{i}').json()
            state = x['JobState']
            if state in ['COMPLETED', 'FAILED']:
                todel.add(i)
                output = x['StdOut'] if state == 'COMPLETED' else x['StdErr']
                output = base64.urlsafe_b64decode(output)  # bytes
                # if 'modfunc' in ids[i] and ids[i]['modfunc'] in ['mtl.layout', 'mtl.ocr']:
                #     output = json.loads(output)
                output = output.decode('utf-8')  # for text output
                # print(i, state, output)
                print(output)
                if ids[i]["modfunc"] == "mtl.layout":
                    result["layout"] = json.loads(output)
                if ids[i]["modfunc"] == "mtl.ocr":
                    result["ocr"] = json.loads(output)
            else:
                print(i, state)
        for i in todel:
            del ids[i]

    return result
