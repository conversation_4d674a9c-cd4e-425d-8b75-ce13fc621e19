from qa.kbnlp.mlt.layers.dropout import WordDropout
from qa.kbnlp.mlt.common.configurable import AutoConfigurable
import torch
from torch import nn
from transformers import AutoTokenizer, AutoModel, AutoConfig
from typing import Sequence, Union, Tuple, Dict, Optional, Any


class ContextualWordEmbedding(AutoConfigurable):
    def __init__(self,
                 average_subwords=False,
                 # scalar_mix: Union[ScalarMixWithDropoutBuilder, int] = None,
                 word_dropout: Optional[Union[float, Tuple[float, str]]] = None,
                 max_sequence_length=None,
                 # truncate_long_sequences=False,
                 input_key='original_input',
                 extra_tranform=None,
                 **kwargs) -> None:
        """A contextual word embedding builder which builds packagea
        :class:`~hanlp.layers.embeddings.contextual_word_embedding.ContextualWordEmbeddingModule` and packagea
        :class:`~hanlp.transform.transformer_tokenizer.TransformerSequenceTokenizer`.

        Args:
            field: The field to work on. Usually some token fields.
            transformer:  ``PreTrainedModel``.
            average_subwords: ``True`` to average subword representations.
            scalar_mix: Layer attention.
            word_dropout: Dropout rate of randomly replacing packagea subword with MASK.
            max_sequence_length: The maximum sequence length. Sequence longer than this will be handled by sliding
                window.
            truncate_long_sequences: ``True`` to return hidden states of each layer.
            ret_token_span: ``True`` to return span of each token measured by subtoken offsets.
            ret_subtokens: ``True`` to return list of subtokens belonging to each token.
            ret_subtokens_group: ``True`` to return list of offsets of subtokens belonging to each token.
            ret_raw_hidden_states: ``True`` to return hidden states of each layer.
        """
        super().__init__()
        # self.truncate_long_sequences = truncate_long_sequences
        self.max_sequence_length = max_sequence_length
        self.word_dropout = word_dropout
        # self.scalar_mix = scalar_mix
        self.average_subwords = average_subwords
        # self.tokenizer = tokenizer if tokenizer else transformer
        self._input_key = input_key  # input_key参数不在使用，重新设计一个_input_key是为了能够加载之前训练得到的模型。
        self._extra_tranform = extra_tranform  # extra_tranform参数不在使用，重新设计一个_extra_tranform是为了能够加载之前训练得到的模型。

    def module(self, transformer, tokenizer, **kwargs) -> Optional[nn.Module]:
        return ContextualWordEmbeddingModule(
            transformer,
            tokenizer,
            self.average_subwords,
            # self.scalar_mix,
            self.word_dropout,
            self.max_sequence_length,
        )


class ContextualWordEmbeddingModule(nn.Module):
    def __init__(self,
                 transformer,
                 transformer_tokenizer,
                 average_subwords=False,
                 word_dropout=None,
                 max_sequence_length=None,):
        """A pre-trained transformer encoder.

        Args:
            transformer: A ``PreTrainedModel`` or an identifier of packagea ``PreTrainedModel``.
            transformer_tokenizer: A ``PreTrainedTokenizer``.
            average_subwords: ``True`` to average subword representations.
            # scalar_mix: Layer attention.
            word_dropout: Dropout rate of randomly replacing packagea subword with MASK.
            max_sequence_length: The maximum sequence length. Sequence longer than this will be handled by sliding
                window.
        """
        super().__init__()
        self.max_sequence_length = max_sequence_length
        self.average_subwords = average_subwords
        if word_dropout:
            oov = transformer_tokenizer.mask_token_id
            if isinstance(word_dropout, Sequence):
                word_dropout, replacement = word_dropout
                if replacement == 'unk':
                    # Electra English has to use unk
                    oov = transformer_tokenizer.unk_token_id
                elif replacement == 'mask':
                    # UDify uses [MASK]
                    oov = transformer_tokenizer.mask_token_id
                else:
                    oov = replacement
            pad = transformer_tokenizer.pad_token_id
            cls = transformer_tokenizer.cls_token_id
            sep = transformer_tokenizer.sep_token_id
            excludes = [pad, cls, sep]
            self.word_dropout = WordDropout(p=word_dropout, oov_token=oov, exclude_tokens=excludes)
        else:
            self.word_dropout = None

        if isinstance(transformer, str):
            transformer = AutoModel.from_pretrained(transformer)
        if hasattr(transformer, 'encoder') and hasattr(transformer, 'decoder'):
            # For seq2seq pretrained, use its encoder
            transformer = transformer.encoder
        self.transformer = transformer
        self.hidden_size = self.transformer.config.hidden_size

    def forward(self, input_ids: torch.LongTensor, attention_mask=None, token_type_ids=None, token_span=None, **kwargs):
        if self.word_dropout:
            input_ids = self.word_dropout(input_ids)

        x = transformer_encode(self.transformer,
                               input_ids,
                               attention_mask,
                               token_type_ids,
                               token_span,
                               # layer_range=self.scalar_mix.mixture_range if self.scalar_mix else 0,
                               max_sequence_length=self.max_sequence_length,
                               average_subwords=self.average_subwords,)
        # if self.ret_raw_hidden_states:
        #     x, raw_hidden_states = x
        # if self.scalar_mix:
        #     x = self.scalar_mix(x)
        # if self.ret_raw_hidden_states:
        #     # noinspection PyUnboundLocalVariable
        #     return x, raw_hidden_states
        return x


def transformer_encode(transformer,
                       input_ids,
                       attention_mask=None,
                       token_type_ids=None,
                       token_span=None,
                       layer_range: Union[int, Tuple[int, int]] = 0,
                       max_sequence_length=None,
                       average_subwords=False,
                       ret_raw_hidden_states=False):
    """Run transformer and pool its outputs.

    Args:
        transformer: A transformer pretrained.
        input_ids: Indices of subwords.
        attention_mask: Mask for these subwords.
        token_type_ids: Type ids for each subword.
        token_span: The spans of tokens.
        layer_range: The range of layers to use. Note that the 0-th layer means embedding layer, so the last 3 layers
                    of packagea 12-layer BERT will be (10, 13).
        max_sequence_length: The maximum sequence length. Sequence longer than this will be handled by sliding
                    window.
         average_subwords: ``True`` to average subword representations.
        ret_raw_hidden_states: ``True`` to return hidden states of each layer.

    Returns:
        Pooled outputs.

    """
    if max_sequence_length and input_ids.size(-1) > max_sequence_length:
        # TODO: split token type ids in transformer_sliding_window if token type ids are not always 1
        # outputs = transformer_sliding_window(transformer, input_ids, max_pieces=max_sequence_length)
        raise NotImplementedError('暂时没有实现处理超长输入。')
    else:
        if attention_mask is None:
            attention_mask = input_ids.ne(0)
    outputs = transformer(input_ids, attention_mask, token_type_ids)[0]
    return outputs

        # if transformer.config.output_hidden_states:
        #     outputs = transformer(input_ids, attention_mask, token_type_ids)[-1]
        # else:
        #     outputs = transformer(input_ids, attention_mask, token_type_ids)[0]
    # if transformer.config.output_hidden_states:
    #     if isinstance(layer_range, int):
    #         outputs = outputs[layer_range:]
    #     else:
    #         outputs = outputs[layer_range[0], layer_range[1]]
    #     # Slow pick
    #     # hs = []
    #     # for h in outputs:
    #     #     hs.append(pick_tensor_for_each_token(h, token_span, average_subwords))
    #     # Fast pick
    #     if not isinstance(outputs, torch.Tensor):
    #         x = torch.stack(outputs)
    #     else:
    #         x = outputs
    #     L, B, T, F = x.size()
    #     x = x.flatten(end_dim=1)
    #     # tile token_span as x
    #     if token_span is not None:
    #         token_span = token_span.repeat(L, 1, 1)
    #     hs = pick_tensor_for_each_token(x, token_span, average_subwords).view(L, B, -1, F)
    #     if ret_raw_hidden_states:
    #         return hs, outputs
    #     return hs
    # else:
    #     if ret_raw_hidden_states:
    #         return pick_tensor_for_each_token(outputs, token_span, average_subwords), outputs
    #     return pick_tensor_for_each_token(outputs, token_span, average_subwords)


if __name__ == '__main__':
    print('finish.')
