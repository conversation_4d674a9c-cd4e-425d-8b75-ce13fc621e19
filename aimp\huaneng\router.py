# -*- coding: utf-8 -*-
# __author__: SH
# 2024-11-30 21:09:18]
import json
import os
import httpx
import zmail
import re
from urllib.parse import urlparse, unquote

import datetime
from pathlib import Path
from pydantic import BaseModel, validator
from typing import Union
from fastapi import Body, APIRouter, HTTPException

from common.customResponse import resp

api = APIRouter(
    prefix="/api/huaneng",
    tags=["huaneng"],
    responses={404: {"description": "Not found"}}
)


class DownloadFile(BaseModel):
    url: str
    header: Union[str, None] = None

    class Config:
        frozen = True

    @validator("url")
    def validate_url(cls, value):
        url_regex = re.compile(
            r'^(https?|ftp)://[^\s/$.?#].[^\s]*$',
            re.IGNORECASE
        )
        if not url_regex.match(value):
            raise ValueError("Invalid URL format")
        return value

    @validator("header", pre=True, always=True)
    def validate_header(cls, value):
        if value is not None:
            try:
                parsed = json.loads(value)
                if type(parsed) != dict:
                    raise ValueError("Header must be a valid JSON string")
            except json.JSONDecodeError:
                raise ValueError("Header must be a valid JSON string")
        return value


@api.post("/downloadfile")
async def download_file(param: DownloadFile = Body()):
    file_path = os.getenv("DOWNLOAD_FILE_PATH", "./downloads")

    # 确保文件夹存在
    os.makedirs(file_path, exist_ok=True)

    headers = json.loads(param.header) if param.header else {}
    headers["User-Agent"] = (
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 "
        "(KHTML, like Gecko) Chrome/********* Safari/537.36"
    )

    mime_to_extension = {
        "application/zip": ".zip",
        "application/pdf": ".pdf",
        "image/jpeg": ".jpg",
        "image/png": ".png",
        # 更多 MIME 类型映射...
    }

    try:
        # 发起请求下载文件
        async with httpx.AsyncClient() as client:
            async with client.stream("GET", param.url, headers=headers) as resp1:
                if resp1.status_code != 200:
                    raise HTTPException(
                        status_code=resp1.status_code,
                        detail=f"Failed to download file: {resp1.reason_phrase}"
                    )

                # 解析文件名
                content_disposition = resp1.headers.get("Content-Disposition")
                if content_disposition and "filename=" in content_disposition:
                    file_name = content_disposition.split("filename=")[-1].strip('"')
                else:
                    url_file_name = os.path.basename(param.url)
                    file_name = url_file_name or "default_file_name"

                # 清理文件名中的查询参数和特殊字符
                file_name = unquote(file_name)  # 解码 URL 编码的字符
                file_name = re.sub(r'[<>:"/\\|?*]', "_", file_name)  # 替换文件名中不允许的字符

                # 获取文件扩展名
                content_type = resp1.headers.get("Content-Type")
                file_extension = mime_to_extension.get(content_type, "") if content_type else ""

                # 如果没有 Content-Type，可以从 URL 中提取扩展名
                if not file_extension:
                    file_extension = os.path.splitext(file_name)[1]

                # 确保文件名有扩展名
                if not file_name.endswith(file_extension):
                    file_name += file_extension

                # 保存文件
                file_full_path = os.path.join(file_path, file_name)
                with open(file_full_path, "wb") as f:
                    async for chunk in resp1.aiter_bytes(1024):
                        f.write(chunk)

        return resp(code=0)

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"An error occurred: {str(e)}")


class readEmail(BaseModel):
    email_username: str
    email_password: str
    subject: Union[str, None] = None
    sender: Union[str, None] = None
    start_time: Union[str, None] = None
    end_time: Union[str, None] = None
    start_index: Union[str, None] = None
    end_index: Union[str, None] = None

    @validator("email_username")
    def validate_url(cls, value):
        if not re.match(r'^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$', value):
            raise ValueError("Invalid username format")
        return value

@api.post("/reademail")
async def read_email(param:readEmail=Body()):
    """
    获取邮件
    :param email_username:
    :param email_password:
    :param subject:
    :param start_time:
    :param end_time:
    :param sender:
    :param start_index:
    :param end_index:
    :param target_path:
    :return:
    """
    print(123)

    email_username = param.email_username
    email_password = param.email_password


    server = zmail.server(email_username, email_password)

    mails = server.get_mails(
        subject=param.subject,
        start_time=param.start_time,
        end_time=param.end_time,
        sender=param.sender,start_index=param.start_index, end_index=param.end_index)

    total_count = server.stat()[0]  # 总邮件数
    print(total_count)

    mail_count = len(mails)
    print(f"Total number of mails: {mail_count}")



    email_list = []
    j = 0
    for mail in reversed(mails):
        j += 1
        date = mail.get('date').strftime("%Y-%m-%d %H:%M:%S") \
            if mail.get('date') else 'Unknown date'
        attachments = ",".join([i[0] for i in mail.get("attachments")]) if mail.get("attachments") else "无"
        email_details = {
            "list_index":j,
            'id': mail.get('id'),
            'subject': mail.get('subject', 'No subject'),  # 如果没有主题，返回 'No subject'
            'sender': mail.get('from', 'Unknown sender'),  # 如果没有发件人，返回 'Unknown sender'
            'date': date,  # 如果没有日期，返回 'Unknown date'
            'content': mail.get('content_text', 'No content'),  # 如果没有内容，返回 'No content'
            'attachments':attachments
        }
        email_list.append(email_details)

    return resp(code=0,data=email_list)



class SaveEmail(BaseModel):
    email_username: str
    email_password: str
    id:int


    @validator("email_username")
    def validate_url(cls, value):
        if not re.match(r'^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$', value):
            raise ValueError("Invalid username format")
        return value


@api.post("/saveattachment")
async def save_attachment(param: SaveEmail = Body()):
    """
    筛选转发并保存邮件附件
    :param email_username: 邮箱用户名
    :param email_password: 邮箱密码
    :param id: 邮件ID
    :return: 保存状态或错误信息
    """
    email_path = os.getenv("EMAIL_FILE_PATH", "./email/filePath")  # 保存附件的路径
    email_username = param.email_username
    email_password = param.email_password
    mail_id = param.id

    try:
        # 登录邮箱
        server = zmail.server(email_username, email_password)

        # 获取指定邮件
        mail = server.get_mail(mail_id)

        if not mail:
            raise HTTPException(status_code=404, detail="Mail not found")

        # 获取附件
        attachments = mail.get('attachments', None)
        if attachments:
            # 确保文件夹存在
            os.makedirs(email_path, exist_ok=True)

            # 保存附件
            for attachment in attachments:

                file_path = os.path.join(email_path, attachment[0])

                # 写入文件
                with open(file_path, 'wb') as f:
                    f.write(attachment[1])
                    print(f"Attachment saved to {file_path}")

            return resp(code=0)
        else:
            return resp(code=0)

    # except zmail.error.AuthenticationError:
    #     raise HTTPException(status_code=401, detail="Authentication failed. Check your username and password.")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"An error occurred: {str(e)}")


class ReEmail(SaveEmail):
    revicer:list[str] = []

    @validator("revicer")
    def validate_url(cls, value_list):
        for value in value_list:
            if not re.match(r'^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$', value):
                raise ValueError("Invalid username format")
        return value_list


@api.post("/reemail")
async def save_path(param: ReEmail = Body()):
    """
    筛选转发并保存邮件附件
    :param email_username: 邮箱用户名
    :param email_password: 邮箱密码
    :param id: 邮件ID
    :return: 保存状态或错误信息
    """

    email_username = param.email_username
    email_password = param.email_password
    mail_id = param.id
    email_path = os.getenv("EMAIL_FILE_PATH", "./email/filePath")  # 保存附件的路径
    email_path = f"{email_path}/.temp"
    try:
        # 登录邮箱
        server = zmail.server(email_username, email_password)

        # 获取指定邮件
        mail = server.get_mail(mail_id)

        if not mail:
            raise HTTPException(status_code=404, detail="Mail not found")

        attachments = mail.get('attachments', [])
        new_attachments = []
        if attachments:
            # 确保文件夹存在
            os.makedirs(email_path, exist_ok=True)

            # 保存附件
            for attachment in attachments:
                file_path = os.path.join(email_path, attachment[0])
                new_attachments.append(file_path)
                # 写入文件
                with open(file_path, 'wb') as f:
                    f.write(attachment[1])
        new_mail = {
            'subject': mail.get('subject'),  # 如果没有主题，返回 'No subject'
            'content': mail.get('content_text'),
            "attachments":new_attachments
        }
        # if mail.get('attachments'):
        #     new_mail["attachments"] = mail.get('attachments') # 如果有附件，保留附件

        # 获取附件
        server.send_mail(param.revicer, new_mail)

        for attachment in new_mail['attachments']:
            os.remove(attachment)
        return resp(code=0)
    # except zmail.error.AuthenticationError:
    #     raise HTTPException(status_code=401, detail="Authentication failed. Check your username and password.")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"An error occurred: {str(e)}")