{"_auto_prompt_vocab": {"入伍日期": 1, "入党日期": 13, "入团日期": 15, "出生日期": 14, "别名": 16, "哪里人": 4, "姓名": 2, "性别": 6, "战争战役": 7, "战功": 9, "政治面貌": 5, "次数": 8, "牺牲日期": 10, "的次数": 12, "职务": 11, "部队": 3}, "_name_or_path": "/home/<USER>/.kbnlp/tasks/information_extraction/uie-nano/", "architectures": ["AutoPromptUIE"], "attention_probs_dropout_prob": 0.1, "auto_prompt_len": 12, "classifier_dropout": null, "conjunct": "的", "entity_labels": ["入伍日期", "姓名", "部队", "哪里人", "政治面貌", "性别", "战争战役", "次数", "战功", "牺牲日期", "职务", "入党日期", "出生日期", "入团日期", "别名"], "gradient_checkpointing": false, "hidden_act": "gelu", "hidden_dropout_prob": 0.1, "hidden_size": 312, "initializer_range": 0.02, "intermediate_size": 1248, "layer_norm_eps": 1e-12, "max_position_embeddings": 2048, "model_type": "bert", "num_attention_heads": 12, "num_hidden_layers": 4, "pad_token_id": 0, "position_embedding_type": "absolute", "predicate2subject_labels": {"次数": ["战功"]}, "task_id": 0, "task_type_vocab_size": 16, "torch_dtype": "float32", "transformers_version": "4.21.0", "type_auto_prompt_size": 1000, "type_vocab_size": 4, "use_cache": true, "use_task_id": true, "vocab_size": 40000}