# Copyright (c) 2022 PaddlePaddle Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.


import torch
from transformers import AutoTokenizer, AutoConfig
from functools import partial
import numpy as np
from torch.utils.data import DataLoader, Dataset
# from datasets import load_dataset, Dataset
from typing import List, Dict, Union
from qa.kbnlp.task import Task
from .models.information_extraction_model import UIE, AutoPromptUIE, AutoPromptUIEConfig, batch_encode
from .utils import SchemaTree, dbc2sbc, get_span, get_id_and_prob, get_bool_ids_greater_than

usage = """"""


class UIETask(Task):
    """
    Universal Information Extraction Task.
    Args:
        task(string): The name of task.
        model(string): The model name in the task.
        kwargs (dict, optional): Additional keyword arguments passed along to the specific task.
    """

    # encoding_model_map = {
    #     "uie-base": "ernie-3.0-base-zh",
    #     "uie-tiny": "ernie-3.0-medium-zh",
    #     "uie-medical-base": "ernie-3.0-base-zh"
    # }
    # resource_files_names = {
    #     "model_state": "model_state.pdparams",
    #     "model_config": "model_config.json",
    #     "vocab_file": "vocab.txt",
    #     "special_tokens_map": "special_tokens_map.json",
    #     "tokenizer_config": "tokenizer_config.json"
    # }

    def __init__(self, model, schema, device='cpu', **kwargs):
        super().__init__(model=model, device=device, **kwargs)
        # self.task  在父类中被自动设置为当前类所在py文件名
        self._usage = usage
        self._schema_tree = None
        self.set_schema(schema)
        # if model not in self.encoding_model_map.keys():
        #     raise ValueError(
        #         "Model should be one of uie-base, uie-tiny and uie-medical-base")
        # self._encoding_model = self.encoding_model_map[model]
        self._position_prob = kwargs[
            'position_prob'] if 'position_prob' in kwargs else 0.5



    def _construct_model(self):
        """
        Construct the inference model for the predictor.
        """
        model_instance = UIE.from_pretrained(self._task_path)
        self._model = model_instance
        self.conjunct = self._model.conjunct
        self._model.eval()

    def _construct_tokenizer(self):
        """
        Construct the tokenizer for the predictor.
        """
        try:
            self._tokenizer = AutoTokenizer.from_pretrained(self._task_path)
        except Exception as e:
            import traceback
            print(traceback.format_exc())

    def _preprocess(self, inputs: Union[str, List[str]], padding=True, add_special_tokens=True, **kwargs):
        """
        Transform the raw text to the model inputs, two steps involved:
           1) Transform the raw text to token ids.
           2) Generate the other model inputs from the raw text and token ids.
        inputs:__call__方法的输入
        """
        inputs = self._check_input_text(inputs)
        outputs = {}
        outputs['text'] = inputs
        return outputs

    def _run_model(self, inputs):
        """
        inputs: _preprocess方法的输出
        Run the task model from the outputs of the `_tokenize` function.
        """
        raw_inputs = inputs['text']
        results = self._multi_stage_predict(raw_inputs)
        inputs['result'] = results
        return inputs

    def _postprocess(self, inputs):
        """
        This function will convert the model output to raw text.
        inputs:_run_model方法的输出
        """
        return inputs['result']

    def collate(self, examples):
        return batch_encode(examples, self._tokenizer, self._max_seq_len)
        # prompt = [exam['prompt'] for exam in examples]
        # text = [exam['text'] for exam in examples]
        # tokenizer_outputs = self._tokenizer(text=prompt,
        #                                     text_pair=text,
        #                                     max_length=self._max_seq_len,
        #                                     truncation='only_second',
        #                                     padding=True,
        #                                     return_token_type_ids=True,
        #                                     return_attention_mask=True,
        #                                     return_tensors='pt',
        #                                     return_offsets_mapping=True,
        #                                     )
        # length = tokenizer_outputs['input_ids'].size(1)
        # tokenizer_outputs['position_ids'] = torch.arange(length).unsqueeze(0)
        # tokenizer_outputs['offset_mapping'] = tokenizer_outputs['offset_mapping'].numpy()
        # return tokenizer_outputs

    # -m kernprof - l
    # @profile
    def _single_stage_predict(self, inputs):
        input_texts = []
        prompts = []
        for i in range(len(inputs)):
            input_texts.append(inputs[i]["text"])
            prompts.append(inputs[i]["prompt"])
        # max predict length should exclude the length of prompt and summary tokens
        max_predict_len = self._max_seq_len - len(max(prompts)) - 3

        short_input_texts, self.input_mapping = self._auto_splitter(
            input_texts, max_predict_len, split_sentence=self._split_sentence)

        short_texts_prompts = []
        for k, v in self.input_mapping.items():
            short_texts_prompts.extend([prompts[k] for i in range(len(v))])
        short_inputs = [{
            "text": short_input_texts[i],
            "prompt": short_texts_prompts[i]
        } for i in range(len(short_input_texts))]

        dataset = UIEDataset(short_inputs)

        loader = DataLoader(dataset, batch_size=self._batch_size, collate_fn=self.collate)
        dataset_batched = UIEDataset([batch for batch in loader])
        device = self.device
        loader = DataLoader(dataset_batched, batch_size=1,
                            collate_fn=lambda examples: dict(
                                (key, val.to(device) if isinstance(val, torch.Tensor) else val) for (key, val) in
                                examples[0].items()))

        sentence_ids = []
        probs = []
        for batch in loader:
            # 这里暂时修改成下面，trace后的模型必须要接受位置参数。
            inputs = [batch['input_ids'], batch['attention_mask'], batch['token_type_ids'], batch['position_ids']]
            if 'auto_prompt_ids' in batch:
                inputs.append(batch['auto_prompt_ids'])
            res = self._model(*inputs)
            start_prob, end_prob = res['start_prob'], res['end_prob']
            start_ids_list = get_bool_ids_greater_than(
                start_prob.cpu().numpy(), limit=self._position_prob, return_prob=True)
            end_ids_list = get_bool_ids_greater_than(
                end_prob.cpu().numpy(), limit=self._position_prob, return_prob=True)

            for start_ids, end_ids, ids, offset_map in zip(
                    start_ids_list, end_ids_list,
                    batch['input_ids'].tolist(), batch['offset_mapping'].tolist()):
                for i in reversed(range(len(ids))):
                    if ids[i] != 0:
                        ids = ids[:i]
                        break
                span_list = get_span(start_ids, end_ids, with_prob=True)
                sentence_id, prob = get_id_and_prob(span_list, offset_map)
                sentence_ids.append(sentence_id)
                probs.append(prob)
        results = self._convert_ids_to_results(short_inputs, sentence_ids,
                                               probs)
        results = self._auto_joiner(results, short_input_texts,
                                    self.input_mapping)
        return results

    def _auto_joiner(self, short_results, short_inputs, input_mapping):
        concat_results = []
        is_cls_task = False
        for short_result in short_results:
            if short_result == []:
                continue
            elif 'start' not in short_result[0].keys(
            ) and 'end' not in short_result[0].keys():
                is_cls_task = True
                break
            else:
                break
        for k, vs in input_mapping.items():
            if is_cls_task:
                cls_options = {}
                single_results = []
                for v in vs:
                    if len(short_results[v]) == 0:
                        continue
                    if short_results[v][0]['text'] not in cls_options.keys():
                        cls_options[short_results[v][0][
                            'text']] = [1, short_results[v][0]['probability']]
                    else:
                        cls_options[short_results[v][0]['text']][0] += 1
                        cls_options[short_results[v][0]['text']][
                            1] += short_results[v][0]['probability']
                if len(cls_options) != 0:
                    cls_res, cls_info = max(cls_options.items(),
                                            key=lambda x: x[1])
                    concat_results.append([{
                        'text': cls_res,
                        'probability': cls_info[1] / cls_info[0]
                    }])
                else:
                    concat_results.append([])
            else:
                offset = 0
                single_results = []
                for v in vs:
                    if v == 0:
                        single_results = short_results[v]
                        offset += len(short_inputs[v])
                    else:
                        for i in range(len(short_results[v])):
                            if 'start' not in short_results[v][
                                    i] or 'end' not in short_results[v][i]:
                                continue
                            short_results[v][i]['start'] += offset
                            short_results[v][i]['end'] += offset
                        offset += len(short_inputs[v])
                        single_results.extend(short_results[v])
                concat_results.append(single_results)
        return concat_results

    # def _multi_stage_predict(self, data):
    #     """
    #     Traversal the schema tree and do multi-stage prediction.
    #
    #     Args:
    #         data (list): a list of strings
    #
    #     Returns:
    #         list: a list of predictions, where the list's length
    #             equals to the length of `data`
    #     """
    #     results = [{} for _ in range(len(data))]
    #     # input check to early return
    #     if len(data) < 1 or self._schema_tree is None:
    #         return results
    #
    #     data = [dbc2sbc(d) for d in data]
    #
    #     # copy to stay `self._schema_tree` unchanged
    #     schema_list = self._schema_tree.children[:]
    #     while len(schema_list) > 0:
    #         node = schema_list.pop(0)
    #         examples = []
    #         input_map = {}
    #         cnt = 0  # 指向data的索引
    #         idx = 0  # 由于每个data在抽取某个实体、关系时可能得到零个或多个结果，导致后续抽取过程的examples个数发生的改变，所以使用idx来指定。这与将一个长文本切分成多个短文本后的记录方式类似。
    #         if not node.prefix:  # schema_list中除了root节点的所有子节点外，其它节点都有prefix。注意：如果某节点的父节点没有抽取出信息来，则子节点不会添加到schema_list里。
    #             for one_data in data:
    #                 examples.append({
    #                     "text": one_data,
    #                     "prompt": dbc2sbc(node.name)
    #                 })
    #                 input_map[cnt] = [idx]
    #                 idx += 1
    #                 cnt += 1
    #         else:
    #             for pre, one_data in zip(node.prefix, data):
    #                 if len(pre) == 0:
    #                     input_map[cnt] = []
    #                 else:
    #                     for p in pre:
    #                         examples.append({
    #                             "text": one_data,
    #                             "prompt": dbc2sbc(p + node.name)
    #                         })
    #                     input_map[cnt] = [i + idx for i in range(len(pre))]
    #                     idx += len(pre)
    #                 cnt += 1
    #         if len(examples) == 0:
    #             result_list = []
    #         else:
    #             with torch.no_grad():
    #                 result_list = self._single_stage_predict(examples)
    #
    #         if not node.parent_relations:  # root节点的子节点会进入if。
    #             relations = [[] for i in range(len(data))]
    #             for k, v in input_map.items():
    #                 for idx in v:
    #                     if len(result_list[idx]) == 0:
    #                         continue
    #                     if node.name not in results[k].keys():
    #                         results[k][node.name] = result_list[idx]
    #                     else:
    #                         results[k][node.name].extend(result_list[idx])
    #                 if node.name in results[k].keys():
    #                     relations[k].extend(results[k][node.name])
    #         else:
    #             relations = node.parent_relations
    #             for k, v in input_map.items():
    #                 for i in range(len(v)):
    #                     if len(result_list[v[i]]) == 0:
    #                         continue
    #                     if "relations" not in relations[k][i].keys():
    #                         relations[k][i]["relations"] = {
    #                             node.name: result_list[v[i]]
    #                         }
    #                     elif node.name not in relations[k][i]["relations"].keys(
    #                     ):
    #                         relations[k][i]["relations"][
    #                             node.name] = result_list[v[i]]
    #                     else:
    #                         relations[k][i]["relations"][node.name].extend(
    #                             result_list[v[i]])
    #             new_relations = [[] for i in range(len(data))]
    #             for i in range(len(relations)):
    #                 for j in range(len(relations[i])):
    #                     if "relations" in relations[i][j].keys(
    #                     ) and node.name in relations[i][j]["relations"].keys():
    #                         for k in range(
    #                                 len(relations[i][j]["relations"][
    #                                     node.name])):
    #                             new_relations[i].append(relations[i][j][
    #                                 "relations"][node.name][k])
    #             relations = new_relations
    #
    #         prefix = [[] for _ in range(len(data))]
    #         for k, v in input_map.items():
    #             for idx in v:
    #                 for i in range(len(result_list[idx])):
    #                     prefix[k].append(result_list[idx][i]["text"] + "的")
    #
    #         for child in node.children:
    #             child.prefix = prefix
    #             child.parent_relations = relations
    #             schema_list.append(child)
    #     return results

    def _multi_stage_predict(self, data):
        """
        Traversal the schema tree and do multi-stage prediction.

        Args:
            data (list): a list of strings

        Returns:
            list: a list of predictions, where the list's length
                equals to the length of `data`
        """
        results = [{} for _ in range(len(data))]
        # input check to early return
        if len(data) < 1 or self._schema_tree is None:
            return results

        data = [dbc2sbc(d) for d in data]

        # copy to stay `self._schema_tree` unchanged
        schema_list = self._schema_tree.children[:]
        while len(schema_list) > 0:
            node = schema_list.pop(0)
            examples = []
            input_map = {}
            cnt = 0  # 指向data的索引
            idx = 0  # 由于每个data在抽取某个实体、关系时可能得到零个或多个结果，导致后续抽取过程的examples个数发生的改变，所以使用idx来指定。这与将一个长文本切分成多个短文本后的记录方式类似。
            if not node.prefix:  # schema_list中除了root节点的所有子节点外，其它节点都有prefix。注意：如果某节点的父节点没有抽取出信息来，则子节点不会添加到schema_list里。
                for one_data in data:
                    examples.append({
                        "text": one_data,
                        "prompt": dbc2sbc(node.name)
                    })
                    input_map[cnt] = [idx]
                    idx += 1
                    cnt += 1
            else:
                for pre, one_data in zip(node.prefix, data):
                    if len(pre) == 0:
                        input_map[cnt] = []
                    else:
                        for p in pre:
                            examples.append({
                                "text": one_data,
                                "prompt": dbc2sbc(p + node.name)
                            })
                        input_map[cnt] = [i + idx for i in range(len(pre))]
                        idx += len(pre)
                    cnt += 1
            if len(examples) == 0:
                result_list = []
            else:
                with torch.no_grad():
                    result_list = self._single_stage_predict(examples)

            if not node.parent_relations:  # root节点的子节点会进入if。
                relations = [[] for i in range(len(data))]
                for k, v in input_map.items():
                    for idx in v:
                        if len(result_list[idx]) == 0:
                            continue
                        if node.name not in results[k].keys():
                            results[k][node.name] = result_list[idx]
                        else:
                            results[k][node.name].extend(result_list[idx])
                    if node.name in results[k].keys():
                        relations[k].extend(results[k][node.name])
            else:
                relations = node.parent_relations
                for k, v in input_map.items():
                    for i in range(len(v)):
                        if len(result_list[v[i]]) == 0:
                            continue
                        if "relations" not in relations[k][i].keys():
                            relations[k][i]["relations"] = {
                                node.name: result_list[v[i]]
                            }
                        elif node.name not in relations[k][i]["relations"].keys(
                        ):
                            relations[k][i]["relations"][
                                node.name] = result_list[v[i]]
                        else:
                            relations[k][i]["relations"][node.name].extend(
                                result_list[v[i]])
                new_relations = [[] for i in range(len(data))]
                for i in range(len(relations)):
                    for j in range(len(relations[i])):
                        if "relations" in relations[i][j].keys(
                        ) and node.name in relations[i][j]["relations"].keys():
                            for k in range(
                                    len(relations[i][j]["relations"][
                                        node.name])):
                                new_relations[i].append(relations[i][j][
                                    "relations"][node.name][k])
                relations = new_relations

            prefix = [[] for _ in range(len(data))]
            for k, v in input_map.items():
                for idx in v:
                    for i in range(len(result_list[idx])):
                        prefix[k].append(result_list[idx][i]["text"] + self.conjunct)

            for child in node.children:
                child.prefix = prefix
                child.parent_relations = relations
                schema_list.append(child)
        return results

    def _convert_ids_to_results(self, examples, sentence_ids, probs):
        """
        Convert ids to raw text in a single stage.
        """
        results = []
        for example, sentence_id, prob in zip(examples, sentence_ids, probs):
            if len(sentence_id) == 0:
                results.append([])
                continue
            result_list = []
            text = example["text"]
            prompt = example["prompt"]
            for i in range(len(sentence_id)):
                start, end = sentence_id[i]
                if start < 0 and end >= 0:
                    continue
                if end < 0:
                    start += (len(prompt) + 1)
                    end += (len(prompt) + 1)
                    result = {"text": prompt[start:end], "probability": prob[i]}
                    result_list.append(result)
                else:
                    result = {
                        "text": text[start:end],
                        "start": start,
                        "end": end,
                        "probability": prob[i]
                    }
                    result_list.append(result)
            results.append(result_list)
        return results

    def set_schema(self, schema):
        if isinstance(schema, dict) or isinstance(schema, str):
            schema = [schema]
        self._schema_tree = self._build_tree(schema)

    @classmethod
    def _build_tree(cls, schema, name='root'):
        """
        Build the schema tree.
        """
        schema_tree = SchemaTree(name)
        if not schema:
            return schema_tree
        for s in schema:
            if isinstance(s, str):
                schema_tree.add_child(SchemaTree(s))
            elif isinstance(s, dict):
                for k, v in s.items():
                    if isinstance(v, str):
                        child = [v]
                    elif isinstance(v, list):
                        child = v
                    else:
                        raise TypeError(
                            "Invalid schema, value for each key:value pairs should be list or string"
                            "but {} received".format(type(v)))
                    schema_tree.add_child(cls._build_tree(child, name=k))
            else:
                raise TypeError(
                    "Invalid schema, element should be string or dict, "
                    "but {} received".format(type(s)))
        return schema_tree


class AutoPromptUIETask(UIETask):

    def __init__(self, model, schema=None, device='cpu', **kwargs):
        if not schema:
            schema = []
        super().__init__(model=model, schema=schema, device=device, **kwargs)
        # self.task  在父类中被自动设置为当前类所在py文件名
        self._usage = usage
        # self._schema_tree = None
        if not schema:
            print('没有设置schema，将从AutoPromptUIE中自动构建schema。')
            self.build_auto_schemaTree()
        # if model not in self.encoding_model_map.keys():
        #     raise ValueError(
        #         "Model should be one of uie-base, uie-tiny and uie-medical-base")
        # self._encoding_model = self.encoding_model_map[model]
        self._position_prob = kwargs[
            'position_prob'] if 'position_prob' in kwargs else 0.5


    def build_auto_schemaTree(self):
        entity_labels = self.entity_labels
        predicate2subject_labels = self.predicate2subject_labels
        schema = dict((e, [])for e in entity_labels)
        for predicate, sub_labels in predicate2subject_labels.items():
            for sub_l in sub_labels:
                if sub_l in schema:
                    if predicate not in schema[sub_l]:
                        schema[sub_l].append(predicate)
                else:
                    print("由于不能抽取实体'%s'，导致不能做'%s-->%s'样式的关系抽取。请仔细检查训练数据中是否包含抽取实体'%s'的数据。"
                          % (sub_l, sub_l, predicate, sub_l))

        schema = [k if not v else {k: v} for k, v in schema.items()]
        print(schema)
        self.set_schema(schema)

    def _construct_model(self):
        """
        Construct the inference model for the predictor.
        """
        config = AutoPromptUIEConfig.from_pretrained(self._task_path)
        model_instance = AutoPromptUIE(config)
        import os
        with open(self._task_path + '/pytorch_model.bin', "rb") as f:
            model_instance.load_state_dict(torch.load(f, map_location=torch.device('cpu')))
        self._model = model_instance
        #  未转化为script模型，提前将这些变量保存。
        self.conjunct = self._model.conjunct
        self.auto_prompt_vocab = self._model.auto_prompt_vocab
        self.entity_labels = self._model.entity_labels
        self.predicate2subject_labels = self._model.predicate2subject_labels
        self.auto_prompt_index = self._model.auto_prompt_index
        self._model.eval()

    def collate(self, examples):
        tokenizer_outputs = super(AutoPromptUIETask, self).collate(examples)
        auto_prompt_idx = [self.auto_prompt_index(exam['prompt'][exam['prompt'].rfind(self.conjunct):]
                                                         if exam['prompt'].rfind(self.conjunct) > 0 else exam['prompt'])
                           for exam in examples]
        tokenizer_outputs['auto_prompt_ids'] = torch.tensor(auto_prompt_idx)
        # TODO: 修改auto_prompt_idx为0的样本的attention_mask,使其退化为使用未微调的UIE。
        return tokenizer_outputs


class UIEDataset(Dataset):

    def __init__(self, data: List[Dict]):
        super().__init__()
        self.data = data

    def __getitem__(self, index):
        return self.data[index]

    def __len__(self):
        return len(self.data)

# TODO: iterable dataset


