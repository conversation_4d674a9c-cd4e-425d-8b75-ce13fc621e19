from .mixin import BatchInferFnMixin
from .cls import ImageClassifier
from .det import ObjectDetector
from aiodataloader import DataLoader


class BatchImageClassifier(DataLoader, ImageClassifier, BatchInferFnMixin):
    cache = False
    batch_load_fn = BatchInferFnMixin.batch_infer_fn
    max_batch_size = 8

class BatchObjectDetector(DataLoader, ObjectDetector, BatchInferFnMixin):
    cache = False
    batch_load_fn = BatchInferFnMixin.batch_infer_fn
    max_batch_size = 2
    collect_fn = list
