# -*- coding: utf-8 -*-
# __author__:SH
# 2022/10/31 10:16
from pydantic import BaseModel, Field


class IdIn(BaseModel):
    # id单个id传入
    id: int


class IdInList(BaseModel):
    id_list: list[int]


class ResponseMsg(BaseModel):
    # 只返回内容提示信息
    msg: str


class ServiceComputer(BaseModel):
    CPU: str
    memory: str
    GPU: str


class Paging(BaseModel):
    order_key: str = Field(default="id")  # 排序条件
    order: int = Field(default=1)  # 顺序还是逆序
    page: int = Field(default=1)  # 页数
    size: int = Field(default=10)  # 大小


class ServiceList(BaseModel):
    service_type:int
    input_data:str


class ServiceConfigure(BaseModel):
    auth: int
    server_list: list[str]
    is_KaPaaS: int
    stop_server: int
    text_unit: ServiceComputer
    image_unit: ServiceComputer
    usual_unit: ServiceComputer


class ServiceAuality(BaseModel):
    key:int
    service_id:int
    service_name:str
    service_time:str
    service_status:int
    service_type:str
    server:str


class ServiceListOut(BaseModel):
    count:int
    service_list:list[ServiceAuality]


class ServiceCrete(BaseModel):
    service_name:str
    model_id:int
    service_pos:str
    ratio:int
    server_list:list[int]
    mode_num:int
    mode_type:int


class TagOut(BaseModel):
    color:str
    tag_id:int


class ServiceOut(BaseModel):
    service_name: str
    service_type: str
    model_id:int
    dataset_id: list[int]


class CreateServiceIn(BaseModel):
    service_name:str
    dataset_type:int


class CreateServiceOut(BaseModel):
    service_id:int
    dataset_id:int