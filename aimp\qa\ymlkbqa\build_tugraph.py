import time

import requests

if __name__ == '__main__':
    graph_name = 'archive'
    login_url = "http://lgraph:7090/login"
    data = requests.post(login_url, json={'user': "admin", 'password': "73@TuGraph"})
    # print(data)
    jwt = data.json()['jwt']
    # print(jwt)
    _auth = {'Authorization': f'Bearer {jwt}'}
    headers = {'Accept': 'application/json; charset=UTF-8',
               'Content-Type': 'application/json; charset=UTF-8'}
    headers.update(_auth)
    _cypher_url = f'http://lgraph:7090/cypher'

    def create_person_label():
        # 创建Label
        r = ['ID', 'name', 'feat', 'gender', 'birthplace', 'birthday', 'join_army_day', 'sacrifice_day', 'politics_status',
             'battle', 'troop', 'duty']
        label_name = '人物'
        label_sql = {
            "name": label_name,
            "fields": [{"name": item, "type": "string", "optional": False if item in ['ID', 'name'] else True} for item in r],
            "is_vertex": True,
            "primary": r[0]
        }
        query_ret = requests.post("http://lgraph:7090/db/%s/label" % graph_name, json=label_sql, headers=headers)
        print(query_ret)
        query_ret = requests.post("http://lgraph:7090/db/%s/index" % graph_name, json={
            'label': label_name, 'field': 'name', 'is_unique': False
        }, headers=headers)
        print(query_ret)

    def insert_person_node():
        from openpyxl import load_workbook

        wb = load_workbook(r'D:\workspace\pycharm\nlp_project\ymlkbqa\二等功及以上英雄_图库文件.xlsx')
        ws = wb.active
        label_name = '人物'
        column_names = []
        datas = []
        for i, rows in enumerate(ws.rows):
            if i == 0:
                column_names.extend([col.value for col in rows if col.value])
            else:
                datas.append(dict((name, str(val)) for val, name in zip([col.value for col in rows], column_names)))

        for data in datas:
            node = {'label': label_name, 'property': data}
            ret = requests.post("http://lgraph:7090/db/%s/node" % graph_name, json=node, headers=headers)
            if ret.status_code != 200:
                print('创建节点失败，失败节点：', node)
                print(ret.json())


    def create_troop_label():
        # 创建Label
        r = ['ID', 'name']
        label_name = '军团'
        label_sql = {
            "name": label_name,
            "fields": [{"name": item, "type": "string", "optional": False if item in ['ID', 'name'] else True} for item in r],
            "is_vertex": True,
            "primary": r[0]
        }
        query_ret = requests.post("http://lgraph:7090/db/%s/label" % graph_name, json=label_sql, headers=headers)
        print(query_ret)
        query_ret = requests.post("http://lgraph:7090/db/%s/index" % graph_name, json={
            'label': label_name, 'field': 'name', 'is_unique': False
        }, headers=headers)
        print(query_ret)

    def insert_troop_node():
        from openpyxl import load_workbook

        wb = load_workbook(r'D:\workspace\pycharm\nlp_project\ymlkbqa\军团_图库文件.xlsx')
        ws = wb.active
        label_name = ws.title
        column_names = []
        datas = []
        for i, rows in enumerate(ws.rows):
            if i == 0:
                column_names.extend([col.value for col in rows if col.value])
            else:
                datas.append(dict((name, str(val)) for val, name in zip([col.value for col in rows], column_names)))

        for data in datas:
            node = {'label': label_name, 'property': data}
            ret = requests.post("http://lgraph:7090/db/%s/node" % graph_name, json=node, headers=headers)
            if ret.status_code != 200:
                print('创建节点失败，失败节点：', node)
                print(ret.json())

    def create_battle_label():
        # 创建Label
        r = ['ID', 'name']
        label_name = '战争战役'
        label_sql = {
            "name": label_name,
            "fields": [{"name": item, "type": "string", "optional": False if item in ['ID', 'name'] else True} for item in r],
            "is_vertex": True,
            "primary": r[0]
        }
        query_ret = requests.post("http://lgraph:7090/db/%s/label" % graph_name, json=label_sql, headers=headers)
        print(query_ret)
        query_ret = requests.post("http://lgraph:7090/db/%s/index" % graph_name, json={
            'label': label_name, 'field': 'name', 'is_unique': False
        }, headers=headers)
        print(query_ret)

    def insert_battle_node():
        from openpyxl import load_workbook

        wb = load_workbook(r'D:\workspace\pycharm\nlp_project\ymlkbqa\战争战役_图库文件.xlsx')
        ws = wb.active
        label_name = ws.title
        column_names = []
        datas = []
        for i, rows in enumerate(ws.rows):
            if i == 0:
                column_names.extend([col.value for col in rows if col.value])
            else:
                datas.append(dict((name, str(val)) for val, name in zip([col.value for col in rows], column_names)))

        for data in datas:
            node = {'label': label_name, 'property': data}
            ret = requests.post("http://lgraph:7090/db/%s/node" % graph_name, json=node, headers=headers)
            if ret.status_code != 200:
                print('创建节点失败，失败节点：', node)
                print(ret.json())

    def create_feat_label():
        # 创建Label
        r = ['ID', 'name']
        label_name = '战功'
        label_sql = {
            "name": label_name,
            "fields": [{"name": item, "type": "string", "optional": False if item in ['ID', 'name'] else True} for item in r],
            "is_vertex": True,
            "primary": r[0]
        }
        query_ret = requests.post("http://lgraph:7090/db/%s/label" % graph_name, json=label_sql, headers=headers)
        print(query_ret)
        query_ret = requests.post("http://lgraph:7090/db/%s/index" % graph_name, json={
            'label': label_name, 'field': 'name', 'is_unique': False
        }, headers=headers)
        print(query_ret)

    def insert_feat_node():
        from openpyxl import load_workbook

        wb = load_workbook(r'D:\workspace\pycharm\nlp_project\ymlkbqa\战功_图库文件.xlsx')
        ws = wb.active
        label_name = ws.title
        column_names = []
        datas = []
        for i, rows in enumerate(ws.rows):
            if i == 0:
                column_names.extend([col.value for col in rows if col.value])
            else:
                datas.append(dict((name, str(val)) for val, name in zip([col.value for col in rows], column_names)))

        for data in datas:
            node = {'label': label_name, 'property': data}
            ret = requests.post("http://lgraph:7090/db/%s/node" % graph_name, json=node, headers=headers)
            if ret.status_code != 200:
                print('创建节点失败，失败节点：', node)
                print(ret.json())


    def create_position_label():
        # 创建Label
        r = ['ID', 'name']
        label_name = '籍贯'
        label_sql = {
            "name": label_name,
            "fields": [{"name": item, "type": "string", "optional": False if item in ['ID', 'name'] else True} for item in r],
            "is_vertex": True,
            "primary": r[0]
        }
        query_ret = requests.post("http://lgraph:7090/db/%s/label" % graph_name, json=label_sql, headers=headers)
        print(query_ret)
        query_ret = requests.post("http://lgraph:7090/db/%s/index" % graph_name, json={
            'label': label_name, 'field': 'name', 'is_unique': False
        }, headers=headers)
        print(query_ret)

    def insert_position_node():
        from openpyxl import load_workbook

        wb = load_workbook(r'D:\workspace\pycharm\nlp_project\ymlkbqa\籍贯_图库文件.xlsx')
        ws = wb.active
        label_name = ws.title
        column_names = []
        datas = []
        for i, rows in enumerate(ws.rows):
            if i == 0:
                column_names.extend([col.value for col in rows if col.value])
            else:
                datas.append(dict((name, str(val)) for val, name in zip([col.value for col in rows], column_names)))

        for data in datas:
            node = {'label': label_name, 'property': data}
            ret = requests.post("http://lgraph:7090/db/%s/node" % graph_name, json=node, headers=headers)
            if ret.status_code != 200:
                print('创建节点失败，失败节点：', node)
                print(ret.json())

    #
    # create_person_label()
    # insert_person_node()
    # create_troop_label()
    # insert_troop_node()
    # create_battle_label()
    # insert_battle_node()
    # create_feat_label()
    # insert_feat_node()
    # create_position_label()
    # insert_position_node()


    # _query_Data = {
    #                 "graph": graph_name,
    #                 "script": """
    #                             MATCH (n1:人物 {name: '黄继光'}
    #                             ) RETURN n1
    #                             """,
    #
    #             }
    # query_ret = requests.post(_cypher_url, json=_query_Data, headers=headers)
    # print(query_ret.json())
    # s = set([i for i in range(1, 2100)])
    # import json
    # for ret in query_ret.json()['result']:
    #     assert len(ret) == 1
    #     ID = json.loads(ret[0])['properties']['ID']
    #     assert ID in s
    #     s.remove(ID)
    # print(s)
    # print(query_ret.json()['result'][0], len(query_ret.json()['result']), query_ret, sep='\n' + '*'*100 + '\n')


