from pprint import pprint
import paddle
import torch
from torch import nn
import math
from collections import OrderedDict
import numpy as np
from transformers import XLMRobertaTokenizerFast
from kbnlp.pretrained.ernie_layout.modeling_ernie_layout import Ernie<PERSON><PERSON>outModel, ErnieLayoutForSequenceClassification,\
    ErnieLayoutForTokenClassification, ErnieLayoutForQuestionAnswering, ErnieLayoutPretrainedModel
from kbnlp.pretrained.ernie_layout.configuration_ernie_layout import Ernie<PERSON>ayoutConfig
from kbnlp.pretrained.ernie_layout.tokenization_ernie_layout_fast import ErnieLayoutTokenizerFast
from kbnlp.taskflow.models.information_extraction.information_extraction_model import UIEXModelOutput
from paddlenlp.transformers import Ernie<PERSON>ayoutPretrainedModel as PaddleErnieLayoutPretrainedModel

paddle.set_device('cpu')


class UIEX(ErnieLayoutPretrainedModel):

    def __init__(self, config):
        super(UIEX, self).__init__(config)
        self.ernie_layout = ErnieLayoutModel(config)
        hidden_size = self.ernie_layout.config.hidden_size
        self.linear_start = nn.Linear(hidden_size, 1)
        self.linear_end = nn.Linear(hidden_size, 1)
        self.sigmoid = nn.Sigmoid()

    def forward(self,
                input_ids,
                token_type_ids=None,
                position_ids=None,
                attention_mask=None,
                head_mask=None,
                bbox=None,
                image=None,
                start_positions=None,
                end_positions=None,
                output_attentions=False,
                output_hidden_states=False,
                output_start_end_logits=False,
                return_dict=None):
        return_dict = return_dict if return_dict is not None else self.config.use_return_dict
        outputs = self.ernie_layout(input_ids=input_ids,
                               token_type_ids=token_type_ids,
                               position_ids=position_ids,
                               attention_mask=attention_mask,
                               head_mask=head_mask,
                               bbox=bbox,
                               image=image,
                               output_attentions=output_attentions,
                               output_hidden_states=output_hidden_states,
                               return_dict=return_dict,)
        sequence_output = outputs[0]
        print(sequence_output)
        start_logits = self.linear_start(sequence_output)
        start_logits = torch.squeeze(start_logits, -1)
        start_prob = self.sigmoid(start_logits)
        end_logits = self.linear_end(sequence_output)
        end_logits = torch.squeeze(end_logits, -1)
        end_prob = self.sigmoid(end_logits)

        loss = None
        if start_positions is not None and end_positions is not None:
            # If we are on multi-GPU, split_trim_underline_fake_relation add a dimension
            if len(start_positions.size()) > 1:
                start_positions = start_positions.squeeze(-1)
            if len(end_positions.size()) > 1:
                end_positions = end_positions.squeeze(-1)

            criterion = torch.nn.BCELoss()
            start_positions = start_positions.to(torch.float32)
            end_positions = end_positions.to(torch.float32)
            loss_start = criterion(start_prob, start_positions)
            loss_end = criterion(end_prob, end_positions)
            loss = (loss_start + loss_end) / 2.0

        if not return_dict:
            output = (start_prob, end_prob) + ((start_logits, end_logits) if output_start_end_logits else tuple())
            output += outputs[2:]
            return ((loss,) + output) if loss is not None else output

        return UIEXModelOutput(
            loss=loss,
            start_prob=start_prob,
            end_prob=end_prob,
            start_logits=start_logits if output_start_end_logits else None,
            end_logits=end_logits if output_start_end_logits else None,
            hidden_states=None,
            attentions=None,
        )


class PaddleUIEX(PaddleErnieLayoutPretrainedModel):

    def __init__(self, encoding_model):
        super(PaddleUIEX, self).__init__()
        self.ernie_layout = encoding_model
        hidden_size = self.ernie_layout.config["hidden_size"]
        self.linear_start = paddle.nn.Linear(hidden_size, 1)
        self.linear_end = paddle.nn.Linear(hidden_size, 1)
        self.sigmoid = paddle.nn.Sigmoid()

    def forward(self,
                input_ids,
                token_type_ids=None,
                position_ids=None,
                attention_mask=None,
                bbox=None,
                image=None):
        sequence_output, _ = self.ernie_layout(input_ids=input_ids,
                                               token_type_ids=token_type_ids,
                                               position_ids=position_ids,
                                               attention_mask=attention_mask,
                                               bbox=bbox,
                                               image=image)
        print(sequence_output)
        start_logits = self.linear_start(sequence_output)
        start_logits = paddle.squeeze(start_logits, -1)
        start_prob = self.sigmoid(start_logits)
        end_logits = self.linear_end(sequence_output)
        end_logits = paddle.squeeze(end_logits, -1)
        end_prob = self.sigmoid(end_logits)
        return start_prob, end_prob


def to_numpy(data):
    if isinstance(data, torch.Tensor) or isinstance(data, paddle.Tensor):
        return data.numpy()
    else:
        return data


def convert(torch_model_class, paddle_model_class, paddle_model_name_or_path, paddle_config_dict, model_inputs, num_labels=0, acc=4):
    if num_labels > 0:
        model_p = paddle_model_class.from_pretrained(paddle_model_name_or_path, num_classes=num_labels).to('cpu')
    else:
        model_p = paddle_model_class.from_pretrained(paddle_model_name_or_path).to('cpu')
    torch_config_dict = dict(paddle_config_dict)
    if 'model_type' in torch_config_dict:
        torch_config_dict.pop('model_type')
    if num_labels > 0:
        torch_config_dict['num_labels'] = num_labels

    config_t = ErnieLayoutConfig(**torch_config_dict)
    model_t = torch_model_class(config_t)
    model_t.eval()
    model_p.eval()
    # 修改key
    state_t = OrderedDict([(k, v) for k, v in model_t.state_dict().items() if 'tracked' not in k])
    state_p = dict([(k.replace('._mean', '.running_mean').replace('._variance', '.running_var'), v)
                    if '._mean' in k or '._variance' in k else (k, v)
                    for k, v in model_p.state_dict().items()])
    assert len(state_t) == len(state_p)

    name_mismatch = [(k1, k2) for k1, k2 in zip(state_t, state_p) if k1 != k2]
    print('name_mismatch:')
    pprint(name_mismatch)

    # 修改value
    for k, v in state_p.items():
        if '._mean' in k:
            k_ = k.replace('._mean', '.running_mean')
        elif '._variance' in k:
            k_ = k.replace('._variance', '.running_var')
        else:
            k_ = k
        v_arr = v.numpy()
        v_ = torch.tensor(v_arr.T if '.weight' in k_ and 'embeddings.' not in k_ and 'conv' not in k_ and len(v_arr.shape) == 2 else v_arr)
        state_t[k_] = v_

    shape_mismatch = []
    for (k1, v1), (k2, v2) in zip(state_t.items(),
                                  [(k, v) for k, v in model_t.state_dict().items() if 'tracked' not in k]):
        assert k1 == k2
        if list(v1.size()) != list(v2.size()):
            shape_mismatch.append((k1, list(v1.size()), list(v2.size())))
    print('shape_mismatch:')
    pprint(shape_mismatch)

    # 向torch模型中加载参数
    print(model_t.load_state_dict(state_t, strict=False))

    # 验证模型精度
    model_inputs = OrderedDict([(k, to_numpy(v)) for k, v in model_inputs.items()])
    inputs_p = dict([(k, paddle.to_tensor(v, place='cpu')) for k, v in model_inputs.items()])
    # if 'labels' in inputs_p:
    #     inputs_p['labels'] = paddle.to_tensor([0] * inputs_p['labels'].shape[0])
    # inputs_p.pop('labels')
    inputs_t = dict([(k, torch.tensor(v)) for k, v in model_inputs.items()])
    with torch.no_grad():
        output_t = model_t(**inputs_t, return_dict=False)
        if not isinstance(output_t, tuple):
            output_t = tuple(output_t)
    with paddle.no_grad():
        output_p = model_p(**inputs_p)
        if not isinstance(output_p, tuple):
            output_p = tuple(output_p)

    acc_ = math.pow(10, -acc)
    all_guaranteed = True
    assert len(output_t) == len(output_p)
    for i in range(len(output_t)):
        acc_guaranteed = np.all(np.abs(output_t[i].numpy() - output_p[i].numpy()) < acc_)
        all_guaranteed &= acc_guaranteed
        print('结果元祖中第%d个值的shape：' % (i + 1), list(output_t[i].numpy().shape))
        print('结果元祖中第%d个值保证小数点后%d位精度：' % (i, acc), acc_guaranteed)
    print('在所有的值中，保证小数点后%d位精度：' % acc, all_guaranteed)
    return model_t


if __name__ == '__main__':

    paddle_config_dict_ = {
            "attention_probs_dropout_prob": 0.1,
            "bos_token_id": 0,
            "coordinate_size": 128,
            "eos_token_id": 2,
            "gradient_checkpointing": False,
            "has_relative_attention_bias": True,
            "has_spatial_attention_bias": True,
            "has_visual_segment_embedding": False,
            "hidden_act": "gelu",
            "hidden_dropout_prob": 0.1,
            "hidden_size": 768,
            "image_feature_pool_shape": [7, 7, 256],
            "initializer_range": 0.02,
            "intermediate_size": 3072,
            "layer_norm_eps": 1e-12,
            "max_2d_position_embeddings": 1024,
            "max_position_embeddings": 514,
            "max_rel_2d_pos": 256,
            "max_rel_pos": 128,
            "model_type": "ernie_layout",
            "num_attention_heads": 12,
            "num_hidden_layers": 12,
            "output_past": True,
            "pad_token_id": 1,
            "shape_size": 128,
            "rel_2d_pos_bins": 64,
            "rel_pos_bins": 32,
            "type_vocab_size": 100,
            "vocab_size": 250002,
        }

    def convert_UIEX(save_dir, paddle_model_name_or_path='ernie-layoutx-base-uncased',
                     ):
        tokenizer = XLMRobertaTokenizerFast.from_pretrained('microsoft/layoutxlm-base')
        model_inputs_ = paddle.load(r'D:\workspace\pycharm\nlp_project\kbnlp\scripts\information_extraction\uiex_inputs.params')
        model_inputs_.pop('labels')
        model = convert(UIEX, PaddleUIEX, paddle_model_name_or_path=paddle_model_name_or_path,
                        paddle_config_dict=paddle_config_dict_, model_inputs=model_inputs_)
        model.save_pretrained(save_dir)
        tokenizer.save_pretrained(save_dir)

    # convert_UIEX(save_dir='./uie-x-base-init-from-ernie-layout-base', paddle_model_name_or_path='ernie-layoutx-base-uncased')
    convert_UIEX(save_dir='./uie-x-base', paddle_model_name_or_path=r'C:\Users\<USER>\.paddlenlp\taskflow\information_extraction\uie-x-base')
