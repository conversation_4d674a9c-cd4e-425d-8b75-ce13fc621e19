epoch:1,time:11.5s.tok: , loss:1.0601.   dep: , loss:1.5352.   
epoch:1,time:3.1s.tok: P: 87.00% R: 84.55% F1: 85.76%, loss:0.3915.   dep: UAS: 61.28% LAS: 43.70% CM: 14.00% CM’: 10.50%, loss:0.7617.   
epoch:2,time:10.4s.tok: , loss:0.3110.   dep: , loss:0.6372.   
epoch:2,time:3.1s.tok: P: 93.48% R: 94.10% F1: 93.79%, loss:0.1474.   dep: UAS: 73.46% LAS: 61.72% CM: 20.50% CM’: 10.00%, loss:0.5186.   
epoch:3,time:10.5s.tok: , loss:0.1709.   dep: , loss:0.3972.   
epoch:3,time:3.0s.tok: P: 94.78% R: 94.87% F1: 94.82%, loss:0.1200.   dep: UAS: 77.23% LAS: 68.93% CM: 25.00% CM’: 17.50%, loss:0.3793.   
epoch:4,time:10.3s.tok: , loss:0.1333.   dep: , loss:0.2932.   
epoch:4,time:3.1s.tok: P: 95.21% R: 95.29% F1: 95.25%, loss:0.1066.   dep: UAS: 80.57% LAS: 73.56% CM: 26.50% CM’: 16.50%, loss:0.3457.   
epoch:5,time:10.4s.tok: , loss:0.1117.   dep: , loss:0.2850.   
epoch:5,time:3.0s.tok: P: 94.88% R: 95.80% F1: 95.34%, loss:0.1096.   dep: UAS: 80.55% LAS: 73.58% CM: 29.00% CM’: 19.00%, loss:0.3215.   
epoch:6,time:10.3s.tok: , loss:0.1076.   dep: , loss:0.2180.   
epoch:6,time:3.8s.tok: P: 95.30% R: 96.09% F1: 95.69%, loss:0.1039.   dep: UAS: 76.25% LAS: 68.49% CM: 25.50% CM’: 16.50%, loss:0.4364.   
epoch:7,time:11.0s.tok: , loss:0.0895.   dep: , loss:0.1989.   
epoch:7,time:3.6s.tok: P: 95.69% R: 96.19% F1: 95.94%, loss:0.0968.   dep: UAS: 81.73% LAS: 75.53% CM: 32.50% CM’: 19.50%, loss:0.3309.   
epoch:8,time:12.6s.tok: , loss:0.0785.   dep: , loss:0.1607.   
epoch:8,time:3.7s.tok: P: 95.67% R: 96.26% F1: 95.97%, loss:0.1010.   dep: UAS: 82.46% LAS: 76.08% CM: 31.50% CM’: 19.50%, loss:0.3453.   
epoch:9,time:11.4s.tok: , loss:0.0771.   dep: , loss:0.1725.   
epoch:9,time:3.7s.tok: P: 95.96% R: 96.34% F1: 96.15%, loss:0.0947.   dep: UAS: 81.91% LAS: 75.07% CM: 33.00% CM’: 22.00%, loss:0.3438.   
epoch:10,time:12.2s.tok: , loss:0.0759.   dep: , loss:0.1481.   
epoch:10,time:3.9s.tok: P: 96.09% R: 96.45% F1: 96.27%, loss:0.0981.   dep: UAS: 82.62% LAS: 75.92% CM: 34.00% CM’: 21.50%, loss:0.3399.   
epoch:11,time:11.4s.tok: , loss:0.0676.   dep: , loss:0.1308.   
epoch:11,time:3.8s.tok: P: 96.36% R: 96.49% F1: 96.42%, loss:0.0964.   dep: UAS: 83.81% LAS: 77.47% CM: 34.00% CM’: 18.50%, loss:0.3435.   
epoch:12,time:11.5s.tok: , loss:0.0624.   dep: , loss:0.1247.   
epoch:12,time:3.7s.tok: P: 96.28% R: 96.28% F1: 96.28%, loss:0.0967.   dep: UAS: 83.97% LAS: 77.47% CM: 32.50% CM’: 20.50%, loss:0.3648.   
epoch:13,time:11.2s.tok: , loss:0.0650.   dep: , loss:0.1055.   
epoch:13,time:3.6s.tok: P: 96.26% R: 96.56% F1: 96.41%, loss:0.1041.   dep: UAS: 82.79% LAS: 76.35% CM: 31.00% CM’: 20.50%, loss:0.3533.   
epoch:14,time:11.6s.tok: , loss:0.0651.   dep: , loss:0.1064.   
epoch:14,time:3.8s.tok: P: 96.24% R: 96.56% F1: 96.40%, loss:0.1040.   dep: UAS: 83.54% LAS: 76.98% CM: 32.50% CM’: 19.50%, loss:0.3634.   
epoch:15,time:11.5s.tok: , loss:0.0591.   dep: , loss:0.0919.   
epoch:15,time:3.7s.tok: P: 96.24% R: 96.54% F1: 96.39%, loss:0.1069.   dep: UAS: 83.38% LAS: 76.27% CM: 30.00% CM’: 19.50%, loss:0.3823.   
epoch:16,time:11.6s.tok: , loss:0.0652.   dep: , loss:0.0994.   
epoch:16,time:4.0s.tok: P: 96.31% R: 96.56% F1: 96.43%, loss:0.1054.   dep: UAS: 83.50% LAS: 77.47% CM: 32.50% CM’: 20.50%, loss:0.3552.   
epoch:17,time:11.2s.tok: , loss:0.0568.   dep: , loss:0.0801.   
epoch:17,time:3.7s.tok: P: 96.38% R: 96.63% F1: 96.51%, loss:0.1037.   dep: UAS: 84.11% LAS: 77.47% CM: 36.50% CM’: 22.00%, loss:0.3616.   
epoch:18,time:10.9s.tok: , loss:0.0502.   dep: , loss:0.0793.   
epoch:18,time:3.3s.tok: P: 96.33% R: 96.60% F1: 96.46%, loss:0.1056.   dep: UAS: 84.23% LAS: 78.10% CM: 36.50% CM’: 21.00%, loss:0.3533.   
epoch:19,time:10.7s.tok: , loss:0.0552.   dep: , loss:0.0754.   
epoch:19,time:3.3s.tok: P: 96.40% R: 96.63% F1: 96.52%, loss:0.1050.   dep: UAS: 84.11% LAS: 77.92% CM: 33.00% CM’: 20.00%, loss:0.3567.   
epoch:20,time:10.6s.tok: , loss:0.0518.   dep: , loss:0.0691.   
epoch:20,time:3.4s.tok: P: 96.40% R: 96.63% F1: 96.52%, loss:0.1055.   dep: UAS: 84.07% LAS: 78.06% CM: 35.00% CM’: 21.50%, loss:0.3517.   
epoch:1,time:10.4s.tok: , loss:0.8850.   ner: , loss:0.4863.   
epoch:1,time:1.0s.tok: P: 90.35% R: 89.66% F1: 90.00%, loss:0.2311.   ner: P: 35.53% R: 11.49% F1: 17.36%, loss:0.2919.   
epoch:2,time:10.0s.tok: , loss:0.2286.   ner: , loss:0.2597.   
epoch:2,time:0.9s.tok: P: 93.84% R: 94.03% F1: 93.93%, loss:0.1388.   ner: P: 0.00% R: 0.00% F1: 0.00%, loss:0.2925.   
epoch:3,time:9.8s.tok: , loss:0.1609.   ner: , loss:0.2046.   
epoch:3,time:0.9s.tok: P: 94.69% R: 94.77% F1: 94.73%, loss:0.1208.   ner: P: 40.84% R: 33.19% F1: 36.62%, loss:0.2937.   
epoch:4,time:10.0s.tok: , loss:0.1389.   ner: , loss:0.1907.   
epoch:4,time:1.1s.tok: P: 95.08% R: 95.15% F1: 95.11%, loss:0.1129.   ner: P: 29.46% R: 28.09% F1: 28.76%, loss:0.2813.   
epoch:5,time:10.1s.tok: , loss:0.1196.   ner: , loss:0.1743.   
epoch:5,time:1.0s.tok: P: 94.98% R: 95.74% F1: 95.36%, loss:0.1118.   ner: P: 24.88% R: 22.55% F1: 23.66%, loss:0.2556.   
epoch:6,time:10.0s.tok: , loss:0.1029.   ner: , loss:0.1642.   
epoch:6,time:1.0s.tok: P: 94.99% R: 95.57% F1: 95.28%, loss:0.1118.   ner: P: 26.61% R: 28.09% F1: 27.33%, loss:0.2462.   
epoch:7,time:9.9s.tok: , loss:0.1028.   ner: , loss:0.1462.   
epoch:7,time:0.9s.tok: P: 95.21% R: 95.72% F1: 95.47%, loss:0.1106.   ner: P: 33.05% R: 33.62% F1: 33.33%, loss:0.2318.   
epoch:8,time:9.7s.tok: , loss:0.0972.   ner: , loss:0.1340.   
epoch:8,time:0.9s.tok: P: 95.30% R: 95.76% F1: 95.53%, loss:0.1081.   ner: P: 47.46% R: 47.66% F1: 47.56%, loss:0.2046.   
epoch:9,time:9.8s.tok: , loss:0.0953.   ner: , loss:0.1317.   
epoch:9,time:1.0s.tok: P: 95.50% R: 95.93% F1: 95.71%, loss:0.1076.   ner: P: 39.29% R: 37.45% F1: 38.34%, loss:0.2071.   
epoch:10,time:9.9s.tok: , loss:0.0906.   ner: , loss:0.1162.   
epoch:10,time:1.0s.tok: P: 95.48% R: 95.95% F1: 95.71%, loss:0.1083.   ner: P: 46.73% R: 39.57% F1: 42.86%, loss:0.2081.   
epoch:1,time:2.2s.tok: , loss:1.2863.   ner: , loss:0.9911.   
epoch:1,time:0.9s.tok: P: 72.50% R: 71.29% F1: 71.89%, loss:0.7584.   ner: P: 0.00% R: 0.00% F1: 0.00%, loss:0.4285.   
epoch:2,time:2.0s.tok: , loss:0.6328.   ner: , loss:0.3462.   
epoch:2,time:0.9s.tok: P: 83.95% R: 84.43% F1: 84.19%, loss:0.3706.   ner: P: 0.00% R: 0.00% F1: 0.00%, loss:0.4605.   
epoch:3,time:2.0s.tok: , loss:0.3649.   ner: , loss:0.3655.   
epoch:3,time:0.9s.tok: P: 86.46% R: 87.37% F1: 86.91%, loss:0.2867.   ner: P: 0.00% R: 0.00% F1: 0.00%, loss:0.4421.   
epoch:4,time:2.0s.tok: , loss:0.3021.   ner: , loss:0.2981.   
epoch:4,time:0.9s.tok: P: 88.66% R: 89.18% F1: 88.92%, loss:0.2430.   ner: P: 0.00% R: 0.00% F1: 0.00%, loss:0.4183.   
epoch:5,time:2.1s.tok: , loss:0.2256.   ner: , loss:0.2613.   
epoch:5,time:0.9s.tok: P: 89.61% R: 90.18% F1: 89.90%, loss:0.2133.   ner: P: 0.00% R: 0.00% F1: 0.00%, loss:0.3671.   
epoch:6,time:2.1s.tok: , loss:0.1896.   ner: , loss:0.2897.   
epoch:6,time:0.9s.tok: P: 90.71% R: 90.59% F1: 90.65%, loss:0.1985.   ner: P: 0.00% R: 0.00% F1: 0.00%, loss:0.3675.   
epoch:7,time:2.1s.tok: , loss:0.1900.   ner: , loss:0.2171.   
epoch:7,time:0.9s.tok: P: 91.04% R: 91.11% F1: 91.08%, loss:0.1941.   ner: P: 9.24% R: 7.23% F1: 8.11%, loss:0.3746.   
epoch:8,time:2.1s.tok: , loss:0.2047.   ner: , loss:0.2094.   
epoch:8,time:0.9s.tok: P: 91.14% R: 91.30% F1: 91.22%, loss:0.1915.   ner: P: 14.77% R: 5.53% F1: 8.05%, loss:0.3643.   
epoch:9,time:2.1s.tok: , loss:0.1515.   ner: , loss:0.1894.   
epoch:9,time:0.9s.tok: P: 91.10% R: 91.39% F1: 91.25%, loss:0.1898.   ner: P: 13.87% R: 8.09% F1: 10.22%, loss:0.3672.   
epoch:10,time:2.0s.tok: , loss:0.1666.   ner: , loss:0.1816.   
epoch:10,time:0.9s.tok: P: 91.14% R: 91.41% F1: 91.27%, loss:0.1891.   ner: P: 11.41% R: 7.23% F1: 8.85%, loss:0.3607.   
epoch:1,time:19.3s.tok: , loss:0.7475.   ner: , loss:0.4249.   
epoch:1,time:0.9s.tok: P: 93.30% R: 93.03% F1: 93.16%, loss:0.1567.   ner: P: 16.67% R: 23.83% F1: 19.61%, loss:0.2921.   
epoch:2,time:19.7s.tok: , loss:0.1634.   ner: , loss:0.2462.   
epoch:2,time:0.9s.tok: P: 94.97% R: 95.13% F1: 95.05%, loss:0.1098.   ner: P: 57.58% R: 40.43% F1: 47.50%, loss:0.2084.   
epoch:3,time:19.8s.tok: , loss:0.1292.   ner: , loss:0.1994.   
epoch:3,time:0.9s.tok: P: 96.04% R: 96.13% F1: 96.09%, loss:0.0908.   ner: P: 54.63% R: 47.66% F1: 50.91%, loss:0.1750.   
epoch:4,time:20.5s.tok: , loss:0.1052.   ner: , loss:0.1740.   
epoch:4,time:1.0s.tok: P: 96.27% R: 96.58% F1: 96.43%, loss:0.0894.   ner: P: 67.33% R: 57.87% F1: 62.24%, loss:0.1935.   
epoch:5,time:20.7s.tok: , loss:0.0899.   ner: , loss:0.1621.   
epoch:5,time:1.0s.tok: P: 96.41% R: 96.76% F1: 96.58%, loss:0.0793.   ner: P: 59.63% R: 55.32% F1: 57.40%, loss:0.2049.   
epoch:6,time:20.7s.tok: , loss:0.0871.   ner: , loss:0.1508.   
epoch:6,time:1.0s.tok: P: 96.48% R: 96.80% F1: 96.64%, loss:0.0755.   ner: P: 65.18% R: 62.13% F1: 63.62%, loss:0.1656.   
epoch:7,time:20.9s.tok: , loss:0.0738.   ner: , loss:0.1385.   
epoch:7,time:1.0s.tok: P: 96.73% R: 96.93% F1: 96.83%, loss:0.0719.   ner: P: 64.63% R: 62.98% F1: 63.79%, loss:0.1632.   
epoch:8,time:20.6s.tok: , loss:0.0804.   ner: , loss:0.1195.   
epoch:8,time:1.0s.tok: P: 96.72% R: 97.02% F1: 96.87%, loss:0.0739.   ner: P: 67.89% R: 62.98% F1: 65.34%, loss:0.1541.   
epoch:9,time:20.6s.tok: , loss:0.0686.   ner: , loss:0.1100.   
epoch:9,time:1.0s.tok: P: 96.79% R: 97.01% F1: 96.90%, loss:0.0725.   ner: P: 66.24% R: 65.96% F1: 66.10%, loss:0.1472.   
epoch:10,time:20.6s.tok: , loss:0.0719.   ner: , loss:0.1001.   
epoch:10,time:1.0s.tok: P: 96.83% R: 97.02% F1: 96.93%, loss:0.0718.   ner: P: 65.96% R: 65.96% F1: 65.96%, loss:0.1470.   
epoch:1,time:9.9s.ner: , loss:0.4519.   
epoch:1,time:0.5s.ner: P: 48.43% R: 45.96% F1: 47.16%, loss:0.1959.   
epoch:2,time:10.0s.ner: , loss:0.1791.   
epoch:2,time:0.5s.ner: P: 42.55% R: 49.79% F1: 45.88%, loss:0.1685.   
epoch:3,time:10.0s.ner: , loss:0.1439.   
epoch:3,time:0.5s.ner: P: 62.83% R: 60.43% F1: 61.61%, loss:0.1635.   
epoch:4,time:10.0s.ner: , loss:0.1254.   
epoch:4,time:0.5s.ner: P: 58.72% R: 58.72% F1: 58.72%, loss:0.1557.   
epoch:5,time:10.0s.ner: , loss:0.1152.   
epoch:5,time:0.5s.ner: P: 62.20% R: 67.23% F1: 64.62%, loss:0.1290.   
epoch:6,time:10.0s.ner: , loss:0.0940.   
epoch:6,time:0.5s.ner: P: 60.64% R: 64.26% F1: 62.40%, loss:0.1274.   
epoch:7,time:10.1s.ner: , loss:0.0845.   
epoch:7,time:0.5s.ner: P: 73.06% R: 68.09% F1: 70.48%, loss:0.1335.   
epoch:8,time:10.7s.ner: , loss:0.0743.   
epoch:8,time:0.5s.ner: P: 67.20% R: 71.49% F1: 69.28%, loss:0.1147.   
epoch:9,time:10.6s.ner: , loss:0.0677.   
epoch:9,time:0.5s.ner: P: 68.00% R: 72.34% F1: 70.10%, loss:0.1176.   
epoch:10,time:10.6s.ner: , loss:0.0538.   
epoch:10,time:0.5s.ner: P: 70.92% R: 75.74% F1: 73.25%, loss:0.1176.   
epoch:1,time:9.8s.ner: , loss:0.6165.   
epoch:1,time:0.4s.ner: P: 59.64% R: 56.60% F1: 58.08%, loss:0.1651.   
epoch:2,time:10.0s.ner: , loss:0.1556.   
epoch:2,time:0.5s.ner: P: 66.35% R: 59.57% F1: 62.78%, loss:0.1480.   
epoch:3,time:9.9s.ner: , loss:0.1549.   
epoch:3,time:0.5s.ner: P: 60.62% R: 49.79% F1: 54.67%, loss:0.1676.   
epoch:4,time:10.0s.ner: , loss:0.1374.   
epoch:4,time:0.5s.ner: P: 65.13% R: 65.96% F1: 65.54%, loss:0.1543.   
epoch:5,time:10.0s.ner: , loss:0.1334.   
epoch:5,time:0.4s.ner: P: 58.00% R: 61.70% F1: 59.79%, loss:0.1280.   
epoch:6,time:10.0s.ner: , loss:0.1200.   
epoch:6,time:0.4s.ner: P: 50.17% R: 62.13% F1: 55.51%, loss:0.1316.   
epoch:7,time:10.2s.ner: , loss:0.1212.   
epoch:7,time:0.5s.ner: P: 71.03% R: 64.68% F1: 67.71%, loss:0.1542.   
epoch:8,time:10.7s.ner: , loss:0.1132.   
epoch:8,time:0.5s.ner: P: 51.88% R: 64.68% F1: 57.58%, loss:0.1408.   
epoch:9,time:10.6s.ner: , loss:0.1052.   
epoch:9,time:0.5s.ner: P: 54.02% R: 60.00% F1: 56.85%, loss:0.1388.   
epoch:10,time:10.8s.ner: , loss:0.0969.   
epoch:10,time:0.5s.ner: P: 59.70% R: 68.09% F1: 63.62%, loss:0.1268.   
epoch:11,time:10.9s.ner: , loss:0.0940.   
epoch:11,time:0.5s.ner: P: 62.31% R: 68.94% F1: 65.45%, loss:0.1380.   
epoch:12,time:10.7s.ner: , loss:0.0812.   
epoch:12,time:0.5s.ner: P: 60.85% R: 66.81% F1: 63.69%, loss:0.1181.   
epoch:13,time:10.6s.ner: , loss:0.0811.   
epoch:13,time:0.5s.ner: P: 65.76% R: 71.91% F1: 68.70%, loss:0.1394.   
epoch:14,time:10.6s.ner: , loss:0.0782.   
epoch:14,time:0.5s.ner: P: 66.67% R: 70.64% F1: 68.60%, loss:0.1301.   
epoch:15,time:10.5s.ner: , loss:0.0651.   
epoch:15,time:0.5s.ner: P: 65.65% R: 73.19% F1: 69.22%, loss:0.1539.   
epoch:16,time:10.5s.ner: , loss:0.0629.   
epoch:16,time:0.5s.ner: P: 67.06% R: 71.91% F1: 69.40%, loss:0.1374.   
epoch:17,time:10.6s.ner: , loss:0.0648.   
epoch:17,time:0.5s.ner: P: 70.85% R: 74.47% F1: 72.61%, loss:0.1234.   
epoch:18,time:10.5s.ner: , loss:0.0641.   
epoch:18,time:0.5s.ner: P: 62.77% R: 73.19% F1: 67.58%, loss:0.1266.   
epoch:19,time:10.5s.ner: , loss:0.0571.   
epoch:19,time:0.5s.ner: P: 68.75% R: 70.21% F1: 69.47%, loss:0.1372.   
epoch:20,time:10.5s.ner: , loss:0.0589.   
epoch:20,time:0.5s.ner: P: 63.37% R: 73.62% F1: 68.11%, loss:0.1437.   
epoch:21,time:10.5s.ner: , loss:0.0482.   
epoch:21,time:0.5s.ner: P: 68.73% R: 75.74% F1: 72.06%, loss:0.1383.   
epoch:22,time:10.5s.ner: , loss:0.0454.   
epoch:22,time:0.5s.ner: P: 69.88% R: 74.04% F1: 71.90%, loss:0.1507.   
epoch:23,time:10.5s.ner: , loss:0.0468.   
epoch:23,time:0.5s.ner: P: 68.88% R: 70.64% F1: 69.75%, loss:0.1454.   
epoch:24,time:10.5s.ner: , loss:0.0406.   
epoch:24,time:0.5s.ner: P: 70.89% R: 71.49% F1: 71.19%, loss:0.1679.   
epoch:25,time:10.5s.ner: , loss:0.0407.   
epoch:25,time:0.5s.ner: P: 70.52% R: 75.32% F1: 72.84%, loss:0.1475.   
epoch:26,time:10.5s.ner: , loss:0.0314.   
epoch:26,time:0.5s.ner: P: 69.23% R: 76.60% F1: 72.73%, loss:0.1449.   
epoch:27,time:10.8s.ner: , loss:0.0347.   
epoch:27,time:0.5s.ner: P: 70.28% R: 74.47% F1: 72.31%, loss:0.1397.   
epoch:28,time:9.8s.ner: , loss:0.0302.   
epoch:28,time:0.4s.ner: P: 71.37% R: 73.19% F1: 72.27%, loss:0.1603.   
epoch:29,time:9.7s.ner: , loss:0.0330.   
epoch:29,time:0.5s.ner: P: 72.43% R: 74.89% F1: 73.64%, loss:0.1508.   
epoch:30,time:9.9s.ner: , loss:0.0340.   
epoch:30,time:0.4s.ner: P: 72.84% R: 75.32% F1: 74.06%, loss:0.1498.   
epoch:1,time:9.9s.ner: , loss:0.6211.   
epoch:1,time:0.4s.ner: P: 69.35% R: 54.89% F1: 61.28%, loss:0.1786.   
epoch:2,time:10.0s.ner: , loss:0.1646.   
epoch:2,time:0.5s.ner: P: 42.21% R: 35.74% F1: 38.71%, loss:0.1838.   
epoch:3,time:10.0s.ner: , loss:0.1634.   
epoch:3,time:0.5s.ner: P: 60.20% R: 50.21% F1: 54.76%, loss:0.1688.   
epoch:4,time:10.0s.ner: , loss:0.1663.   
epoch:4,time:0.5s.ner: P: 53.52% R: 58.30% F1: 55.80%, loss:0.1459.   
epoch:5,time:10.0s.ner: , loss:0.1340.   
epoch:5,time:0.5s.ner: P: 53.33% R: 61.28% F1: 57.03%, loss:0.1407.   
epoch:6,time:10.0s.ner: , loss:0.1283.   
epoch:6,time:0.5s.ner: P: 51.91% R: 51.91% F1: 51.91%, loss:0.1613.   
epoch:7,time:10.0s.ner: , loss:0.1161.   
epoch:7,time:0.5s.ner: P: 59.07% R: 65.11% F1: 61.94%, loss:0.1499.   
epoch:8,time:10.0s.ner: , loss:0.1123.   
epoch:8,time:0.5s.ner: P: 58.87% R: 66.38% F1: 62.40%, loss:0.1419.   
epoch:9,time:10.1s.ner: , loss:0.0979.   
epoch:9,time:0.5s.ner: P: 69.49% R: 69.79% F1: 69.64%, loss:0.1343.   
epoch:10,time:11.1s.ner: , loss:0.0968.   
epoch:10,time:0.5s.ner: P: 62.07% R: 68.94% F1: 65.32%, loss:0.1615.   
epoch:11,time:10.7s.ner: , loss:0.0920.   
epoch:11,time:0.5s.ner: P: 64.26% R: 68.09% F1: 66.12%, loss:0.1452.   
epoch:12,time:10.8s.ner: , loss:0.0817.   
epoch:12,time:0.5s.ner: P: 61.36% R: 68.94% F1: 64.93%, loss:0.1536.   
epoch:13,time:11.3s.ner: , loss:0.0744.   
epoch:13,time:0.5s.ner: P: 66.14% R: 70.64% F1: 68.31%, loss:0.1456.   
epoch:14,time:10.9s.ner: , loss:0.0827.   
epoch:14,time:0.6s.ner: P: 66.67% R: 70.64% F1: 68.60%, loss:0.1529.   
epoch:15,time:10.9s.ner: , loss:0.0713.   
epoch:15,time:0.5s.ner: P: 57.48% R: 71.91% F1: 63.89%, loss:0.1507.   
epoch:16,time:10.8s.ner: , loss:0.0743.   
epoch:16,time:0.5s.ner: P: 68.16% R: 77.45% F1: 72.51%, loss:0.1255.   
epoch:17,time:10.5s.ner: , loss:0.0632.   
epoch:17,time:0.5s.ner: P: 71.60% R: 74.04% F1: 72.80%, loss:0.1461.   
epoch:18,time:10.4s.ner: , loss:0.0577.   
epoch:18,time:0.5s.ner: P: 68.77% R: 74.04% F1: 71.31%, loss:0.1347.   
epoch:19,time:10.6s.ner: , loss:0.0561.   
epoch:19,time:0.5s.ner: P: 73.62% R: 73.62% F1: 73.62%, loss:0.1377.   
epoch:1,time:26.6s.ner: , loss:0.4565.   
epoch:1,time:1.0s.ner: P: 56.52% R: 60.85% F1: 58.61%, loss:0.1442.   
epoch:2,time:23.8s.ner: , loss:0.1560.   
epoch:2,time:1.0s.ner: P: 57.78% R: 66.38% F1: 61.78%, loss:0.1501.   
epoch:1,time:5.6s.ner: , loss:1.2763.   
epoch:1,time:0.8s.ner: P: 27.16% R: 36.17% F1: 31.02%, loss:0.3126.   
epoch:2,time:4.1s.ner: , loss:0.5096.   
epoch:2,time:0.8s.ner: P: 49.80% R: 53.19% F1: 51.44%, loss:0.1791.   
epoch:3,time:4.2s.ner: , loss:0.4119.   
epoch:3,time:0.8s.ner: P: 62.43% R: 45.96% F1: 52.94%, loss:0.1445.   
epoch:4,time:4.2s.ner: , loss:0.3555.   
epoch:4,time:0.8s.ner: P: 63.78% R: 53.19% F1: 58.00%, loss:0.1759.   
epoch:5,time:4.2s.ner: , loss:0.2942.   
epoch:5,time:0.8s.ner: P: 62.20% R: 55.32% F1: 58.56%, loss:0.1613.   
epoch:6,time:4.2s.ner: , loss:0.2597.   
epoch:6,time:0.8s.ner: P: 44.29% R: 39.57% F1: 41.80%, loss:0.1764.   
epoch:7,time:4.9s.ner: , loss:0.2633.   
epoch:7,time:0.8s.ner: P: 66.25% R: 67.66% F1: 66.95%, loss:0.1348.   
epoch:8,time:4.2s.ner: , loss:0.2278.   
epoch:8,time:0.8s.ner: P: 60.00% R: 54.89% F1: 57.33%, loss:0.1718.   
epoch:9,time:4.2s.ner: , loss:0.2204.   
epoch:9,time:0.8s.ner: P: 57.04% R: 67.23% F1: 61.72%, loss:0.1466.   
epoch:10,time:4.4s.ner: , loss:0.1952.   
epoch:10,time:0.8s.ner: P: 53.18% R: 71.06% F1: 60.84%, loss:0.1630.   
epoch:11,time:4.2s.ner: , loss:0.1860.   
epoch:11,time:0.8s.ner: P: 62.99% R: 68.09% F1: 65.44%, loss:0.1155.   
epoch:12,time:4.2s.ner: , loss:0.1955.   
epoch:12,time:0.8s.ner: P: 49.83% R: 63.40% F1: 55.81%, loss:0.1829.   
epoch:13,time:4.2s.ner: , loss:0.1415.   
epoch:13,time:0.8s.ner: P: 58.24% R: 67.66% F1: 62.60%, loss:0.1341.   
epoch:14,time:4.2s.ner: , loss:0.1433.   
epoch:14,time:0.8s.ner: P: 67.22% R: 68.94% F1: 68.07%, loss:0.1335.   
epoch:15,time:4.2s.ner: , loss:0.1345.   
epoch:15,time:1.0s.ner: P: 63.46% R: 70.21% F1: 66.67%, loss:0.1289.   
epoch:16,time:5.0s.ner: , loss:0.1176.   
epoch:16,time:1.2s.ner: P: 57.10% R: 73.62% F1: 64.31%, loss:0.1487.   
epoch:17,time:4.9s.ner: , loss:0.1348.   
epoch:17,time:1.4s.ner: P: 63.60% R: 73.62% F1: 68.24%, loss:0.1442.   
epoch:18,time:5.9s.ner: , loss:0.1136.   
epoch:18,time:1.2s.ner: P: 58.89% R: 71.91% F1: 64.75%, loss:0.1491.   
epoch:19,time:5.1s.ner: , loss:0.0748.   
epoch:19,time:1.1s.ner: P: 60.92% R: 73.62% F1: 66.67%, loss:0.1479.   
epoch:20,time:5.1s.ner: , loss:0.0916.   
epoch:20,time:1.0s.ner: P: 70.77% R: 78.30% F1: 74.34%, loss:0.1347.   
epoch:21,time:4.9s.ner: , loss:0.0780.   
epoch:21,time:1.0s.ner: P: 64.26% R: 75.74% F1: 69.53%, loss:0.1741.   
epoch:22,time:5.2s.ner: , loss:0.0687.   
epoch:22,time:1.2s.ner: P: 66.67% R: 71.49% F1: 68.99%, loss:0.1669.   
epoch:23,time:4.9s.ner: , loss:0.0540.   
epoch:23,time:1.1s.ner: P: 65.13% R: 72.34% F1: 68.55%, loss:0.1762.   
epoch:24,time:4.9s.ner: , loss:0.0549.   
epoch:24,time:1.3s.ner: P: 67.67% R: 76.60% F1: 71.86%, loss:0.1543.   
epoch:25,time:5.6s.ner: , loss:0.0476.   
epoch:25,time:1.4s.ner: P: 66.41% R: 73.19% F1: 69.64%, loss:0.1782.   
epoch:26,time:7.8s.ner: , loss:0.0471.   
epoch:26,time:1.2s.ner: P: 66.93% R: 72.34% F1: 69.53%, loss:0.1602.   
epoch:27,time:5.4s.ner: , loss:0.0346.   
epoch:27,time:1.1s.ner: P: 68.97% R: 76.60% F1: 72.58%, loss:0.1586.   
epoch:28,time:4.9s.ner: , loss:0.0299.   
epoch:28,time:1.0s.ner: P: 67.57% R: 74.47% F1: 70.85%, loss:0.1590.   
epoch:29,time:4.7s.ner: , loss:0.0254.   
epoch:29,time:0.8s.ner: P: 69.38% R: 76.17% F1: 72.62%, loss:0.1599.   
epoch:30,time:4.2s.ner: , loss:0.0367.   
epoch:30,time:0.8s.ner: P: 69.65% R: 76.17% F1: 72.76%, loss:0.1610.   
