from .utils import configMap, read_metadata, write_metadata
from .database import DatabaseClient, session_scope
from sqlalchemy import text
from .sync_tugraph import TuGraphData
from tugraph import GraphDatabase
from common.logger import logger
from common.http_client import hc


class SyncData(DatabaseClient):

    def __init__(self, database_uri):
        self.config = configMap()
        super().__init__(database_uri)
        tugraph_db = GraphDatabase(**self.config.TUGRAPH_CONFIG)
        self.tugraph_data = TuGraphData(tugraph_db)

    @logger.catch
    def run(self, id, map_config):
        try:
            table_name = map_config.get("tableName")
            # sql
            _sql = self.config.SQL
            # read last_id
            last_id = read_metadata(self.config.LAST_METADATA_PATH,
                                    f"{self.database}_{table_name}") if not self.config.LAST_ID else self.config.LAST_ID
            _sql = _sql.replace("_tb", table_name)
            sql = text(_sql).bindparams(last_id=last_id)
            # 查询数据
            with session_scope(self.session) as db_session:
                result = db_session.execute(sql)
                data = result.fetchall()
            record = len(data)
            # 序列化数据
            for row in data:
                record -= 1
                hc.request_api("POST", "http://localhost:8000/map/update_status", json={"id": id, "dataNums": len(data),
                                                                                      "surplus": record, "nodeNums": 6,
                                                                                      "relationNums": 6})
                item_data = dict(zip(result.keys(), row))
                current_id = item_data.get(self.config.PK)
                write_metadata(self.config.LAST_METADATA_PATH, current_id, f"{self.database}_{table_name}")
                new_item_data = {
                    "dh": item_data.get(map_config.get("dh")),
                    "qzmc": item_data.get(map_config.get("qzmc")) if item_data.get(map_config.get("qzmc")) else "",
                    "tm": item_data.get(map_config.get("tm")) if item_data.get(map_config.get("tm")) else "",
                    "cwsj": item_data.get(map_config.get("cwsj")).strftime('%Y-%m-%d %H:%M:%S') if item_data.get(
                        map_config.get("cwsj")) else "",
                    "mj": item_data.get(map_config.get("mj")) if item_data.get(map_config.get("mj")) else "",
                    "wjlj": item_data.get(map_config.get("wjlj")),
                    "wh": item_data.get(map_config.get("wh")) if item_data.get(map_config.get("wh")) else "",
                    "jh": item_data.get(map_config.get("jh")) if item_data.get(map_config.get("jh")) else "",
                    "jxh": item_data.get(map_config.get("jxh")) if item_data.get(map_config.get("jxh")) else "",
                    "syh": item_data.get(map_config.get("syh")) if item_data.get(map_config.get("syh")) else ""
                }
                self.tugraph_data.sync_tugraph(new_item_data, self.config.IMG_DIR_PATH,
                                               self.config.TUGRAPH_CONFIG.get("graph_name"))
        except Exception as e:
            hc.request_api("POST", "http://localhost:8000/map/update_status", json={"id": id, "status": 2})
            import traceback
            raise Exception(traceback.format_exc())
        finally:
            hc.request_api("POST", "http://localhost:8000/map/stop_map", json={"id": id})
