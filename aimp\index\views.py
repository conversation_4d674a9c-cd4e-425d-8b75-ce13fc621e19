from fastapi import APIRouter
from common.http_client import hc
from common.customResponse import resp
from common.logger import logger

router = APIRouter(
    prefix="/index",
    tags=["index"],
    responses={404: {"description": "Not found"}}
)


@router.get("/server_info")
@logger.catch
def server_info():
    ret = hc.request_api("GET", "http://ubt-b450m-02.lan:9000/workload/info")
    print(ret.json())

    # CPUsState cpu使用量
    # Reason  节点下线原因
    # Gres  资源描述  gpu:1660:1,mps:1660:200  名称:子名称:数量
    #       gpu: 指物理gpu 数量就是卡的张数
    #       mps: 英伟达多进程共享GPU运行环境 （NVIDIA MPS），用于分配卡资源给多个程序
    # NodeList 节点名， 目前不会出现若其他字段都相同，会进行节点名称合并 ubt-B450M-[02-03] node[001-003,100-299]
    # CPUsLoad CPU使用率
    # Partition 节点分区名称，指集群管理员配置将节点分组的组名
    # StateLong 节点状态（完整写法）
    # FreeMem 操作系统报告的未使用内存（MB）
    # OverSubscribe 节点是否允许CPU等过载（如为YES，则CPU使用数会超过总数）
    # Memory 内存数 （配置设置）

    return resp(data=ret.json())
