from collections import Counter, OrderedDict
from torchtext.vocab import vocab
from typing import Iterable, Optional, List
from qa.kbnlp.utils.datareader import CoNLLReader
from qa.kbnlp.pretrained.standard import Sample
import torch


def build_vocab_from_counter(counter: Counter,
                             min_freq: int = 1,
                             specials: Optional[List[str]] = ['<unk>'],
                             special_first=True):
    if specials is not None:
        for tok in specials:
            del counter[tok]
    sorted_by_freq_tuples = sorted(counter.items(), key=lambda x: x[0])
    sorted_by_freq_tuples.sort(key=lambda x: x[1], reverse=True)
    ordered_dict = OrderedDict(sorted_by_freq_tuples)
    if specials is not None:
        if special_first:
            specials = specials[::-1]
        for symbol in specials:
            ordered_dict.update({symbol: min_freq})
            ordered_dict.move_to_end(symbol, last=not special_first)
    voc = vocab(ordered_dict, min_freq=min_freq)
    voc.specials = list(reversed(specials))
    return voc


def build_vocabs_from_CoNLL_iterator(iterator: Iterable,
                                     min_freqs: List[int] = [1, 1, 1],
                                     specials: Optional[List[List[str]]] = [['<unk-w>', '<root-w>'], ['<unk-t>', '<root-t>'], ['<unk-l>']],
                                     default_indice: List[int] = [0, 0, 0],
                                     special_first: bool = True):
    word_counter, tag_counter, label_counter = [Counter(), Counter(), Counter()]
    for words, heads, tags, labels in iterator:
        # TODO:将isProjective放入到utils包下
        sample = Sample(words=words, heads=heads, tags=tags, labels=labels)
        if sample.isProjective():
            word_counter.update(words)
            tag_counter.update(tags)
            label_counter.update(labels)
    word_voc = build_vocab_from_counter(word_counter, min_freq=min_freqs[0], specials=specials[0],
                                        special_first=special_first)
    tag_voc = build_vocab_from_counter(tag_counter, min_freq=min_freqs[1], specials=specials[1],
                                       special_first=special_first)
    label_voc = build_vocab_from_counter(label_counter, min_freq=min_freqs[2], specials=specials[2],
                                         special_first=special_first)
    if default_indice[0] is not None:
        word_voc.set_default_index(default_indice[0])
    if default_indice[1] is not None:
        tag_voc.set_default_index(default_indice[1])
    if default_indice[2] is not None:
        label_voc.set_default_index(default_indice[2])
    return word_voc, tag_voc, label_voc


def load_vectors(voc, vectors, norm=False):
    if not isinstance(vectors, list):
        vectors = [vectors]
    tot_dim = sum(v.dim for v in vectors)
    weights = torch.Tensor(len(voc), tot_dim)
    for i, token in enumerate(voc.get_itos()):
        start_dim = 0
        for v in vectors:
            end_dim = start_dim + v.dim
            weights[i][start_dim:end_dim] = v[token.strip()] / torch.norm(v[token.strip()], p=2) if norm \
                else v[token.strip()]
            start_dim = end_dim
        assert (start_dim == tot_dim)
    return weights


def unk_init(tensor):
    return torch.randn(tensor.size(), out=tensor)


if __name__ == '__main__':
    corpus = '../raw_data/train.conll'
    _word_voc, _tag_voc, _label_voc = build_vocabs_from_CoNLL_iterator(CoNLLReader(corpus))
    _word_voc.set_default_index(0)
    _tag_voc.set_default_index(0)
    _label_voc.set_default_index(0)
    print(len(_word_voc), _word_voc.get_itos(), len(_tag_voc), _tag_voc.get_itos(),
          len(_label_voc), _label_voc.get_itos(), sep='\n')
    # _vectors = Vectors('../data/vectors-100.txt', unk_init=unk_init)
    # print(_vectors.vectors.size())
    # _weights = load_vectors(_word_voc, _vectors)
    # print(_weights.size())
    print(_word_voc(['的']))
    # print(_weights[_word_voc(['八万一千二百六十五'])])
    # print(_weights[_word_voc(['**&(&*((**'])])
    # print(_weights[_word_voc(['**&(erqwe&*((**'])])







