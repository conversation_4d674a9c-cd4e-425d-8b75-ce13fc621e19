from typing import Dict, List
from functools import partial
import torch
from torch.utils.data.dataloader import DataLoader
from qa.kbnlp.mlt.dataset import BaseDataset
from qa.kbnlp.mlt.data_transform import TransformList
from qa.kbnlp.mlt.task import Task
from qa.kbnlp.mlt.dataset import SortingSamplerBuilder
from qa.kbnlp.mlt.common.vocab import Vocab
from qa.kbnlp.mlt.common.metric import F1
from qa.kbnlp.mlt.data_transform import VocabDict
from qa.kbnlp.mlt.util import merge_locals_kwargs, ids_to_tags
from qa.kbnlp.mlt.util.tensor_util import lengths_to_mask
from qa.kbnlp.mlt.layers.decoder.linear_decoder import LinearCRFDecoder


def read_pos_data(path, sep=None):
    data = []
    with open(path, 'r', encoding='utf-8') as fr:
        ws, ts = [], []
        for line in fr:
            line = line.strip().split(sep)[:2]
            if len(line) == 2:
                ws.append(line[0])
                ts.append(line[1])
            elif len(line) == 0:
                data.append((ws, ts))
                ws, ts = [], []
            else:
                raise ValueError('仅支持csv格式，前两列分别是单词和词性标注。')
    return data


class PosTagDataset(BaseDataset):
    def __init__(self, data: List[Dict], transform_list: TransformList = None):
        super().__init__(data, transform_list)


class PosTag(Task):
    def __init__(self,
                 trn=None,
                 dev=None,
                 tst=None,
                 batch_size=None,
                 batch_max_tokens=None,
                 eval_batch_size=None,
                 eval_batch_max_tokens=None,
                 # dependencies: str = None,
                 lr=None,
                 separate_optimizer=False,
                 cls_is_bos=False,
                 sep_is_eos=False,
                 **kwargs,
                 ):
        super(PosTag, self).__init__(**merge_locals_kwargs(locals(), kwargs, excludes=['self', 'kwargs', '__class__']))
        self.vocabs = VocabDict()  # 至少需要一个tag的vocab

    def build_model(self, encoder_size, task_name, **kwargs):
        return LinearCRFDecoder(encoder_size, num_labels=len(self.vocabs['%s_tag' % task_name]))

    def build_sample(self, data):
        examples = []
        for i, d in enumerate(data):
            ws, ts = None, None
            if len(d) == 2:
                ws, ts = d
            else:
                ws = d
            examples.append({'original_input': ws})
            examples[-1]['_id'] = i
            if isinstance(ws, list):
                examples[-1]['token'] = ws
                if ts:
                    examples[-1]['postag'] = ts
            else:
                raise ValueError('token保存的是单词列表。')
        return examples

    def build_transformList(self, mode, task_name, **kwargs):
        if mode == 'train' or mode == 'eval':
            def rename(sample, src_name, tar_name):
                sample[tar_name] = sample.pop(src_name)
                return sample
            rename_transform = partial(rename, src_name='postag', tar_name='%s_tag' % task_name)
            return TransformList(rename_transform, self.vocabs)
        else:
            return TransformList()

    def build_dataset(self, data, transform_list, **kwargs):
        return PosTagDataset(data, transform_list=transform_list)

    def build_dataloader(self, dataset, batch_size=None, batch_max_tokens=None, shuffle=False, generator=None, gradient_accumulation=1,
                         use_effective_tokens=False, **kwargs):
        return DataLoader(dataset=dataset,
                          batch_sampler=SortingSamplerBuilder.build([data['subtoken_len'] for data in dataset],
                                                                    batch_size, batch_max_tokens, use_effective_tokens,
                                                                    shuffle, generator, gradient_accumulation),
                          collate_fn=partial(PosTagDataset.collate_fn, padding_value=0))

    def build_criterion(self, reduction='mean', **kwargs):
        return torch.nn.CrossEntropyLoss(reduction=reduction)

    def build_optimizer(self, decoder, **kwargs):
        pass

    def compute_loss(self, task_name, batch, out, mask, criterion):
        y = batch['%s_tag_ids' % task_name]
        loss = criterion(out[mask], y[mask])
        return loss

    def build_metric(self):
        return F1()

    def update_metric(self, prediction, batch, metric, task_name):
        gold_tags = batch['%s_tag' % task_name]
        for pred, gold in zip(prediction, gold_tags):
            metric(set(pred), set(gold))

    def feed_batch(self, mode, h: torch.FloatTensor, batch: Dict, output_dict, decoder: torch.nn.Module, task_name):
        mask = lengths_to_mask(batch['token_len'] - 2)
        logits = decoder(h, batch=batch, mask=mask)
        output_dict[task_name] = {
            'output': logits,
            'mask': mask
        }

    def decode_output(self, mode, output_dict, batch, model, task_name, **kwargs):
        output_per_task = output_dict[task_name]
        output = output_per_task['output']
        mask = output_per_task['mask']
        predict_tag_ids = torch.argmax(output, dim=-1)
        predict_tags = ids_to_tags(predict_tag_ids.tolist(), mask.sum(1).tolist(), self.vocabs['%s_tag' % task_name])
        output_per_task['prediction'] = predict_tags

    def prediction_to_result(self, pred, vocab, batch, output_dict, task_name,  **kwargs):
        return pred

    def post_transform(self, task_name, batch, output_dict, device, **kwargs):
        output_dict.pop(task_name)


if __name__ == '__main__':
    HOME = 'C:/Users/<USER>/AppData/Roaming/hanlp'
    _CTB8_HOME = HOME + '/thirdparty/wakespace.lib.wfu.edu/bitstream/handle/10339/39379/LDC2013T21/data/'
    CTB8_POS_TRAIN = _CTB8_HOME + 'tasks/pos/train.tsv'
    CTB8_POS_DEV = _CTB8_HOME + 'tasks/pos/dev.tsv'
    CTB8_POS_TEST = _CTB8_HOME + 'tasks/pos/test.tsv'
    # print(len(read_pos_data(path=CTB_POS_TRAIN)[:10]))
    pos = PosTag(trn=read_pos_data(path=CTB8_POS_TRAIN)[:10])
    examples = pos.build_sample(pos.trn)
    pos.build_vocab(task_name='pos')
    transform_list_train = pos.build_transformList(predict=False, task_name='pos')
    dataset = pos.build_dataset(data=examples, transform_list=transform_list_train)
    pos.vocabs.lock()
    # loader = pos.build_dataloader(dataset, batch_size=6)
    # for batch in loader:
    #     print(batch)

    print(pos.vocabs)

    vocab = pos.vocabs['pos_tag']
    vocab.NR_index = 0
    print(vocab.__dict__)

    pos.vocabs.save_vocabs(save_dir='./pos.vocab')
    pos.vocabs.load_vocabs(save_dir='./pos.vocab')

    vocab = pos.vocabs['pos_tag']
    vocab.NR_index = 0
    print(vocab.__dict__)

    print('finish.')
