import jieba
import hanlp
from datareader import CoNLLReader
from collections import defaultdict
import jieba.posseg as pseg


tok_pos = hanlp.load(hanlp.pretrained.mtl.CLOSE_TOK_POS_NER_SRL_DEP_SDP_CON_ELECTRA_SMALL_ZH)

path_train = '../raw_data/train.conll'
path_dev = '../raw_data/dev.conll'
path_test = '../raw_data/test.conll'
sen_train = CoNLLReader(path=path_train, check_projective=False)
sen_dev = CoNLLReader(path=path_dev, check_projective=False)
sen_test = CoNLLReader(path=path_test, check_projective=False)


def write_dictionary():
    vocab = defaultdict(int)
    raw_sens = []

    sen_ctb = []
    hanlp_pseg_cut = []

    jieba_cut = []
    jieba_cut_user_dict = []
    jieba_pseg_cut = []

    t_vocab = defaultdict(int)

    def update(data):
        for ws, _, _, _ in data:
            raw_sens.append(''.join(ws))
            for w in ws:
                vocab[w] += 2
            sen_ctb.append(' '.join(ws))
            postag = tok_pos(raw_sens[-1], tasks=['tok/coarse', 'pos/ctb'], skip_tasks=['tok/fine'])
            hanlp_pseg_cut.append(''.join(postag['tok/coarse']))
            for t in postag['pos/ctb']:
                t_vocab[t] += 1
            # jieba_cut.append(' '.join(jieba.cut(raw_sens[-1])))

    update(sen_train)
    update(sen_dev)
    update(sen_test)

    with open(file='dictionary.txt', mode='w', encoding='utf-8') as fw:
        for w, c in vocab.items():
            fw.write(w + ' ' + str(c) + '\n')

    # jieba.load_userdict('dictionary.txt')
    # for sen in raw_sens:
    #     jieba_cut_user_dict.append(' '.join(jieba.cut(sen)))
    #     z = zip(*[(w, t) for w, t in pseg.cut(sen)])
    #     w_sen, t_sen = tuple(z)
    #     for t in t_sen:
    #         t_vocab[t] += 1
    #     jieba_pseg_cut.append(' '.join(w_sen))
    #
    for i, j, k in zip(sen_ctb, jieba_pseg_cut, hanlp_pseg_cut):
        print(i, j, k, sep='\n')

    for t, c in t_vocab.items():
        print(t, c)


def replace_tags(path):
    data = CoNLLReader(path, check_projective=False)
    with open(path + '.hanlp.fine.pseudo', 'w', encoding='utf-8') as fw:
        for ws, hs, _, ls in data:
            hs = [h if h != (len(ws) + 1) else 0 for h in hs]
            ts = ['<unk-t>'] * len(ws)
            s, e = 0, 0
            d_ctb = dict()
            for i, w in enumerate(ws):
                s = e + 1
                e = s + len(w) - 1
                d_ctb[(s, e)] = i
            s, e = 0, 0
            d_jieba = dict()
            # postag = tok_pos(''.join(ws), tasks=['tok/coarse', 'pos/ctb'], skip_tasks=['tok/fine'])
            # pair = [(w, t) for w, t in zip(postag['tok/coarse'], postag['pos/ctb'])]

            postag = tok_pos(''.join(ws), tasks=['tok/fine', 'pos/ctb'], skip_tasks=['tok/coarse'])
            pair = [(w, t) for w, t in zip(postag['tok/fine'], postag['pos/ctb'])]
            # pair = [pair for pair in pseg.cut(''.join(ws)) if len(pair.word) > 0]
            for w, t in pair:
                s = e + 1
                e = s + len(w) - 1
                if (s, e) in d_ctb:
                    ts[d_ctb[(s, e)]] = t

            for i, (w, t, h, l) in enumerate(zip(ws, ts, hs, ls)):
                fw.write('  '.join([str(i+1), w, '_', t, t, '_', str(h), l, '_', '_']) + '\n')
            fw.write('\n')


if __name__ == '__main__':
    # write_dictionary()
    # # jieba.load_userdict('dictionary.txt')
    replace_tags(path=path_train)
    replace_tags(path=path_dev)
    replace_tags(path=path_test)
    postag = tok_pos('使用更大的方差来试探最优点的质量。', tasks=['tok/coarse', 'pos/ctb'], skip_tasks=['tok/fine'])
    pair = [(w, t) for w, t in zip(postag['tok/coarse'], postag['pos/ctb'])]
    print(postag)
    print(pair)

